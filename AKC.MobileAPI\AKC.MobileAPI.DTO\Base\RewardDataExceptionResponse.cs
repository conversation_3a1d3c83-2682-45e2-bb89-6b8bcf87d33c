﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Base
{
    public class RewardDataExceptionResponse
    {
        public int status { get; set; }
        public RewardDataExceptionResultItem result { get; set; }
    }
    public class RewardDataExceptionResultItem
    {
        public string code { get; set; }
        public string message { get; set; }
    }
    public class LoyaltyExceptionResponse
    {
        public int result { get; set; }
        public bool success { get; set; }
        public LoyaltyExceptionErrorItem error { get; set; }
    }

    public class LoyaltyExceptionErrorItem
    {
        public string code { get; set; }
        public string message { get; set; }
    }
}
