﻿using AKC.MobileAPI.DTO.Loyalty.GiftFlashSaleProgram;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyGiftFlashSaleProgramService
    {
        Task<GiftDetailFlashSaleProgramOutput> GetGiftDetailFlashSaleProgram(GiftDetailFlashSaleProgramInput input);
        Task<GiftFlashSaleProgramOutput> ListGiftByFlashSaleProgram(GiftFlashSaleProgramInput input);
        Task<GiftFlashSaleProgramForViewOutput> GetAllFlashSaleProgram(GetAllFlashSaleProgramInput input);
    }
}
