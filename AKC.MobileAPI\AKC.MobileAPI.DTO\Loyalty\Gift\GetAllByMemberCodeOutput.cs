﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
	public class GetAllByMemberCodeOutput
	{
		public ListResultGetAllByMemberCodeOutput Result { get; set; }
		public string TargetUrl { get; set; }
		public bool Success { get; set; }
		public string Error { get; set; }
		public bool UnAuthorizedRequest { get; set; }
		public bool __abp { get; set; }
	}
	public class ListResultGetAllByMemberCodeOutput
	{
		public int TotalCount { get; set; }

		public List<ListResultListResultGetAllByMemberCodeOutputIteams> Items { get; set; }
	}

	public class ListResultListResultGetAllByMemberCodeOutputIteams
	{
		public GetAllByMemberCodeDto GiftCategory { get; set; }

		public string ParentNameGiftCategory { get; set; }
	}
	
	public class GetAllByMemberCodeDto
	{
		public string Code { get; set; }

		public string Name { get; set; }

		public string Description { get; set; }

		public string Status { get; set; }

		public int Level { get; set; }

		public string ParentCode { get; set; }


		public int? ParentId { get; set; }

		public ImageLinkDto ImageLink { get; set; }

		public int? VendorId { get; set; }
		public bool Is3rdPartyGiftCategory { get; set; }
		public int? ThirdPartyGiftCategoryId { get; set; }
		public string GiftListJSON { get; set; }

		public string VendorName { get; set; }
		public DateTime? LastModificationTime { get; set; }
		public long? LastModifierUserId { get; set; }
		public string LastModifierUserName { get; set; }
		public int Id { get; set; }

	}
}
