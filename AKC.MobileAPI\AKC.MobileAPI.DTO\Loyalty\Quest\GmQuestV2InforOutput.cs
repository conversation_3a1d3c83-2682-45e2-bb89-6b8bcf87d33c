﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Quest
{
    public class GmQuestV2InforOutput
    {
        public GmQuestV2Infor result { get; set; }
        public object targetUrl { get; set; }
        public bool success { get; set; }
        public object error { get; set; }
        public bool unAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class GmQuestV2Infor
    {
        public GmQuestV2Dto GmQuestV2Dto { get; set; }
        public List<GmMissionV2Dto> GmMissionV2Dtos { get; set; }
        public List<GiftClaimedOnQuest> ListGiftClaimedOnQuest { get; set; }
        public List<CoinClaimOnQuest> ListCoinClaimedOnQuest { get; set; }
    }

    public class GmMissionV2Dto
    {
        public int Id { get; set; }
        public int? TenantId { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Status { get; set; }
        public int ActionCount { get; set; }
        public string ActionCodeForCoin { get; set; }
        public string ActionCodeForGift { get; set; }
        public int? ClaimDuration { get; set; }
        public DateTime? ClaimTime { get; set; }
        public string Description { get; set; }
        public long? CreatorUserId { get; set; }
        public DateTime CreationTime { get; set; }
        public string CreatorUserName { get; set; }
        public long? LastModifierUserId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string LastModifierUserName { get; set; }
        public int TotalAvailableSlot { get; set; }
        public int TotalRewardedSlot { get; set; }
        public bool AllowOnce { get; set; }
        public string QuestCode { get; set; }
    }

    public class GiftClaimedOnQuest
    {
        public int? GiftId { get; set; }
        public string GiftCode { get; set; }
        public string GiftName { get; set; }
        public DateTime? ExpireDate { get; set; }
        public string ImageLink { get; set; }
        public string TransactionCode { get; set; }
    }

    public class CoinClaimOnQuest
    {
        public decimal? Coin { get; set; }
        public decimal? Point { get; set; }
    }

}
