using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.MobileAPI;
using AKC.MobileAPI.Service.Abstract;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Astrologee
{
    [Route("api/astrologee")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class AstrologeeController : ControllerBase
    {
        private readonly ILogger<AstrologeeController> _logger;
        private readonly IDistributedCache _cache;
        private readonly IAstrologeeService _service;
        private readonly IExceptionReponseService _exceptionReponseService;

        public AstrologeeController(IExceptionReponseService p, IAstrologeeService sv, ILogger<AstrologeeController> lg)
        {
            _service = sv;
            _logger = lg;
            _exceptionReponseService = p;
        }

        [HttpGet]
        [Route("now")]
        public async Task<ActionResult<LunarCalendarCellDto>> Now()
        {
            try
            {
                var res = await _service.Now();
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result;
                _logger.LogError(ex, "GET NOW() - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }
        [HttpGet]
        [Route("list-years")]
        public async Task<ActionResult<List<YearDto>>> Years([FromQuery] GetYearsInput input)
        {
            try
            {
                var res = await _service.Years(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result;
                _logger.LogError(ex, "Get List Years- Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }

        private LunarCalendarCellDto getMockData()
        {
            string jsonData = @"
                    {
                        'LunarHolidayName': '',
                        'HourName': 'Mậu Tý',
                        'MonthName': 'Tân Mùi',
                        'YearName': 'Giáp Thìn',
                        'DateName': 'Bính Thân',
                        'DayOfWeek': 'WED',
                        'LunarDateNumber': 26,
                        'LunarMonthNumber': 6,
                        'LunarYearNumber': 2024,
                        'SolarDateNumber': 31,
                        'SolarMonthNumber': 7,
                        'SolarYearNumber': 2024,
                        'LuckyHours': [
                            {
                                'name': 'Tý',
                                'time': [23, 1]
                            },
                            {
                                'name': 'Sửu',
                                'time': [1, 3]
                            },
                            {
                                'name': 'Thìn',
                                'time': [7, 9]
                            },
                            {
                                'name': 'Tỵ',
                                'time': [9, 11]
                            },
                            {
                                'name': 'Mùi',
                                'time': [13, 15]
                            },
                            {
                                'name': 'Tuất',
                                'time': [19, 21]
                            }
                        ]
                    }";
            var res = JsonConvert.DeserializeObject<LunarCalendarCellDto>(jsonData);
            return res;
        }

        [HttpGet]
        [Route("month-calendar")]
        public async Task<ActionResult<CalOfMonthOutput>> CalOfMonth([FromQuery] MonthAndYearNum input)
        {
            try
            {
                var res = await _service.CalOfMonth(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result;
                _logger.LogError(ex, "Get CalOfMonth - Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }
        
        [HttpGet]
        [Route("get-n-dates")]
        public async Task<ActionResult<List<LunarCalendarCellDto>>> GetNDates([FromQuery] GetNDatesInput input)
        {
            if (input == null || input.Year < 0 || input.Month < 1 || input.Month > 12 || input.N < 0)
            {
                return StatusCode(400, new { Message = "InvalidInput"});
            }

            try
            {
                var res = await _service.GetNDates(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result;
                _logger.LogError(ex, "Get CalOfMonth - Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }


        [HttpGet]
        [Route("get-document")]
        public async Task<ActionResult<TamLynkDocumentResponse>> GetDocument([FromQuery] GetDocumentInput input)
        {
            try
            {
                var res = await _service.GetDocument(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result;
                _logger.LogError(ex, "get-document - Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }
    }
}