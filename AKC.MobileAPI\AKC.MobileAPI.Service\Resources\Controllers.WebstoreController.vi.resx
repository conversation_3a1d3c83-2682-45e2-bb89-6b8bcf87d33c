<?xml version="1.0" encoding="utf-8"?>
<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
        <xsd:element name="root" msdata:IsDataSet="true">
            <xsd:complexType>
                <xsd:choice maxOccurs="unbounded">
                    <xsd:element name="metadata">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" />
                            </xsd:sequence>
                            <xsd:attribute name="name" use="required" type="xsd:string" />
                            <xsd:attribute name="type" type="xsd:string" />
                            <xsd:attribute name="mimetype" type="xsd:string" />
                            <xsd:attribute ref="xml:space" />
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="assembly">
                        <xsd:complexType>
                            <xsd:attribute name="alias" type="xsd:string" />
                            <xsd:attribute name="name" type="xsd:string" />
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="data">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="0" />
                            <xsd:attribute name="type" type="xsd:string" />
                            <xsd:attribute name="mimetype" type="xsd:string" />
                            <xsd:attribute ref="xml:space" />
                        </xsd:complexType>
                    </xsd:element>
                </xsd:choice>
            </xsd:complexType>
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>2.0</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="InvalidCredentials" xml:space="preserve">
    <value>Thông tin đăng nhập không chính xác</value>
  </data>
    <data name="LoginError" xml:space="preserve">
    <value>Có lỗi xảy ra khi thực hiện đăng nhập</value>
  </data>
    <data name="RegistrationRequired" xml:space="preserve">
    <value>SME chưa được đăng ký sử dụng dịch vụ</value>
  </data>
    <data name="RepPhoneMismatch" xml:space="preserve">
    <value>Số điện thoại của đại diện không còn hợp lệ. Vui lòng xác thực lại.</value>
  </data>
    <data name="ThirdPartyError" xml:space="preserve">
    <value>Có lỗi xảy ra khi kiểm tra thông tin SME</value>
  </data>
    <data name="InternalServerError" xml:space="preserve">
    <value>Có lỗi xảy ra, vui lòng thử lại sau</value>
  </data>
    <data name="InvalidRefreshToken" xml:space="preserve">
    <value>Refresh token không hợp lệ</value>
  </data>
    <data name="InvalidOrExpiredRefreshToken" xml:space="preserve">
    <value>Refresh token không hợp lệ hoặc đã hết hạn</value>
  </data>
    <data name="MemberNotExistOrInactive" xml:space="preserve">
        <value>Thành viên không tồn tại hoặc đang inactive </value>
    </data>
</root>