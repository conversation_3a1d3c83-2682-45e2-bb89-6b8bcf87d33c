﻿using AKC.MobileAPI.DTO.Gamification;
using AKC.MobileAPI.DTO.Gamification.Exchange;
using AKC.MobileAPI.DTO.Loyalty.Adjust;
using AKC.MobileAPI.DTO.Gamification.Game;
using AKC.MobileAPI.Service.Abstract.Gamification;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using System.Security.Cryptography;
using AKC.MobileAPI.DTO.Loyalty.Payment;
using AKC.MobileAPI.DTO.Reward.Claim_Deposit;
using AKC.MobileAPI.Service.Reward;
using Microsoft.AspNetCore.Http.Extensions;
using AKC.MobileAPI.Service.Helpers;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.DTO.Reward.Payment;
using AKC.MobileAPI.Service.Abstract.Reward;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/Payment")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    public class LoyaltyPaymentController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltyPaymentService _loyaltyPaymentService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IRewardCashoutTransactionService _rewardCashoutTransactionService;
        public LoyaltyPaymentController(
            ILogger<LoyaltyPaymentController> logger,
            IExceptionReponseService exceptionReponseService,
            ILoyaltyPaymentService loyaltyPaymentService,
            ICommonHelperService commonHelperService,
            IRewardCashoutTransactionService rewardCashoutTransactionService
            )
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _loyaltyPaymentService = loyaltyPaymentService;
            _commonHelperService = commonHelperService;
            _rewardCashoutTransactionService = rewardCashoutTransactionService;
        }

        [HttpGet]
        [Route("GetDataBackUrl")]
        public async Task<ActionResult<GetDataFromURLBackUrlOutputDto>> GetDataFromURLBackUrl()
        {
            try
            {
                return new GetDataFromURLBackUrlOutputDto()
                {
                    Message = "Cancel transaction",
                    Result = 200,
                };
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetDataFromURLBackUrl Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetDataReturnUrl")]
        public async Task<ActionResult<GetDataFromURLReturnUrlOutputDto>> GetDataFromURLReturnUrl()
        {
            try
            {
                var url = Request.Scheme + "://" + Request.Host.Value + Request.GetEncodedPathAndQuery();
                _logger.LogInformation("topup transaction payment: " + url);
                var result = await _loyaltyPaymentService.GetDataFromReturnUrl(url, Request.Query);
                _logger.LogInformation("topup transaction result data url: " + url);
                if (result != null && result.IsBackQuery)
                {
                    return StatusCode(200, result);
                }
                return StatusCode(200);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetDataFromURLCallBack Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("GeneratePaymentLink")]
        [Authorize]
        public async Task<ActionResult<GeneratePaymentLinkResponse>> GeneratePaymentLink ([FromBody] GeneratePaymentLinkRequest input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var url = Request.Scheme + "://" + Request.Host.Value;
                _logger.LogInformation("return url generate payment: " + url);
                var result = await _loyaltyPaymentService.GeneratePaymentLink(url, input, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GeneratePaymentLink Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetFeeCashOut")]
        [Authorize]
        public async Task<ActionResult<GetFeeCashOutOutput>> GetFeeCashOut([FromQuery] GetFeeCashOutInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                _logger.LogInformation("GetGlobalSetting Start");
                var resultFee = await _rewardCashoutTransactionService.GetGlobalSetting();
                _logger.LogInformation("GetGlobalSetting End");
                var totalAmount = input.RequiredCoin * input.Quanity;
                var feeAmount = Math.Round((totalAmount * resultFee.FeeAmountCashoutAmountRatio) / 100);
                feeAmount = feeAmount < resultFee.MinimumCashoutFeeAmount ? resultFee.MinimumCashoutFeeAmount : feeAmount;
                feeAmount = feeAmount > resultFee.MaximumCashoutFeeAmount ? resultFee.MaximumCashoutFeeAmount : feeAmount;

                var result = new GetFeeCashOutOutput()
                {
                    Result = new FeeCashOut() { Fee = feeAmount },
                    Success = true
                };
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetFeeCashOut Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }
    }
}
