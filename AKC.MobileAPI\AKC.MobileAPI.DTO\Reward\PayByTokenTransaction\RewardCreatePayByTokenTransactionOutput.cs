﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.PayByTokenTransaction
{
    public class RewardCreatePayByTokenTransactionOutput
    {
        public int Result { get; set; }
        public RewardCreatePayByTokenTransactionResult Items { get; set; }
        public string Message { get; set; }
    }

    public class RewardCreatePayByTokenTransactionResult
    {
        public int MemberId { get; set; }
        public string NationalId { get; set; }
        public string OrderCode { get; set; }
        public double PointAmount { get; set; }
    }
}
