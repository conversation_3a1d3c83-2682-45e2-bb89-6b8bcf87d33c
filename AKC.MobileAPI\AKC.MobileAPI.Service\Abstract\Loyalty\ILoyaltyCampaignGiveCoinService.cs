﻿using AKC.MobileAPI.DTO.Loyalty;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyCampaignGiveCoinService
    {
        Task<GetActiveCampaignOutput> GetActiveCampaign();
        Task<CreateOrEditMemberCampaignGiveOutput> Create(CreateOrEditMemberCampaignGiveCoinDto input);
        Task<GetMemberByCifOutput> GetMemberByCif(GetMemberByCifInput input);
        Task<GetTransactionForCampaignOutput> GetTransactionForCampaign(GetTransactionForCampaignInput input);
        Task<GetListRegistrationByMemberCodeOutput> GetListRegistrationByMemberCode(GetListRegistrationByMemberCodeInput input);
    }
}
