﻿using AKC.MobileAPI.DTO.Loyalty.AuditLog;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions.ThirdParty;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.ThirdParty.Base
{
    public class BaseThirdPartyVPBankService
    {
        protected readonly HttpClient _client;
        protected readonly IConfiguration _configuration;
        protected readonly string baseURL;
        protected readonly string userName;
        protected readonly string password;
        protected readonly string IDNApp;
        protected readonly ILogger _logger;
        protected int tenantId;
        private readonly ILoyaltyAuditLogService _loyaltyAuditLogService;

        public BaseThirdPartyVPBankService(
            IConfiguration configuration,
            ILogger<BaseThirdPartyVPBankService> logger,
            ILoyaltyAuditLogService loyaltyAuditLogService)
        {
            _configuration = configuration;
            baseURL = _configuration.GetSection("ThirdPartyMerchant:VPBank:BaseURL").Value;
            IDNApp = _configuration.GetSection("ThirdPartyMerchant:VPBank:IDNApp").Value;
            userName = _configuration.GetSection("ThirdPartyMerchant:VPBank:UserName").Value;
            password = _configuration.GetSection("ThirdPartyMerchant:VPBank:Password").Value;
            HttpClientHandler clientHandler = new HttpClientHandler();
            clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
            _client = new HttpClient(clientHandler);
            _logger = logger;
            tenantId = Convert.ToInt32(_configuration.GetSection("Loyalty:TenantId").Value);
            _loyaltyAuditLogService = loyaltyAuditLogService;
        }

        public async Task<T> SendPostAsync<T>(string operationName, object body = null, HttpContext request = null, string memberCode = null, string apiURL = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body);

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", getAuthorization());
            req.Headers.Add("IDN-App", IDNApp);
            var xRequestId = GenOTPSession("VPB");
            req.Headers.Add("X-Request-Id", xRequestId);
            req.Headers.Add("X-Operation-Name", operationName);
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            _logger.LogInformation("Start call " + operationName + " vpbank request data: " + getLogText(JsonConvert.SerializeObject(body)));
            _logger.LogInformation("Start call " + operationName + " vpbank X-Request-ID: " + JsonConvert.SerializeObject(xRequestId));
            var startDate = DateTime.UtcNow;
            //var IP = request?.Connection.RemoteIpAddress.ToString();
            var Client_Request_Address = request.Request.Headers.ContainsKey("Client-Request-Address") ?
                                           request?.Request.Headers["Client-Request-Address"].ToString() : request?.Connection.RemoteIpAddress.ToString();
            
            // Add get dest IP from partner
            try
            {
                Uri myUri = new Uri(baseURL);
                var ipHost = Dns.GetHostAddresses(myUri.Host);
                if (ipHost != null && ipHost.Length > 0)
                {
                    Client_Request_Address = ipHost[0].MapToIPv4().ToString();
                }
            } catch { }
            // Add get dest IP from partner

            var flagWritelog = false;
            try
            {
                var response = await _client.SendAsync(req);
                var endDate = DateTime.UtcNow;
                var rawData = await response.Content.ReadAsStringAsync();
                dynamic obj = body;
                _logger.LogInformation("End call " + operationName + " vpbank response data: " + getLogText(rawData));
                OperationLog(operationName, xRequestId, body, rawData, response, startDate, endDate, Client_Request_Address, memberCode);
                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new ThirdPartyVPBankException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    flagWritelog = true;
                    throw ex;
                }

                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
        }
            catch (Exception ex) {
                if (!flagWritelog) {
                    dynamic objResponse = new ExpandoObject();
                    objResponse.IsSuccessStatusCode = false;
                    objResponse.StatusCode = 500;
                    objResponse.ReasonPhrase = "Internal Server Error";
                    OperationLog(operationName, xRequestId, body, JsonConvert.SerializeObject(ex), objResponse, startDate, DateTime.UtcNow, Client_Request_Address, memberCode);
                }
                throw ex;
            }
        }

        public string OperationLog(string operationName, string xRequestId, object body, string rawData, dynamic response, DateTime startDate, DateTime endDate, string ip, string memberCode)
        {
            try
            {
                var listOperationLogDto = new CreateOperationLogDto();
                var operationLog = new OperationLogDto
                {
                    TenantId = tenantId,
                    MemberId = memberCode,
                    Code = "VPBank_" + DateTime.Now.Ticks.ToString() + new Random().Next(4),
                    ReferenceKey = xRequestId,
                    PartnerCode = "VPBank",
                    DestIP = ip,
                    StartDate = startDate,
                    RequestMsg = hiddenTokenKeyInLogText(JsonConvert.SerializeObject(body)),
                    ServiceName = operationName,
                    EndDate = endDate,
                    Status = response.IsSuccessStatusCode,
                    ErrorCode = (int)response.StatusCode + " " + response.ReasonPhrase,
                    ResponseMsg = hiddenTokenKeyInLogText(rawData)
                };
                _logger.LogInformation("Dest IP" + operationLog.DestIP);
                listOperationLogDto.OperationLogs.Add(operationLog);

                var result = _loyaltyAuditLogService.CreateOperationLog(listOperationLogDto);
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Operation exception: " + ex);
            }
            return "";
        }

        public string GenOTPSession(string prefix = "")
        {
            return prefix + DateTime.Now.ToString("HHmmss") + DateTime.Now.Ticks.ToString();
        }

        private string getAuthorization()
        {
            String encoded = Convert.ToBase64String(Encoding.GetEncoding("ISO-8859-1").GetBytes(userName + ":" + password));
            return encoded;
        }

        private string getLogText(string data)
        {
            if (string.IsNullOrWhiteSpace(data))
            {
                return "";
            }
            var result = JObject.Parse(data);
            if (result.ContainsKey("accessToken"))
            {
                result.Remove("accessToken");
            }
            if (result.ContainsKey("refreshToken"))
            {
                result.Remove("refreshToken");
            }
            return JsonConvert.SerializeObject(result);
        }
        private string hiddenTokenKeyInLogText(string data)
        {
            if (string.IsNullOrWhiteSpace(data))
            {
                return "";
            }
            var result = JObject.Parse(data);
            if (result.ContainsKey("accessToken"))
            {
                result["accessToken"] = "******";
            }
            if (result.ContainsKey("refreshToken"))
            {
                result["refreshToken"] = "******";
            }
            return JsonConvert.SerializeObject(result);
        }
    }
}
