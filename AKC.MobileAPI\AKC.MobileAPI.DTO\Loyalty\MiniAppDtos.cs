using System;
using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.Loyalty
{
    public class GetMiniAppInput
    {
        public int SkipCount { get; set; } = 0;
        public int MaxResultCount { get; set; } = 10;
        public string ScreenCode { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Status { get; set; }
        public string Section { get; set; }
        public string Sorting { get; set; }
    }

    public class GetMiniAppOutput
    {
        public int TotalCount { get; set; }
        public List<GetMiniAppDto> Items { get; set; }
    }
    public class GetMiniAppDto
    {
        public int? TenantId { get; set; }
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Status { get; set; }
        public string ScreenCode { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public string GiftGroupType { get; set; }
        public string GiftGroupCode { get; set; }
        public string Tags { get; set; }
        public string Section { get; set; } // HOME Or only in DETAILS screen
        public string AppLogo { get; set; }
        public string AppBanner { get; set; }
        public string ChannelName { get; set; } // Channel view quà như các channel của các app khác đang có.
        public int Ordinal { get; set; } = 1000;
        public int? LastModifierUserId { get; set; }
        public int CreatorUserId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public DateTime CreationTime { get; set; }
        public bool IsDeleted { get; set; }
    }
}