﻿<?xml version="1.0" encoding="utf-8" ?>
<log4net>
  <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
    <file value="App_Data/Logs/Audit/Logs.txt" />
    <datePattern value="yyyy-MM-dd.'txt'"/>
    <staticLogFileName value="false"/>
    <appendToFile value="true"/>
    <rollingStyle value="Date"/>
    <maxSizeRollBackups value="100"/>
    <maximumFileSize value="15MB"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%-5level %date [%-5.5thread] %-10.10logger - %message%newline"/>    </layout>
  </appender>

  <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
   <layout type="log4net.Layout.PatternLayout">
     <conversionPattern value="vpid-mobile-api-application-audit-log %-5level %date [%-5.5thread] %-10.10logger - %message%newline" />
   </layout>
 </appender>

  <root>
    <level value="ALL"/>
    <appender-ref ref="RollingLogFileAppender"/>
    <appender-ref ref="ConsoleAppender"/>
  </root>
</log4net>