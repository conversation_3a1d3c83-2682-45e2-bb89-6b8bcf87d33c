﻿using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.PartnerPointCaching;
using AKC.MobileAPI.DTO.ThirdParty.VPBank;
using Microsoft.AspNetCore.Http;

namespace AKC.MobileAPI.Service.Abstract.ThirdParty
{
    public interface IThirdPartyVPBankSecuritiesService
    {
        Task<LoyaltyThirdPartyVerifyNationalIdOutput> VerifyNationalId(LoyaltyThirdPartyVerifyNationalIdInput input, HttpContext context, string authorization);
        Task<LoyaltyThirdPartyVerifyOTPOutput> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input, HttpContext context, string authorization);
        Task<RewardPartnerPoingCachingItems> UpdatePartnerCaching(LoyaltyThirdPartyVPBankUpdatePartnerCachingInput input, HttpContext context);
        Task<LoyaltyThirdPartyRemoveConnectedMerchantOutput> RemoveConnectedMerchant(RewardMemberUpdateOutputDto member, LoyaltyThirdPartyRemoveConnectedMerchant input);

        Task<LoyaltyResponse<RemoveConnectRespone>> RemoveConnect(RemoveConnectInput Input);

        Task<LoyaltyThirdPartyUpdatePhoneNumberOutput> UpdatePhoneNumber(UpdatePhoneNumberInput input);
    }
}
