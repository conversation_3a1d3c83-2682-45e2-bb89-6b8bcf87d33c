INFO  2025-08-04 15:12:42,496 [33   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\"MoneyCardCodes\":[\"cde77a70621e4f719a0ab1390c864be4\"],\"PinCode\":\"111111\",\"DeviceId\":\"2146BD1B-E74A-4E0C-9895-180FD5135958\",\"MemberCode\":\"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\"GiftCode\":\"GiftInfor_20250804041222018_5858\",\"Quantity\":1,\"TotalAmount\":50000,\"Description\":null,\"MerchantId\":null,\"FlashSaleProgramCode\":null,\"FlashSaleProgramName\":null,\"MemberId\":null,\"UserAddress\":null}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 15:12:44,760 [33   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
ERROR 2025-08-04 15:12:51,417 [33   ] Middleware - An unhandled exception has occurred while executing the request.
StackExchange.Redis.RedisConnectionException: No connection is available to service this operation: EVAL; UnableToConnect on localhost:6379/Interactive, Initializing/NotStarted, last: NONE, origin: BeginConnectAsync, outstanding: 0, last-read: 2s ago, last-write: 2s ago, keep-alive: 60s, state: Connecting, mgr: 10 of 10 available, last-heartbeat: never, global: 0s ago, v: 2.0.593.37019; IOCP: (Busy=0,Free=2000,Min=1000,Max=2000), WORKER: (Busy=1,Free=32766,Min=1000,Max=32767), Local-CPU: n/a
 ---> StackExchange.Redis.RedisConnectionException: UnableToConnect on localhost:6379/Interactive, Initializing/NotStarted, last: NONE, origin: BeginConnectAsync, outstanding: 0, last-read: 2s ago, last-write: 2s ago, keep-alive: 60s, state: Connecting, mgr: 10 of 10 available, last-heartbeat: never, global: 0s ago, v: 2.0.593.37019
   at StackExchange.Redis.TaskExtensions.TimeoutAfter(Task task, Int32 timeoutMs) in C:\projects\stackexchange-redis\src\StackExchange.Redis\TaskExtensions.cs:line 49
   at StackExchange.Redis.ConnectionMultiplexer.WaitAllIgnoreErrorsAsync(Task[] tasks, Int32 timeoutMilliseconds, TextWriter log, String caller, Int32 callerLineNumber) in C:\projects\stackexchange-redis\src\StackExchange.Redis\ConnectionMultiplexer.cs:line 705
   --- End of inner exception stack trace ---
   at StackExchange.Redis.ConnectionMultiplexer.ExecuteSyncImpl[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\projects\stackexchange-redis\src\StackExchange.Redis\ConnectionMultiplexer.cs:line 2215
   at StackExchange.Redis.RedisBase.ExecuteSync[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\projects\stackexchange-redis\src\StackExchange.Redis\RedisBase.cs:line 54
   at StackExchange.Redis.RedisDatabase.ScriptEvaluate(String script, RedisKey[] keys, RedisValue[] values, CommandFlags flags) in C:\projects\stackexchange-redis\src\StackExchange.Redis\RedisDatabase.cs:line 1134
   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisExtensions.HashMemberGet(IDatabase cache, String key, String[] members)
   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.GetAndRefresh(String key, Boolean getData)
   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.Get(String key)
   at Microsoft.Extensions.Caching.Distributed.DistributedCacheExtensions.GetString(IDistributedCache cache, String key)
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetAccessToken(Boolean mustResetCache) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 418
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetLoyaltyAsync[T](String apiURL, Object query) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 73
   at AKC.MobileAPI.Service.Loyalty.LoyaltyLanguageService.GetListLanguageTexts(LoyaltyMultiLanguageTextListInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyLanguageService.cs:line 28
   at AKC.MobileAPI.Service.Loyalty.LoyaltyLanguageService.GetListMessagesByKey(String keyName) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyLanguageService.cs:line 46
   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyGiftTransactionsController.RedeemWithMoneyCard(RedeemWithMoneyCardInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\Loyalty\LoyaltyGiftTransactionsController.cs:line 648
   at lambda_method(Closure , Object )
   at Microsoft.Extensions.Internal.ObjectMethodExecutorAwaitable.Awaiter.GetResult()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location where exception was thrown ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|19_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at AKC.MobileAPI.Helper.WebstoreCustomAuthenticationMiddleware.InvokeAsync(HttpContext context) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Helper\WebstoreCustomAuthenticationMiddleware.cs:line 36
   at AKC.MobileAPI.Helper.CSTicketAnnonymousRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Helper\CSTicketAnnonymousRateLimitingMiddleware.cs:line 64
   at AKC.MobileAPI.AuditLog.RequestLoginMiddleware.Invoke(HttpContext context) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\AuditLog\RequestLoginMiddleware.cs:line 47
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at AspNetCoreRateLimit.RateLimitMiddleware`1.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AKC.MobileAPI.Helper.SwaggerBasicAuthMiddleware.InvokeAsync(HttpContext context) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Helper\SwaggerBasicAuthMiddleware.cs:line 48
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
INFO  2025-08-04 15:13:31,396 [30   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 15:13:33,703 [30   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:13:52,738 [54   ] Controller - redeem transaction Fzw4wS04C4P2JBWbGRbT93lHXMK2_Error{"Error":{"error":"invalid_grant","error_description":"Invalid grant: account not found","error_uri":null},"StatusCode":400,"StackTrace":"   at Google.Apis.Auth.OAuth2.Requests.TokenRequestExtenstions.ExecuteAsync(TokenRequest request, HttpClient httpClient, String tokenServerUrl, CancellationToken taskCancellationToken, IClock clock)\r\n   at Google.Apis.Auth.OAuth2.ServiceAccountCredential.RequestAccessTokenAsync(CancellationToken taskCancellationToken)\r\n   at Google.Apis.Auth.OAuth2.TokenRefreshManager.RefreshTokenAsync()\r\n   at Google.Apis.Auth.OAuth2.TokenRefreshManager.ResultWithUnwrappedExceptions[T](Task`1 task)\r\n   at Google.Apis.Auth.OAuth2.TokenRefreshManager.<>c.<GetAccessTokenForRequestAsync>b__10_0(Task`1 task)\r\n   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()\r\n   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)\r\n--- End of stack trace from previous location where exception was thrown ---\r\n   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)\r\n--- End of stack trace from previous location where exception was thrown ---\r\n   at Google.Apis.Auth.OAuth2.TokenRefreshManager.GetAccessTokenForRequestAsync(CancellationToken cancellationToken)\r\n   at Google.Apis.Auth.OAuth2.ServiceAccountCredential.GetAccessTokenForRequestAsync(String authUri, CancellationToken cancellationToken)\r\n   at Google.Apis.Auth.OAuth2.ServiceCredential.InterceptAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Google.Apis.Http.ConfigurableMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)\r\n   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndReadAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Util.ErrorHandlingHttpClient`1.SendAndDeserializeAsync[TResult](HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseUserManager.PostAndDeserializeAsync[TResult](String path, Object body, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseUserManager.GetUserAsync(UserQuery query, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseUserManager.GetUserByPhoneNumberAsync(String phoneNumber, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.GetUserByPhoneNumberAsync(String phoneNumber, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.GetUserByPhoneNumberAsync(String phoneNumber)\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.VerifyPinCode(RewardMemberVerifyPinCodeRequest input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 469\r\n   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyGiftTransactionsController.RedeemWithMoneyCard(RedeemWithMoneyCardInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Loyalty\\LoyaltyGiftTransactionsController.cs:line 586","Message":"Error:\"invalid_grant\", Description:\"Invalid grant: account not found\", Uri:\"\"","Data":{},"InnerException":null,"HelpLink":null,"Source":"Google.Apis.Auth","HResult":-**********}
INFO  2025-08-04 15:14:18,771 [53   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 15:14:20,416 [53   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:14:41,474 [55   ] onsService -  638898920809633834Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard start >> {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:14:44,270 [59   ] onsService -  638898920809633834Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard GET_GIFT_CATEGORY_TYPE_CODE startGiftInfor_20250804041222018_5858
INFO  2025-08-04 15:14:51,992 [55   ] onsService -  638898920809633834Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetMerchantIdFromGiftCode(GiftInfor_20250804041222018_5858)___Result:{"Result":{"GiftCode":"GiftInfor_20250804041222018_5858","MerchantId":217,"BrandId":5,"VendorId":14,"GiftCategory":null,"Vendor":null,"Brand":null,"FullPrice":0.0,"RequiredCoin":0.0,"Merchant":null},"TargetUrl":null,"Success":true,"Error":null,"UnAuthorizedRequest":false,"__abp":true}
INFO  2025-08-04 15:14:59,703 [55   ] onsService -  638898920809633834Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard check the moneycard in operator...
INFO  2025-08-04 15:15:00,878 [55   ] onsService -  638898920809633834Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetBalanceMember end {"Items":[{"Id":1893,"CreatedAt":"2025-07-31T07:45:03.315Z","UpdatedAt":"2025-08-04T08:11:42.566Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-28T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"cde77a70621e4f719a0ab1390c864be4","RefCode":"ORDER_CODE_000Z_03105","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":2000000.0000000000000000000000,"RemainingAmount":1357000.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null},{"Id":1892,"CreatedAt":"2025-07-31T07:45:01.982Z","UpdatedAt":"2025-08-01T03:25:22.323Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-31T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"7ce2d107548b4211bc83b6d96772490b","RefCode":"ORDER_CODE_000Z_03107","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":1000000.0000000000000000000000,"RemainingAmount":977900.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null}],"TotalCount":2}
INFO  2025-08-04 15:15:04,144 [55   ] onsService -  638898920809633834Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2025-08-04 15:15:08,880 [55   ] onsService -  638898920809633834Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2025-08-04 15:15:13,757 [55   ] onsService -  638898920809633834Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard >> Call Reward RedeemUsingMoneyCard start... {"OrderCode":"1566D88EB524134A87B790C6FDD6AFF9F41458","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","PhoneNumber":null,"Reason":"REDEEM","MoneyCardIds":["cde77a70621e4f719a0ab1390c864be4"],"TokenAmount":50000.0,"MerchantId":217}
INFO  2025-08-04 15:15:18,626 [55   ] onsService -  638898920809633834Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard CreateRedeem end
INFO  2025-08-04 15:15:23,317 [55   ] onsService -  638898920809633834Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION start
INFO  2025-08-04 15:17:20,546 [77   ] onsService - Call UPDATE_ERROR_WHEN_CREATE_REDEEM fail: Exception of type 'AKC.MobileAPI.Service.Exceptions.LoyaltyException' was thrown.
INFO  2025-08-04 15:39:53,673 [33   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 15:39:56,709 [33   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:40:01,268 [29   ] onsService -  638898936012649169Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard start >> {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:40:01,819 [25   ] onsService -  638898936012649169Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetMerchantIdFromGiftCode(GiftInfor_20250804041222018_5858)___Result:{"Result":{"GiftCode":"GiftInfor_20250804041222018_5858","MerchantId":217,"BrandId":5,"VendorId":14,"GiftCategory":null,"Vendor":null,"Brand":null,"FullPrice":0.0,"RequiredCoin":0.0,"Merchant":null},"TargetUrl":null,"Success":true,"Error":null,"UnAuthorizedRequest":false,"__abp":true}
INFO  2025-08-04 15:40:01,828 [25   ] onsService -  638898936012649169Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard check the moneycard in operator...
INFO  2025-08-04 15:40:01,951 [25   ] onsService -  638898936012649169Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetBalanceMember end {"Items":[{"Id":1893,"CreatedAt":"2025-07-31T07:45:03.315Z","UpdatedAt":"2025-08-04T08:15:16.726Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-28T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"cde77a70621e4f719a0ab1390c864be4","RefCode":"ORDER_CODE_000Z_03105","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":2000000.0000000000000000000000,"RemainingAmount":1307000.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null},{"Id":1892,"CreatedAt":"2025-07-31T07:45:01.982Z","UpdatedAt":"2025-08-01T03:25:22.323Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-31T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"7ce2d107548b4211bc83b6d96772490b","RefCode":"ORDER_CODE_000Z_03107","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":1000000.0000000000000000000000,"RemainingAmount":977900.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null}],"TotalCount":2}
INFO  2025-08-04 15:40:01,955 [25   ] onsService -  638898936012649169Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2025-08-04 15:40:02,500 [25   ] onsService -  638898936012649169Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2025-08-04 15:40:02,513 [25   ] onsService -  638898936012649169Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard >> Call Reward RedeemUsingMoneyCard start... {"OrderCode":"15E7F6081471C74CC0AAF5CD41B7418C8E4001","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","PhoneNumber":null,"Reason":"REDEEM","MoneyCardIds":["cde77a70621e4f719a0ab1390c864be4"],"TokenAmount":50000.0,"MerchantId":217}
INFO  2025-08-04 15:40:02,946 [29   ] onsService -  638898936012649169Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard CreateRedeem end
INFO  2025-08-04 15:40:02,949 [29   ] onsService -  638898936012649169Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION start
INFO  2025-08-04 15:43:58,105 [27   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 15:44:00,780 [27   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:44:12,235 [29   ] onsService -  638898938522318031Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard start >> {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:44:12,842 [34   ] onsService -  638898938522318031Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetMerchantIdFromGiftCode(GiftInfor_20250804041222018_5858)___Result:{"Result":{"GiftCode":"GiftInfor_20250804041222018_5858","MerchantId":217,"BrandId":5,"VendorId":14,"GiftCategory":null,"Vendor":null,"Brand":null,"FullPrice":0.0,"RequiredCoin":0.0,"Merchant":null},"TargetUrl":null,"Success":true,"Error":null,"UnAuthorizedRequest":false,"__abp":true}
INFO  2025-08-04 15:44:12,851 [34   ] onsService -  638898938522318031Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard check the moneycard in operator...
INFO  2025-08-04 15:44:13,008 [34   ] onsService -  638898938522318031Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetBalanceMember end {"Items":[{"Id":1893,"CreatedAt":"2025-07-31T07:45:03.315Z","UpdatedAt":"2025-08-04T08:40:04.263Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-28T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"cde77a70621e4f719a0ab1390c864be4","RefCode":"ORDER_CODE_000Z_03105","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":2000000.0000000000000000000000,"RemainingAmount":1257000.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null},{"Id":1892,"CreatedAt":"2025-07-31T07:45:01.982Z","UpdatedAt":"2025-08-01T03:25:22.323Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-31T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"7ce2d107548b4211bc83b6d96772490b","RefCode":"ORDER_CODE_000Z_03107","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":1000000.0000000000000000000000,"RemainingAmount":977900.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null}],"TotalCount":2}
INFO  2025-08-04 15:44:13,011 [34   ] onsService -  638898938522318031Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2025-08-04 15:44:18,198 [34   ] onsService -  638898938522318031Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2025-08-04 15:44:18,205 [34   ] onsService -  638898938522318031Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard >> Call Reward RedeemUsingMoneyCard start... {"OrderCode":"158C43D0422C9342B4AC39C4C1CB7126294412","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","PhoneNumber":null,"Reason":"REDEEM","MoneyCardIds":["cde77a70621e4f719a0ab1390c864be4"],"TokenAmount":50000.0,"MerchantId":217}
INFO  2025-08-04 15:44:18,673 [34   ] onsService -  638898938522318031Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard CreateRedeem end
INFO  2025-08-04 15:44:18,676 [34   ] onsService -  638898938522318031Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION start
INFO  2025-08-04 15:46:30,943 [49   ] Controller - redeem transaction Fzw4wS04C4P2JBWbGRbT93lHXMK2_Error{"ClassName":"System.Exception","Message":"Timed Out with API: https://vpid-loyalty-api-uat.linkid.vn/api/services/app/GiftTransactions/CreateRedeemTransaction","Data":null,"InnerException":{"ClassName":"System.Threading.Tasks.TaskCanceledException","Message":"The operation was canceled.","Data":null,"InnerException":{"ClassName":"System.IO.IOException","Message":"Unable to read data from the transport connection: The I/O operation has been aborted because of either a thread exit or an application request..","Data":null,"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"The I/O operation has been aborted because of either a thread exit or an application request.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":null,"RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":null,"WatsonBuckets":null,"NativeErrorCode":995},"HelpURL":null,"StackTraceString":"   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.GetResult(Int16 token)\r\n   at System.Net.Security.SslStream.<FillBufferAsync>g__InternalFillBufferAsync|215_0[TReadAdapter](TReadAdapter adap, ValueTask`1 task, Int32 min, Int32 initial)\r\n   at System.Net.Security.SslStream.ReadAsyncInternal[TReadAdapter](TReadAdapter adapter, Memory`1 buffer)\r\n   at System.Net.Http.HttpConnection.SendAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2146232800,"Source":"System.Net.Sockets","WatsonBuckets":null},"HelpURL":null,"StackTraceString":"   at System.Net.Http.HttpConnection.SendAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithNtConnectionAuthAsync(HttpConnection connection, HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)\r\n   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.PostLoyaltyAsync[T](String apiURL, Object body, HttpContext request) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\BaseLoyaltyService.cs:line 271","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2146233029,"Source":"System.Net.Http","WatsonBuckets":null},"HelpURL":null,"StackTraceString":"   at AKC.MobileAPI.Service.Loyalty.LoyaltyGiftTransactionsService.RedeemWithMoneyCard(RedeemWithMoneyCardInput input, HttpContext context) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyGiftTransactionsService.ext.cs:line 504\r\n   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyGiftTransactionsController.RedeemWithMoneyCard(RedeemWithMoneyCardInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Loyalty\\LoyaltyGiftTransactionsController.cs:line 599","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-**********,"Source":"AKC.MobileAPI.Service","WatsonBuckets":null}
INFO  2025-08-04 15:46:42,568 [53   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 15:46:44,312 [53   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:46:47,240 [38   ] onsService -  638898940072374149Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard start >> {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:46:47,252 [51   ] onsService -  638898940072374149Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard GET_GIFT_CATEGORY_TYPE_CODE startGiftInfor_20250804041222018_5858
INFO  2025-08-04 15:46:47,957 [38   ] onsService -  638898940072374149Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetMerchantIdFromGiftCode(GiftInfor_20250804041222018_5858)___Result:{"Result":{"GiftCode":"GiftInfor_20250804041222018_5858","MerchantId":217,"BrandId":5,"VendorId":14,"GiftCategory":null,"Vendor":null,"Brand":null,"FullPrice":0.0,"RequiredCoin":0.0,"Merchant":null},"TargetUrl":null,"Success":true,"Error":null,"UnAuthorizedRequest":false,"__abp":true}
INFO  2025-08-04 15:46:47,960 [38   ] onsService -  638898940072374149Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard check the moneycard in operator...
INFO  2025-08-04 15:46:48,074 [38   ] onsService -  638898940072374149Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetBalanceMember end {"Items":[{"Id":1893,"CreatedAt":"2025-07-31T07:45:03.315Z","UpdatedAt":"2025-08-04T08:44:19.989Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-28T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"cde77a70621e4f719a0ab1390c864be4","RefCode":"ORDER_CODE_000Z_03105","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":2000000.0000000000000000000000,"RemainingAmount":1207000.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null},{"Id":1892,"CreatedAt":"2025-07-31T07:45:01.982Z","UpdatedAt":"2025-08-01T03:25:22.323Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-31T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"7ce2d107548b4211bc83b6d96772490b","RefCode":"ORDER_CODE_000Z_03107","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":1000000.0000000000000000000000,"RemainingAmount":977900.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null}],"TotalCount":2}
INFO  2025-08-04 15:46:48,079 [38   ] onsService -  638898940072374149Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2025-08-04 15:46:50,363 [38   ] onsService -  638898940072374149Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2025-08-04 15:46:50,370 [38   ] onsService -  638898940072374149Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard >> Call Reward RedeemUsingMoneyCard start... {"OrderCode":"1528D93DBB05CF43D28017083F915EA4874647","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","PhoneNumber":null,"Reason":"REDEEM","MoneyCardIds":["cde77a70621e4f719a0ab1390c864be4"],"TokenAmount":50000.0,"MerchantId":217}
INFO  2025-08-04 15:46:50,847 [38   ] onsService -  638898940072374149Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard CreateRedeem end
INFO  2025-08-04 15:46:50,850 [38   ] onsService -  638898940072374149Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION start
INFO  2025-08-04 15:48:53,603 [22   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 15:49:01,505 [22   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:49:04,045 [26   ] onsService -  638898941440406026Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard start >> {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:49:04,718 [23   ] onsService -  638898941440406026Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetMerchantIdFromGiftCode(GiftInfor_20250804041222018_5858)___Result:{"Result":{"GiftCode":"GiftInfor_20250804041222018_5858","MerchantId":217,"BrandId":5,"VendorId":14,"GiftCategory":null,"Vendor":null,"Brand":null,"FullPrice":0.0,"RequiredCoin":0.0,"Merchant":null},"TargetUrl":null,"Success":true,"Error":null,"UnAuthorizedRequest":false,"__abp":true}
INFO  2025-08-04 15:49:04,722 [23   ] onsService -  638898941440406026Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard check the moneycard in operator...
INFO  2025-08-04 15:49:04,936 [23   ] onsService -  638898941440406026Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetBalanceMember end {"Items":[{"Id":1893,"CreatedAt":"2025-07-31T07:45:03.315Z","UpdatedAt":"2025-08-04T08:46:52.178Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-28T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"cde77a70621e4f719a0ab1390c864be4","RefCode":"ORDER_CODE_000Z_03105","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":2000000.0000000000000000000000,"RemainingAmount":1157000.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null},{"Id":1892,"CreatedAt":"2025-07-31T07:45:01.982Z","UpdatedAt":"2025-08-01T03:25:22.323Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-31T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"7ce2d107548b4211bc83b6d96772490b","RefCode":"ORDER_CODE_000Z_03107","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":1000000.0000000000000000000000,"RemainingAmount":977900.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null}],"TotalCount":2}
INFO  2025-08-04 15:49:04,940 [23   ] onsService -  638898941440406026Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2025-08-04 15:49:10,501 [6    ] onsService -  638898941440406026Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2025-08-04 15:49:10,511 [6    ] onsService -  638898941440406026Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard >> Call Reward RedeemUsingMoneyCard start... {"OrderCode":"15F2983F1DFDCF4942A62F823FD8EE91034904","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","PhoneNumber":null,"Reason":"REDEEM","MoneyCardIds":["cde77a70621e4f719a0ab1390c864be4"],"TokenAmount":50000.0,"MerchantId":217}
INFO  2025-08-04 15:49:10,880 [6    ] onsService -  638898941440406026Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard CreateRedeem end
INFO  2025-08-04 15:49:10,884 [6    ] onsService -  638898941440406026Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION start
INFO  2025-08-04 15:57:11,021 [28   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 15:57:12,949 [28   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:57:15,796 [31   ] onsService -  638898946357907335Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard start >> {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 15:57:16,516 [7    ] onsService -  638898946357907335Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetMerchantIdFromGiftCode(GiftInfor_20250804041222018_5858)___Result:{"Result":{"GiftCode":"GiftInfor_20250804041222018_5858","MerchantId":217,"BrandId":5,"VendorId":14,"GiftCategory":null,"Vendor":null,"Brand":null,"FullPrice":0.0,"RequiredCoin":0.0,"Merchant":null},"TargetUrl":null,"Success":true,"Error":null,"UnAuthorizedRequest":false,"__abp":true}
INFO  2025-08-04 15:57:16,522 [7    ] onsService -  638898946357907335Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard check the moneycard in operator...
INFO  2025-08-04 15:57:16,664 [7    ] onsService -  638898946357907335Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetBalanceMember end {"Items":[{"Id":1893,"CreatedAt":"2025-07-31T07:45:03.315Z","UpdatedAt":"2025-08-04T08:49:12.206Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-28T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"cde77a70621e4f719a0ab1390c864be4","RefCode":"ORDER_CODE_000Z_03105","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":2000000.0000000000000000000000,"RemainingAmount":1107000.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null},{"Id":1892,"CreatedAt":"2025-07-31T07:45:01.982Z","UpdatedAt":"2025-08-01T03:25:22.323Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-31T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"7ce2d107548b4211bc83b6d96772490b","RefCode":"ORDER_CODE_000Z_03107","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":1000000.0000000000000000000000,"RemainingAmount":977900.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null}],"TotalCount":2}
INFO  2025-08-04 15:57:16,668 [7    ] onsService -  638898946357907335Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2025-08-04 15:57:18,562 [7    ] onsService -  638898946357907335Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2025-08-04 15:57:18,573 [7    ] onsService -  638898946357907335Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard >> Call Reward RedeemUsingMoneyCard start... {"OrderCode":"151DA77221D1EF444EBA49C5DEBDC199185716","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","PhoneNumber":null,"Reason":"REDEEM","MoneyCardIds":["cde77a70621e4f719a0ab1390c864be4"],"TokenAmount":50000.0,"MerchantId":217}
INFO  2025-08-04 15:57:19,015 [7    ] onsService -  638898946357907335Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard CreateRedeem end
INFO  2025-08-04 15:57:19,020 [7    ] onsService -  638898946357907335Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION start
INFO  2025-08-04 16:00:50,096 [33   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 16:00:52,278 [33   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 16:00:55,820 [29   ] onsService -  638898948558171100Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard start >> {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 16:00:56,420 [35   ] onsService -  638898948558171100Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetMerchantIdFromGiftCode(GiftInfor_20250804041222018_5858)___Result:{"Result":{"GiftCode":"GiftInfor_20250804041222018_5858","MerchantId":217,"BrandId":5,"VendorId":14,"GiftCategory":null,"Vendor":null,"Brand":null,"FullPrice":0.0,"RequiredCoin":0.0,"Merchant":null},"TargetUrl":null,"Success":true,"Error":null,"UnAuthorizedRequest":false,"__abp":true}
INFO  2025-08-04 16:00:56,429 [35   ] onsService -  638898948558171100Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard check the moneycard in operator...
INFO  2025-08-04 16:00:56,577 [25   ] onsService -  638898948558171100Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetBalanceMember end {"Items":[{"Id":1893,"CreatedAt":"2025-07-31T07:45:03.315Z","UpdatedAt":"2025-08-04T08:57:20.352Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-28T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"cde77a70621e4f719a0ab1390c864be4","RefCode":"ORDER_CODE_000Z_03105","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":2000000.0000000000000000000000,"RemainingAmount":1057000.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null},{"Id":1892,"CreatedAt":"2025-07-31T07:45:01.982Z","UpdatedAt":"2025-08-01T03:25:22.323Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-31T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"7ce2d107548b4211bc83b6d96772490b","RefCode":"ORDER_CODE_000Z_03107","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":1000000.0000000000000000000000,"RemainingAmount":977900.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null}],"TotalCount":2}
INFO  2025-08-04 16:00:56,582 [25   ] onsService -  638898948558171100Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2025-08-04 16:11:23,015 [29   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 16:11:25,380 [29   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 16:11:27,856 [31   ] onsService -  638898954878524835Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard start >> {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 16:11:28,405 [31   ] onsService -  638898954878524835Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetMerchantIdFromGiftCode(GiftInfor_20250804041222018_5858)___Result:{"Result":{"GiftCode":"GiftInfor_20250804041222018_5858","MerchantId":217,"BrandId":5,"VendorId":14,"GiftCategory":null,"Vendor":null,"Brand":null,"FullPrice":0.0,"RequiredCoin":0.0,"Merchant":null},"TargetUrl":null,"Success":true,"Error":null,"UnAuthorizedRequest":false,"__abp":true}
INFO  2025-08-04 16:11:28,411 [31   ] onsService -  638898954878524835Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard check the moneycard in operator...
INFO  2025-08-04 16:11:28,556 [31   ] onsService -  638898954878524835Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetBalanceMember end {"Items":[{"Id":1893,"CreatedAt":"2025-07-31T07:45:03.315Z","UpdatedAt":"2025-08-04T08:57:20.352Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-28T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"cde77a70621e4f719a0ab1390c864be4","RefCode":"ORDER_CODE_000Z_03105","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":2000000.0000000000000000000000,"RemainingAmount":1057000.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null},{"Id":1892,"CreatedAt":"2025-07-31T07:45:01.982Z","UpdatedAt":"2025-08-01T03:25:22.323Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-31T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"7ce2d107548b4211bc83b6d96772490b","RefCode":"ORDER_CODE_000Z_03107","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":1000000.0000000000000000000000,"RemainingAmount":977900.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null}],"TotalCount":2}
INFO  2025-08-04 16:11:28,559 [31   ] onsService -  638898954878524835Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2025-08-04 16:11:30,213 [31   ] onsService -  638898954878524835Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2025-08-04 16:11:30,223 [31   ] onsService -  638898954878524835Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard >> Call Reward RedeemUsingMoneyCard start... {"OrderCode":"1621E2634895734F87835DD5990AC222DA1128","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","PhoneNumber":null,"Reason":"REDEEM","MoneyCardIds":["cde77a70621e4f719a0ab1390c864be4"],"TokenAmount":50000.0,"MerchantId":217}
INFO  2025-08-04 16:11:30,711 [31   ] onsService -  638898954878524835Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard CreateRedeem end
INFO  2025-08-04 16:11:30,716 [31   ] onsService -  638898954878524835Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION start
INFO  2025-08-04 16:25:27,153 [27   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 16:25:29,000 [27   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 16:25:31,832 [29   ] onsService -  638898963318294901Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard start >> {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 16:25:31,838 [27   ] onsService -  638898963318294901Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard GET_GIFT_CATEGORY_TYPE_CODE startGiftInfor_20250804041222018_5858
INFO  2025-08-04 16:25:32,195 [6    ] onsService -  638898963318294901Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetMerchantIdFromGiftCode(GiftInfor_20250804041222018_5858)___Result:{"Result":{"GiftCode":"GiftInfor_20250804041222018_5858","MerchantId":217,"BrandId":5,"VendorId":14,"GiftCategory":null,"Vendor":null,"Brand":null,"FullPrice":0.0,"RequiredCoin":0.0,"Merchant":null},"TargetUrl":null,"Success":true,"Error":null,"UnAuthorizedRequest":false,"__abp":true}
INFO  2025-08-04 16:25:32,203 [6    ] onsService -  638898963318294901Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard check the moneycard in operator...
INFO  2025-08-04 16:25:32,331 [6    ] onsService -  638898963318294901Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetBalanceMember end {"Items":[{"Id":1893,"CreatedAt":"2025-07-31T07:45:03.315Z","UpdatedAt":"2025-08-04T09:11:32.046Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-28T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"cde77a70621e4f719a0ab1390c864be4","RefCode":"ORDER_CODE_000Z_03105","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":2000000.0000000000000000000000,"RemainingAmount":1007000.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null},{"Id":1892,"CreatedAt":"2025-07-31T07:45:01.982Z","UpdatedAt":"2025-08-01T03:25:22.323Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-31T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"7ce2d107548b4211bc83b6d96772490b","RefCode":"ORDER_CODE_000Z_03107","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":1000000.0000000000000000000000,"RemainingAmount":977900.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null}],"TotalCount":2}
INFO  2025-08-04 16:25:32,336 [6    ] onsService -  638898963318294901Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2025-08-04 16:25:33,791 [6    ] onsService -  638898963318294901Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2025-08-04 16:25:33,800 [6    ] onsService -  638898963318294901Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard >> Call Reward RedeemUsingMoneyCard start... {"OrderCode":"168A21710F684E40E3A0990140AD23A9112532","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","PhoneNumber":null,"Reason":"REDEEM","MoneyCardIds":["cde77a70621e4f719a0ab1390c864be4"],"TokenAmount":50000.0,"MerchantId":217}
INFO  2025-08-04 16:25:34,172 [6    ] onsService -  638898963318294901Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard CreateRedeem end
INFO  2025-08-04 16:25:34,180 [6    ] onsService -  638898963318294901Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION start
INFO  2025-08-04 16:29:27,207 [31   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 16:29:29,582 [31   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 16:29:31,508 [10   ] onsService -  638898965715056750Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard start >> {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 16:29:32,026 [10   ] onsService -  638898965715056750Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetMerchantIdFromGiftCode(GiftInfor_20250804041222018_5858)___Result:{"Result":{"GiftCode":"GiftInfor_20250804041222018_5858","MerchantId":217,"BrandId":5,"VendorId":14,"GiftCategory":null,"Vendor":null,"Brand":null,"FullPrice":0.0,"RequiredCoin":0.0,"Merchant":null},"TargetUrl":null,"Success":true,"Error":null,"UnAuthorizedRequest":false,"__abp":true}
INFO  2025-08-04 16:29:32,032 [10   ] onsService -  638898965715056750Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard check the moneycard in operator...
INFO  2025-08-04 16:29:32,233 [10   ] onsService -  638898965715056750Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetBalanceMember end {"Items":[{"Id":1893,"CreatedAt":"2025-07-31T07:45:03.315Z","UpdatedAt":"2025-08-04T09:25:35.514Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-28T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"cde77a70621e4f719a0ab1390c864be4","RefCode":"ORDER_CODE_000Z_03105","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":2000000.0000000000000000000000,"RemainingAmount":957000.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null},{"Id":1892,"CreatedAt":"2025-07-31T07:45:01.982Z","UpdatedAt":"2025-08-01T03:25:22.323Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-31T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"7ce2d107548b4211bc83b6d96772490b","RefCode":"ORDER_CODE_000Z_03107","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":1000000.0000000000000000000000,"RemainingAmount":977900.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null}],"TotalCount":2}
INFO  2025-08-04 16:29:32,236 [10   ] onsService -  638898965715056750Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2025-08-04 16:29:34,385 [10   ] onsService -  638898965715056750Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2025-08-04 16:29:34,391 [10   ] onsService -  638898965715056750Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard >> Call Reward RedeemUsingMoneyCard start... {"OrderCode":"16AA0A45DF646C4F5F9FF076C0049096352932","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","PhoneNumber":null,"Reason":"REDEEM","MoneyCardIds":["cde77a70621e4f719a0ab1390c864be4"],"TokenAmount":50000.0,"MerchantId":217}
INFO  2025-08-04 16:29:34,817 [10   ] onsService -  638898965715056750Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard CreateRedeem end
INFO  2025-08-04 16:29:34,820 [10   ] onsService -  638898965715056750Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION start
INFO  2025-08-04 16:33:06,181 [28   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 16:33:07,749 [28   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 16:33:09,875 [24   ] onsService -  638898967898718997Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard start >> {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-04 16:33:10,499 [24   ] onsService -  638898967898718997Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetMerchantIdFromGiftCode(GiftInfor_20250804041222018_5858)___Result:{"Result":{"GiftCode":"GiftInfor_20250804041222018_5858","MerchantId":217,"BrandId":5,"VendorId":14,"GiftCategory":null,"Vendor":null,"Brand":null,"FullPrice":0.0,"RequiredCoin":0.0,"Merchant":null},"TargetUrl":null,"Success":true,"Error":null,"UnAuthorizedRequest":false,"__abp":true}
INFO  2025-08-04 16:33:10,505 [24   ] onsService -  638898967898718997Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard check the moneycard in operator...
INFO  2025-08-04 16:33:10,643 [24   ] onsService -  638898967898718997Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetBalanceMember end {"Items":[{"Id":1893,"CreatedAt":"2025-07-31T07:45:03.315Z","UpdatedAt":"2025-08-04T09:29:36.149Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-28T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"cde77a70621e4f719a0ab1390c864be4","RefCode":"ORDER_CODE_000Z_03105","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":2000000.0000000000000000000000,"RemainingAmount":907000.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null},{"Id":1892,"CreatedAt":"2025-07-31T07:45:01.982Z","UpdatedAt":"2025-08-01T03:25:22.323Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-31T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"7ce2d107548b4211bc83b6d96772490b","RefCode":"ORDER_CODE_000Z_03107","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":1000000.0000000000000000000000,"RemainingAmount":977900.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null}],"TotalCount":2}
INFO  2025-08-04 16:33:10,646 [24   ] onsService -  638898967898718997Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2025-08-04 16:33:13,166 [24   ] onsService -  638898967898718997Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2025-08-04 16:33:13,176 [24   ] onsService -  638898967898718997Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard >> Call Reward RedeemUsingMoneyCard start... {"OrderCode":"160CD70787654E485B89FA6105A3B9567F3310","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","PhoneNumber":null,"Reason":"REDEEM","MoneyCardIds":["cde77a70621e4f719a0ab1390c864be4"],"TokenAmount":50000.0,"MerchantId":217}
INFO  2025-08-04 16:33:13,584 [24   ] onsService -  638898967898718997Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard CreateRedeem end
INFO  2025-08-04 16:33:13,588 [24   ] onsService -  638898967898718997Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION start
INFO  2025-08-04 16:34:39,677 [7    ] onsService -  638898967898718997Fzw4wS04C4P2JBWbGRbT93lHXMK2 - retryRevertToken start
INFO  2025-08-04 16:34:41,766 [7    ] onsService -  638898967898718997Fzw4wS04C4P2JBWbGRbT93lHXMK2 - retryRevertToken end
INFO  2025-08-04 18:16:50,894 [26   ]            - Http Request Information: {"ControllerName":"NotificationHistoryV2","ActionName":"GetTransDetail","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/notification-history-v2/get-tx-detail","QueryString":"{\"MemberCode\":[\"XvguzAVcrOYQlvoXmDOcuEoJihw2\"],\"Type\":[\"MONEYCARD\"],\"CardTransactionId\":[\"2989\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2025-08-04 18:16:52,358 [35   ] Controller -  >> ERROR PARSING DESCRIPTION >> 
INFO  2025-08-04 18:16:52,380 [35   ] Controller -  Final Output: {"Result":{"Id":null,"TokenTransId":null,"Title":"Đổi quà thành công","Content":null,"Amount":100000.0,"ActionType":"Redeem","ActionCode":null,"OrderCode":"1161490B5F584D480686D3A931D830ABB71145","ContentPhoto":null,"DescriptionPhoto":null,"PartnerName":null,"PartnerIcon":null,"MemberCode":"XvguzAVcrOYQlvoXmDOcuEoJihw2","WalletAddress":"dc63bcefc3cd4c34b992aa4efad1dd2e","ToWalletAddress":"db7bb25e538a5b8778ca4954f6159115779de1","FromWalletAddress":"dc63bcefc3cd4c34b992aa4efad1dd2e","CreationTime":"2025-08-04T11:11:46.015Z","ExpiredTime":"2026-08-04T16:59:59Z","RelatedTokenTransId":null,"ServiceName":null,"PackageName":null,"CardValue":null,"ToPhoneNumber":null,"UsageAddress":null,"LastModificationTime":null,"GiftName":"Quà test timeout evoucher1","GiftId":"43176","GiftImage":"https://linkidstorage.s3-ap-southeast-1.amazonaws.com/upload-gift/3765640c8e9f2897f9044ad6786da5d8.jpg","GiftPaidCoin":100000.0,"EGiftExpiredDate":"2027-08-04T16:59:59Z","BrandName":"Gong cha","BrandImage":"https://linkidstorage.s3-ap-southeast-1.amazonaws.com/upload-gift/3eeda88e8de761350844017776a99c54.png","Vendor":{"Type":"LinkID","Image":null,"HotLine":null,"Id":14,"VendorName":"LinkID"},"RedeemQuantity":2,"PartnerPointAmount":0.0,"BatchNote":null,"CampaignName":null,"InvitedMember":null,"PartnerPointExchangeType":null,"PartnerBindingTxId":"","PartnerMemberFirstName":null,"PartnerMemberLastName":null,"AirlineMemberCode":null,"PromotionCode":null,"PromotionDate":null,"PromotionValue":null},"IsSuccess":true,"Message":""}
INFO  2025-08-04 18:17:23,750 [33   ]            - Http Request Information: {"ControllerName":"NotificationHistoryV2","ActionName":"GetTransDetail","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/notification-history-v2/get-tx-detail","QueryString":"{\"MemberCode\":[\"XvguzAVcrOYQlvoXmDOcuEoJihw2\"],\"Type\":[\"MONEYCARD\"],\"CardTransactionId\":[\"2989\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2025-08-04 18:17:52,865 [45   ] Controller -  >> ERROR PARSING DESCRIPTION >> 
INFO  2025-08-04 18:17:52,869 [45   ] Controller -  Final Output: {"Result":{"Id":null,"TokenTransId":null,"Title":"Đổi quà thành công","Content":null,"Amount":100000.0,"ActionType":"Redeem","ActionCode":null,"OrderCode":"1161490B5F584D480686D3A931D830ABB71145","ContentPhoto":null,"DescriptionPhoto":null,"PartnerName":null,"PartnerIcon":null,"MemberCode":"XvguzAVcrOYQlvoXmDOcuEoJihw2","WalletAddress":"dc63bcefc3cd4c34b992aa4efad1dd2e","ToWalletAddress":"db7bb25e538a5b8778ca4954f6159115779de1","FromWalletAddress":"dc63bcefc3cd4c34b992aa4efad1dd2e","CreationTime":"2025-08-04T11:11:46.015Z","ExpiredTime":"2026-08-04T16:59:59Z","RelatedTokenTransId":null,"ServiceName":null,"PackageName":null,"CardValue":null,"ToPhoneNumber":null,"UsageAddress":null,"LastModificationTime":null,"GiftName":"Quà test timeout evoucher1","GiftId":"43176","GiftImage":"https://linkidstorage.s3-ap-southeast-1.amazonaws.com/upload-gift/3765640c8e9f2897f9044ad6786da5d8.jpg","GiftPaidCoin":100000.0,"EGiftExpiredDate":"2027-08-04T16:59:59Z","BrandName":"Gong cha","BrandImage":"https://linkidstorage.s3-ap-southeast-1.amazonaws.com/upload-gift/3eeda88e8de761350844017776a99c54.png","Vendor":{"Type":"LinkID","Image":null,"HotLine":null,"Id":14,"VendorName":"LinkID"},"RedeemQuantity":2,"PartnerPointAmount":0.0,"BatchNote":null,"CampaignName":null,"InvitedMember":null,"PartnerPointExchangeType":null,"PartnerBindingTxId":"","PartnerMemberFirstName":null,"PartnerMemberLastName":null,"AirlineMemberCode":null,"PromotionCode":null,"PromotionDate":null,"PromotionValue":null},"IsSuccess":true,"Message":""}
INFO  2025-08-04 18:20:08,720 [44   ]            - Http Request Information: {"ControllerName":"NotificationHistoryV2","ActionName":"GetTransDetail","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/notification-history-v2/get-tx-detail","QueryString":"{\"MemberCode\":[\"XvguzAVcrOYQlvoXmDOcuEoJihw2\"],\"Type\":[\"MONEYCARD\"],\"CardTransactionId\":[\"2991\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 18:20:19,348 [60   ]            - Http Request Information: {"ControllerName":"NotificationHistoryV2","ActionName":"GetTransDetail","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/notification-history-v2/get-tx-detail","QueryString":"{\"MemberCode\":[\"XvguzAVcrOYQlvoXmDOcuEoJihw2\"],\"Type\":[\"MONEYCARD\"],\"CardTransactionId\":[\"2991\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 18:20:56,625 [60   ]            - Http Request Information: {"ControllerName":"NotificationHistoryV2","ActionName":"GetTransDetail","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/notification-history-v2/get-tx-detail","QueryString":"{\"MemberCode\":[\"XvguzAVcrOYQlvoXmDOcuEoJihw2\"],\"Type\":[\"MONEYCARD\"],\"CardTransactionId\":[\"2991\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 18:21:04,314 [72   ]            - Http Request Information: {"ControllerName":"NotificationHistoryV2","ActionName":"GetTransDetail","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/notification-history-v2/get-tx-detail","QueryString":"{\"MemberCode\":[\"Fzw4wS04C4P2JBWbGRbT93lHXMK2\"],\"Type\":[\"MONEYCARD\"],\"CardTransactionId\":[\"2991\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2025-08-04 18:21:10,120 [64   ] Controller -  >> ERROR PARSING DESCRIPTION >> 
INFO  2025-08-04 18:21:10,124 [64   ] Controller -  Final Output: {"Result":{"Id":null,"TokenTransId":null,"Title":null,"Content":null,"Amount":50000.0,"ActionType":"Redeem","ActionCode":null,"OrderCode":"113407460EC60540FBAC27970B5DE12FAC1911","ContentPhoto":null,"DescriptionPhoto":null,"PartnerName":null,"PartnerIcon":null,"MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","WalletAddress":"cde77a70621e4f719a0ab1390c864be4","ToWalletAddress":"9a9bf72c91e1a56b2aefcda62ba3c85e2cc0e1","FromWalletAddress":"cde77a70621e4f719a0ab1390c864be4","CreationTime":"2025-08-04T11:19:11.237Z","ExpiredTime":"2025-08-28T16:59:59Z","RelatedTokenTransId":null,"ServiceName":null,"PackageName":null,"CardValue":null,"ToPhoneNumber":null,"UsageAddress":null,"LastModificationTime":null,"GiftName":null,"GiftId":null,"GiftImage":null,"GiftPaidCoin":0.0,"EGiftExpiredDate":null,"BrandName":null,"BrandImage":null,"Vendor":null,"RedeemQuantity":0,"PartnerPointAmount":0.0,"BatchNote":null,"CampaignName":null,"InvitedMember":null,"PartnerPointExchangeType":null,"PartnerBindingTxId":"","PartnerMemberFirstName":null,"PartnerMemberLastName":null,"AirlineMemberCode":null,"PromotionCode":null,"PromotionDate":null,"PromotionValue":null},"IsSuccess":true,"Message":""}
INFO  2025-08-04 18:21:26,062 [73   ]            - Http Request Information: {"ControllerName":"NotificationHistoryV2","ActionName":"GetTransDetail","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/notification-history-v2/get-tx-detail","QueryString":"{\"MemberCode\":[\"Fzw4wS04C4P2JBWbGRbT93lHXMK2\"],\"Type\":[\"MONEYCARD\"],\"CardTransactionId\":[\"2991\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-04 18:22:00,275 [59   ]            - Http Request Information: {"ControllerName":"NotificationHistoryV2","ActionName":"GetTransDetail","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/notification-history-v2/get-tx-detail","QueryString":"{\"MemberCode\":[\"Fzw4wS04C4P2JBWbGRbT93lHXMK2\"],\"Type\":[\"MONEYCARD\"],\"CardTransactionId\":[\"2991\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2025-08-04 18:22:03,887 [75   ] Controller -  >> ERROR PARSING DESCRIPTION >> 
INFO  2025-08-04 18:22:03,911 [75   ] Controller -  Final Output: {"Result":{"Id":null,"TokenTransId":null,"Title":null,"Content":null,"Amount":50000.0,"ActionType":"Redeem","ActionCode":null,"OrderCode":"113407460EC60540FBAC27970B5DE12FAC1911","ContentPhoto":null,"DescriptionPhoto":null,"PartnerName":null,"PartnerIcon":null,"MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","WalletAddress":"cde77a70621e4f719a0ab1390c864be4","ToWalletAddress":"9a9bf72c91e1a56b2aefcda62ba3c85e2cc0e1","FromWalletAddress":"cde77a70621e4f719a0ab1390c864be4","CreationTime":"2025-08-04T11:19:11.237Z","ExpiredTime":"2025-08-28T16:59:59Z","RelatedTokenTransId":null,"ServiceName":null,"PackageName":null,"CardValue":null,"ToPhoneNumber":null,"UsageAddress":null,"LastModificationTime":null,"GiftName":null,"GiftId":null,"GiftImage":null,"GiftPaidCoin":0.0,"EGiftExpiredDate":null,"BrandName":null,"BrandImage":null,"Vendor":null,"RedeemQuantity":0,"PartnerPointAmount":0.0,"BatchNote":null,"CampaignName":null,"InvitedMember":null,"PartnerPointExchangeType":null,"PartnerBindingTxId":"","PartnerMemberFirstName":null,"PartnerMemberLastName":null,"AirlineMemberCode":null,"PromotionCode":null,"PromotionDate":null,"PromotionValue":null},"IsSuccess":true,"Message":""}
ERROR 2025-08-04 18:22:08,852 [91   ] Controller -  >> ERROR PARSING DESCRIPTION >> 
INFO  2025-08-04 18:22:08,856 [91   ] Controller -  Final Output: {"Result":{"Id":null,"TokenTransId":null,"Title":null,"Content":null,"Amount":50000.0,"ActionType":"Redeem","ActionCode":null,"OrderCode":"113407460EC60540FBAC27970B5DE12FAC1911","ContentPhoto":null,"DescriptionPhoto":null,"PartnerName":null,"PartnerIcon":null,"MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","WalletAddress":"cde77a70621e4f719a0ab1390c864be4","ToWalletAddress":"9a9bf72c91e1a56b2aefcda62ba3c85e2cc0e1","FromWalletAddress":"cde77a70621e4f719a0ab1390c864be4","CreationTime":"2025-08-04T11:19:11.237Z","ExpiredTime":"2025-08-28T16:59:59Z","RelatedTokenTransId":null,"ServiceName":null,"PackageName":null,"CardValue":null,"ToPhoneNumber":null,"UsageAddress":null,"LastModificationTime":null,"GiftName":null,"GiftId":null,"GiftImage":null,"GiftPaidCoin":0.0,"EGiftExpiredDate":null,"BrandName":null,"BrandImage":null,"Vendor":null,"RedeemQuantity":0,"PartnerPointAmount":0.0,"BatchNote":null,"CampaignName":null,"InvitedMember":null,"PartnerPointExchangeType":null,"PartnerBindingTxId":"","PartnerMemberFirstName":null,"PartnerMemberLastName":null,"AirlineMemberCode":null,"PromotionCode":null,"PromotionDate":null,"PromotionValue":null},"IsSuccess":true,"Message":""}
INFO  2025-08-04 18:22:14,686 [89   ]            - Http Request Information: {"ControllerName":"NotificationHistoryV2","ActionName":"GetTransDetail","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/notification-history-v2/get-tx-detail","QueryString":"{\"MemberCode\":[\"Fzw4wS04C4P2JBWbGRbT93lHXMK2\"],\"Type\":[\"MONEYCARD\"],\"CardTransactionId\":[\"2991\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2025-08-04 18:22:45,427 [92   ] Controller -  >> ERROR PARSING DESCRIPTION >> 
INFO  2025-08-04 18:22:45,432 [92   ] Controller -  Final Output: {"Result":{"Id":null,"TokenTransId":null,"Title":null,"Content":null,"Amount":50000.0,"ActionType":"Redeem","ActionCode":null,"OrderCode":"113407460EC60540FBAC27970B5DE12FAC1911","ContentPhoto":null,"DescriptionPhoto":null,"PartnerName":null,"PartnerIcon":null,"MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","WalletAddress":"cde77a70621e4f719a0ab1390c864be4","ToWalletAddress":"9a9bf72c91e1a56b2aefcda62ba3c85e2cc0e1","FromWalletAddress":"cde77a70621e4f719a0ab1390c864be4","CreationTime":"2025-08-04T11:19:11.237Z","ExpiredTime":"2025-08-28T16:59:59Z","RelatedTokenTransId":null,"ServiceName":null,"PackageName":null,"CardValue":null,"ToPhoneNumber":null,"UsageAddress":null,"LastModificationTime":null,"GiftName":null,"GiftId":null,"GiftImage":null,"GiftPaidCoin":0.0,"EGiftExpiredDate":null,"BrandName":null,"BrandImage":null,"Vendor":null,"RedeemQuantity":0,"PartnerPointAmount":0.0,"BatchNote":null,"CampaignName":null,"InvitedMember":null,"PartnerPointExchangeType":null,"PartnerBindingTxId":"","PartnerMemberFirstName":null,"PartnerMemberLastName":null,"AirlineMemberCode":null,"PromotionCode":null,"PromotionDate":null,"PromotionValue":null},"IsSuccess":true,"Message":""}
INFO  2025-08-04 18:22:48,420 [98   ]            - Http Request Information: {"ControllerName":"NotificationHistoryV2","ActionName":"GetTransDetail","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/notification-history-v2/get-tx-detail","QueryString":"{\"MemberCode\":[\"Fzw4wS04C4P2JBWbGRbT93lHXMK2\"],\"Type\":[\"MONEYCARD\"],\"CardTransactionId\":[\"2991\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2025-08-04 18:23:17,490 [91   ] Controller -  >> ERROR PARSING DESCRIPTION >> 
INFO  2025-08-04 18:23:17,493 [91   ] Controller -  Final Output: {"Result":{"Id":null,"TokenTransId":null,"Title":null,"Content":null,"Amount":50000.0,"ActionType":"Redeem","ActionCode":null,"OrderCode":"113407460EC60540FBAC27970B5DE12FAC1911","ContentPhoto":null,"DescriptionPhoto":null,"PartnerName":null,"PartnerIcon":null,"MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","WalletAddress":"cde77a70621e4f719a0ab1390c864be4","ToWalletAddress":"9a9bf72c91e1a56b2aefcda62ba3c85e2cc0e1","FromWalletAddress":"cde77a70621e4f719a0ab1390c864be4","CreationTime":"2025-08-04T11:19:11.237Z","ExpiredTime":"2025-08-28T16:59:59Z","RelatedTokenTransId":null,"ServiceName":null,"PackageName":null,"CardValue":null,"ToPhoneNumber":null,"UsageAddress":null,"LastModificationTime":null,"GiftName":null,"GiftId":null,"GiftImage":null,"GiftPaidCoin":0.0,"EGiftExpiredDate":null,"BrandName":null,"BrandImage":null,"Vendor":null,"RedeemQuantity":0,"PartnerPointAmount":0.0,"BatchNote":null,"CampaignName":null,"InvitedMember":null,"PartnerPointExchangeType":null,"PartnerBindingTxId":"","PartnerMemberFirstName":null,"PartnerMemberLastName":null,"AirlineMemberCode":null,"PromotionCode":null,"PromotionDate":null,"PromotionValue":null},"IsSuccess":true,"Message":""}
INFO  2025-08-04 18:23:35,219 [99   ]            - Http Request Information: {"ControllerName":"NotificationHistoryV2","ActionName":"GetTransDetail","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/notification-history-v2/get-tx-detail","QueryString":"{\"MemberCode\":[\"Fzw4wS04C4P2JBWbGRbT93lHXMK2\"],\"Type\":[\"MONEYCARD\"],\"CardTransactionId\":[\"2991\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2025-08-04 18:23:42,110 [102  ] Controller -  >> ERROR PARSING DESCRIPTION >> 
INFO  2025-08-04 18:23:42,117 [102  ] Controller -  Final Output: {"Result":{"Id":null,"TokenTransId":null,"Title":null,"Content":null,"Amount":50000.0,"ActionType":"Redeem","ActionCode":null,"OrderCode":"113407460EC60540FBAC27970B5DE12FAC1911","ContentPhoto":null,"DescriptionPhoto":null,"PartnerName":null,"PartnerIcon":null,"MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","WalletAddress":"cde77a70621e4f719a0ab1390c864be4","ToWalletAddress":"9a9bf72c91e1a56b2aefcda62ba3c85e2cc0e1","FromWalletAddress":"cde77a70621e4f719a0ab1390c864be4","CreationTime":"2025-08-04T11:19:11.237Z","ExpiredTime":"2025-08-28T16:59:59Z","RelatedTokenTransId":null,"ServiceName":null,"PackageName":null,"CardValue":null,"ToPhoneNumber":null,"UsageAddress":null,"LastModificationTime":null,"GiftName":null,"GiftId":null,"GiftImage":null,"GiftPaidCoin":0.0,"EGiftExpiredDate":null,"BrandName":null,"BrandImage":null,"Vendor":null,"RedeemQuantity":0,"PartnerPointAmount":0.0,"BatchNote":null,"CampaignName":null,"InvitedMember":null,"PartnerPointExchangeType":null,"PartnerBindingTxId":"","PartnerMemberFirstName":null,"PartnerMemberLastName":null,"AirlineMemberCode":null,"PromotionCode":null,"PromotionDate":null,"PromotionValue":null},"IsSuccess":true,"Message":""}
INFO  2025-08-04 18:24:31,927 [103  ]            - Http Request Information: {"ControllerName":"NotificationHistoryV2","ActionName":"GetTransDetail","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/notification-history-v2/get-tx-detail","QueryString":"{\"MemberCode\":[\"Fzw4wS04C4P2JBWbGRbT93lHXMK2\"],\"Type\":[\"MONEYCARD\"],\"CardTransactionId\":[\"2991\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2025-08-04 18:24:55,059 [105  ] Controller -  >> ERROR PARSING DESCRIPTION >> 
INFO  2025-08-04 18:24:55,062 [105  ] Controller -  Final Output: {"Result":{"Id":null,"TokenTransId":null,"Title":"Đổi quà thành công","Content":null,"Amount":50000.0,"ActionType":"Redeem","ActionCode":null,"OrderCode":"113407460EC60540FBAC27970B5DE12FAC1911","ContentPhoto":null,"DescriptionPhoto":null,"PartnerName":null,"PartnerIcon":null,"MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","WalletAddress":"cde77a70621e4f719a0ab1390c864be4","ToWalletAddress":"9a9bf72c91e1a56b2aefcda62ba3c85e2cc0e1","FromWalletAddress":"cde77a70621e4f719a0ab1390c864be4","CreationTime":"2025-08-04T11:19:11.237Z","ExpiredTime":"2025-08-28T16:59:59Z","RelatedTokenTransId":null,"ServiceName":null,"PackageName":null,"CardValue":null,"ToPhoneNumber":null,"UsageAddress":null,"LastModificationTime":null,"GiftName":"Quà test timeout evoucher1","GiftId":"43176","GiftImage":"https://linkidstorage.s3-ap-southeast-1.amazonaws.com/upload-gift/3765640c8e9f2897f9044ad6786da5d8.jpg","GiftPaidCoin":50000.0,"EGiftExpiredDate":"2027-08-04T16:59:59Z","BrandName":"Gong cha","BrandImage":"https://linkidstorage.s3-ap-southeast-1.amazonaws.com/upload-gift/3eeda88e8de761350844017776a99c54.png","Vendor":{"Type":"LinkID","Image":null,"HotLine":null,"Id":14,"VendorName":"LinkID"},"RedeemQuantity":1,"PartnerPointAmount":0.0,"BatchNote":null,"CampaignName":null,"InvitedMember":null,"PartnerPointExchangeType":null,"PartnerBindingTxId":"","PartnerMemberFirstName":null,"PartnerMemberLastName":null,"AirlineMemberCode":null,"PromotionCode":null,"PromotionDate":null,"PromotionValue":null},"IsSuccess":true,"Message":""}
