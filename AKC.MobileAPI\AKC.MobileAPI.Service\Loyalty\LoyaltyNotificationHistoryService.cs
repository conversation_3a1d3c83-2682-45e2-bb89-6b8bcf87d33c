﻿using AKC.MobileAPI.DTO.Loyalty.NotificationHistory;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Reward;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyNotificationHistoryService : BaseLoyaltyService, ILoyaltyNotificationHistoryService
    {
        public LoyaltyNotificationHistoryService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {

        }

        public async Task<LoyaltyNotificationHistoryGetAllOutput> GetAllByMemberCode(MobileLoyaltyGetAllNotificationsInput input, string authorization)
        {
            var Code = !string.IsNullOrWhiteSpace(input.MemberCode) ? input.MemberCode : await GetNational(authorization);
            var notiCate = input.NotificationCategory;
            if (string.IsNullOrEmpty(notiCate))
            {
                notiCate = "TRANS";
            }
            LoyaltyGetAllNotificationsInput LoyaltyInput = new LoyaltyGetAllNotificationsInput()
            {
                MemberCode = Code,
                Language = input.Language,
                MaxResultCount = input.MaxResultCount,
                SkipCount = input.SkipCount,
                NotificationCategory = notiCate
            };

            return await GetLoyaltyAsync<LoyaltyNotificationHistoryGetAllOutput>(LoyaltyApiUrl.NOTIFICATION_HISTORY_GET_ALL_BY_MEMBER_CODE, LoyaltyInput);
        }

        public async Task<LoyaltyNotificationHistoryChangeStatusOutput> ChangeStatus(MobileLoyaltyChangeStatusInput input, string authorization)
        {
            var Code = !string.IsNullOrWhiteSpace(input.MemberCode) ? input.MemberCode : await GetNational(authorization);
            LoyaltyChangeStatusInput LoyaltyInput = new LoyaltyChangeStatusInput()
            {
                MemberCode = Code,
                IsChangeAll = input.IsChangeAll,
                IdNotification = input.IdNotification
            };
            
            return await PostLoyaltyAsync<LoyaltyNotificationHistoryChangeStatusOutput>(LoyaltyApiUrl.NOTIFICATION_HISTORY_CHANGE_STATUS, LoyaltyInput);
        }

        public async Task<LoyaltyNotificationHistoryChangeCountOutput> ChangeNotificationCount(MobileLoyaltyChangeNotificationCountInput input, string authorization)
        {
            var Code = !string.IsNullOrWhiteSpace(input.MemberCode) ? input.MemberCode : await GetNational(authorization);
            LoyaltyChangeNotificationCountInput LoyaltyInput = new LoyaltyChangeNotificationCountInput()
            {
                MemberCode = Code
            };

            return await PostLoyaltyAsync<LoyaltyNotificationHistoryChangeCountOutput>(LoyaltyApiUrl.NOTIFICATION_HISTORY_CHANGE_NOTIFICATION_COUNT, LoyaltyInput);
        }

        public async Task<LoyaltyNotificationHistoryDeleteOutput> Delete(MobileLoyaltyDeleteInput input, string authorization)
        {
            var Code = !string.IsNullOrWhiteSpace(input.MemberCode) ? input.MemberCode : await GetNational(authorization);
            LoyaltyDeleteInput LoyaltyInput = new LoyaltyDeleteInput()
            {
                MemberCode = Code,
                Id = input.Id
            };

            return await DeleteLoyaltyAsync<LoyaltyNotificationHistoryDeleteOutput>(LoyaltyApiUrl.NOTIFICATION_HISTORY_DELETE, LoyaltyInput);
        }

        public async Task<LoyaltyNotificationHistoryDeleteOutput> HardDelete(MobileLoyaltyDeleteInput input, string authorization)
        {
            var Code = !string.IsNullOrWhiteSpace(input.MemberCode) ? input.MemberCode : await GetNational(authorization);
            LoyaltyDeleteInput LoyaltyInput = new LoyaltyDeleteInput()
            {
                MemberCode = Code,
                Id = input.Id
            };

            return await PostLoyaltyAsync<LoyaltyNotificationHistoryDeleteOutput>(LoyaltyApiUrl.NOTIFICATION_HISTORY_HARD_DELETE, LoyaltyInput);
        }

        public async Task<SendNotificationAboutLogoutOutput> SendNotificationAboutLogout(
            SendNotificationAboutLogoutInput input)
        {
            return await PostLoyaltyAsync<SendNotificationAboutLogoutOutput>(LoyaltyApiUrl.NOTIFICATION_SEND_NOTI_TO_OTHERDEVICE_EXCEPT_NEWESTDEVICE, input);
        }
    }
}
