﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class LoyaltyGetAllForCategoryInput
    {
        public string MemberCode { get; set; }

        public string Filter { get; set; }

        public string IntroduceFilter { get; set; }

        public string FullGiftCategoryCodeFilter { get; set; }

        public string ProducerFilter { get; set; }

        public string VendorFilter { get; set; }

        public string StatusFilter { get; set; }

        public bool? IsEGiftFilter { get; set; }

        public string Sorting { get; set; }

        public int SkipCount { get; set; }

        public int MaxResultCount { get; set; } = int.MaxValue;
    }
}
