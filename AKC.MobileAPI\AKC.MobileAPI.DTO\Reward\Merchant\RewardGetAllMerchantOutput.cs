﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Merchant
{
    public class RewardGetAllMerchantOutput
    {
        public int Result { get; set; }
        public List<RewardGetAllMerchantItems> Items { get; set; }
        public int TotalCount { get; set; }
        public string Message { get; set; }
    }

    public class RewardGetAllMerchantItems
    {
        public string WalletAddress { get; set; }
        public int Id { get; set; }
        public string MerchantName { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Status { get; set; }
        public string Logo { get; set; }
        public string Type { get; set; }
        public int BaseUnit { get; set; }
        public int PointExchangeRate { get; set; }
        public string MaintenanceFrom { get; set; }
        public string MaintenanceTo { get; set; }
        public string MaintenanceStatus { get; set; }
        public bool IsAKCLoyalty { get; set; }
        public string X1lTenantId { get; set; }
        public string OrgId { get; set; }
        public string CoinIcon { get; set; }
        public bool AllowConnectFromLynkId { get; set; }
        public List<RewardGetAllMerchantStore> StoreList { get; set; }
        public RewardGetAllMerchantAdditionalData AdditionalData { get; set; }
        public decimal? RedeemExchangeRate { get; set; }
        public int? MerchantUnit { get; set; }
        public string PartnerPointExchangeType { get; set; }
        public bool IsConnectedToRedeemExchange { get; set; }
        public int? MinExchangePerTime { get; set; }
        public int? LimitExchangePerTime { get; set; }
        public bool IsPublish { get; set; }
    }

    public class RewardGetAllMerchantStore
    {
        public int? Id { get; set; }
        public int? MerchantId { get; set; }
        public string StoreName { get; set; }
        public string UnsignName { get; set; }
        public string PhoneNumber { get; set; }
        public string Address { get; set; }
        public string Email { get; set; }
        public string Status { get; set; }
        public string Avatar { get; set; }
        public string Region { get; set; }
        public long? Longitude { get; set; }
        public string Description { get; set; }
        public long? Latitude { get; set; }
    }

    public class RewardGetAllMerchantAdditionalData
    {
        public int? Id { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool? IsDeleted { get; set; }
        public int? MerchantId { get; set; }
        public string Introduction { get; set; }
        public string Hotline { get; set; }
        public string Website { get; set; }
        public string CoverPhoto { get; set; }
        public string EarningDescription { get; set; }
        public int? ShowedUsers { get; set; }
    }
}
