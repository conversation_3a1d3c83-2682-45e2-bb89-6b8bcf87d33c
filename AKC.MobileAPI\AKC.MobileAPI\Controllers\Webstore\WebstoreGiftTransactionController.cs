﻿using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory.V2;
using AKC.MobileAPI.DTO.Webstore;
using AKC.MobileAPI.Service.Abstract;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.EGiftInfors;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.Service.Abstract.Loyalty.EGiftInfors;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;

namespace AKC.MobileAPI.Controllers.Webstore
{
    [ApiController]
    [Route("api/webstore/gift-transaction")]
    [ApiConventionType(typeof(DefaultApiConventions))]
    public class WebstoreGiftTransactionController : ControllerBase
    {
        private readonly IDistributedCache _cache;
        private readonly IConfiguration _config;
        private readonly ILogger<WebstoreGiftTransactionController> _logger;
        private readonly ILoyaltyGiftTransactionsService _giftTransactionsService;
        private readonly ILoyaltyEGiftInforsService _egiftService;
        private readonly IRewardMemberService _rewardService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltySecondaryCustomersService _memberLoyService;
        private readonly ISmeTransactionService _smeTransactionService;
        private readonly IConfiguration _configuration;
        private readonly ILoyaltyLocationService _loyaltyLocationService;
        private int maxQuantityPerRedeem = 0;

        public WebstoreGiftTransactionController(
            IDistributedCache cache, IConfiguration config,
            ILogger<WebstoreGiftTransactionController> lg,
            ILoyaltyEGiftInforsService es,
            ILoyaltyGiftTransactionsService giftTransactionsService,
            IRewardMemberService rewardService, 
            IExceptionReponseService er,
            IConfiguration ic,
            ILoyaltySecondaryCustomersService ml,
            ILoyaltyLocationService lsl,
            ISmeTransactionService smeTransactionService
         )
        {
            _memberLoyService = ml;
            _cache = cache;
            _config = config;
            _egiftService = es;
            _exceptionReponseService = er;
            _logger = lg;
            _giftTransactionsService = giftTransactionsService;
            _rewardService = rewardService;
            _smeTransactionService = smeTransactionService;
            _configuration = ic;
            _loyaltyLocationService = lsl;
            var check = int.TryParse(_configuration.GetSection("WebStoreMaxQuantityDefault").Value, out maxQuantityPerRedeem);
            if (!check)
            {
                maxQuantityPerRedeem = 9;
            }
        }

        [HttpGet]
        [Route("get-all-my-gifts")]
        public async Task<ActionResult<WebstoreBaseOutputDto<WebStoreGetAllMyGiftResponse>>> GetAllMyGift([FromQuery] WebStoreGetAllMyGiftRequest input)
        {
            _logger.LogInformation($"WebstoreGiftTransaction_GetAllMyGift Input: {JsonConvert.SerializeObject(input)}");
            try
            {
                var memberCode = HttpContext.Items["MemberCode"]?.ToString();
                if (string.IsNullOrEmpty(memberCode) || memberCode != input.MemberCode)
                {
                    return BadRequest(WebstoreBaseOutputDto<WebStoreGetAllMyGiftResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotMatch_Token, "Invalid Token"));
                }

                var cacheKey = "";
                if (string.IsNullOrWhiteSpace(input.GiftTransactionCode))
                {
                    cacheKey = $"WebAppGiftTransactions_{input.MemberCode}_{input.EGiftStatusFilter}_{input.StatusFilter}_{input.FromDateFilter?.ToString("yyyyMMdd")}_{input.ToDateFilter?.ToString("yyyyMMdd")}_{input.GiftTransactionCode}_{input.SkipCount}_{input.MaxResultCount}_{input.TransactionCode}";
                }
                else
                {
                    cacheKey = $"WebAppGiftTransactions_{input.GiftTransactionCode}";
                }
                var cachedResult = await _cache.GetStringAsync(cacheKey);
                var getSingle = string.IsNullOrEmpty(input.GiftTransactionCode) == false;
                WebStoreGetAllMyGiftResponse ret;
    
                if (cachedResult != null)
                {
                    ret = JsonConvert.DeserializeObject<WebStoreGetAllMyGiftResponse>(cachedResult);
                }
                else
                {
                    var result = await _giftTransactionsService.GiftTransactionHistory(new MerchantGiftTransactionDetailInputDto
                    {
                        TransactionCode = input.TransactionCode,
                        OwnerCodeFilter = input.MemberCode,
                        EGiftStatusFilter = input.EGiftStatusFilter,
                        StatusFilter = input.StatusFilter,
                        FromDateFilter = input.FromDateFilter,
                        ToDateFilter = input.ToDateFilter,
                        GiftTransactionCode = input.GiftTransactionCode,
                        SkipCount = input.SkipCount,
                        MaxResultCount = input.MaxResultCount,
                        GetBrandInfo = "YES",
                        ConnectSource = "WebApp"
                        // MerchantName = "LinkiD"
                    });

                    var listItem = new List<WebStoreGetAllMyGiftResponseItem>();
                    foreach (var xView in result.Result.Items)
                    {
                        var isEGift = xView.EGift != null && !string.IsNullOrEmpty(xView.EGift.Code);
                        var xInside = new WebStoreGetAllMyGiftResponseItem
                        {
                            GiftCode = xView.GiftTransaction.GiftCode,
                            GiftName = xView.GiftTransaction.GiftName,
                            ExpiredDate = xView.EGift?.ExpiredDate?.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                            GiftTransactionCode = xView.GiftTransaction.Code,
                            BrandImage = xView.BrandInfo?.BrandImage,
                            BrandName = xView.BrandInfo?.BrandName,
                            WhyHaveIt = xView.GiftTransaction.WhyHaveIt,
                            GiftId = xView.GiftTransaction.GiftId ?? 0,
                            IsEGift = isEGift,
                            UsageCheck = xView.EGift != null && xView.EGift.UsageCheck,
                            Status = xView.GiftTransaction.Status,
                            EGiftCode = xView.EGift?.Code,
                            Description = xView.GiftTransaction.Description,
                            UsedStatus = xView.EGift?.UsedStatus,
                            PurchaseDate = xView.GiftTransaction.CreationTime,
                            IsAvailableToRedeemAgain = xView.IsAvailableToRedeemAgain,
                            SerialNo = xView.GiftTransaction.SerialNo,
                            GiftIntroduce = xView.GiftTransaction.Introduce,
                            GiftDescription = xView.GiftTransaction.GiftDescription,
                            EGiftUsedAt = xView.GiftTransaction.EGiftUsedAt,
                            Quantity = xView.GiftTransaction.Quantity,
                            RejectReason = xView.GiftTransaction.RejectReason
                        };
                        if (getSingle && !isEGift)
                        {
                            
                            // View detail và là Quà VL thì sẽ ghép Địa chỉ cho Web sẵn show
                            var desc = xView.GiftTransaction.Description;
                            if (!string.IsNullOrEmpty(desc))
                            {
                                try
                                {
                                    var descObj = JsonConvert.DeserializeObject<WebstoreQuaVLAddressDto>(desc);
                                    if (descObj != null)
                                    {
                                        var address = descObj.shipAddress;
                                        var dbAddress =
                                            await _giftTransactionsService.GetAddressBy3Ids(new GetAddressBy3IdsInput()
                                            {
                                                CityId = descObj.cityId,
                                                DistrictId = descObj.districtId,
                                                WardId = descObj.wardId,
                                            });
                                        if (dbAddress != null && dbAddress.Result != null)
                                        {
                                            if (!string.IsNullOrEmpty(dbAddress.Result.Ward))
                                            {
                                                address += ", " + dbAddress.Result.Ward;
                                            }
                                            if (!string.IsNullOrEmpty(dbAddress.Result.District))
                                            {
                                                address += ", " + dbAddress.Result.District;
                                            }
                                            if (!string.IsNullOrEmpty(dbAddress.Result.Province))
                                            {
                                                address += ", " + dbAddress.Result.Province;
                                            }

                                            xInside.ShipAddress = address;
                                        }
                                    }
                                }
                                catch (Exception e)
                                {
                                    // Error Parsing. Skip it.
                                }
                            }
                        }
                        var receiveGiftFrom = "";
                        if (getSingle && xView.GiftTransaction.WhyHaveIt == "RECEIVED")
                        {
                            var memberBuy =
                                await _memberLoyService.GetProfileByCode(xView.GiftTransaction.BuyerCode);
                            if (memberBuy != null && memberBuy.Result != null)
                            {
                                receiveGiftFrom = memberBuy.Result.FirstName + " " + memberBuy.Result.LastName;
                                receiveGiftFrom = receiveGiftFrom.Trim();
                                if (string.IsNullOrEmpty(receiveGiftFrom))
                                {
                                    receiveGiftFrom = "****" + memberBuy.Result.Phone?.Substring(5);
                                }
                            }

                            xInside.ReceiveGiftFrom = receiveGiftFrom;
                        }
                        listItem.Add(xInside);
                    }

                    ret = new WebStoreGetAllMyGiftResponse
                    {
                        TotalCount = result.Result.TotalCount,
                        Items = listItem
                    };

                    await _cache.SetStringAsync(
                        cacheKey,
                        JsonConvert.SerializeObject(ret),
                        new DistributedCacheEntryOptions
                        {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(getSingle ? 5 : 1)
                        });
                }

                return Ok(WebstoreBaseOutputDto<WebStoreGetAllMyGiftResponse>.Success(ret));
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return BadRequest(WebstoreBaseOutputDto<WebStoreMarkUseOutput>.Error(res.Code, res.Message));
                }

                _logger.LogError(ex, "WebstoreGiftTransaction_GetAllMyGift");
                return BadRequest(WebstoreBaseOutputDto<WebStoreMarkUseOutput>.Error("500", "Internal server error"));
            }
        }

        [HttpPost]
        [Route("mark-use")]
        public async Task<ActionResult<WebstoreBaseOutputDto<WebStoreMarkUseOutput>>> MarkEGiftAsUse(
            [FromBody] WebStoreMarkUseInput input)
        {
            try
            {
                var memberCode = HttpContext.Items["MemberCode"]?.ToString();
                if (string.IsNullOrEmpty(memberCode) || memberCode != input.MemberCode)
                {
                    return BadRequest(WebstoreBaseOutputDto<WebStoreGetAllMyGiftResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotMatch_Token, "Invalid Token"));
                }
                _logger.LogInformation($"MarkEGiftAsUse Input: {JsonConvert.SerializeObject(input)}");
                await _egiftService.UpdateGiftStatus(new UpdateGiftStatusInput()
                {
                    MemberCode = input.MemberCode, GiftRedeemTransactionCode = input.GiftRedeemTransaction
                });
                var cacheKey = $"WebAppGiftTransactions_" + input.GiftRedeemTransaction;
                await _cache.RemoveAsync(cacheKey);
                return Ok(WebstoreBaseOutputDto<WebStoreMarkUseOutput>.Success(new WebStoreMarkUseOutput()));
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return BadRequest(WebstoreBaseOutputDto<WebStoreMarkUseOutput>.Error(res.Code, res.Message));
                }

                _logger.LogError(ex, "ErrorMarkEGiftAsUse");
                return BadRequest(WebstoreBaseOutputDto<WebStoreMarkUseOutput>.Error("500", "Internal server error"));
            }
        }


        /// <summary>
        /// API thực hiện khởi tạo việc redeem gift
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("create-redeem-gift-transaction")]
        public async Task<ActionResult<WebstoreBaseOutputDto<WebStoreCreateRedeemGiftTransactionOutput>>> CreateRedeemGiftTransaction(WebStoreCreateRedeemGiftTransactionInput input)
        {
            try
            {
                var memberCode = HttpContext.Items["MemberCode"]?.ToString();
                if (string.IsNullOrEmpty(memberCode) || memberCode != input.MemberCode)
                {
                    return BadRequest(WebstoreBaseOutputDto<WebStoreCreateRedeemGiftTransactionOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotMatch_Token, "Invalid Token"));
                }
                _logger.LogInformation(" >> CreateRedeemGiftTransaction >> " + JsonConvert.SerializeObject(input));
                if (string.IsNullOrWhiteSpace(input.GiftCode))
                {
                    return BadRequest(WebstoreBaseOutputDto<WebStoreCreateRedeemGiftTransactionOutput>.Error(WebstoreConstants.RedeemGiftErrorCodes.REDEEM_INVALID_GIFTCODE, "Invalid GiftCode"));
                }
                if (string.IsNullOrWhiteSpace(input.SessionId))
                {
                    return BadRequest(WebstoreBaseOutputDto<WebStoreCreateRedeemGiftTransactionOutput>.Error(WebstoreConstants.RedeemGiftErrorCodes.REDEEM_INVALID_SESSIONID, "Invalid SessionId"));
                }
                if (!input.TotalAmount.HasValue)
                {
                    return BadRequest(WebstoreBaseOutputDto<WebStoreCreateRedeemGiftTransactionOutput>.Error(WebstoreConstants.RedeemGiftErrorCodes.REDEEM_INVALID_TOTALAMOUNT, "Invalid TotatlAmount"));
                }
                if (!input.Quantity.HasValue)
                {
                    return BadRequest(WebstoreBaseOutputDto<WebStoreCreateRedeemGiftTransactionOutput>.Error(WebstoreConstants.RedeemGiftErrorCodes.REDEEM_INVALID_QUANTITY, "Invalid Quantity"));
                }
                var savedString = await _cache.GetStringAsync("RequestRedeem_" + input.MemberCode + "_" + input.SessionId);
                if (!string.IsNullOrWhiteSpace(savedString))
                {
                    return BadRequest(WebstoreBaseOutputDto<WebStoreCreateRedeemGiftTransactionOutput>.Error(WebstoreConstants.RedeemGiftErrorCodes.REDEEM_DUPLICATE_SESSIONID, "Duplicate SessionId"));
                }
                var result = await _smeTransactionService.CreateRedeemGiftTransaction(input);
                if (string.IsNullOrEmpty(result.Code))
                {
                    return Ok(WebstoreBaseOutputDto<WebStoreCreateRedeemGiftTransactionOutput>.Success(
                        new WebStoreCreateRedeemGiftTransactionOutput
                        { IsSendOtp = result.IsSendOtp, IsVerifyPassWord = result.IsVerifyPassWord, SessionId = input.SessionId }));
                }
                else
                {
                    return BadRequest(WebstoreBaseOutputDto<WebStoreCreateRedeemGiftTransactionOutput>.Error(result.Code, result.Message));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"WebstoreGiftTransactionController_CreateRedeemGiftTransactionmo Error: {ex.Message} __ STACKE {ex.StackTrace}");
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return BadRequest(WebstoreBaseOutputDto<WebStoreCreateRedeemGiftTransactionOutput>.Error(res.Code, res.Message));
                }

                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                    return BadRequest(WebstoreBaseOutputDto<WebStoreCreateRedeemGiftTransactionOutput>.Error(res.Code, res.Message));
                }
                return BadRequest(WebstoreBaseOutputDto<WebStoreCreateRedeemGiftTransactionOutput>.Error(WebstoreConstants.RedeemGiftErrorCodes.REDEEM_INTERNAL_SERVER, "Internal Server"));
            }

        }
        /// <summary>
        /// API xác nhận redeem. Nếu mức coin nhỏ hơn config, thì cần password token, còn lớn hơn sẽ dùng SMS OTP
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("verify-redeem-gift-transaction")]
        public async Task<ActionResult<WebstoreBaseOutputDto<SmeRedeemGiftOutput>>> VerifyRedeemGiftTransaction(VerifyRedeemGiftTransactionInput input)
        {
            try
            {
                var memberCode = HttpContext.Items["MemberCode"]?.ToString();
                if (string.IsNullOrEmpty(memberCode) || memberCode != input.MemberCode)
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRedeemGiftOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotMatch_Token, "Invalid Token"));
                }
                if (string.IsNullOrWhiteSpace(input.SessionId))
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRedeemGiftOutput>.Error(WebstoreConstants.RedeemGiftErrorCodes.REDEEM_INVALID_SESSIONID, "Invalid SessionId"));
                }
                var result = await _smeTransactionService.VerifyRedeemGiftTransaction(input, HttpContext);
                if (string.IsNullOrEmpty(result.Code) || result.Code == "00")
                {
                    return Ok(WebstoreBaseOutputDto<SmeRedeemGiftOutput>.Success(new SmeRedeemGiftOutput
                    {
                        Egifts = result.Egifts,
                        TransactionCode = result.TransactionCode,
                    }));
                }
                else
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRedeemGiftOutput>.Error(result.Code, result.Message));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"WebstoreGiftTransactionController_VerifyRedeemGiftTransaction Error: {ex.Message} __ STACKE {ex.StackTrace}");
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return BadRequest(WebstoreBaseOutputDto<SmeRedeemGiftOutput>.Error(res.Code, res.Message));
                }

                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                    if ("InvalidOtp".Equals(res.Code))
                    {
                        return StatusCode(400, new RewardInvalidResponse()
                        {
                            Code = WebstoreConstants.LoginErrorCodes.COMMON_InvalidOTP, Message = "OTP không chính xác"
                            , numberOfRetries = res.numberOfRetries
                        });
                    }
                    if ("OtpExpired".Equals(res.Code))
                    {
                        return StatusCode(400, new RewardInvalidResponse()
                        {
                            Code = WebstoreConstants.LoginErrorCodes.COMMON_SESSIONOTP_EXPIRED, Message = "OTP đã hết hạn"
                            , numberOfRetries = res.numberOfRetries
                        });
                    }
                    if ("PhoneNumberReachMaxFailure".Equals(res.Code))
                    {
                        return StatusCode(400, new RewardInvalidResponse()
                        {
                            Code = WebstoreConstants.LoginErrorCodes.COMMON_TooManyWrongOtpAttempts, Message = "Bạn đã điền sai OTP quá nhiều lần. Hãy chờ và thử lại."
                            , numberOfRetries = res.numberOfRetries
                        });
                    }
                    if ("PhoneNumberBlocked".Equals(res.Code))
                    {
                        return StatusCode(400, new RewardInvalidResponse()
                        {
                            Code = WebstoreConstants.LoginErrorCodes.COMMON_PhoneBlockedFromOtp, Message = "Số điện thoại tạm thời bị chặn khỏi tính năng OTP. Vui lòng thử lại sau."
                            , numberOfRetries = res.numberOfRetries
                        });
                    }
                    if (res.Code == "PositiveCreditBalance")
                    {
                        return StatusCode(400, new RewardInvalidResponse()
                        {
                            Code = WebstoreConstants.LoginErrorCodes.COMMON_CREDITBALANCEPOSITIVE, 
                            Message = "Bạn đang có giao dịch cần xử lý. Bạn vui lòng liên hệ với Hotline LynkiD để được hỗ trợ nhé.",
                            MessageDetail = "Bạn đang có giao dịch cần xử lý. Bạn vui lòng liên hệ với Hotline LynkiD để được hỗ trợ nhé."
                        });
                    }
                    return BadRequest(WebstoreBaseOutputDto<SmeRedeemGiftOutput>.Error(res.Code, res.Message));
                }
                return BadRequest(WebstoreBaseOutputDto<SmeRedeemGiftOutput>.Error(WebstoreConstants.RedeemGiftErrorCodes.REDEEM_INTERNAL_SERVER, "Internal Server"));
            }

        }


        //[HttpPost]
        //[Route("resend-otp")]
        //public async Task<ActionResult<WebstoreBaseOutputDto<GetOTPOutput>>> ResendOtp(GetOTPInput input)
        //{
        //    try
        //    {
        //        if (string.IsNullOrEmpty(input.PhoneNumber))
        //        {
        //            return BadRequest(WebstoreBaseOutputDto<GetOTPOutput>.Error(WebstoreConstants.RedeemGiftErrorCodes.INVALID_PHONENUMBER, "Invalid PhoneNumber"));
        //        }
        //        if (string.IsNullOrEmpty(input.SessionId))
        //        {
        //            return BadRequest(WebstoreBaseOutputDto<GetOTPOutput>.Error(WebstoreConstants.RedeemGiftErrorCodes.REDEEM_INVALID_SESSIONID, "Invalid SessionId"));
        //        }
        //        var result = await _smeTransactionService.ResendOtp(input);
        //        return Ok(WebstoreBaseOutputDto<GetOTPOutput>.Success(new GetOTPOutput()));

        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"WebstoreGiftTransactionController_ResendOtp Error: {ex.Message} __ STACKE {ex.StackTrace}");
        //        return BadRequest(WebstoreBaseOutputDto<SmeRedeemGiftOutput>.Error(WebstoreConstants.RedeemGiftErrorCodes.REDEEM_INTERNAL_SERVER, "Internal Server"));
        //    }
        //}
    }
}