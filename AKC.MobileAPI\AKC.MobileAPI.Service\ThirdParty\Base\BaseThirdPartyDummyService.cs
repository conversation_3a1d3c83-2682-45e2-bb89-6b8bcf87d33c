﻿using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.ThirdParty.Base
{
    public class BaseThirdPartyDummyService
    {
        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        protected readonly string baseURL;
        protected readonly int tenantId;
        protected readonly string defaultUserName;
        protected readonly string defaultPassowrd;
        private readonly IDistributedCache _cache;
        protected readonly ILogger _logger;

        public BaseThirdPartyDummyService(
            IConfiguration configuration,
            IDistributedCache cache,
            ILogger<BaseThirdPartyDummyService> logger)
        {
            _configuration = configuration;
            baseURL = _configuration.GetSection("ThirdPartyMerchant:LoyaltyDummy:BaseURL").Value;
            tenantId = Convert.ToInt32(_configuration.GetSection("ThirdPartyMerchant:LoyaltyDummy:TenantId").Value);
            defaultUserName = _configuration.GetSection("ThirdPartyMerchant:LoyaltyDummy:Username").Value;
            defaultPassowrd = _configuration.GetSection("ThirdPartyMerchant:LoyaltyDummy:Password").Value;
            _cache = cache;
            _logger = logger;
        }

        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetLoyaltyAsync<T>(string apiURL, object query = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };

            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            var token = GetAccessToken();
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            _logger.LogInformation("Start call dummy exchange request data: " + JsonConvert.SerializeObject(requestURL));
            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();
            _logger.LogInformation("End call dummy exchange response data: " + JsonConvert.SerializeObject(rawData));
            //Recall API when Unauthorized
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                string accessToken = GetAccessToken(true);
                req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var reqClone = CloneHttpRequest(req);

                response = await _client.SendAsync(reqClone);
                rawData = await response.Content.ReadAsStringAsync();
            }
            //End Recall API when Unauthorized

            if (response.IsSuccessStatusCode == false)
            {
                var ex = new LoyaltyException();
                ex.Data.Add("ErrorData", rawData);
                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    ex.Data.Add("StatusCode", 400);
                }
                else
                {
                    ex.Data.Add("StatusCode", 500);
                }
                throw ex;
            }

            response.EnsureSuccessStatusCode();


            // Convert response to result object which is a instance of 'T'.
            var result = JsonConvert.DeserializeObject<T>(rawData);
            return result;
        }

        /// <summary>
        /// Convert a object to query string format.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string GetQueryString(object obj)
        {
            var properties = from p in obj.GetType().GetProperties()
                             where p.GetValue(obj, null) != null
                             select p.Name + "=" + HttpUtility.UrlEncode(p.GetValue(obj, null).ToString());

            return string.Join("&", properties.ToArray());
        }

        public async Task<T> PostLoyaltyAsync<T>(string apiURL, object body = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            _logger.LogInformation("Start call dummy exchange request data: " + JsonConvert.SerializeObject(body));
            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();
            _logger.LogInformation("End call dummy exchange response data: " + JsonConvert.SerializeObject(rawData));
            //Recall API when Unauthorized
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                string accessToken = GetAccessToken(true);
                req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var reqClone = CloneHttpRequest(req);

                response = await _client.SendAsync(reqClone);
                rawData = await response.Content.ReadAsStringAsync();
            }
            //End Recall API when Unauthorized

            if (response.IsSuccessStatusCode == false)
            {
                var ex = new LoyaltyException();
                ex.Data.Add("ErrorData", rawData);
                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    ex.Data.Add("StatusCode", 400);
                }
                else
                {
                    ex.Data.Add("StatusCode", 500);
                }
                throw ex;
            }

            response.EnsureSuccessStatusCode();

            // Get respone result.
            var result = JsonConvert.DeserializeObject<T>(rawData);

            return result;
        }
        public async Task<T> PutLoyaltyAsync<T>(string apiURL, object body = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Put
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            _logger.LogInformation("Start call dummy exchange request data: " + JsonConvert.SerializeObject(body));
            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();
            _logger.LogInformation("End call dummy exchange response data: " + JsonConvert.SerializeObject(rawData));
            //Recall API when Unauthorized
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                string accessToken = GetAccessToken(true);
                req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var reqClone = CloneHttpRequest(req);

                response = await _client.SendAsync(reqClone);
                rawData = await response.Content.ReadAsStringAsync();
            }
            //End Recall API when Unauthorized

            if (response.IsSuccessStatusCode == false)
            {
                var ex = new LoyaltyException();
                ex.Data.Add("ErrorData", rawData);
                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    ex.Data.Add("StatusCode", 400);
                }
                else
                {
                    ex.Data.Add("StatusCode", 500);
                }
                throw ex;
            }

            response.EnsureSuccessStatusCode();

            // Get respone result.
            var result = JsonConvert.DeserializeObject<T>(rawData);

            return result;
        }


        /// <summary>
        /// Get a new accessToken form Loyalty.
        /// </summary>
        /// <returns></returns>
        private string GetAccessToken(bool mustResetCache = false)
        {
            var token = _cache.GetString(CommonConstants.ACCESSS_TOKEN_DUMMY_LOYALTY_CACHE_KEY);

            // Request body
            if (string.IsNullOrEmpty(token) || mustResetCache)
            {
                return LoyaltyHelper.RenewAccessTokenDummyCacheValue(_cache, TimeSpan.FromHours(2), true);
            }

            return token;
        }

        //Clone a HttpRequest
        private HttpRequestMessage CloneHttpRequest(HttpRequestMessage req)
        {
            HttpRequestMessage clone = new HttpRequestMessage(req.Method, req.RequestUri);

            clone.Content = req.Content;
            clone.Version = req.Version;

            foreach (KeyValuePair<string, object> prop in req.Properties)
            {
                clone.Properties.Add(prop);
            }

            foreach (KeyValuePair<string, IEnumerable<string>> header in req.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            return clone;
        }
    }
}
