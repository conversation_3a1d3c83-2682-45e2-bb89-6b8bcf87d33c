﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Abstract;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Reward;
using FirebaseAdmin.Auth;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory;

namespace AKC.MobileAPI.Service
{
    /// <summary>
    /// This class using for manager user in firebase and loyalty system.
    /// </summary>
    public class UserService : RewardBaseService, IUserService
    {
        private readonly ILoyaltySecondaryCustomersService _loyaltySecondaryCustomersService;
        private readonly ILoyaltyNotificationHistoryService _notificationHistoryService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILogger<UserService> _logger;

        public UserService(
            IConfiguration configuration,
            ILoyaltySecondaryCustomersService loyaltySecondaryCustomersService,
            IRewardMemberService rewardMemberService,
            IExceptionReponseService exceptionResponseService,
            ILoyaltyNotificationHistoryService x,
            ILogger<UserService> logger)
            : base(configuration)
        {
            _loyaltySecondaryCustomersService = loyaltySecondaryCustomersService;
            _rewardMemberService = rewardMemberService;
            _exceptionReponseService = exceptionResponseService;
            _logger = logger;
            _notificationHistoryService = x;
        }

        public async Task<GetOTPOutput> GetOTP(ValidateUserInput input)
        {
            checkPhone(input.PhoneNumber);
            await _rewardMemberService.CheckDeletedMemberHistory(new RewardMemberCheckDeletedHistoryCodeRequest()
            {
                PhoneNumber = input.PhoneNumber,
            });
            await _rewardMemberService.SendOtp(new RewardMemberSendOtpInput()
            {
                PhoneNumber = input.PhoneNumber,
                SessionId = input.SessionId,
                SmsType = "RegisterMember",
            });
            return new GetOTPOutput()
            {
                result = 200,
                message = "Success",
                messageDetail = null,
            };
        }

        public async Task<ValidateUserOutput> IsExisted(ValidateGetUserInput input)
        {
            var result = new ValidateUserOutput() { PhoneNumber = input.PhoneNumber };
            try
            {
                var setttingGlobal = await _rewardMemberService.GetUsingFirebaseOtp();
                result.UsingFirebaseOtp = setttingGlobal.UsingFirebaseOtp;
            } catch (Exception ex)
            {
                _logger.LogError("Get setting config firebase fail", ex);
                result.UsingFirebaseOtp = true;
            }
            try
            {
                var auth = FirebaseAuth.DefaultInstance;
                var phone = input.PhoneNumber;
                var userRecord = await auth.GetUserByPhoneNumberAsync(phone);
                result.IsExisted = true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Get firebase error from IsExisted phone number", ex);
                result.IsExisted = false;
                result.HasPinCode = false;
                result.IsLocked = false;
            }

            if (!result.IsExisted)
            {
                await _rewardMemberService.CheckDeletedMemberHistory(new RewardMemberCheckDeletedHistoryCodeRequest()
                    {
                        PhoneNumber = input.PhoneNumber,
                });
                return result;
            }

            try
            {
                var resultReward = await _rewardMemberService.HasPinCode(new RewardMemberHasPinCodeRequest()
                {
                    PhoneNumber = input.PhoneNumber,
                    DeviceId = input.DeviceId ?? "",
                });
                result.HasPinCode = resultReward.HasPinCode;
				result.IsLocked = resultReward.IsLocked;
                result.MemberCode = resultReward.MemberCode;
                result.WrongDeviceId = resultReward.WrongDeviceId;
            }
            catch (Exception ex)
            {
                _logger.LogError("Get member error from IsExisted phone number", ex);
                result.HasPinCode = false;
                result.IsLocked = false;
            }

            return result;
        }

        public async Task<VerifyOTPOutput> VerifyOTPAndLogin(SendOTPUserInput input)
        {
            checkPhone(input.PhoneNumber);
            await _rewardMemberService.VerityOtp(new RewardMemberVerifyOtpInput()
            {
                OtpCode = input.OtpCode,
                PhoneNumber = input.PhoneNumber,
                SessionId = input.SessionId,
                SmsType = "RegisterMember",
            });
            var item = new VerifyOTPItemOutput()
            {
                Status = StatusReturnConst.IncorrectPinCode,
                CustomToken = null,
                Message = "Incorrect OTP Code"
            };
            try
            {
                var auth = FirebaseAuth.DefaultInstance;
                var user = await auth.GetUserByPhoneNumberAsync(input.PhoneNumber);
                _logger.LogInformation(" >> GetUserByPhoneNumberAsync success >> " + user?.Uid);
                await auth.RevokeRefreshTokensAsync(user.Uid);
                var customToken = await auth.CreateCustomTokenAsync(user.Uid);
                var resultReward = await _rewardMemberService.getMemberByIdCardOrPhoneNumber(new GetMemberByIdCardOrPhoneNumberInput()
                {
                    PhoneNumber = input.PhoneNumber
                });
                item.CustomToken = customToken;
                item.MemberCode = resultReward.MemberNationalId;
                item.Message = "Success";
                _logger.LogInformation(" >> GetUserByPhoneNumberAsync success HERE  >> " + user?.Uid);
                // Send notification to other devices
                await _notificationHistoryService.SendNotificationAboutLogout(
                    new SendNotificationAboutLogoutInput()
                    {
                        MemberCode = resultReward.MemberNationalId,
                        ExcludeDeviceId = input.DeviceId,
                    });
                _logger.LogInformation(" >> GetUserByPhoneNumberAsync success - SENT LOG OUT MESSAGE >> " + user?.Uid);
            }
            catch (FirebaseAuthException ex)
            {
                _logger.LogError("FirebaseAuthException >> Get firebase error from phone number" + ex.Message + " - " + ex.StackTrace);
                if (ex.AuthErrorCode == AuthErrorCode.UserNotFound)
                {
                    getExceptionCustome("MemberNotExits", "Member does not exist");
                }
                getExceptionCustome("SystemError", "System error");
            }
            catch (Exception ex)
            {
                _logger.LogError("Exception >> Get firebase error from phone number" + ex.Message + " - " + ex.StackTrace);
                getExceptionCustome("SystemError", "System error");
            }

            return new VerifyOTPOutput()
            {
                Item = item,
                Message = "Success",
                Result = 200,
            };
        }

        public async Task<VerifyOTPOutput> VerifyOTPAndRegister(CreateUserMobileDTO input)
        {
            checkPhone(input.PhoneNumber);
            await _rewardMemberService.VerityOtp(new RewardMemberVerifyOtpInput()
            {
                OtpCode = input.OtpCode,
                PhoneNumber = input.PhoneNumber,
                SessionId = input.SessionId,
                SmsType = "RegisterMember",
            });
            var item = new VerifyOTPItemOutput()
            {
                Status = StatusReturnConst.IncorrectPinCode,
                CustomToken = null,
                Message = "Incorrect OTP Code"
            };

            var auth = FirebaseAuth.DefaultInstance;
            UserRecord user = null;
            try
            {
                user = await auth.CreateUserAsync(new UserRecordArgs()
                {
                    PhoneNumber = input.PhoneNumber
                });
            }
            catch (FirebaseAuthException ex)
            {
                _logger.LogError("Get firebase error from phone number", ex);
                if (ex.AuthErrorCode == AuthErrorCode.PhoneNumberAlreadyExists)
                {
                    getExceptionCustome("DuplicatePhoneNumber", "Duplicate phone number request");
                }
                getExceptionCustome("SystemError", "System error");
            }
            catch (Exception ex)
            {
                _logger.LogError("Get firebase error from phone number", ex);
                getExceptionCustome("SystemError", "System error");
            }
            var hasCreateMember = true;
            var memberCode = "";
            var isDeleteUserFirebase = false;
            try
            {
                var resultcreatemember = new RewardMemberCreateOutput();
                var memberReward = await _rewardMemberService.VerifyProviderIdByPhoneNumber(
                     new VerifyProviderIdByPhoneNumberRequest()
                     {
                         ProviderName = "Firebase",
                         ProviderId = user.Uid,
                         PhoneNumber = input.PhoneNumber
                     }
                 );
                memberCode = memberReward.Items.MemberCode;
                // If cannot member at loyalty then create member, else create return member code
                if (!memberReward.Items.MemberExist)
                {
                    try
                    {
                        var res = await VerifyOrCreateMember(input, user.Uid);
                        memberCode = res.Result.MemberCode;
                        hasCreateMember = true;
                    }
                    catch (Exception exCreate)
                    {
                        await auth.DeleteUserAsync(user.Uid);
                        isDeleteUserFirebase = true;
                        var res = await _exceptionReponseService.GetExceptionRewardReponse(exCreate);
                        if (exCreate.GetType() == typeof(RewardException))
                        {
                            throw exCreate;
                        }
                        _logger.LogError("Fail to create member for reward", exCreate);
                        getExceptionCustome("SystemError", "System error");
                    }
                }
                else
                {
                    var updateProvider = new VerifyProviderIdByPhoneNumberRequest()
                    {
                        ProviderName = "Firebase",
                        ProviderId = user.Uid,
                        PhoneNumber = input.PhoneNumber
                    };
                    await _rewardMemberService.UpdateProvider(updateProvider);
                }
            }
            catch (Exception exCreate)
            {
                // Nếu chưa xóa user firebase ở catch đầu tiên thì mới xóa
                 if (!isDeleteUserFirebase)
                 {
                    await auth.DeleteUserAsync(user.Uid);
                 }
                if (exCreate.GetType() == typeof(RewardException))
                {
                    throw exCreate;
                }
                _logger.LogError("Fail to create member for reward", exCreate);
                getExceptionCustome("SystemError", "System error");
            }

            var customToken = await auth.CreateCustomTokenAsync(user.Uid);
            item.CustomToken = customToken;
            item.MemberCode = memberCode;
            item.Message = "Success";

            if (hasCreateMember && !string.IsNullOrWhiteSpace(input.ReferralCode))
            {
                try
                {
                    await _loyaltySecondaryCustomersService.VerifyReferralCode(new LoyaltyVerifyReferralCodeInput()
                    {
                        NationalId = user.Uid,
                        ReferralCode = input.ReferralCode,
                        DistributionChannelList = null,
                        ReferenceAmount = 0
                    });
                } catch  {}
            }

            return new VerifyOTPOutput()
            {
                Item = item,
                Message = "Success",
                Result = 200,
            };
        }

        public async Task<RewardMemberVerifyOrCreateOutput> VerifyOrCreateMember(CreateUserMobileDTO input, string firebaseId)
        {
            string refCode = string.Empty;
            bool isContinue = true;
            do
            {
                refCode = await GenerateReferralCode();
                var checkReferrCode = await _loyaltySecondaryCustomersService.CheckRefCode(new CheckRefCodeInput { RefCode = refCode });
                if(!checkReferrCode.Result.isExit)
                {
                    isContinue = false;
                }    

            } while (isContinue);
            
            var createMemberDto = new RewardMemberVerifyOrCreateDto()
            {
                NationalId = firebaseId,
                IdCard = "",
                PartnerPhoneNumber = null,
                Type = "Member",
                Phone = input.PhoneNumber,
                Gender = !(new List<string> { "M", "F", "O" }).Contains(input.Gender) ? "O" : input.Gender,
                Status = "A",
                RankTypeCode = "Customer",
                IsDeleted = false,
                Address = input.Address,
                Dob = null, // Fix bug LA-665: Set default DOB is null
                Name = input.Name,
                Email = "",
                PointUsingOrdinary = "",
                HashAddress = "",
                RegionCode = "",
                FullRegionCode = "",
                MemberTypeCode = "",
                FullMemberTypeCode = "",
                ChannelType = "",
                FullChannelTypeCode = "",
                StandardMemberCode = firebaseId,
                ReferralCode = refCode,
                Avatar = "",
                FirebaseId = firebaseId,
                ReferenceId = refCode,
                RegisterType = "PhoneNumber",
            };

            var res = await PostRewardAsync<RewardMemberVerifyOrCreateOutputDto>(RewardApiUrl.MEMBER_VERIFY_OR_CREATE, createMemberDto);
            return new RewardMemberVerifyOrCreateOutput()
            {
                Messages = "Success",
                Result = new RewardMemberVerifyOrCreateOutputItem()
                {
                    MemberCode = res.Result.NationalId,
                    PhoneNumber = res.Result.Phone,
                },
                Status = 200,
            };
        }

        private async Task<string> GenerateReferralCode()
        {
            int sumNumberCharConfig = Convert.ToInt32(_configuration.GetSection("SumNumerChar").Value);
            string characters = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            Random random = new Random();
            char[] referralCode = new char[sumNumberCharConfig];

            for (int i = 0; i < referralCode.Length; i++)
            {
                referralCode[i] = characters[random.Next(characters.Length)];
            }

            return new string(referralCode);
        }


        private string GenReferralCode(string phoneNumber)
        {
            if (!string.IsNullOrEmpty(phoneNumber))
            {
                return phoneNumber.Replace("+84", "0");
            }
            return null;
        }

        public void checkPhone(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                getExceptionCustome("CannotEmptyPhoneNumber", "Cannot empty phone number");
            }
            if (!phoneNumber.StartsWith("+84"))
            {
                getExceptionCustome("InvalidFormatPhone", "Invalid format phone");
            }
            var temp = phoneNumber.Replace("+", "");
            var regOnlyNumberChar = new Regex(@"^[0-9]+$");
            if (!regOnlyNumberChar.IsMatch(temp))
            {
                getExceptionCustome("InvalidFormatPhone", "Invalid format phone");
            }
            if (temp.Length <= 10 || temp.Length >= 12)
            {
                getExceptionCustome("InvalidFormatPhone", "Invalid format phone");
            }
        }

        public void getExceptionCustome(string errorCode, string message)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = message,
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }
    }
}
