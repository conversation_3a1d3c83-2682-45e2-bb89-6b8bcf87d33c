﻿using AKC.MobileAPI.DTO.Loyalty.AuditLog;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions.ThirdParty;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Dynamic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using AKC.MobileAPI.Service.Helpers;

namespace AKC.MobileAPI.Service.ThirdParty.Base
{
    public class BaseThirdPartyAppotaService
    {
        protected readonly HttpClient _client;
        protected readonly IConfiguration _configuration;
        protected readonly string baseURL;
        protected readonly string userName;
        protected readonly string password;
        protected readonly string apiKey;
        protected readonly string SecretKeyForSignature;
        protected readonly string LinkIdCodeOnAppota;
        protected readonly string IDNApp;
        protected readonly ILogger _logger;
        protected int tenantId;
        private readonly ILoyaltyAuditLogService _loyaltyAuditLogService;

        public BaseThirdPartyAppotaService(
            IConfiguration configuration,
            ILogger<BaseThirdPartyAppotaService> logger,
            ILoyaltyAuditLogService loyaltyAuditLogService)
        {
            _configuration = configuration;
            baseURL = _configuration.GetSection("ThirdPartyMerchant:Appota:BaseURL").Value;
            userName = _configuration.GetSection("ThirdPartyMerchant:Appota:UserName").Value;
            password = _configuration.GetSection("ThirdPartyMerchant:Appota:Password").Value;
            apiKey = _configuration.GetSection("ThirdPartyMerchant:Appota:ApiKey").Value;
            LinkIdCodeOnAppota = _configuration.GetSection("ThirdPartyMerchant:Appota:LinkIdCodeOnAppota").Value;
            SecretKeyForSignature = _configuration.GetSection("ThirdPartyMerchant:Appota:SecretKeyForSignature").Value;
            HttpClientHandler clientHandler = new HttpClientHandler();
            clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) =>
            {
                return true;
            };
            _client = new HttpClient(clientHandler);
            _logger = logger;
            tenantId = Convert.ToInt32(_configuration.GetSection("Loyalty:TenantId").Value);
            _loyaltyAuditLogService = loyaltyAuditLogService;
        }

        public async Task<T> GetMemberInfor<T>(string url, string accessToken, HttpContext request = null, string memberCode = "")
        {
            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };
            var xRequestId = GenOTPSession("APPOTA");
            var Client_Request_Address = request.Request.Headers.ContainsKey("Client-Request-Address")
                ? request?.Request.Headers["Client-Request-Address"].ToString()
                : request?.Connection.RemoteIpAddress.ToString();
            req.RequestUri = new Uri(url + "?token=" + accessToken);
            var flagWritelog = false;
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                OperationLog(url, xRequestId, null, rawData, response, DateTime.UtcNow, DateTime.UtcNow,
                    Client_Request_Address, memberCode);
                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new ThirdPartyAppotaException();
                    ex.Data.Add("ErrorData", rawData);
                    ex.Data.Add("ApiGroup", "1");
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }

                    flagWritelog = true;
                    throw ex;
                }
                response.EnsureSuccessStatusCode();
                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (Exception ex)
            {
                if (!flagWritelog)
                {
                    dynamic objResponse = new ExpandoObject();
                    objResponse.IsSuccessStatusCode = false;
                    objResponse.StatusCode = 500;
                    objResponse.ReasonPhrase = "Internal Server Error";
                    OperationLog(url, xRequestId, null, JsonConvert.SerializeObject(ex), objResponse,
                        DateTime.UtcNow, DateTime.UtcNow, Client_Request_Address, memberCode);
                }

                throw ex;
            }
        }
        
        public async Task<T> GetAccessFromRefresh<T>(string apiFullURL, HttpContext request = null, string memberCode = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            var requestBody = JsonConvert.SerializeObject(new object(), new JsonSerializerSettings 
            { 
                ContractResolver = new CamelCasePropertyNamesContractResolver() 
            });

            req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            req.RequestUri = new Uri(apiFullURL);
            var startDate = DateTime.UtcNow;
            //var IP = request?.Connection.RemoteIpAddress.ToString();
            var Client_Request_Address = request.Request.Headers.ContainsKey("Client-Request-Address")
                ? request?.Request.Headers["Client-Request-Address"].ToString()
                : request?.Connection.RemoteIpAddress.ToString();

            // Add get dest IP from partner
            try
            {
                Uri myUri = new Uri(baseURL);
                var ipHost = Dns.GetHostAddresses(myUri.Host);
                if (ipHost != null && ipHost.Length > 0)
                {
                    Client_Request_Address = ipHost[0].MapToIPv4().ToString();
                }
            }
            catch
            {
            }
            // Add get dest IP from partner

            var flagWritelog = false;
            var xRequestId = GenOTPSession("APPOTA");
            try
            {
                var response = await _client.SendAsync(req);
                var endDate = DateTime.UtcNow;
                var rawData = await response.Content.ReadAsStringAsync();
                
                OperationLog(apiFullURL, xRequestId, new object(), rawData, response, startDate, endDate,
                    Client_Request_Address, memberCode);
                if (response.IsSuccessStatusCode == false)
                {
                    if (response.StatusCode == HttpStatusCode.Unauthorized)
                    {
                        _logger.LogInformation($" >> GetAccessFromRefresh >> {memberCode} >> Error 401: >> {rawData}");
                        throw new UnauthorizedAccessException();
                    }
                    var ex = new ThirdPartyAppotaException();
                    ex.Data.Add("ApiGroup", "1");
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }

                    flagWritelog = true;
                    throw ex;
                }

                response.EnsureSuccessStatusCode();
                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (UnauthorizedAccessException ex)
            {
                if (!flagWritelog)
                {
                    dynamic objResponse = new ExpandoObject();
                    objResponse.IsSuccessStatusCode = false;
                    objResponse.StatusCode = 401;
                    objResponse.ReasonPhrase = "UnauthorizedAccess";
                    OperationLog(apiFullURL, xRequestId, new object(), JsonConvert.SerializeObject(ex), objResponse,
                        startDate, DateTime.UtcNow, Client_Request_Address, memberCode);
                }

                throw ex;
            }
            catch (Exception ex)
            {
                if (!flagWritelog)
                {
                    dynamic objResponse = new ExpandoObject();
                    objResponse.IsSuccessStatusCode = false;
                    objResponse.StatusCode = 500;
                    objResponse.ReasonPhrase = "Internal Server Error";
                    OperationLog(apiFullURL, xRequestId, new object(), JsonConvert.SerializeObject(ex), objResponse,
                        startDate, DateTime.UtcNow, Client_Request_Address, memberCode);
                }

                throw ex;
            }
        }

        public async Task<T> SendRevertPoint_UNSED<T>(string apiFullURL, object body = null, HttpContext request = null, string memberCode = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings 
                { 
                    ContractResolver = new CamelCasePropertyNamesContractResolver() 
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            var authTokenAppotaPay =
                "Bearer " + AppotaJwtUtils.Get_X_APPOTAPAY_AUTH(apiKey, SecretKeyForSignature, LinkIdCodeOnAppota, _logger);
            req.Headers.Add("X-APPOTAPAY-AUTH",
                authTokenAppotaPay);
            var xRequestId = GenOTPSession("APPOTA");
            req.RequestUri = new Uri(apiFullURL);
            var startDate = DateTime.UtcNow;
            //var IP = request?.Connection.RemoteIpAddress.ToString();
            var Client_Request_Address = request.Request.Headers.ContainsKey("Client-Request-Address")
                ? request?.Request.Headers["Client-Request-Address"].ToString()
                : request?.Connection.RemoteIpAddress.ToString();
            try
            {
                Uri myUri = new Uri(baseURL);
                var ipHost = Dns.GetHostAddresses(myUri.Host);
                if (ipHost != null && ipHost.Length > 0)
                {
                    Client_Request_Address = ipHost[0].MapToIPv4().ToString();
                }
            }
            catch
            {
            }
            var flagWritelog = false;
            try
            {
                var response = await _client.SendAsync(req);
                var endDate = DateTime.UtcNow;
                var rawData = await response.Content.ReadAsStringAsync();
                dynamic obj = body;
                OperationLog(apiFullURL, xRequestId, body, rawData, response, startDate, endDate,
                    Client_Request_Address, memberCode);
                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new ThirdPartyAppotaException();
                    ex.Data.Add("ApiGroup", "1");
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }

                    flagWritelog = true;
                    throw ex;
                }
                response.EnsureSuccessStatusCode();
                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (Exception ex)
            {
                if (!flagWritelog)
                {
                    dynamic objResponse = new ExpandoObject();
                    objResponse.IsSuccessStatusCode = false;
                    objResponse.StatusCode = 500;
                    objResponse.ReasonPhrase = "Internal Server Error";
                    OperationLog(apiFullURL, xRequestId, body, JsonConvert.SerializeObject(ex), objResponse,
                        startDate, DateTime.UtcNow, Client_Request_Address, memberCode);
                }

                throw ex;
            }
        }
        public async Task<T> SendPostExchangeAsync<T>(string apiFullURL, string accessToken, object body = null,
            HttpContext request = null, string memberCode = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings 
                { 
                    ContractResolver = new CamelCasePropertyNamesContractResolver() 
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            var authTokenAppotaPay =
                "Bearer " + AppotaJwtUtils.Get_X_APPOTAPAY_AUTH(apiKey, SecretKeyForSignature, LinkIdCodeOnAppota, _logger);
            req.Headers.Add("X-APPOTAPAY-AUTH",
                authTokenAppotaPay);
            var xRequestId = GenOTPSession("APPOTA");
            if (accessToken != null && !accessToken.StartsWith("Bearer "))
            {
                accessToken = "Bearer " + accessToken;
            }

            req.Headers.Add("Authorization", accessToken);
            req.RequestUri = new Uri(apiFullURL);
            var startDate = DateTime.UtcNow;
            //var IP = request?.Connection.RemoteIpAddress.ToString();
            var Client_Request_Address = request.Request.Headers.ContainsKey("Client-Request-Address")
                ? request?.Request.Headers["Client-Request-Address"].ToString()
                : request?.Connection.RemoteIpAddress.ToString();

            // Add get dest IP from partner
            try
            {
                Uri myUri = new Uri(baseURL);
                var ipHost = Dns.GetHostAddresses(myUri.Host);
                if (ipHost != null && ipHost.Length > 0)
                {
                    Client_Request_Address = ipHost[0].MapToIPv4().ToString();
                }
            }
            catch
            {
            }
            // Add get dest IP from partner

            var flagWritelog = false;
            try
            {
                var response = await _client.SendAsync(req);
                var endDate = DateTime.UtcNow;
                var rawData = await response.Content.ReadAsStringAsync();
                dynamic obj = body;
                OperationLog(apiFullURL, xRequestId, body, rawData, response, startDate, endDate,
                    Client_Request_Address, memberCode);
                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new ThirdPartyAppotaException();
                    ex.Data.Add("ErrorData", rawData);
                    ex.Data.Add("ApiGroup", "2");
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }

                    flagWritelog = true;
                    throw ex;
                }
                response.EnsureSuccessStatusCode();
                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (Exception ex)
            {
                if (!flagWritelog)
                {
                    dynamic objResponse = new ExpandoObject();
                    objResponse.IsSuccessStatusCode = false;
                    objResponse.StatusCode = 500;
                    objResponse.ReasonPhrase = "Internal Server Error";
                    OperationLog(apiFullURL, xRequestId, body, JsonConvert.SerializeObject(ex), objResponse,
                        startDate, DateTime.UtcNow, Client_Request_Address, memberCode);
                }

                throw ex;
            }
        }

        public string OperationLog(string operationName, string xRequestId, object body, string rawData,
            dynamic response, DateTime startDate, DateTime endDate, string ip, string memberCode)
        {
            try
            {
                var listOperationLogDto = new CreateOperationLogDto();
                var operationLog = new OperationLogDto
                {
                    TenantId = tenantId,
                    MemberId = memberCode,
                    Code = "VPBank_" + DateTime.Now.Ticks.ToString() + new Random().Next(4),
                    ReferenceKey = xRequestId,
                    PartnerCode = "VPBank",
                    DestIP = ip,
                    StartDate = startDate,
                    RequestMsg = hiddenTokenKeyInLogText(JsonConvert.SerializeObject(body)),
                    ServiceName = operationName,
                    EndDate = endDate,
                    Status = response.IsSuccessStatusCode,
                    ErrorCode = (int)response.StatusCode + " " + response.ReasonPhrase,
                    ResponseMsg = hiddenTokenKeyInLogText(rawData)
                };
                _logger.LogInformation("Dest IP" + operationLog.DestIP);
                listOperationLogDto.OperationLogs.Add(operationLog);

                var result = _loyaltyAuditLogService.CreateOperationLog(listOperationLogDto);
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Operation exception: " + ex);
            }

            return "";
        }

        public string GenOTPSession(string prefix = "")
        {
            return prefix + DateTime.Now.ToString("HHmmss") + DateTime.Now.Ticks.ToString();
        }

        private string getAuthorization()
        {
            String encoded =
                Convert.ToBase64String(Encoding.GetEncoding("ISO-8859-1").GetBytes(userName + ":" + password));
            return encoded;
        }

        private string getLogText(string data)
        {
            if (string.IsNullOrWhiteSpace(data))
            {
                return "";
            }

            var result = JObject.Parse(data);
            if (result.ContainsKey("accessToken"))
            {
                result.Remove("accessToken");
            }

            if (result.ContainsKey("refreshToken"))
            {
                result.Remove("refreshToken");
            }

            return JsonConvert.SerializeObject(result);
        }

        private string hiddenTokenKeyInLogText(string data)
        {
            if (string.IsNullOrWhiteSpace(data))
            {
                return "";
            }

            var result = JObject.Parse(data);
            if (result.ContainsKey("accessToken"))
            {
                result["accessToken"] = "******";
            }

            if (result.ContainsKey("refreshToken"))
            {
                result["refreshToken"] = "******";
            }

            return JsonConvert.SerializeObject(result);
        }
    }
}