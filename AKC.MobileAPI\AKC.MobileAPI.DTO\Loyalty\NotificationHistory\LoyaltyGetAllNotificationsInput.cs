﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.NotificationHistory
{
    public class LoyaltyGetAllNotificationsInput
    {
        public string MemberCode { get; set; }
        public string Language { get; set; }
        
        public string NotificationCategory { get; set; }

        [Range(0, int.MaxValue)]
        public int MaxResultCount { get; set; }

        [Range(0, int.MaxValue)]
        public int SkipCount { get; set; }
    }
}
