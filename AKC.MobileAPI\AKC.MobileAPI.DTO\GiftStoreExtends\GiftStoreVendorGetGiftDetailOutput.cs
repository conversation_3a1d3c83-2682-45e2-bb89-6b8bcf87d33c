﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.GiftStoreExtends
{
    public class GiftStoreVendorGetGiftDetailOutput
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public decimal Price { get; set; }
        public decimal Quantity { get; set; }
        public string ImageLink { get; set; }
        public string Status { get; set; }
        public int BrandId { get; set; }
        public string BrandCode { get; set; }
        public string BrandName { get; set; }
        public int CategoryId { get; set; }
        public string CategoryCode { get; set; }
        public string CategoryName { get; set; }
        public List<string> ImagesLink { get; set; }
        public string GiftType { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public DateTime EffectiveTo { get; set; }
        public DateTime LastUpdateChanges { get; set; }
        public string ShortDescription { get; set; }
        public string Description { get; set; }
    }
}
