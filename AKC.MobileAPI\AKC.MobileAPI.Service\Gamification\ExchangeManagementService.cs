﻿using AKC.MobileAPI.DTO.Gamification;
using AKC.MobileAPI.DTO.Gamification.Exchange;
using AKC.MobileAPI.Service.Abstract.Gamification;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Gamification
{
    public class ExchangeManagementService : ExchangeManagementBaseService, IExchangeManagementService
    {
        public ExchangeManagementService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<CreateExchangeTransactionOutput> CreateExchangeTransaction(CreateExchangeTransactionInput input)
        {
            return await PostGamificationManagementAsync<CreateExchangeTransactionOutput>(GamificationApiUrl.CREATE_EXCHANGE_TRANSACTION, input);
        }

        public async Task<ExchangeTransactionHistoryOutput> ExchangeTransactionHistory(ExchangeTransactionHistoryInput input)
        {
            return await GetGamificationExchangeManagementAsync<ExchangeTransactionHistoryOutput>(GamificationApiUrl.EXCHANGE_TRANSACTION_HISTORY, input);
        }
        
    }
}
