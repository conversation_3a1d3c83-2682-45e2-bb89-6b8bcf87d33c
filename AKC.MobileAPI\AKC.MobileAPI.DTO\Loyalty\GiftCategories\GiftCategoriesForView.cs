﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftCategories
{
    public class GiftListCategoriesInTwoRowsOutputWrapper
    {
        public GiftListCategoriesInTwoRowsOutput Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }
    public class GiftListCategoriesInTwoRowsOutput
    {
        public List<GiftCategoriesForMB> Row1 { get; set; }
        public List<GiftCategoriesForMB> Row2 { get; set; }
        
    }

    public class GiftListCategoriesInTwoRowsInput
    {
        public string MemberCode { get; set; }
    }
    public class GiftCategoriesForView
    {
        public ListResultGiftCategoriesForMB Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class ListResultGiftCategoriesForMB
    {
        public int TotalCount { get; set; }

        public List<GiftCategoriesForMB> Items { get; set; }
    }

    public class GiftCategoriesForMB
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string FullLink { get; set; }
        public string CategoryTypeCode { get; set; }
    }
}
