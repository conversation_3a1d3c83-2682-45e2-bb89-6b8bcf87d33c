using System.Threading.Tasks;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyThemeService : BaseLoyaltyService, ILoyaltyThemeService
    {
        public LoyaltyThemeService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }
        public async Task<ThemeSettingsInfoDtoWrapper> GetCurrentTheme(GetCurrentThemeInput input)
        {
            return await GetLoyaltyAsync<ThemeSettingsInfoDtoWrapper>(LoyaltyApiUrl.GETCURRENTTHEME, null);
        }
    }
}