﻿using AKC.MobileAPI.DTO.Loyalty.Challenge;
using AKC.MobileAPI.DTO.User;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyChallengeService
    {
        Task<GetAllQuestByGroupDto> GetListByGroup(GetAllQuestByGroupInput input);

        Task<GetQuestDetailOutputDto> GetDetail(GetQuestDetailInputDto input);

        Task<GetListQuestByActivityOutputDto> GetListQuestByActivity(GetListQuestByActivityInputDto input);

        Task<JoinQuestOutputDto> Join(JoinQuestInputDto input);

        Task<ViewActualQuestOutputDto> ViewActual(ViewActualQuestInputDto input);

        Task<ClaimOutputDto> Claim(ClaimInputDto input);

        Task<GetAllQuestOutputDto> GetAll(GetAllQuestInputDto input);

        Task<GetListByMemberCodeOutputDto> GetListByMemberCode(GetListByMemberCodeInputDto input);
        #region Loyalty challenge quest stage
        Task<GetAllQuestStageOutputDto> GetAllQuestStage(GetAllQuestStageInputDto input);

        Task<JoinQuestStageOutputDto> JoinQuestStage(JoinQuestStageInputDto input);

        //Task<ClaimQuestStageOutputDto> ClaimQuestStage(ClaimQuestStageInputDto input);

        //Task<GetListQuestStageJoinedByMemberOutPutDto> GetListQuestStageJoinedByMember(GetListQuestStageJoinedByMemberInputDto input);

        //Task<GetListQuestStageByMemberCodeOutputDto> GetListQuestStageByMemberCode(GetListQuestStageByMemberCodeInputDto input);

        Task<GetQuestStageDetailOutputDto> GetQuestStageDetail(GetQuestStageDetailInputDto input);

        Task<QuestStageClaimOutputDto> ClaimQuestStageOrLevel(QuestStageClaimInputDto input);

        Task<GetListQuestStageByMemberCodeOutputDto> GetListQuestAndQuestStageByMemberCode(GetListQuestStageByMemberCodeInputDto input);
        #endregion

    }
}
