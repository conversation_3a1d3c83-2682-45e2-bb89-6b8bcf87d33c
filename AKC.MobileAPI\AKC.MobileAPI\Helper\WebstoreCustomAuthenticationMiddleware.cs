using System;
using System.Threading.Tasks;
using AKC.MobileAPI.Service.Abstract.Reward;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Helper
{
    public class WebstoreCustomAuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly IDistributedCache _cache;

        public WebstoreCustomAuthenticationMiddleware(
            RequestDelegate next,
            IServiceScopeFactory y,
            IDistributedCache cache
        )
        {
            _next = next;
            _scopeFactory = y;
            _cache = cache;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Get the current route data
            var routeData = context.GetRouteData();
            if (routeData == null || !context.Request.Path.StartsWithSegments("/api/webstore"))
            {
                // Các API của ednpoindt khác Webstore sẽ không bị ảnh hưởng
                await _next(context);
                return;
            }

            // Check if the current action requires authentication
            if (!RequiresAuthentication(context))
            {
                // No authentication required, proceed
                await _next(context);
                return;
            }

            if (!context.Request.Headers.TryGetValue("Authorization", out var authHeader))
            {
                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";
    
                var errorResponse = new
                {
                    code = "HTTP401",
                    message = "Authorization header missing"
                };
    
                await context.Response.WriteAsync(JsonConvert.SerializeObject(errorResponse));
                return;
            }

            string token = authHeader.ToString().Replace("Bearer ", "");
            if (!(await IsValidToken(token, context)))
            {
                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";
    
                var errorResponse = new
                {
                    code = "HTTP401",
                    message = "Invalid or expired token"
                };
    
                await context.Response.WriteAsync(JsonConvert.SerializeObject(errorResponse));
                return;
            }

            await _next(context);
        }

        private bool RequiresAuthentication(HttpContext context)
        {
            return !(context.Request.Path.StartsWithSegments("/api/webstore/public")
                || context.Request.Path.StartsWithSegments("/api/webstore/auth/sme-login")
                || context.Request.Path.StartsWithSegments("/api/webstore/auth/check-sme-login-info")
                || context.Request.Path.StartsWithSegments("/api/webstore/auth/refresh-token")
                || context.Request.Path.StartsWithSegments("/api/webstore/auth/sme/request-otp-change-password")
                || context.Request.Path.StartsWithSegments("/api/webstore/auth/sme/verify-otp-change-password")
                || context.Request.Path.StartsWithSegments("/api/webstore/auth/sme/change-password-after-otp-verification")
                || context.Request.Path.StartsWithSegments("/api/webstore/auth/sme/send-otp-register")
                || context.Request.Path.StartsWithSegments("/api/webstore/auth/sme/validate-otp-register")
                || context.Request.Path.StartsWithSegments("/api/webstore/auth/sme/set-password-after-otp-of-register"));
        }

        private async Task<bool> IsValidToken(string token, HttpContext context)
        {
            // Nếu token null hoặc empty thì return false ngay, không cache
            if (string.IsNullOrWhiteSpace(token))
            {
                return false;
            }

            var cacheKey = "TOKEN_" + token;
            var cachedResult = await _cache.GetStringAsync(cacheKey);
            if (cachedResult != null)
            {
                if (cachedResult == "YES")
                {
                    context.Items["MemberCode"] = await _cache.GetStringAsync("MemberCode_" + token);
                    context.Items["MemberType"] = await _cache.GetStringAsync("MemberType_" + token);
                    return true;
                }

                if (cachedResult == "NO")
                {
                    return false;
                }
            }
            using (var scope = context.RequestServices.CreateScope())
            {
                var rewardMemberService = scope.ServiceProvider.GetRequiredService<IRewardMemberService>();
                var ret = false;
                try
                {
                    var tokenData = await rewardMemberService.WebstoreValidateToken(token);
                    if (!tokenData.IsValid)
                    {
                        // Đã xác định rõ là invalid tọken thì cache lại NO để nó cố retry thì báo lỗi ngay ko cần spam lên Operator.
                        ret = false;
                    }
                    else
                    {
                        context.Items["MemberCode"] = tokenData.MemberCode;
                        context.Items["MemberType"] = tokenData.MemberType;
                        await _cache.SetStringAsync("MemberCode_" + token, tokenData.MemberCode, new DistributedCacheEntryOptions
                        {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1)
                        });
                        await _cache.SetStringAsync("MemberType_" + token, tokenData.MemberType, new DistributedCacheEntryOptions
                        {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1)
                        });
                        ret = true;
                    }
                }
                catch (Exception e)
                {
                    // Issue happened when call RewardnEtwork to Validate access token
                    // Dont write to cache
                    return false;
                }
                var cacheVal = ret ? "YES" : "NO";
                await _cache.SetStringAsync(cacheKey, cacheVal, new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1)
                });
                return ret;
            }
        }
    }
}
