﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.Challenge;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/Quest")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyChallengeController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyChallengeService _challengeService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        public LoyaltyChallengeController(
            ILogger<LoyaltyChallengeController> logger,
            ILoyaltyChallengeService challengeService,
            IExceptionReponseService exceptionReponseService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _challengeService = challengeService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
        }

        [HttpGet]
        [Route("GetAll")]
        public async Task<ActionResult<GetAllQuestOutputDto>> GetAll([FromQuery] GetAllQuestInputDto input)
        {
            try
            {
                var result = await _challengeService.GetAll(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Get all Error- " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetListByGroup")]
        public async Task<ActionResult<GetAllQuestByGroupDto>> GetListByGroup([FromQuery] GetAllQuestByGroupInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _challengeService.GetListByGroup(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Get List By Group Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetDetail")]
        public async Task<ActionResult<GetQuestDetailOutputDto>> GetDetail([FromQuery] GetQuestDetailInputDto input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _challengeService.GetDetail(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Get Detail Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetListQuestByActivity")]
        public async Task<ActionResult<GetListQuestByActivityOutputDto>> GetListQuestByActivity([FromQuery]GetListQuestByActivityInputDto input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _challengeService.GetListQuestByActivity(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Get List Quest By Activity Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("Join")]
        public async Task<ActionResult<JoinQuestOutputDto>> Join(JoinQuestInputDto input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _challengeService.Join(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Quest join Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("ViewActual")]
        public async Task<ActionResult<ViewActualQuestOutputDto>> ViewActual(ViewActualQuestInputDto input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _challengeService.ViewActual(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Quest ViewActual Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("Claim")]
        public async Task<ActionResult<ClaimOutputDto>> Claim(ClaimInputDto input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _challengeService.Claim(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Quest Claim Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetListByMemberCode")]
        public async Task<ActionResult<GetListByMemberCodeOutputDto>> GetListByMemberCode([FromQuery]GetListByMemberCodeInputDto input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _challengeService.GetListByMemberCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Quest get list by member code Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

    }
}
