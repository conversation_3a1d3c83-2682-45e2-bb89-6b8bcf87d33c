﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberGetReferenceDataConnectedOutput
    {
        public string Message { get; set; }
        public int Result { get; set; }
        public RewardMemberGetReferenceDataConnectedItem Item { get; set; }
    }

    public class RewardMemberGetReferenceDataConnectedItem
    {
        public string MemberCode { get; set; }
        public int MerchantId { get; set; }
        public decimal TokenBalance { get; set; }
        public decimal MerchantExchangeRate { get; set; }
        public decimal CurrencyExchangeRate { get; set; }
        public decimal BaseUnit { get; set; }
    }
}
