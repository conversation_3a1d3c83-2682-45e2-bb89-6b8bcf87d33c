﻿using System;
using System.Collections.Generic;


namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class GetAllQuestOutputDto
    {
        public ListResultGetAll Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }



    public class ListResultGetAll
    {
        public int TotalCount { get; set; }

        public List<ListGetAllItems> Items { get; set; }

    }

    public class ListGetAllItems
    {
        public QuestAllDto Quest { get; set; }

        public int QuestGroupId { get; set; }
    }

    public class QuestAllDto
    {
        public string Code { get; set; }

        public string Name { get; set; }
        public virtual string Description { get; set; }

        public virtual string LinkAvatar { get; set; }


        public string Status { get; set; }

        public DateTime FromDate { get; set; }

        public DateTime ToDate { get; set; }

        public string Tag { get; set; }

        public int? MissionCount { get; set; }

        public decimal Point { get; set; }
        public decimal Coin { get; set; }

        public int Id { get; set; }

    }

}
