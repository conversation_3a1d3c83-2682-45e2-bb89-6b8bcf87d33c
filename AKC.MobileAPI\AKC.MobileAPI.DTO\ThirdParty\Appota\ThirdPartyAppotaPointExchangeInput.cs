﻿using System.Text.Json.Serialization;

namespace AKC.MobileAPI.DTO.ThirdParty.Appota
{
    public class ThirdPartyAppotaPointExchangeInputWrapper
    {
        public ThirdPartyAppotaPointExchangeInput SendToPartner { get; set; }
        public string AccessToken { get; set; }
        public string IdNumber { get; set; }
        public int ExchangeAmount { get; set; }
    }
    public class ThirdPartyAppotaPointExchangeInput
    {
        [JsonPropertyName("order_id")] public string OrderId { get; set; }
        [JsonPropertyName("order_info")] public string OrderInfo { get; set; }
        [JsonPropertyName("extra_data")] public string ExtraData { get; set; }
        [JsonPropertyName("amount")] public long Amount { get; set; }
        [JsonPropertyName("redeem_apoint")] public long RedeemApoint { get; set; }
        [JsonPropertyName("signature")] public string Signature { get; set; }
    }

    public class ThirdPartyAppotaPointExchangeOutput
    {
        [J<PERSON><PERSON>ropertyName("errorCode")] public string ErrorCode { get; set; }
        [JsonPropertyName("message")] public string Message { get; set; }
        [JsonPropertyName("transaction")] public ThirdPartyAppotaPointExchangeOutputTrans Transaction { get; set; }
    }

    public class ThirdPartyAppotaPointExchangeOutputTrans
    {
        [JsonPropertyName("status")] public string Status { get; set; }
        [JsonPropertyName("amount")] public int Amount { get; set; }
        [JsonPropertyName("redeemApoint")] public int redeemApoint { get; set; }
        [JsonPropertyName("orderId")] public string OrderId { get; set; }
        [JsonPropertyName("appotapayTransId")] public string AppotapayTransId { get; set; }
        [JsonPropertyName("transactionTs")] public int TransactionTs { get; set; }
    }
}