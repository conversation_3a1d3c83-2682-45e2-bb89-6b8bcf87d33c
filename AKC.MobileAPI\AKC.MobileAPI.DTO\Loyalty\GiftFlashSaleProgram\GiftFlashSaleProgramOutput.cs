﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftFlashSaleProgram
{
    public class GiftFlashSaleProgramOutput
    {
        public GiftFlashSaleProgramResult result { get; set; }
        public object targetUrl { get; set; }
        public bool success { get; set; }
        public object error { get; set; }
        public bool unAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class GiftFlashSaleProgramResult
    {
        public int totalCount { get; set; }
        public List<GiftFlashSaleProgramResultItem> items { get; set; }
    }

    public class GiftFlashSaleProgramResultItem
    {
        // Id quà
        public int GiftId { get; set; }
        // Mã quà
        public string GiftCode { get; set; }
        // Link ảnh
        public string ImageLink { get; set; }
        // Tên quà
        public string GiftName { get; set; }
        // Số lượng còn lại
        public decimal? RemainingQuantity { get; set; }
        // Số lượng triển khai
        public decimal? Amount { get; set; }
        // Giá gốc
        public decimal? OriginalPrice { get; set; }
        // Tỉ lệ giảm
        public float? ReductionRate { get; set; }
        // Số lượng mua
        public decimal? UsedQuantity { get; set; }
        public decimal? SalePrice { get; set; }
        public bool WarningOutOfStock { get; set; } 
        // Số lượng quà tối đa
        public int MaxAmountRedeemed { get; set; }
        public DateTime? CreationTime { get; set; }
        public long? CreatorUserId { get; set; }
        public string CreatorUserName { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public long? LastModifierUserId { get; set; }
        public string LastModifierUserName { get; set; }
        public string Status { get; set; }
        public int? UsedQuantityFlashSale { get; set; }
        public int ReductionRateDisplay
        {
            get
            {
                if (ReductionRate.HasValue)
                {
                    if (ReductionRate > 0 && ReductionRate < 1)
                    {
                        return 1;
                    }
                    else if (ReductionRate > 99 && ReductionRate < 100)
                    {
                        return 99;
                    }
                    else
                    {
                        return (int)Math.Round((double)ReductionRate);
                    }
                }
                return 0;
            }
        }
        public int? RemainingQuantityFlashSale { get; set; }
        public string FullGiftCategoryCode { get; set; }
        public string GiftStatus { get; set; }
        public int? TotalWish { get; set; }
        public bool IsSoldOut { get; set; }
    }
}
