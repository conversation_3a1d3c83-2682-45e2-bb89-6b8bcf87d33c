﻿using AKC.MobileAPI.DTO.Webstore;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace AKC.MobileAPI.Service.Abstract
{
    public interface ISmeTransactionService
    {
        Task<WebStoreCreateRedeemGiftTransactionDto> CreateRedeemGiftTransaction(WebStoreCreateRedeemGiftTransactionInput input);

        Task<SmeRedeemGiftDto> VerifyRedeemGiftTransaction(VerifyRedeemGiftTransactionInput input, HttpContext context);

        Task<GetOTPOutput> ResendOtp(GetOTPInput input);
    }
}
