﻿using System;
using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.AirlineDto
{
    public class MileExchangeTransaction
    {
        public string MemberName { get; set; }
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
        public string TransactionCode { get; set; }
        public DateTime TransactionTime { get; set; }
        public decimal Miles { get; set; }
        public long Coins { get; set; }
        public string AirlineCode { get; set; }
        public string AirlineTransactionCode { get; set; }
        public string AirlineMemberCode { get; set; }
    }

    public class GetListMileExchangeTransactionRespone
    {
        public List<MileExchangeTransaction> items { get; set; }
        public int totalCount { get; set; }
    }
}
