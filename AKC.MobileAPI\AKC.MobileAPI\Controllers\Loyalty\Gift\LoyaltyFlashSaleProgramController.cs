using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;

namespace AKC.MobileAPI.Controllers.Loyalty.Gift
{
    [Route("api/FlashSaleProgram")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyFlashSaleProgramController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyFlashSaleProgramService _loyaltyFlashSaleProgramService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IDistributedCache _cache;
        public LoyaltyFlashSaleProgramController
        (
              ILogger<LoyaltyGiftInforController> logger,
              ILoyaltyFlashSaleProgramService loyaltyFlashSaleProgramService,
              IExceptionReponseService exceptionReponseService,
              ICommonHelperService commonHelperService,
              IDistributedCache cache
        )
        {
            _logger = logger;
            _loyaltyFlashSaleProgramService = loyaltyFlashSaleProgramService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
            _cache = cache;
        }

        [HttpGet]
        [Route("GetAll")]
        public async Task<ActionResult<LoyaltyGetAllForFlashSaleProgramOutPut>> GetAll([FromQuery] LoyaltyGetAllForFlashSaleProgramInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var CacheKey = "FlashSaleProgramController_GetAll_Dec26";
                var cachedData = await _cache.GetStringAsync(CacheKey);
                if (!string.IsNullOrEmpty(cachedData))
                {
                    var convertedObj = JsonConvert.DeserializeObject<LoyaltyGetAllForFlashSaleProgramOutPut>(cachedData);
                    _logger.LogInformation(">> FlashSaleProgramController_GetAll_Dec26 >> CACHE HIT");
                    return StatusCode(200, convertedObj);
                }
                var request = new MemberInfoRequest
                {
                    MemberCode = input.MemberCode
                };
                var result = _loyaltyFlashSaleProgramService.GetAll(request).Result;
                _logger.LogInformation(">> FlashSaleProgramController_GetAll_Dec26 >> CACHE MISS; GET FROM DB");
                await _cache.SetStringAsync(CacheKey, JsonConvert.SerializeObject(result),
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(5)));

                return StatusCode(200, result);
            }
            catch (Exception ex)
            {

                var res = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result;
                _logger.LogError(ex, "GetAll FlashSale Program Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllGiftCategoryForFlashSaleProgram")]
        public async Task<ActionResult<LoyaltyGiftCategoryGetAllOutput>> GetAllGiftCategoryForFlashSaleProgram([FromQuery] LoyaltyGetAllGiftCategoryForFlashSaleProgramInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var CacheKey = "GetAllGiftCategoryForFlashSaleProgram_" + input.FlashSaleProgramCode;
                var cachedData = await _cache.GetStringAsync(CacheKey);
                if (!string.IsNullOrEmpty(cachedData))
                {
                    var convertedObj = JsonConvert.DeserializeObject<LoyaltyGiftCategoryGetAllOutput>(cachedData);
                    _logger.LogInformation(">> GetAllGiftCategoryForFlashSaleProgram >> CACHE HIT");
                    return StatusCode(200, convertedObj);
                }
                
                var result = _loyaltyFlashSaleProgramService.GetAllGiftCategoryForFlashSaleProgram(input).Result;
                _logger.LogInformation(">> GetAllGiftCategoryForFlashSaleProgram >> CACHE MISS; GET FROM DB");
                await _cache.SetStringAsync(CacheKey, JsonConvert.SerializeObject(result),
                                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(5)));
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {

                var res = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result;
                _logger.LogError(ex, "GetAll GiftCategory for FlashSale Program Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }
    }
}
