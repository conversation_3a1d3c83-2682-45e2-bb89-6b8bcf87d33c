﻿using System;
using System.Collections.Generic;


namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{

    public class GetAllQuestByGroupDto
    {
        public ListResult Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class ListResult
    {
        public int TotalCount { get; set; }

        public List<ListItems> Items { get; set; }
    }

    public class ListItems
    {
        public QuestGroupType QuestGroupType { get; set; }
        public List<QuestListByGroupDto> QuestGroup { get; set; }
    }

    public enum QuestGroupType
    {
        List,
        Banner
    }

    public class QuestListByGroupDto
    {
        public string QuestGroupCode { get; set; }
        public IEnumerable<QuestDto> Quests { get; set; }
        public QuestGroupDto QuestGroupInfor { get; set; }
    }

    public class QuestDto
    {
        public DateTime LastModificationTime { get; set; }
        public string Code { get; set; }

        public string Name { get; set; }
        public virtual string Description { get; set; }

        public virtual string LinkAvatar { get; set; }

        public int? Ordinal { get; set; }

        public string Status { get; set; }

        public DateTime FromDate { get; set; }

        public DateTime ToDate { get; set; }

        public string Tag { get; set; }

        public string State { get; set; }
        public List<MissionDto> Missions { get; set; }
        public int? MissionCount { get; set; }
        public DateTime? Date { get; set; }
        public decimal Point { get; set; }
        public decimal Coin { get; set; }
        public decimal Actual { get; set; }
        public decimal Target { get; set; }
        public int Id { get; set; }
        public int? QuestBaselineId { get; set; }
        public DateTime CreationTime { get; set; }
    }
    
    public class QuestGroupDto
    {
        public int TenantId { get; set; }

        public string Code { get; set; }

        public string Name { get; set; }

        public int Ordinal { get; set; }

        public string Status { get; set; }

        public QuestGroupType Type { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public long? LastModifierUserId { get; set; }
        public DateTime creationTime { get; set; }
        public long? creatorUserId { get; set; }
        public int Id { get; set; }
    }

    public class MissionDto
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public int Ordinal { get; set; }
        public string Description { get; set; }
        public string LinkAvatar { get; set; }
        public decimal Target { get; set; }
        public decimal Actual { get; set; }
        public decimal Coin { get; set; }
        public decimal Point { get; set; }
        public string Status { get; set; }
        public string State { get; set; }
        public DateTime? Date { get; set; }
        public string Process { get; set; }
        public int Id { get; set; }
        public int? MissionBaselineId { get; set; }
    }
}
