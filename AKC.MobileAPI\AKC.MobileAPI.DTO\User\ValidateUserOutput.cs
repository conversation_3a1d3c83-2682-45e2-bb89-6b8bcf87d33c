﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.User
{
    public class ValidateUserOutput
    {
        public string PhoneNumber { get; set; }
        public bool IsExisted { get; set; }
        public bool HasPinCode { get; set; }
        public bool IsLocked { get; set; }
        public bool WrongDeviceId { get; set; }
        public bool UsingFirebaseOtp { get; set; } = true;
        public string MemberCode { get; set; }
    }
}
