﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.Service.Constants.ThirdParty
{
    public class VPBankApiUrl
    {
        public const string VERIFY_NATIONAL_ID_URL = "";
        public const string VERIFY_NATIONAL_ID_OPERATION_NAME = "verifyNationalId";

        public const string VERIFY_OTP_URL = "";
        public const string VERIFY_OTP_OPERATION_NAME = "validateOTP";

        public const string POINT_VIEW_URL = "";
        public const string POINT_VIEW_OPERATION_NAME = "viewPoint";

        public const string POINT_EXCHANGE_URL = "";
        public const string POINT_EXCHANGE_OPERATION_NAME = "exchangePoint";

        public const string REVERT_POINT_URL = "";
        public const string REVERT_POINT_OPERATION_NAME = "revertPoint";

        public const string REQUEST_ACCESS_TOKEN_URL = "";
        public const string REQUEST_ACCESS_TOKEN_OPERATION_NAME = "requestAccessToken";
    }
}
