﻿using AKC.MobileAPI.DTO.Loyalty.CampaignGift;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/campaign-gift")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyCampaignGiftController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly ILoyaltyCampaignGiftService _loyaltyCampaignGiftService;

        public LoyaltyCampaignGiftController(ILogger<LoyaltyCampaignGiftController> logger, IExceptionReponseService exceptionReponseService, ICommonHelperService commonHelperService, ILoyaltyCampaignGiftService loyaltyCampaignGiftService)
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
            _loyaltyCampaignGiftService = loyaltyCampaignGiftService;
        }

        [HttpPost("check-campaign-gift")]
        public async Task<ActionResult<CheckCampaignGiftScanQrResponse>> CheckCampaignGiftScanQr([FromBody] CheckCampaignGiftScanQrInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyCampaignGiftService.CheckCampaignGiftScanQr(input, HttpContext);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "CheckCampaignGiftScanQr Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost("submit-campaign-qrcode")]
        public async Task<ActionResult<ScanQRCodeResponse>> ScanCampaignQRCode([FromBody] ScanQRCodeInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyCampaignGiftService.ScanCampaignQRCode(input, HttpContext);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "ScanCampaignQRCode Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}
