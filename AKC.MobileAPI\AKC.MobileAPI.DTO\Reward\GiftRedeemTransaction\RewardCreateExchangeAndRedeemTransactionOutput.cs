﻿using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction
{
    public class RewardCreateExchangeAndRedeemTransactionOutput
    {
        public List<DataRedeemTransaction> Items { get; set; }
        //public int TotalCount { get; set; }
        public string Exception { get; set; }
        public string Messages { get; set; }
        public bool SuccessedRedeem { get; set; }
        public bool IsNotEnoughBalance { get; set; }
        public bool Timeout { get; set; }
        public bool InvalidInput { get; set; }
        public string ErrorCode { get; set; }
        public List<DataErrorExhangeTransaction> ErrorExchanges { get; set; }
    }

    public class RedeemLoyaltyResult
    {
        public string ErrorCode { get; set; }
        public bool Error { get; set; }
        public string Exception { get; set; }
        public List<DataRedeemTransaction> Items { get; set; }
        public bool Timeout { get; set; }
        public bool ErrorLoyalty { get; set; }
    }

    public class ExchangePartnerResult
    {
        public string ErrorCode { get; set; }
        public bool Error { get; set; }
        public string Exception { get; set; }
        public int MerchantId { get; set; }
        public bool Timeout { get; set; }
        public string IdNumber { get; set; }
        public string AccessToken { get; set; }
        public string VpidTransactionId { get; set; }
        public string LoyaltlyTransactionId { get; set; }
        public double ExchangeAmount { get; set; }
    }

    public class DataErrorExhangeTransaction
    {
        public string ErrorCode { get; set; }
        public string Messages { get; set; }
        public int MerchantId { get; set; }
    }
}
