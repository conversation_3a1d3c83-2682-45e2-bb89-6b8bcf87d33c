﻿using AKC.MobileAPI.DTO.Reward.GiveToken;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardGiveTokenService : RewardBaseService, IRewardGiveTokenService
    {
        public RewardGiveTokenService(
            IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<RewardGiveTokenTransferTransactionOutput> TransferTransaction(RewardGiveTokenTransferTransactionInput input)
        {
            return await PostRewardAsync<RewardGiveTokenTransferTransactionOutput>(RewardApiUrl.GIVE_TOKEN_TRANSFER_TRANSACTION, input);
        }

        public async Task<RewardGiveTokenUserVerifyingOutput> UserVerifying(RewardGiveTokenUserVerifyingInput input)
        {
            return await PostRewardAsync<RewardGiveTokenUserVerifyingOutput>(RewardApiUrl.GIVE_TOKEN_USER_VERIFYING, input);
        }
    }
}
