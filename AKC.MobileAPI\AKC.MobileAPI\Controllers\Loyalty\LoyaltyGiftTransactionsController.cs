﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Reward;
using AKC.MobileAPI.DTO.Reward.Member;
using Microsoft.Extensions.Caching.Distributed;
using AKC.MobileAPI.DTO.Loyalty.GiftSearch;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/GiftTransactions")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    //[Authorize]
    public class LoyaltyGiftTransactionsController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyGiftTransactionsService _giftTransactionsService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltyLanguageService _loyaltyLanguageService;
        private readonly IRewardCreateExchangeAndRedeemService _rewardCreateExchangeAndRedeemService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IDistributedCache _cache;
        public LoyaltyGiftTransactionsController(
            ILogger<LoyaltyGiftTransactionsController> logger,
            ILoyaltyGiftTransactionsService giftTransactionsService,
            IExceptionReponseService exceptionReponseService,
            ILoyaltyLanguageService loyaltyLanguageService,
            IRewardCreateExchangeAndRedeemService rewardCreateExchangeAndRedeemService,
            IRewardMemberService rw,
            IDistributedCache c,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _giftTransactionsService = giftTransactionsService;
            _exceptionReponseService = exceptionReponseService;
            _loyaltyLanguageService = loyaltyLanguageService;
            _rewardCreateExchangeAndRedeemService = rewardCreateExchangeAndRedeemService;
            _commonHelperService = commonHelperService;
            _rewardMemberService = rw;
            _cache = c;
        }

        [HttpPost]
        [Route("CreateRedeemTransaction")]
        public async Task<ActionResult<LoyaltyCreateRedeemTransactionOutput>> CreateRedeemTransaction(LoyaltyCreateRedeemTransactionInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var context = HttpContext;
                var result = await _giftTransactionsService.CreateRedeemTransaction(input, context);
                _logger.LogInformation("redeem transaction " + input.MemberCode + "_Response" + JsonConvert.SerializeObject(result));
                if (result.Result.SuccessedRedeem)
                {
                    return StatusCode(200, result);
                }
                else if (result.Result.IsNotEnoughBalance)
                {
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_BALANCE_NOT_ENOUGH);
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "BalanceNotEnough",
                        Message = result.Result.Messages,
                        MessageDetail = null,
                        ListMessages = listMessages,
                    });
                }
                else
                {
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(result.Result.Exception ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = result.Result.Exception,
                        Message = result.Result.Messages,
                        MessageDetail = null,
                        ListMessages = listMessages,
                    });
                }
            }
            catch (Exception ex)
            {

                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogInformation("redeem transaction " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));
                    if (res.Code == "SystemProcessData" && res.Message == "BadRequest" && res.MessageDetail is string)
                    {
                        res.Message = res.MessageDetail.ToString();
                    }
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogInformation("redeem transaction " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }


        [HttpPost]
        [Route("CreateExchangeAndRedeemTransaction")]
        public async Task<ActionResult<RewardCreateExchangeAndRedeemTransactionOutput>> CreateExchangeAndRedeemTransaction(RewardCreateExchangeAndRedeemTransactionInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var context = HttpContext;
                var result = await _rewardCreateExchangeAndRedeemService.CreateExchangeAndRedeemTransaction(input, context);
                _logger.LogInformation("create exchange redeem " + input.MemberCode + "_" + JsonConvert.SerializeObject(result));
                if (result.SuccessedRedeem)
                {
                    return StatusCode(200, result);
                }
                else if (result.InvalidInput)
                {
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "InvalidInput",
                        Message = "Input request invalid",
                        MessageDetail = null,
                        ListMessages = null,
                    });
                }
                else if (result.IsNotEnoughBalance)
                {
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_BALANCE_NOT_ENOUGH);
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_BALANCE_NOT_ENOUGH,
                        Message = result.Messages,
                        MessageDetail = result.Messages,
                        ListMessages = listMessages,
                    });
                }
                else if (result.ErrorExchanges != null && result.ErrorExchanges.Count > 0)
                {
                    return StatusCode(400, result);
                }
                else
                {
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(result.ErrorCode ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = result.ErrorCode,
                        Message = result.Messages,
                        MessageDetail = result.Exception,
                        ListMessages = listMessages,
                    });
                }
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogInformation("create exchange redeem " + input.MemberCode + "_Error_Catch" + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogInformation("create exchange redeem " + input.MemberCode + "_Error_Catch" + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("GetAllWithEGift")]
        // [AllowAnonymous]
        public async Task<ActionResult<LoyaltyGetAllWithEGiftOutput>> GetAllWithEGift([FromQuery] LoyaltyGetAllWithEGiftInput input)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(input.OwnerCodeFilter))
                {
                    var authorization = Request.Headers["Authorization"].ToString();
                    var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.OwnerCodeFilter);
                    if (checkAuthen != null)
                    {
                        return StatusCode(401, checkAuthen);
                    }
                }

                if (input.MaxResultCount <= 0)
                {
                    input.MaxResultCount = 10;
                }
                if (input.SkipCount < 0)
                {
                    input.SkipCount = 0;
                }
                var result = await _giftTransactionsService.GetAllWithEGift(input);
                if (!string.IsNullOrEmpty(input.GiftTransactionCode) && "next" != input.Ver)
                {
                    // Nếu là ver cũ, và call hàm này với TxCode cụ thể thì nối introduce và 
                    if (result != null && result.Result != null && result.Result.Items.Count > 0 &&
                        result.Result.Items[0].GiftTransaction != null)
                    {
                        result.Result.Items[0].GiftTransaction.Introduce +=
                            result.Result.Items[0].GiftTransaction.Condition;
                        var address = "";
                        if (result.Result.Items[0].GiftUsageAddress != null &&
                            result.Result.Items[0].GiftUsageAddress.Count > 0)
                        {
                            address = "<b>Địa chỉ sử dụng:</b><br/><ul>";
                            foreach (var ua in result.Result.Items[0].GiftUsageAddress)
                            {
                                var li = "<li>";
                                if (string.IsNullOrEmpty(ua.Name) == false)
                                {
                                    li += "<b>" + ua.Name + "</b>: " + ua.Address;
                                }
                                else
                                {
                                    li += ua.Address;
                                }
                                li += "</li>";
                                address += li;
                            }
                            address += "</ul>";
                        }

                        if (!string.IsNullOrEmpty(address))
                        {
                            if (string.IsNullOrEmpty(result.Result.Items[0].GiftTransaction.Introduce))
                            {
                                result.Result.Items[0].GiftTransaction.Introduce += address;
                            }
                            else
                            {
                                result.Result.Items[0].GiftTransaction.Introduce += "<br/>" + address;
                            }
                        }
                    }
                }
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetAllWithEGift GiftTransactions Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("GiftTransfer")]
        public async Task<ActionResult<GiftTransferOutput>> GiftTransfer(UpdateGiftTransactionsForTransferInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.OwnerCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _giftTransactionsService.GiftTransfer(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GiftTransfer GiftTransactions Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("UserVerifying")]
        public async Task<ActionResult<GetMemberCodeOutput>> UserVerifying(GetMemberCodeInput input)
        {
            try
            {
                var result = await _giftTransactionsService.UserVerifying(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "UserVerifying GiftTransactions Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllWithTopupPhone")]
        public async Task<ActionResult<LoyaltyGetAllWithTopupPhoneOutput>> GetAllWithTopupPhone([FromQuery] LoyaltyGetAllWithTopupPhoneInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCodeFilter);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                input.MaxResultCount = input.MaxResultCount == 0 ? 10 : input.MaxResultCount;
                var result = await _giftTransactionsService.GetAllWithTopupPhone(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetAllWithTopupPhone GiftTransactions Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllWithTopupPhone_v1")]
        public async Task<ActionResult<LoyaltyGetAllWithTopupPhone_v1Output>> GetAllWithTopupPhone_v1([FromQuery] LoyaltyGetAllWithTopupPhoneInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCodeFilter);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                input.MaxResultCount = input.MaxResultCount == 0 ? 10 : input.MaxResultCount;
                var result = await _giftTransactionsService.GetAllWithTopupPhone_v1(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetAllWithTopupPhone_v1 GiftTransactions Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetCustomerInfoFromTransaction")]
        public async Task<ActionResult<GetCustomerInfoFromTransactionOutput>> GetCustomerInfoFromTransaction([FromQuery] GetCustomerInfoFromTransactionInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _giftTransactionsService.GetCustomerInfoFromTransaction(input);

                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetCustomerInfoFromTransaction Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        /// <summary>
        /// Thực hiện Redeem quà với money card.
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("RedeemWithMoneyCard")]
        [AllowAnonymous]
        public async Task<ActionResult<RedeemWithMoneyCardOutput>> RedeemWithMoneyCard(RedeemWithMoneyCardInput input)
        {
            try
            {
                _logger.LogInformation($"RedeemWithMoneyCard___Input: {JsonConvert.SerializeObject(input)}");
                //var authorization = Request.Headers["Authorization"].ToString();
                //var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                //if (checkAuthen != null)
                //{
                //    return StatusCode(401, checkAuthen);
                //}

                if (string.IsNullOrEmpty(input.GiftCode))
                {
                    return StatusCode(400, new RewardErrorResponse()
                    {
                        Result = 400,
                        Code = "GiftCodeRequired",
                        Message = "Gift Code không được bỏ trống"
                    });
                }
                if (string.IsNullOrEmpty(input.PinCode))
                {
                    return StatusCode(400, new RewardErrorResponse()
                    {
                        Result = 400,
                        Code = "PinCodeRequired",
                        Message = "PinCode không được bỏ trống"
                    });
                }
                if (input.TotalAmount < 0)
                {
                    return StatusCode(400, new RewardErrorResponse()
                    {
                        Result = 400,
                        Code = "InvalidAmount",
                        Message = "Amount không hợp lệ"
                    });
                }
                if (input.MoneyCardCodes == null || input.MoneyCardCodes.Count == 0)
                {
                    return StatusCode(400, new RewardErrorResponse()
                    {
                        Result = 400,
                        Code = "InvalidMoneyCards",
                        Message = "List MoneyCard không được bỏ trống"
                    });
                }
                // // Validate moneycards phải nằm trong DS khả dụng
                var listPaymentMethod = await GetListPaymentMethodByMemberCodeAndGift(input.MemberCode, input.GiftCode);
                if (listPaymentMethod == null || listPaymentMethod.Item == null ||
                    !listPaymentMethod.Item.ListPaymentMethod.Any(x => x.Type == "MONEYCARD"))
                {
                    _logger.LogError(" >> redeem transaction with moneycard - " + input.MemberCode + " failed because member has no card for this gift");
                    return StatusCode(400, new RewardErrorResponse()
                    {
                        Result = 400,
                        Code = "NoAvailableCards",
                        Message = "Member không có thẻ điểm nào khả dụng cho quà này"
                    });
                }
                var availableCodes = listPaymentMethod.Item.ListPaymentMethod.Select(x => x.CardCode).ToHashSet();
                var invalidCards = input.MoneyCardCodes.Except(availableCodes).ToList(); // Tìm những mã quà mà request truyền lên KHÔNG THUỘC ds mã hợp lệ 
                if (invalidCards.Any())
                {
                    _logger.LogError(" >> redeem transaction with moneycard - " + input.MemberCode + " There are some card codes not valid: " + string.Join(", ", invalidCards));
                    return StatusCode(400, new RewardErrorResponse()
                    {
                        Result = 400,
                        Code = "SomeCardsInvalid",
                        Message = "Có những mã thẻ truyền lên không hợp lệ. Vui lòng thử lại."
                    });
                }
                var context = HttpContext;
                // Validate PinCode here first
                //var getInfoByCode = await _rewardMemberService.GetInfo(new RewardMemberGetInfoInput()
                //{
                //    NationalId = input.MemberCode
                //});
                //var verifyPinCode = await  _rewardMemberService.VerifyPinCode(new RewardMemberVerifyPinCodeRequest()
                //{
                //    PinCode = input.PinCode, PhoneNumber = getInfoByCode.PhoneNumber, DeviceId = input.DeviceId, IsRevoke = false
                //});
                //if (!verifyPinCode.Success)
                //{
                //    return StatusCode(400, new RewardErrorResponse()
                //    {
                //        Result = 400,
                //        Code = "CanNotVerifyPinCodeNow",
                //        Message = "Can Not Verify PinCode Now"
                //    });
                //}
                var result = await _giftTransactionsService.RedeemWithMoneyCard(input, context);
                _logger.LogInformation("redeem RedeemWithMoneyCard " + input.MemberCode + "_Response" + JsonConvert.SerializeObject(result));
                if (result.Result.SuccessedRedeem)
                {
                    return StatusCode(200, result);
                }
                else if (result.Result.IsNotEnoughBalance)
                {
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_BALANCE_NOT_ENOUGH);
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "BalanceNotEnough",
                        Message = result.Result.Messages,
                        MessageDetail = null,
                        ListMessages = listMessages,
                    });
                }
                else
                {
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(result.Result.Exception ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = result.Result.Exception,
                        Message = result.Result.Messages,
                        MessageDetail = null,
                        ListMessages = listMessages,
                    });
                }
            }
            catch (Exception ex)
            {
        
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogInformation("redeem RedeemWithMoneyCard " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));
                    if (res.Code == "SystemProcessData" && res.Message == "BadRequest" && res.MessageDetail is string)
                    {
                        res.Message = res.MessageDetail.ToString();
                    }
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogInformation("redeem RedeemWithMoneyCard " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("GetGroupCodesOfGift")]
        public async Task<ActionResult<GetGroupCodesOfGiftOutput>> GetGroupCodesOfGift([FromQuery] string GiftCode)
        {
            if (string.IsNullOrEmpty(GiftCode))
            {
                throw new Exception("InvalidInput");
            }
            var cacheKey = "MoneyCard:GetGroupCodesOfGift:" + GiftCode;
            var cachedString = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(cachedString))
            {
                return JsonConvert.DeserializeObject<GetGroupCodesOfGiftOutput>(cachedString);
            }
            var res = await _giftTransactionsService.GetGroupCodesOfGift(GiftCode);
            await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(res),
                new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(10)));
            return StatusCode(200, res);
        }
        
        // Private util function for this service
        private async Task<RewardMemberGetPaymentMethodOutput> GetListPaymentMethodByMemberCodeAndGift(string memberCode, string giftCode)
        {
            var cacheKey = $"PrivateFunc:GetPaymentMethod_{memberCode}_{giftCode}";
        
            // Try to get cached data
            var cachedData = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(cachedData))
            {
                return JsonConvert.DeserializeObject<RewardMemberGetPaymentMethodOutput>(cachedData);
            }
        
            // Fetch data if not found in cache
            var listGiftCodeResult = await _giftTransactionsService.GetGroupCodesOfGift(giftCode);
            var result = await _rewardMemberService.GetPaymentMethod(new RewardMemberGetPaymentMethodInput()
            {
                MemberCode = memberCode,
                ListGiftGroupCode = listGiftCodeResult.Result.Items,
                MemberType = "KHCN"
            });
        
            // Cache the result for 10 minutes
            var cacheEntryOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(10)
            };
        
            var serializedResult = JsonConvert.SerializeObject(result);
            await _cache.SetStringAsync(cacheKey, serializedResult, cacheEntryOptions);
        
            return result;
        }
        
        [HttpGet]
        [Route("GetGiftInfo")]
        public async Task<ActionResult<LoyaltyGetGiftInfoOutput>> GetGiftInfo([FromQuery] LoyaltyGetGiftInfoInput input)
        {

            try
            {
                if (string.IsNullOrEmpty(input.OrderCode) || string.IsNullOrEmpty(input.MemberCode))
                {
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "InvalidInput",
                        Message = "Input request invalid",
                        MessageDetail = null,
                        ListMessages = null,
                    });
                }
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var res = await _giftTransactionsService.GetGiftInfo(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetGiftInfo Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}

