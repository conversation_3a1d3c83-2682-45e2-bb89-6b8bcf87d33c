﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class GetListQuestStageByMemberCodeInputDto
    {
        public string MemberCode { get; set; }
        public string StateFilter { get; set; }
        public QuestStatus? Type { get; set; }
        public string Sorting { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
    }
}
