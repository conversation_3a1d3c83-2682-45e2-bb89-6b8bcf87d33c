﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.Insurance;
using AKC.MobileAPI.DTO.Loyalty.Rewards;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    /// <summary>
    /// Bộ API tích hợp phần BẢO HIỂM
    /// </summary>
    [Route("api/insurance")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    // [AllowAnonymous]
    public class LoyaltyInsuranceController : ControllerBase
    {
        private readonly ILoyaltyInsuranceService _insuranceService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltyLanguageService _loyaltyLanguageService;
        protected readonly IConfiguration _configuration;
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionResponseService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly ILoyaltyGiftTransactionsService _giftTransactionsService;
        private readonly ILoyaltyRewardsService _rewardsService;
        private readonly ILoyaltySecondaryCustomersService _loyaltySecondaryCustomersService;

        public LoyaltyInsuranceController(ILogger<LoyaltyInsuranceController> lg,
            ILoyaltyInsuranceService ix,
            IExceptionReponseService exceptionReponseService,
            ILoyaltyLanguageService loyaltyLanguageService,
            ILoyaltyGiftTransactionsService giftSrv,
            ICommonHelperService help,
            ILoyaltyRewardsService x,
            IConfiguration xx,
            IRewardMemberService y,
            ILoyaltySecondaryCustomersService z,
            IExceptionReponseService ex)
        {
            _exceptionReponseService = exceptionReponseService;
            _loyaltyLanguageService = loyaltyLanguageService;
            _giftTransactionsService = giftSrv;
            _logger = lg;
            _exceptionResponseService = ex;
            _insuranceService = ix;
            _commonHelperService = help;
            _rewardsService = x;
            _rewardMemberService = y;
            _configuration = xx;
            _loyaltySecondaryCustomersService = z;
        }

        /// <summary>
        /// Xem danh sách các chương trình bảo hiểm
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotSupportedException"></exception>
        [HttpGet]
        [Route("list-programs")]
        public async Task<ActionResult<GetListProgramsOutput>> GetListProgram([FromQuery] GetListProgramsInput input)
        {
            try
            {
                var result = await _insuranceService.FindProgram(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Insurance GetListProgram Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        /// <summary>
        /// Get danh sách các sản phẩm bảo hiểm
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotSupportedException"></exception>
        [HttpGet]
        [Route("list-products")]
        public async Task<ActionResult<GetListProductsOutput>> GetListProduct([FromQuery] GetListProductsInput input)
        {
            try
            {
                var result = await _insuranceService.FindProduct(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Insurance GetListProduct Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        /// <summary>
        /// Get danh sách các gói bảo hiểm
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotSupportedException"></exception>
        [HttpGet]
        [Route("list-packages")]
        public async Task<ActionResult<GetListPackagesOutput>> GetListPackage([FromQuery] GetListPackagesInput input)
        {
            try
            {
                var result = await _insuranceService.FindPackage(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Insurance GetListPackage Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        /// <summary>
        /// Get danh sách các Hợp đồng bảo hiểm đã mua
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotSupportedException"></exception>
        [HttpGet]
        [Route("list-purchased-contracts")]
        public async Task<ActionResult<GetListInsuranceContractOutput>> GetListInsuranceContracts([FromQuery] GetListContractInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _insuranceService.FindContract(input);
                if (result?.Result?.Items?.Count > 0)
                {
                    foreach (var xItem in result?.Result?.Items)
                    {
                        if (xItem != null && xItem.ContractInfo != null)
                        {
                            xItem.ContractInfo.VehicleEngineNumber = xItem.ContractInfo?.VehicleEngineNo;
                            xItem.ContractInfo.VehicleFrameNumber = xItem.ContractInfo?.VehicleChassisNo;
                            xItem.ContractInfo.VehicleLicensePlate = xItem.ContractInfo?.VehicleLicensePlate;
                        }
                    }
                }
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Insurance GetListInsuranceContracts Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        /// <summary>
        /// Hành động mua gói bảo hiểm
        /// Gọi sang bên đối tác rồi trả về đối tượng Hợp đồng cho Mobile View
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotSupportedException"></exception>
        [HttpPost]
        [Route("purchase-package")]
        public async Task<ActionResult<InsuranceSingleContractDto>> PurchasePackage([FromBody] PurchasePackageInput input)
        {
            
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var context = HttpContext;
                var durations = input.ContractInfo.InsuranceEffectiveTime + "";
                if (input.ContractInfo.InsuranceEffectiveTimeType == "YEAR")
                {
                    // Phòng trường hợp số thời gian đơn vị là năm, thì phải nhân 12 để đúng dữ liệu truyền cho OPES
                    durations = input.ContractInfo.InsuranceEffectiveTime * 12 + "";
                }

                var vehicleType = "";
                if (InsuranceProviders.OPES.Equals(input.VendorCode))
                {
                    vehicleType = input.PackageCode;
                }
                // Validate xem bắt buộc phải điền hoặc là biển số, hoặc là combo số khung số máy
                // CẬP NHẬT LINKID-3953 - vehicleEngineNo và vehicleChassisNo phải required. Điền từ 6 ký tự trở lên. Không vượt quá 30 ký tự đối với VehicleEngineNo và 17 ký tự với vehicleChassisNo
                _logger.LogInformation(" >> PurchasePackge >> input = " + JsonConvert.SerializeObject(input));
                if (string.IsNullOrWhiteSpace(input.ContractInfo.CustomerName) ||
                    input.ContractInfo.CustomerName.Trim().Length > 50)
                {
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "NameRequiredAndLessThan50",
                        Message = "Bạn phải điền tên chủ sở hữu và không quá 50 ký tự",
                        MessageDetail = null,
                        ListMessages = null,
                    });
                }
                input.ContractInfo.CustomerName = input.ContractInfo.CustomerName.Trim();
                
                if (string.IsNullOrWhiteSpace(input.ContractInfo.CustomerIdCard) ||
                    input.ContractInfo.CustomerIdCard.Trim().Length > 20)
                {
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "IdCardRequiredAndLessThan20",
                        Message = "Bạn phải điền giấy tờ tuỳ thân của chủ sở hữu và không quá 20 ký tự",
                        MessageDetail = null,
                        ListMessages = null,
                    });
                }
                input.ContractInfo.CustomerIdCard = input.ContractInfo.CustomerIdCard.Trim();
                
                
                if (string.IsNullOrWhiteSpace(input.ContractInfo.CustomerPhoneNumber) ||
                    input.ContractInfo.CustomerPhoneNumber.Trim().Length > 20)
                {
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "PhoneRequiredAndLessThan20",
                        Message = "Bạn phải điền SĐT chủ sở hữu và không quá 20 ký tự",
                        MessageDetail = null,
                        ListMessages = null,
                    });
                }
                input.ContractInfo.CustomerPhoneNumber = input.ContractInfo.CustomerPhoneNumber.Trim();
                
                if (string.IsNullOrWhiteSpace(input.ContractInfo.CustomerEmailAddress) ||
                    input.ContractInfo.CustomerEmailAddress.Trim().Length > 50)
                {
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "EmailRequiredAndLessThan50",
                        Message = "Bạn phải điền Email chủ sở hữu và không quá 50 ký tự",
                        MessageDetail = null,
                        ListMessages = null,
                    });
                }
                input.ContractInfo.CustomerEmailAddress = input.ContractInfo.CustomerEmailAddress.Trim();

                if (string.IsNullOrWhiteSpace(input.ContractInfo.VehicleEngineNo))
                {
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "EngineNoRequired",
                        Message = "Bạn phải điền vào dữ liệu Số Máy",
                        MessageDetail = null,
                        ListMessages = null,
                    });
                }

                input.ContractInfo.VehicleEngineNo = input.ContractInfo.VehicleEngineNo.Trim();
                if (input.ContractInfo.VehicleEngineNo.Length < 6 || input.ContractInfo.VehicleEngineNo.Length > 30)
                {
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "EngineNoInvalid",
                        Message = "Bạn phải điền vào dữ liệu Số Máy từ 6 đến 30 ký tự",
                        MessageDetail = null,
                        ListMessages = null,
                    });
                }
                if (string.IsNullOrWhiteSpace(input.ContractInfo.VehicleChassisNo))
                {
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "ChassisNoRequired",
                        Message = "Bạn phải điền vào dữ liệu Số Khung",
                        MessageDetail = null,
                        ListMessages = null,
                    });
                }
                if (input.ContractInfo.VehicleChassisNo.Length < 6 || input.ContractInfo.VehicleChassisNo.Length > 17)
                {
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "ChassisNoInvalid",
                        Message = "Bạn phải điền vào dữ liệu Số Khung từ 6 đến 17 ký tự",
                        MessageDetail = null,
                        ListMessages = null,
                    });
                }

                if (input.ContractInfo.VehicleLicensePlate != null)
                {
                    input.ContractInfo.VehicleLicensePlate = input.ContractInfo.VehicleLicensePlate.Trim();
                    if (input.ContractInfo.VehicleLicensePlate.Length > 20)
                    {
                        return StatusCode(400, new LoyaltyErrorResponse()
                        {
                            Result = 400,
                            Code = "PlateNoNoInvalid",
                            Message = "Biển số xe không được vượt quá 50 ký tự",
                            MessageDetail = null,
                            ListMessages = null,
                        });
                    }
                }
                // End check LINKID-3953

                var metadata = new InsuranceCustomerAndVehicleInfoDto()
                {
                    vehiclePlateNo = input.ContractInfo.VehicleLicensePlate,
                    vehicleEngineNo = input.ContractInfo.VehicleEngineNo,
                    vehicleChassisNo = input.ContractInfo.VehicleChassisNo,
                    vehicleBrand = "",
                    vehicleColor = "",
                    vehicleType = vehicleType,
                    ownerAddress = input.ContractInfo.CustomerAddress,
                    ownerEmail = input.ContractInfo.CustomerEmailAddress,
                    ownerId = input.ContractInfo.CustomerIdCard,
                    ownerName = input.ContractInfo.CustomerName,
                    ownerPhone = input.ContractInfo.CustomerPhoneNumber,
                    durations = durations,
                    premium = "0",
                    customerDoB = input.ContractInfo.CustomerDoB.ToString("yyyy-MM-dd"),
                    effectiveDate = input.ContractInfo.BeginDate.ToString("yyyy-MM-dd HH:mm:ss")
                };
                var result = await _giftTransactionsService.CreateRedeemTransaction(new LoyaltyCreateRedeemTransactionInput()
                {
                    Description = JsonConvert.SerializeObject(metadata),
                    Quantity = 1,
                    GiftCode = input.GiftCode,
                    MemberCode = input.MemberCode,
                    TotalAmount = input.TotalAmount
                }, context);
                _logger.LogInformation("Insurance: Redeem transaction "+ input.MemberCode + "_Response" + JsonConvert.SerializeObject(result));
                if (result.Result.SuccessedRedeem)
                {
                    // need an InsuranceSingleContractDto object
                    if (result.Result.Items.Count > 0)
                    {
                        var currentTx = result.Result.Items[0].Code;
                        var listContractByTx = await _insuranceService.FindContract(new GetListContractInput()
                        {
                            Code = currentTx, Skip = 0, MaxItem = 1, MemberCode = input.MemberCode
                        });
                        if(listContractByTx.Result.Items.Count > 0)
                        {
                            // Send input action to LinkID and Opes
                            var inputActionRequest = new LoyaltyInputActionInput()
                            {
                                Actions = new List<ActionRequestDto>()
                                {
                                    new ActionRequestDto()
                                    {
                                        TransactionCode = listContractByTx.Result.Items[0].ContractInfo.InsuranceContractNo,
                                        Type = 1,
                                        OriginalTransactionCode = "",
                                        ActionCode = "TPLRedeemSuccess",
                                        ActionFilter = new ActionListValueFilterReward()
                                        {
                                            ListCodeFilter = input.VendorCode + ";" + input.GiftCode,
                                        },
                                        MemberCode = input.MemberCode,
                                        Value = 1,
                                        ActionTime = DateTime.UtcNow,
                                        MlmApplied = false,
                                        MLMDealerChannel = null,
                                        MLMDistributionChannel = null,
                                        ReferenceCode = null,
                                        Tag = "",
                                        RankCode = ""
                                    }
                                }
                            };
                            try
                            {
                                // Send to LinkID
                                await _rewardsService.InputAction(inputActionRequest);
                            }
                            catch (Exception e)
                            {
                                _logger.LogError(" >> Error while sending InputAction to LinkId tenant");
                                _logger.LogError(e.StackTrace);
                            }
                            try
                            {
                                var checkInPartner =
                                    (await _loyaltySecondaryCustomersService.GetProfileByCode(input.MemberCode, input.VendorCode)).Result;
                                var partnerTenantIdInLinkid = (_configuration
                                    .GetSection("MerchantUseLoyalty:" + input.VendorCode + ":TenantId").Value);
                                if (checkInPartner == null || string.IsNullOrEmpty(checkInPartner.Code))
                                {
                                    var memberLinkIdTenant = (await _loyaltySecondaryCustomersService.GetProfileByCode(input.MemberCode)).Result;
                                    await _loyaltySecondaryCustomersService.CreateMemberOfCustomTenant(
                                    new CreateMemberOfCustomTenantInput()
                                    {
                                        AccountType = "Master",
                                        Address = memberLinkIdTenant.Address,
                                        Avatar = memberLinkIdTenant.Avatar,
                                        Birthday = memberLinkIdTenant.Birthday,
                                        Cards = "",
                                        ChannelType = memberLinkIdTenant.ChannelType,
                                        CityId = 0,
                                        Code = memberLinkIdTenant.Code,
                                        ContractCode = "",
                                        DistrictId = 0,
                                        Email = memberLinkIdTenant.Email,
                                        FirstName = memberLinkIdTenant.FirstName,
                                        FullChannelTypeCode = memberLinkIdTenant.FullChannelTypeCode,
                                        FullMemberTypeCode = memberLinkIdTenant.FullMemberTypeCode,
                                        FullRegionCode = memberLinkIdTenant.FullRegionCode,
                                        Gender = memberLinkIdTenant.Gender,
                                        IdCard = memberLinkIdTenant.IdCard,
                                        IsDeleted = false,
                                        IsIdCardVerified = false,
                                        JoiningDate = memberLinkIdTenant.JoiningDate,
                                        LastName = memberLinkIdTenant.LastName,
                                        MemberTypeCode = memberLinkIdTenant.MemberTypeCode,
                                        NationId = 0,
                                        PartnerPhoneNumber = "",
                                        Phone = memberLinkIdTenant.Phone,
                                        RankType = "",
                                        RankTypeCode = memberLinkIdTenant.RankTypeCode,
                                        ReferenceId = null,
                                        ReferenceInfo = "",
                                        ReferralCode = memberLinkIdTenant.ReferralCode,
                                        RegionCode = memberLinkIdTenant.RegionCode,
                                        RegisterType = "",
                                        StandardMemberCode = memberLinkIdTenant.StandardMemberCode,
                                        Status = memberLinkIdTenant.Status,
                                        StreetDetail = "",
                                        TenantId = int.Parse(partnerTenantIdInLinkid),
                                        Type = "Member",
                                        WardId = 0,
                                        WithdrawnDate = DateTime.Parse("2999-12-31T00:00:00.000Z"),
                                    }, input.VendorCode);
                                }
                                
                            }
                            catch (Exception e)
                            {
                                _logger.LogError(" >> Error while sending Creating Loyalty Member of to Vendor tenant >> " + input.VendorCode);
                                _logger.LogError(e.StackTrace);
                            }
                            try
                            {
                                // Send to vendor tenant
                                await _rewardsService.InputAction(inputActionRequest, input.VendorCode);
                            }
                            catch (Exception e)
                            {
                                _logger.LogError(" >> Error while sending InputAction to Vendor tenant >> " + input.VendorCode);
                                _logger.LogError(e.StackTrace);
                            }
                            
                            return StatusCode(200, listContractByTx.Result.Items[0]);
                        }
                        else
                        {
                            // TODO : ADD MORE LOGIC HEREs
                            throw new Exception("BÁO THÀNH CÔNG MÀ KHÔNG TÌM THẤY HỢP ĐỒNG LÀ HƠI DỊ");// FIXME
                        }
                    }
                    else
                    {
                        throw new Exception("BÁO THÀNH CÔNG MÀ KO KÈM MÃ GIAO DỊCH");// FIXME
                    }
                    
                } else if (result.Result.IsNotEnoughBalance)
                {
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_BALANCE_NOT_ENOUGH);
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "BalanceNotEnough",
                        Message = result.Result.Messages,
                        MessageDetail = null,
                        ListMessages = listMessages,
                    });
                }
                else
                {
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(result.Result.Exception ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    var resultCode = 400;
                    if (result.Result.Timeout)
                    {
                        resultCode = 408;
                    }
                    if (result.Result.Exception == "12")
                    {
                        resultCode = 12;
                    }
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = resultCode + "",
                        Message = result.Result.Messages,
                        MessageDetail = null,
                        ListMessages = listMessages,
                    });
                }
            }
            catch (Exception ex)
            {

                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogInformation("Insurance: Redeem transaction " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogInformation("Insurance: Redeem transaction " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }
        /// <summary>
        /// Get thông tin chi tiết gói bảo hiểm
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotSupportedException"></exception>
        [HttpGet]
        [Route("insurance-detail")]
        public async Task<ActionResult<InsuranceDetailRes>> GetInsuranceDetail([FromQuery] InsuranceDetailInput input)
        {
            try
            {
                var result = await _insuranceService.FindProductByCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Insurance GetInsuranceDetail Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }
    }
}