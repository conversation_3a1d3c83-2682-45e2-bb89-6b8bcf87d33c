﻿using AKC.MobileAPI.DTO.Loyalty.Quest;
using AKC.MobileAPI.Service.Abstract.Loyalty.Quest;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using AKC.MobileAPI.Service.Common;

namespace AKC.MobileAPI.Controllers.Loyalty.Quest
{
    [Route("api/QuestV2")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class QuestV2Controller : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyQuestService _loyaltyQuestService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        public QuestV2Controller(
            ILogger<QuestV2Controller> logger,
            ILoyaltyQuestService loyaltyQuestService,
            IExceptionReponseService exceptionReponseService,
            ICommonHelperService commonHelperService
            )
        {
            _logger = logger;
            _loyaltyQuestService = loyaltyQuestService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
        }

        [HttpGet("get-quest")]
        public async Task<ActionResult<GmQuestV2Output>> GetQuestById([FromQuery] GetQuestByIdInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyQuestService.GetQuestById(input.QuestId, input.MemberCode);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetQuestById Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpGet("get-list-quest-active")]
        public async Task<ActionResult<GmQuestV2Output>> GetListQuestActive([FromQuery] SearchGmQuestV2Request input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyQuestService.GetListQuestActive(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetListQuestActive Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpPost("view-member-progress-on-quest")]
        public async Task<ActionResult<ViewMemberProgessQuestOutput>> ViewMemberProgressOnQuest([FromBody] ViewMemberProgessQuestInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyQuestService.ViewMemberProgressOnQuest(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "ViewMemberProgressOnQuest Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpPost("perform-action")]
        public async Task<ActionResult<PerformActionOutput>> PerformAction([FromBody] PerformActionInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyQuestService.PerformAction(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "PerformAction Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpGet("get-quest-active-by-code")]
        public async Task<ActionResult<GmQuestV2InforOutput>> GetQuestActiveByCode([FromQuery] GmQuestV2InforInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyQuestService.GetQuestActiveByCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetQuestActiveByCode Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpPost("member-enrol-quest")]
        public async Task<ActionResult<GmQuestV2InforOutput>> MemberEnrolmentQuest([FromBody] GmQuestEnrolmentV2Input input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var memberEnrolRequest = new GmQuestEnrolmentV2Request()
                {
                    QuestCode = input.QuestCode,
                    MemberCode = input.MemberCode,
                    EnrolmentDate = DateTime.UtcNow,
                    CurrentCount = 0,
                    TotalCount = 0,
                    LastValidActionTime = null
                };
                var memberEnrolQuestResult = await _loyaltyQuestService.MemberEnrolmentQuest(memberEnrolRequest);
                if (memberEnrolQuestResult.success)
                {
                    var gmQuestEnrolRequest = new GmQuestV2InforInput() { MemberCode = input.MemberCode, QuestCode = input.QuestCode };
                    var result = await _loyaltyQuestService.GetQuestActiveByCode(gmQuestEnrolRequest);
                    return StatusCode(200, result);
                }
                else
                {
                    return StatusCode(400, memberEnrolQuestResult);
                }
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "MemberEnrolmentQuest Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpPost("member-claim-mission")]
        public async Task<ActionResult<PerformActionOutput>> MemberClaimMission([FromBody] MemberClaimMissionInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyQuestService.MemberClaimMission(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "MemberClaimMission Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }
    }
}
