﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class GetAllQuestStageInputDto
    {
        public string Filter { get; set; }

        public string StatusFilter { get; set; }

        public DateTime? FromDateFilter { get; set; }

        public DateTime? TodateFilter { get; set; }

        public string Sorting { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
    }
}
