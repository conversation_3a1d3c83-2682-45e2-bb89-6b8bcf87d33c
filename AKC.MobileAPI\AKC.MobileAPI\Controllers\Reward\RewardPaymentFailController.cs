﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Reward.CashoutTransaction;
using AKC.MobileAPI.DTO.Reward.PaymentFail;
using AKC.MobileAPI.DTO.Reward.TopUpTransaction;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Reward
{
    [Route("api/PaymentFail")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class RewardPaymentFailController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IRewardPaymentFailService _rewardPaymentFailService;
        private readonly ICommonHelperService _commonHelperService;
        public RewardPaymentFailController(
            ILogger<RewardPaymentFailController> logger,
            IExceptionReponseService exceptionReponseService,
            IRewardPaymentFailService rewardPaymentFailService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _rewardPaymentFailService = rewardPaymentFailService;
            _commonHelperService = commonHelperService;
        }

        // [HttpPost]
        // [Route("Create")]
        // public async Task<ActionResult<RewardPaymentFailCreateOutput>> CreateCashoutTransaction(RewardPaymentFailCreateInput input)
        // {
        //     try
        //     {
        //         var authorization = Request.Headers["Authorization"].ToString();
        //         var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
        //         if (checkAuthen != null)
        //         {
        //             return StatusCode(401, checkAuthen);
        //         }
        //         _logger.LogInformation("payment fail request " + input.MemberCode + "_" + JsonConvert.SerializeObject(input));
        //         var result = await _rewardPaymentFailService.Create(input);
        //         _logger.LogInformation("payment fail result " + input.MemberCode + "_" + JsonConvert.SerializeObject(result));
        //         return StatusCode(200, result);
        //     }
        //     catch (Exception ex)
        //     {
        //         _logger.LogError(ex, "payment fail error " + input.MemberCode + "_" + JsonConvert.SerializeObject(ex));
        //         var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //         return StatusCode(400, res);
        //     }
        // }
    }
}
