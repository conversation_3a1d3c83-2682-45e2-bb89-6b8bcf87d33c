﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

namespace AKC.MobileAPI.DTO.Reward.MemberOtp
{
    public class SendOtpChangePhoneInput
    {
        [JsonProperty(PropertyName = "memberCode")]
        public string MemberCode { get; set; }

        [JsonProperty(PropertyName = "phoneNumber")]
        public string PhoneNumber { get; set; }

        [JsonProperty(PropertyName = "oldPhoneNumber")]
        public string OldPhoneNumber { get; set; }
    }
}
