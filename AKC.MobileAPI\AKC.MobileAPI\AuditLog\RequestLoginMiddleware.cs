﻿using AKC.MobileAPI.Service;
using Gelf.Extensions.Logging;
using log4net;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.Logging;
using Microsoft.IO;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web.Http;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.MobileAPI;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Webstore;

namespace AKC.MobileAPI.AuditLog
{
    public class RequestLoginMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly RecyclableMemoryStreamManager _recyclableMemoryStreamManager;


        public RequestLoginMiddleware(RequestDelegate next, ILoggerFactory loggerFactory)
        {
            _next = next;
            _recyclableMemoryStreamManager = new RecyclableMemoryStreamManager();
        }

        public async Task Invoke(HttpContext context)
        {
            var endpoint = context.GetEndpoint();

            if (endpoint != null)
            {
                var logData = await GetRequestLogData(context, endpoint);

                LogRequest(logData);
            }

            await _next(context);
        }

        private void LogRequest(RequestLogData logData)
        {
            AuditLoggerProvider.Instance.LogAudit(logData);
        }

        private async Task<RequestLogData> GetRequestLogData(HttpContext context, Endpoint endpoint)
        {
            // BY PASS Body field if it is UPLOAD api, đặt ở đầu hàm vì tránh phải tạo stream từ Body thừa thãi 
            var controllerActionDescriptor = endpoint.Metadata.GetMetadata<ControllerActionDescriptor>();
            if (controllerActionDescriptor == null)
            {
                // Log minimal data for unmatched endpoints
                return new RequestLogData()
                {
                    Browser = context.Request.Headers["User-Agent"],
                    Ip = context.Connection.RemoteIpAddress.ToString(),
                    Scheme = context.Request.Scheme,
                    Method = context.Request.Method,
                    ServerHost = context.Request.Host.Value,
                    Path = context.Request.Path,
                    QueryString = "",
                    Body = "--- endpoint & httpmethod not matched ---"
                };
            }
            if (controllerActionDescriptor?.ControllerName == "CSTicket" && controllerActionDescriptor?.ActionName == "UploadImage")
            {
                // End flow, vì body là binary image log ra gây rối file log
                return new RequestLogData()
                {
                    ControllerName = controllerActionDescriptor?.ControllerName,
                    ActionName = controllerActionDescriptor?.ActionName,
                    Browser = context.Request.Headers["User-Agent"],
                    Ip = context.Connection.RemoteIpAddress.ToString(),
                    Scheme = context.Request.Scheme,
                    Method = context.Request.Method,
                    ServerHost = context.Request.Host.Value,
                    Path = context.Request.Path,
                    QueryString = JsonConvert.SerializeObject(context.Request.Query.ToDictionary(k => k.Key, v => v.Value)),
                    Body = "--- binary data ---",
                    TrueClientIp = context.Request.Headers["True-Client-IP"].ToString(),
                    XForwardedFor = context.Request.Headers["X-Forwarded-For"].ToString()
                };
            }
            
            
            context.Request.EnableBuffering();
            var requestStream = _recyclableMemoryStreamManager.GetStream();
            await context.Request.Body.CopyToAsync(requestStream);
            string clientIp = "";
            string xForwardedFor = "";
            if (context.Request.Headers["True-Client-IP"].ToString() != null)
            {
                clientIp = context.Request.Headers["True-Client-IP"].ToString(); //if the user is behind a proxy server
            }
            if (context.Request.Headers["X-Forwarded-For"].ToString() != null)
            {
                xForwardedFor = context.Request.Headers["X-Forwarded-For"].ToString();
            }
            var result = new RequestLogData()
            {
                Browser = context.Request.Headers["User-Agent"],
                Ip = context.Connection.RemoteIpAddress.ToString(),
                Scheme = context.Request.Scheme,
                Method = context.Request.Method,
                ServerHost = context.Request.Host.Value,
                Path = context.Request.Path,
                QueryString = JsonConvert.SerializeObject(context.Request.Query.ToDictionary(k => k.Key, v => v.Value)),
                Body = ReadStreamInChunks(requestStream),
                TrueClientIp = clientIp,
                XForwardedFor = xForwardedFor
            };

            // Reset stream Position request body.
            context.Request.Body.Position = 0;

            if (controllerActionDescriptor != null)
            {
                result.ControllerName = controllerActionDescriptor.ControllerName;
                result.ActionName = controllerActionDescriptor.ActionName;

                // KHÔNG SHOW PINCODE CỦA KHÁCH RA LOG:
                try
                {
                    if (result.ControllerName == "RewardMember" && result.ActionName == "VerifyPinCode")
                    {
                        var inputCheckPinCode = JsonConvert.DeserializeObject<RewardMemberVerifyPinCodeRequest>(result.Body);
                        inputCheckPinCode.PinCode = "******";
                        inputCheckPinCode.DeviceId = "******";
                        result.Body = JsonConvert.SerializeObject(inputCheckPinCode);
                    }
                    if (result.ControllerName == "WebstoreAuth" && result.ActionName == "SMELogin")
                    {
                        var inputCheckPinCode = JsonConvert.DeserializeObject<SMELoginRequest>(result.Body);
                        inputCheckPinCode.Password = "***hidden***";
                        result.Body = JsonConvert.SerializeObject(inputCheckPinCode);
                    }
                    if (result.ControllerName == "WebstoreAuth" && result.ActionName == "VerifyPassWord")
                    {
                        var inputCheckPinCode = JsonConvert.DeserializeObject<WebStoreVerifyPassWordInput>(result.Body);
                        inputCheckPinCode.PassWord = "***hidden***";
                        result.Body = JsonConvert.SerializeObject(inputCheckPinCode);
                    }
                }
                catch (Exception e)
                {
                    // BỎ QUA exception để tránh ảnh hưởng luồng đang có
                }
                // KHÔNG SHOW ACCESS TOKEN Ở HÀM TRONG LoyaltyThirdPartyPointsController
                try
                {
                    if (result.ControllerName == "LoyaltyThirdPartyPoints" && result.ActionName == "UpdatePartnerCaching")
                    {
                        var sDeserializeObject = JsonConvert.DeserializeObject<LoyaltyThirdPartyUpdatePartnerCachingInput>(result.Body);
                        if (sDeserializeObject is { Items: { } } && sDeserializeObject.Items.Count > 0)
                        {
                            foreach (var item in sDeserializeObject.Items)
                            {
                                item.AccessToken = "**********";
                            }
                        }
                        result.Body = JsonConvert.SerializeObject(sDeserializeObject);
                    }
                    if (result.ControllerName == "LoyaltyThirdPartyPoints" && result.ActionName == "PointView")
                    {
                        var dict = context.Request.Query.ToDictionary(k => k.Key, v => v.Value);
                        dict["AccessToken"] = "****AAAA****";
                        result.QueryString = JsonConvert.SerializeObject(dict);
                    }
                    if (result.ControllerName == "LoyaltyThirdPartyPoints" && result.ActionName == "VerifyOTP")
                    {
                        var verifyOtpInput = JsonConvert.DeserializeObject<LoyaltyThirdPartyVerifyOTPInput>(result.Body);
                        if (verifyOtpInput != null)
                        {
                            verifyOtpInput.OtpNumber = "******";
                        }
                        result.Body = JsonConvert.SerializeObject(verifyOtpInput);
                    }
                    if (result.ControllerName == "LoyaltyThirdPartyPoints" && result.ActionName == "PointExchange")
                    {
                        var exchange = JsonConvert.DeserializeObject<LoyaltyThirdPartyPointExchangeInput>(result.Body);
                        if (exchange != null)
                        {
                            exchange.AccessToken = "**********";
                        }
                        result.Body = JsonConvert.SerializeObject(exchange);
                    }
                    if (result.ControllerName == "LoyaltyThirdPartyPoints" && result.ActionName == "ConfirmConnect")
                    {
                        var verifyOtpInput = JsonConvert.DeserializeObject<LoyaltyThirdPartyConfirmConnectInput>(result.Body);
                        if (verifyOtpInput != null)
                        {
                            verifyOtpInput.OptCode = "******";
                        }
                        result.Body = JsonConvert.SerializeObject(verifyOtpInput);
                    }
                }
                catch (Exception e)
                {
                    // BỎ QUA exception để tránh ảnh hưởng luồng đang có
                }
            }

            return result;
        }

        private static string ReadStreamInChunks(Stream stream)
        {
            const int readChunkBufferLength = 4096;
            stream.Seek(0, SeekOrigin.Begin);
            var textWriter = new StringWriter();
            var reader = new StreamReader(stream);
            var readChunk = new char[readChunkBufferLength];

            int readChunkLength;
            do
            {
                readChunkLength = reader.ReadBlock(readChunk, 0, readChunkBufferLength);
                textWriter.Write(readChunk, 0, readChunkLength);
            } while (readChunkLength > 0);

            return textWriter.ToString();
        }
    }
}
