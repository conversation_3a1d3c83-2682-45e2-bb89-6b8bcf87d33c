﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Gamification
{
    public class GetGamificationLinkOutput
    {
        public int result { get; set; }
        public GetGamificationLinkDto item { get; set; }
        public int totalCount { get; set; }
        public string message { get; set; }
        public string messageDetail { get; set; }
    }
    public class CreateMemberDto
    {
        public int TenantID { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Status { get; set; }
        public string Avatar { get; set; }
        public string MemberCode { get; set; }

    }
    public class GetGamificationLinkDto
    {
        public string url { get; set; }
    }
}
