using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Gelf.Extensions.Logging;
using System.Reflection;
using System.Xml;
using System.IO;
using AKC.MobileAPI.Service;

namespace AKC.MobileAPI
{
#pragma warning disable CS1591
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args)
        {
            var configuration = AccessConfigurationService.Instance.GetConfiguration();

            var hostBuilder = Host.CreateDefaultBuilder(args)
                  .ConfigureWebHostDefaults(webBuilder =>
                  {
                      webBuilder.UseStartup<Startup>();
                      if (bool.Parse(configuration.GetSection("GrayLog:IsEnabled").Value))
                      {
                          webBuilder.ConfigureLogging((context, builder) => builder.AddGelf(options =>
                          {
                              options.Host = configuration.GetSection("GrayLog:Host").Value;
                              options.Port = int.Parse(configuration.GetSection("GrayLog:Port").Value);
                              options.Protocol = GetGelfProtocol(configuration.GetSection("GrayLog:Protocol").Value);
                              options.LogSource = configuration.GetSection("GrayLog:LogSource").Value;
                          }));
                      }
                  });

            return hostBuilder;
        }

        private static GelfProtocol GetGelfProtocol(string protocol)
        {
            switch (protocol.Trim().ToLower())
            {
                case "udp":
                    return GelfProtocol.Udp;
                case "http":
                    return GelfProtocol.Http;
                case "https":
                    return GelfProtocol.Https;
                default:
                    throw new Exception("Wrong Gelf protocal name.");
            }
        }

#pragma warning restore CS1591

    }
}