﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.GiftStoreExtends
{
    public class GiftStoreExtendsGetMerchantIdFromGiftCodeOutput
    {
        public GiftStoreExtendsGetMerchantIdFromGiftCodeResult Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }

    public class GiftStoreExtendsGetMerchantIdFromGiftCodeResult
    {
        public string GiftCode { get; set; }
        public int? MerchantId { get; set; }
    }
}
