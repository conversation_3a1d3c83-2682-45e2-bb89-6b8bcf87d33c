﻿using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.ExchangeTransaction;
using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Reward.Merchant;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Abstract.ThirdParty;
using Microsoft.Extensions.Caching.Distributed;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardCreateExchangeAndRedeemService : RewardBaseService, IRewardCreateExchangeAndRedeemService
    {
        private IRewardMemberService _rewardMemberService;
        private IRewardGiftRedeemTransactionService _rewardGiftRedeemTransactionService;
        private ILoyaltyGiftTransactionsService _loyaltyGiftTransactionsService;
        private readonly ILoyaltyThirdPartyService _loyaltyThirdPartyService;
        private readonly ILoyaltyGiftService _getLoyaltyData;
        private readonly IRewardExchangeTransactionService _rewardExchangeTransactionService;
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IThirdPartySIAService _siaService;
        private readonly IRewardMerchantService _rewardMerchantService;
        private readonly IDistributedCache _cache;
        public RewardCreateExchangeAndRedeemService(
            IConfiguration configuration,
            IRewardMemberService rewardMemberService,
            IRewardGiftRedeemTransactionService rewardGiftRedeemTransactionService,
            ILoyaltyGiftTransactionsService loyaltyGiftTransactionsService,
            ILogger<RewardCreateExchangeAndRedeemService> logger,
            ILoyaltyThirdPartyService loyaltyThirdPartyService,
            IExceptionReponseService exceptionReponseService,
            IRewardExchangeTransactionService rewardExchangeTransactionService,
            IRewardMerchantService rewardMerchantService,
            IThirdPartySIAService ss,
            ILoyaltyGiftService sx,
            IDistributedCache st
        ) : base(configuration)
        {
            _rewardMemberService = rewardMemberService;
            _rewardGiftRedeemTransactionService = rewardGiftRedeemTransactionService;
            _loyaltyGiftTransactionsService = loyaltyGiftTransactionsService;
            _logger = logger;
            _loyaltyThirdPartyService = loyaltyThirdPartyService;
            _exceptionReponseService = exceptionReponseService;
            _rewardExchangeTransactionService = rewardExchangeTransactionService;
            _rewardMerchantService = rewardMerchantService;
            _siaService = ss;
            _getLoyaltyData = sx;
            _cache = st;
        }
        public async Task<RewardCreateExchangeAndRedeemTransactionOutput> CreateExchangeAndRedeemTransaction(RewardCreateExchangeAndRedeemTransactionInput input, HttpContext httpContext)
        {
            if (!CheckValidInput(input))
            {
                return new RewardCreateExchangeAndRedeemTransactionOutput()
                {
                    ErrorCode = "InvalidInput",
                    IsNotEnoughBalance = false,
                    SuccessedRedeem = false,
                    InvalidInput = true,
                    Messages = "Redeem fail with invalid input",
                    Items = null,
                    Timeout = false,
                };
            }

            var merchantId = Convert.ToInt32(_configuration.GetSection("Reward" + MerchantNameConfig.VPID + ":MerchantId").Value);
            try
            {
                var merchantIdFromGiftCode = await _loyaltyGiftTransactionsService.GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
                {
                    GiftCode = input.Redeem.GiftCode
                });
                if (merchantIdFromGiftCode != null && merchantIdFromGiftCode.Success && merchantIdFromGiftCode.Result != null && merchantIdFromGiftCode.Result.MerchantId.HasValue)
                {
                    merchantId = merchantIdFromGiftCode.Result.MerchantId.Value;
                }
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);

                    var result = new RewardCreateExchangeAndRedeemTransactionOutput()
                    {
                        ErrorCode = res.Code,
                        IsNotEnoughBalance = false,
                        SuccessedRedeem = false,
                        InvalidInput = true,
                        Messages = res.Message,
                        Items = null,
                        Timeout = false
                    };
                    return result;
                }
                throw ex;
            }


            var orderCode = genOrderCode();
            var optionsLoop = new ParallelOptions
            {
                MaxDegreeOfParallelism = 10
            };
            var listExchangePartnerResult = new List<ExchangePartnerResult>();
            var listExchangeInternalResult = new List<ExchangePartnerResult>();
            var taskListExchangePartner = new List<Task>();
            // Run all exchange from partner
            Task taskRunAllExchange = Task.Run(() =>
            {
                Parallel.ForEach(input.ExchangeList, optionsLoop, item =>
                {
                    var exchange = ExchangePartnerTransaction(input.MemberCode, item, orderCode, httpContext).Result;
                    listExchangePartnerResult.Add(exchange);
                });
            });
            taskListExchangePartner.Add(taskRunAllExchange);
            Task.WaitAll(taskListExchangePartner.ToArray());
            // If all partner exchange success then call exchange internal
            var listExchangePartnerError = listExchangePartnerResult.Where(x => x.Error).ToList();
            var listExchangeSucess = listExchangePartnerResult.Where(x => !x.Error).ToList();

            if (listExchangePartnerError.Count() == 0)
            {
                listExchangePartnerResult.ForEach((item) =>
                {
                    var exchangeInternal = ExchangeInternalTransaction(input.MemberCode, item.AccessToken, item.IdNumber, item.ExchangeAmount, item.MerchantId, item.VpidTransactionId, item.LoyaltlyTransactionId).Result;
                    listExchangeInternalResult.Add(exchangeInternal);
                });
            }

            var listExchangeInternalError = listExchangeInternalResult.Where(x => x.Error).ToList();
            // Has exchange internal and exchange internal not error
            if (listExchangeInternalResult.Count > 0 && listExchangeInternalError.Count() == 0)
            {
                var redeemTransaction = await RetryRedeemLoyaltyTransaction(input, orderCode, httpContext, merchantId);
                _logger.LogInformation("create exchange redeem loyalty reward" + input.MemberCode + "_RedeemLoyaltyTransaction" + JsonConvert.SerializeObject(redeemTransaction));
                if (redeemTransaction.Error)
                {
                    // When redeem error then revert all exchange success
                    SendRetryRevertPartnerTransaction(listExchangePartnerResult, httpContext);
                    SendRetryRevertExchangeInternalTransaction(listExchangePartnerResult, input.MemberCode);
                    return new RewardCreateExchangeAndRedeemTransactionOutput()
                    {
                        ErrorCode = redeemTransaction.ErrorCode,
                        IsNotEnoughBalance = redeemTransaction.ErrorCode == "BalanceNotEnough" ? true : false,
                        SuccessedRedeem = false,
                        Messages = redeemTransaction.Exception,
                        Timeout = redeemTransaction.Timeout,
                        InvalidInput = false,
                    };
                }
                return new RewardCreateExchangeAndRedeemTransactionOutput()
                {
                    ErrorCode = null,
                    IsNotEnoughBalance = false,
                    SuccessedRedeem = true,
                    Messages = "Redeem successfuly",
                    Items = redeemTransaction.Items,
                    Timeout = false,
                    InvalidInput = false,
                };
            }

            // Show all error when exchange fail
            var listErrorExchanges = listExchangePartnerError.Select(x => new DataErrorExhangeTransaction()
            {
                ErrorCode = x.ErrorCode,
                Messages = x.Exception,
                MerchantId = x.MerchantId
            }).ToList();

            // When exchange has merchant error then revert all merchant exchange success
            SendRetryRevertPartnerTransaction(listExchangeSucess, httpContext);
            return new RewardCreateExchangeAndRedeemTransactionOutput()
            {
                ErrorCode = "CreateExchangeAndRedeemFail",
                IsNotEnoughBalance = false,
                SuccessedRedeem = false,
                Messages = "Create exchange and redeem fail with any exchange fail",
                Items = null,
                Timeout = false,
                ErrorExchanges = listErrorExchanges,
                InvalidInput = false,
            };
        }

        private void SendRetryRevertExchangeInternalTransaction(
            List<ExchangePartnerResult> input, string memberCode
        )
        {
            input.ForEach(item =>
            {
                RetryRevertTokenExchange(new RewardRevertExchangeTransactionInput()
                {
                    MemberCode = memberCode,
                    MerchantId = item.MerchantId,
                    PartnerBindingTxId = item.LoyaltlyTransactionId,
                    RevertAmount = item.ExchangeAmount,
                    TransactionCode = item.VpidTransactionId,
                }).Wait();
            });
        }

        private void SendRetryRevertPartnerTransaction(
            List<ExchangePartnerResult> input, HttpContext httpContext
        )
        {
            var optionsLoop = new ParallelOptions
            {
                MaxDegreeOfParallelism = 10
            };
            var taskListRevertExchange = new List<Task>();
            Task taskRunAllExchangeRedeemError = Task.Run(() =>
            {
                Parallel.ForEach(input, optionsLoop, item =>
                {
                    var revert = new LoyaltyThirdPartyRevertPointInput()
                    {
                        AccessToken = item.AccessToken,
                        LoyaltlyTransactionId = item.LoyaltlyTransactionId,
                        MemberCode = item.IdNumber,
                        MerchantId = item.MerchantId,
                        VpidTransactionId = item.VpidTransactionId
                    };
                    RetryRevertPartnerTransaction(revert, httpContext).Wait();
                });
            });
            taskListRevertExchange.Add(taskRunAllExchangeRedeemError);
            Task.WaitAll(taskListRevertExchange.ToArray());
        }

        private async Task RetryRevertPartnerTransaction(
            LoyaltyThirdPartyRevertPointInput input, HttpContext httpContext
        )
        {
            var retryNum = 5;
            while (retryNum != 0)
            {
                var result = await RevertPartnerTransaction(input, httpContext);
                if (result == true)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<bool> RevertPartnerTransaction(
            LoyaltyThirdPartyRevertPointInput input, HttpContext httpContext
        )
        {
            try
            {
                await _loyaltyThirdPartyService.RevertPoint(new LoyaltyThirdPartyRevertPointInput()
                {
                    AccessToken = input.AccessToken,
                    MemberCode = input.MemberCode,
                    LoyaltlyTransactionId = input.LoyaltlyTransactionId,
                    MerchantId = input.MerchantId,
                    VpidTransactionId = input.VpidTransactionId,
                }, httpContext);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task<ExchangePartnerResult> ExchangeInternalTransaction(
            string memberCode, string accessToken, string idNumber, double exchangeAmount, int merchantId, string orderCode, string loyaltlyTransactionId
        )
        {
            var requestReward = new RewardCreateExchangeTransactionInput()
            {
                TransactionCode = orderCode,
                ExchangeAmount = exchangeAmount,
                MemberCode = memberCode,
                MerchantId = merchantId,
                PartnerBindingTxId = loyaltlyTransactionId,
            };

            try
            {
                var rewardExchange = await _rewardExchangeTransactionService.CreateExchangeTransactionIntegration(requestReward);

                return new ExchangePartnerResult()
                {
                    Error = false,
                    ErrorCode = null,
                    Exception = null,
                    Timeout = false,
                    LoyaltlyTransactionId = loyaltlyTransactionId,
                    MerchantId = merchantId,
                    AccessToken = accessToken,
                    IdNumber = idNumber,
                    VpidTransactionId = orderCode,
                };
            }
            catch (Exception ex)
            {
                _logger.LogInformation("create exchange redeem " + memberCode + "_Error_Internal" + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return new ExchangePartnerResult()
                    {
                        Error = true,
                        ErrorCode = res.Code,
                        Exception = res.Message,
                        Timeout = false,
                        LoyaltlyTransactionId = loyaltlyTransactionId,
                        MerchantId = merchantId,
                        AccessToken = accessToken,
                        IdNumber = idNumber,
                        VpidTransactionId = orderCode,
                    };
                }
                else if (ex.Message == "YouAreNotInTheTestList")
                {
                    return new ExchangePartnerResult()
                    {
                        Error = true,
                        ErrorCode = "YouAreNotInTheTestList",
                        Exception = "Bạn không nằm trong danh sách thử nghiệm, vui lòng liên hệ số hotline để biết thêm chi tiết",
                        Timeout = false,
                        LoyaltlyTransactionId = loyaltlyTransactionId,
                        MerchantId = merchantId,
                        AccessToken = accessToken,
                        IdNumber = idNumber,
                        VpidTransactionId = orderCode,
                    };
                }
                else
                {
                    var errorCode = "SystemError";
                    var exception = "Exception";
                    if (ex.GetType() == typeof(LoyaltyException))
                    {
                        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                        errorCode = res.Code;
                        exception = res.Message;
                    }
                    return new ExchangePartnerResult()
                    {
                        Error = true,
                        ErrorCode = errorCode,
                        Exception = exception,
                        Timeout = false,
                        LoyaltlyTransactionId = loyaltlyTransactionId,
                        MerchantId = merchantId,
                        AccessToken = accessToken,
                        IdNumber = idNumber,
                        VpidTransactionId = orderCode,
                    };
                }
            }
        }

        private async Task<ExchangePartnerResult> ExchangePartnerTransaction(
            string memberCode, RewardCreateExchangeTransactionItems input, string orderCode, HttpContext httpContext
        )
        {
            try
            {
                var requestExchange = new LoyaltyThirdPartyPointExchangeInput()
                {
                    AccessToken = input.AccessToken,
                    ExchangeAmount = input.ExchangeAmount,
                    IdNumber = input.IdNumber,
                    MemberCode = memberCode,
                    MerchantId = input.MerchantId,
                };
                var rewardResult = await _loyaltyThirdPartyService.PointExchangeIntegration(requestExchange, httpContext, orderCode);
                return new ExchangePartnerResult()
                {
                    Error = false,
                    ErrorCode = null,
                    Exception = null,
                    Timeout = false,
                    LoyaltlyTransactionId = rewardResult.Items.Transaction.PartnerBindingTxId,
                    MerchantId = input.MerchantId,
                    AccessToken = input.AccessToken,
                    IdNumber = input.IdNumber,
                    VpidTransactionId = orderCode,
                    ExchangeAmount = input.ExchangeAmount
                };
            }
            catch (Exception ex)
            {
                _logger.LogInformation("create exchange redeem " + memberCode + "_Error" + JsonConvert.SerializeObject(ex));
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return new ExchangePartnerResult()
                {
                    Error = true,
                    ErrorCode = res.Code,
                    Exception = res.Message,
                    Timeout = false,
                    MerchantId = input.MerchantId,
                };
            }
        }

        private async Task<RedeemLoyaltyResult> RetryRedeemLoyaltyTransaction(
            RewardCreateExchangeAndRedeemTransactionInput input, string orderCode, HttpContext httpContext, int merchantId
        )
        {
            var errorCode = "SystemError";
            var exception = "SystemError";
            var retryNum = 5;
            var error = true;
            var errorLoyalty = false;
            var items = new List<DataRedeemTransaction>();
            while (retryNum != 0 && !errorLoyalty)
            {
                Thread.Sleep(1000);
                var redeemTransaction = await RedeemLoyaltyTransaction(input, orderCode, httpContext, merchantId);
                errorLoyalty = redeemTransaction.ErrorLoyalty;
                if (redeemTransaction.Error)
                {
                    retryNum--;
                    errorCode = redeemTransaction.ErrorCode;
                    exception = redeemTransaction.Exception;
                    _logger.LogInformation("create exchange redeem " + input.MemberCode + "_retry" + JsonConvert.SerializeObject(redeemTransaction));
                }
                else
                {
                    error = false;
                    retryNum = 0;
                    items = redeemTransaction.Items;
                }
            }
            return new RedeemLoyaltyResult()
            {
                Error = error,
                ErrorCode = errorCode,
                Exception = exception,
                Items = items.Count > 0 ? items : null,
                Timeout = false,
            };
        }


        private async Task<RedeemLoyaltyResult> RedeemLoyaltyTransaction(
            RewardCreateExchangeAndRedeemTransactionInput input, string orderCode, HttpContext httpContext, int merchantId
        )
        {
            var cts = new CancellationTokenSource();
            var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
            {
                MerchantId = merchantId,
                NationalId = input.MemberCode,
                OrderCode = orderCode,
                TokenAmount = input.Redeem.TotalAmount,
            };
            try
            {
                var resultBalance = await _rewardMemberService.GetBalanceMember(input.MemberCode);
                var sumExchangeAmount = input.ExchangeList.Select(x => x.ExchangeAmount).Sum(x => x);
                var detail = new DetailExchangeAndRedeem()
                {
                    CurrentBalance = resultBalance.TokenBalance,
                    ExchangeBalance = sumExchangeAmount,
                    RedeemBalance = input.Redeem.TotalAmount,
                };
                _logger.LogInformation("create exchange redeem " + input.MemberCode + "_balance" + JsonConvert.SerializeObject(detail));
                // If avalible token + exchange token < total request redeem then show error BalanceNotEnough
                //if (resultBalance.TokenBalance + sumExchangeAmount < input.Redeem.TotalAmount)
                //{
                //    //throw new ArgumentException("Token Balance is not enough to make this transaction");
                //    return new RedeemLoyaltyResult()
                //    {
                //        Error = true,
                //        ErrorCode = "BalanceNotEnough",
                //        Exception = "Token Balance is not enough to make this transaction",
                //        Items = null,
                //    };
                //}
                var requestRedeem = new RewardCreateGiftRedeemTransactionRequest()
                {
                    OrderCode = orderCode,
                    MerchantId = merchantId,
                    NationalId = input.MemberCode,
                    TotalRequestedAmount = input.Redeem.TotalAmount,
                };
                var resultRedeemReward = await _rewardGiftRedeemTransactionService.CreateRedeem(requestRedeem);
                var request = new LoyaltyCreateRedeemTransactionDto()
                {
                    Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"), //,input.Redeem.Date,
                    Description = input.Redeem.Description,
                    GiftCode = input.Redeem.GiftCode,
                    MemberCode = input.MemberCode,
                    Quantity = input.Redeem.Quantity,
                    TotalAmount = input.Redeem.TotalAmount,
                    TransactionCode = orderCode,
                };
                var result = await _loyaltyGiftTransactionsService.PostLoyaltyRedeem(request, httpContext);
                if (!string.IsNullOrWhiteSpace(result.Result.Exception) || !result.Result.SuccessedRedeem)
                {
                    await RetryRevertTokenRedeem(requestRevert);
                    return new RedeemLoyaltyResult()
                    {
                        Error = true,
                        ErrorCode = result.Result.Exception,
                        Exception = result.Result.Messages,
                        Items = null,
                        Timeout = false,
                        ErrorLoyalty = true,
                    };
                }
                return new RedeemLoyaltyResult()
                {
                    Error = false,
                    Items = result.Result.Items,
                    ErrorLoyalty = false,
                };
            }
            catch (WebException ex)
            {
                var errorCode = "SystemError";
                var exception = "WebException";
                var errorLoyalty = false;
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    errorCode = res.Code;
                    exception = res.Message;
                    errorLoyalty = true;
                }
                else if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    errorCode = res.Code;
                    exception = res.Message;
                }
                _logger.LogInformation("create exchange redeem " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));
                await RetryRevertTokenRedeem(requestRevert);
                return new RedeemLoyaltyResult()
                {
                    Error = true,
                    ErrorCode = errorCode,
                    Exception = exception,
                    Items = null,
                    Timeout = false,
                    ErrorLoyalty = errorLoyalty,
                };
            }
            catch (TaskCanceledException ex)
            {
                var errorCode = "SystemError";
                var exception = "TaskCanceledException";
                var errorLoyalty = false;
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    errorCode = res.Code;
                    exception = res.Message;
                    errorLoyalty = true;
                }
                else if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    errorCode = res.Code;
                    exception = res.Message;
                }
                _logger.LogInformation("create exchange redeem " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));
                await RetryRevertTokenRedeem(requestRevert);
                if (!cts.Token.IsCancellationRequested)
                {
                    return new RedeemLoyaltyResult()
                    {
                        Error = true,
                        ErrorCode = errorCode,
                        Exception = exception,
                        Items = null,
                        Timeout = true,
                        ErrorLoyalty = errorLoyalty,
                    };
                }
                else
                {
                    return new RedeemLoyaltyResult()
                    {
                        Error = true,
                        ErrorCode = errorCode,
                        Exception = exception,
                        Items = null,
                        Timeout = true,
                        ErrorLoyalty = errorLoyalty,
                    };
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                var errorCode = "SystemError";
                var exception = "WebException";
                var errorLoyalty = false;
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    errorCode = res.Code;
                    exception = res.Message;
                    errorLoyalty = true;
                }
                else if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    errorCode = res.Code;
                    exception = res.Message;
                }
                _logger.LogInformation("create exchange redeem " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));
                await RetryRevertTokenRedeem(requestRevert);
                return new RedeemLoyaltyResult()
                {
                    Error = true,
                    ErrorCode = errorCode,
                    Exception = exception,
                    Items = null,
                    Timeout = false,
                    ErrorLoyalty = errorLoyalty,
                };
            }
        }

        private async Task RetryRevertTokenRedeem(RewardRevertGiftRedeemTransactionRequest request)
        {
            var retryNum = 5;
            while (retryNum != 0)
            {
                var result = await RevertTokenRedeem(request);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<bool> RevertTokenRedeem(RewardRevertGiftRedeemTransactionRequest request)
        {
            try
            {
                await _rewardGiftRedeemTransactionService.RevertRedeem(request);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task RetryRevertTokenExchange(RewardRevertExchangeTransactionInput request)
        {
            var retryNum = 8;
            while (retryNum != 0)
            {
                var result = await RevertTokenExchange(request);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<bool> RevertTokenExchange(RewardRevertExchangeTransactionInput request)
        {
            try
            {
                _logger.LogInformation("exchange and redeem revert token exchange" + JsonConvert.SerializeObject(request));
                await _rewardExchangeTransactionService.RevertExchangeTransactionIntegration(request);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private string genOrderCode()
        {
            return LoyaltyHelper.GenAirlineTxCode("MIL");
        }

        private bool CheckValidInput(RewardCreateExchangeAndRedeemTransactionInput input)
        {
            if (input.Redeem == null || input.ExchangeList == null || (input.ExchangeList != null && input.ExchangeList.Count == 0))
            {
                return false;
            }
            var checkExchangeAmountValid = input.ExchangeList.Where(x => x.ExchangeAmount <= 0).Select(x => x.ExchangeAmount).ToList();
            if (checkExchangeAmountValid.Count() > 0)
            {
                return false;
            }
            //var sumExchangeAmount = input.ExchangeList.Select(x => x.ExchangeAmount).Sum(x => x);
            //if (sumExchangeAmount < input.Redeem.TotalAmount)
            //{
            //    return false;
            //}
            return true;
        }

        public async Task<CreateExchangeMilesAirlineOutput> CreateExchangeMilesAirline(CreateExchangeMilesAirlineRequest input)
        {
            _logger.LogInformation($"CreateExchangeMilesAirline >> Request : {JsonConvert.SerializeObject(input)}");
            if (input.TotalAmount < 0 || input.Miles < 0)
            {
                return new CreateExchangeMilesAirlineOutput { ErrorCode = AirlineIntegrationErrorCodes.MilesAndAmountNotValid, Message = "Số điểm LynkiD và số điểm đối tác đang không hợp lệ" };
            }

            var vendorsInLoy = await GetExchangeVendors();
            var foundVendor = vendorsInLoy
                .FirstOrDefault(x => x.MerchantId == input.MerchantId);
            if (foundVendor == null)
            {
                _logger.LogError(" >> CreateExchangeMilesAirline >> MerchantId#" + input.MerchantId + " was not configured in Vendor list in Loyalty");
                var result = new CreateExchangeMilesAirlineOutput()
                {
                    ErrorCode = AirlineIntegrationErrorCodes.MerchantMisconfigured,
                    Message = "Merchant thực hiện đổi điểm đang chưa được cấu hình đúng",
                };
                return result;
            }
            var merchant = await _rewardMerchantService.GetFullInfoMerchantById(new GetFullInfoMerchantByIdInput
            {
                MerchantId = input.MerchantId
            });
            if (merchant == null)
            {
                _logger.LogError(" >> CreateExchangeMilesAirline >> MerchantId#" + input.MerchantId + " - Merchant Not Exist");
                var result = new CreateExchangeMilesAirlineOutput()
                {
                    ErrorCode = AirlineIntegrationErrorCodes.MerchantNotExist,
                    Message = "Merchant không tồn tại",
                };
                return result;
            }

            if (!merchant.RedeemExchangeRate.HasValue || merchant.MerchantUnit <= 0 || merchant.PartnerPointExchangeType != "MILE")
            {
                _logger.LogError(" >> CreateExchangeMilesAirline >> Airline#" + input.MerchantId + " - Merchant Misconfigured");
                var result = new CreateExchangeMilesAirlineOutput()
                {
                    ErrorCode = AirlineIntegrationErrorCodes.MerchantMisconfigured,
                    Message = "Merchant thực hiện đổi điểm đang chưa được cấu hình đúng",
                };
                return result;
            }
            // Dựa vào inputTotalAmount, tính toán với exchangerange/MerchantUnit, rồi quy ra MILES (số nguyên) xem match ko.
            var calculatedMiles = (int)((input.TotalAmount * merchant.RedeemExchangeRate.Value) / merchant.MerchantUnit);
            if (input.Miles != calculatedMiles)
            {
                _logger.LogError(" >> CreateExchangeMilesAirline >> Member: " + input.MemberCode + " >> " + input.MerchantId + " - Input Miles and TotalAmount not match");
                var result = new CreateExchangeMilesAirlineOutput()
                {
                    ErrorCode = AirlineIntegrationErrorCodes.MilesAndTotalAmountNotMatch,
                    Message = "Số điểm LynkiD và điểm nhận bên đối tác không khớp",
                };
                return result;
            }

            // Check số dư LynkiD của khách hàng có thoả mãn điều kiện không?
            var checkCreditBalanceOutput =
            await _rewardMemberService.GetCreditBalanceByMemberCode(new GetCreditBalanceByMemberCodeInput()
            {
                MemberCode = input.MemberCode
            });
            if (checkCreditBalanceOutput is { Items: { } } && checkCreditBalanceOutput.Items.CreditBalance > 0 && input.TotalAmount > 0)
            {
                _logger.LogError(" >> CreateExchangeMilesAirline >> Member: " + input.MemberCode + " >> Cannot Exchange Miles Airline Because User has CreditBalance");
                var result = new CreateExchangeMilesAirlineOutput()
                {
                    ErrorCode = AirlineIntegrationErrorCodes.BalanceNotEnough,
                    Message = "Số điểm LynkiD không đủ để thực hiện giao dịch",
                };
                return result;
            }

            var resultBalance = await _rewardMemberService.GetBalanceMember(input.MemberCode);
            if (resultBalance.TokenBalance < input.TotalAmount)
            {
                _logger.LogError(" >> CreateExchangeMilesAirline >> Member: " + input.MemberCode + " >> Cannot Exchange Miles Airline Because User does nt have enough balance");
                var result = new CreateExchangeMilesAirlineOutput()
                {
                    ErrorCode = AirlineIntegrationErrorCodes.BalanceNotEnough,
                    Message = "Số điểm LynkiD không đủ để thực hiện giao dịch",
                };
                return result;
            }

            var memberId = resultBalance.MemberId;
            var lynkidTransactionCode = LoyaltyHelper.GenerateRandomString(15); // Ben SIA dang accept only 15 char. Phần thừa sẽ bị cắt
            var req = new CreditNonAirMilesCallerInput()
            {
                FirstName = input.MemberFirstName,
                LastName = input.MemberLastName,
                Miles = input.Miles,
                SIAMemberCode = input.AirlineMemberCode,
                LynkiDTransactionCode = lynkidTransactionCode
            };
            if (!string.IsNullOrWhiteSpace(input.PromotionCode))
            {
                req.promotionCode = input.PromotionCode;
                req.promotionAwardDate = input.PromotionDate?.ToString("yyyy-MM-dd");
                req.promotionMileAwarded = input.PromotionMiles;
            }

            _logger.LogInformation(" >> CreateExchangeMilesAirline >> Member: " + input.MemberCode + " - Making call to reward network...");
            var requestMileExchange = new ExchangeLynkiDToPartnerPointInput()
            {
                MerchantId = merchant.Id,
                MemberCode = input.MemberCode,
                TransactionCode = lynkidTransactionCode,
                TokenAmount = Convert.ToDouble(input.TotalAmount)
                ,
                Cif = input.AirlineMemberCode,
                SourceExchange = "LynkIDApp",
                ExtraData = JsonConvert.SerializeObject(new {
                    FirstName = input.MemberFirstName,
                    LastName = input.MemberLastName,
                    AirlineMemberCode = input.AirlineMemberCode,
                    PromotionCode = input.PromotionCode,
                    PromotionDate = input.PromotionDate,
                    PromotionValue = input.PromotionMiles,
                }),
                PartnerBindingTxID = null
            };
            var resultRedeemReward = await _rewardMemberService.ExchangeLynkiDToPartnerPoint(requestMileExchange);
            if (resultRedeemReward.result == 202)
            {
                _logger.LogError($"CreateExchangeMilesAirline >> Create redeem reward with status 202 {JsonConvert.SerializeObject(resultRedeemReward)}");
                var result = new CreateExchangeMilesAirlineOutput()
                {
                    ErrorCode = AirlineIntegrationErrorCodes.Error202Blockchain,
                    Message = "Có lỗi xảy ra khi thực hiện đổi điểm: ERROR 202",
                };
                return result;
            }

            PartnerExchangeOutput createAirlineMilesExchangeResult = null;
            _logger.LogInformation("Step 1 - Call to Partner To exchange  for #" + input.MemberCode + " - and " + input.MerchantId);
            if (foundVendor.VendorName == "SIA" && merchant.PartnerPointExchangeType == "MILE")
            {
                createAirlineMilesExchangeResult = await _siaService.CreditNonAirMiles(input.MemberCode, req);
            }
            _logger.LogInformation("Step 1 - Done Call to SIA CreditNonAirMiles for #"
                                   + input.MemberCode + " - and " + input.MerchantId + " - Response: " + JsonConvert.SerializeObject(createAirlineMilesExchangeResult));

            if (createAirlineMilesExchangeResult.Code == AirlineIntegrationErrorCodes.Success)
            {
                // Cap nhat PartnerBindingTxId
                try
                {
                    _logger.LogInformation(" >> Credit Non Airs successfully >> Updating PartnerBindingTxId " + createAirlineMilesExchangeResult.TransactionId);
                    await _rewardMemberService.UpdatePartnerBindingTransactionId(new UpdatePartnerBindingTransactionIdInput()
                    {
                        TransactionCode = lynkidTransactionCode,
                        PartnerBindingTxID = createAirlineMilesExchangeResult.TransactionId
                    });
                }
                catch (Exception e)
                {
                    _logger.LogError(" Error Update PartnerBindingTxId " + createAirlineMilesExchangeResult.TransactionId + " to TransactionCode#" + lynkidTransactionCode);
                }
                return new CreateExchangeMilesAirlineOutput
                {
                    ErrorCode = createAirlineMilesExchangeResult.Code,
                    Message = createAirlineMilesExchangeResult.Message,
                    Result = new ExchangeMilesAirlineDto
                    {
                        ExchangedValue = input.Miles,
                        LynkIdTransactionCode = lynkidTransactionCode,
                        UsedToken = input.TotalAmount,
                        PartnerTransactionCode = createAirlineMilesExchangeResult.TransactionId
                    }
                };
            }
            if (createAirlineMilesExchangeResult.Code == AirlineIntegrationErrorCodes.Timeout || createAirlineMilesExchangeResult.Code == AirlineIntegrationErrorCodes.GeneralError)
            {
                _logger.LogError(" CreateExchangeMilesAirline >> Timeout Or Unknown error >> " + JsonConvert.SerializeObject(createAirlineMilesExchangeResult));
                var result = new CreateExchangeMilesAirlineOutput()
                {
                    ErrorCode = createAirlineMilesExchangeResult.Code,
                    Message = createAirlineMilesExchangeResult.Message
                };
                return result;
            }
            //  Nếu mã lỗi ko phải là success, ko phải timeout và không phải unknown, thì revert giao dịch
            _logger.LogError(" CreateExchangeMilesAirline >> Not successful >> " + JsonConvert.SerializeObject(createAirlineMilesExchangeResult));
            _logger.LogInformation(" CreateExchangeMilesAirline >> retryRevertToken start");
            var requestRevert = new RevertExchangeLynkiDToPartnerPointInput()
            {
                MerchantId = input.MerchantId,
                MemberCode = input.MemberCode,
                TransactionCode = lynkidTransactionCode,
                Message = "",
                MemberId = (int)memberId
            };
            await retryRevertToken(requestRevert);
            _logger.LogInformation(" CreateExchangeMilesAirline >> retryRevertToken end");
            return new CreateExchangeMilesAirlineOutput()
            {
                ErrorCode = createAirlineMilesExchangeResult.Code,
                Message = createAirlineMilesExchangeResult.Message
            };
        }

        public async Task<List<GetThirdPartyGiftVendorForViewInner>> GetExchangeVendors()
        {
            var cacheKey = "GetExchangeVendors";
            var saved = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(saved))
            {
                try
                {
                    var ret = JsonConvert.DeserializeObject<List<GetThirdPartyGiftVendorForViewInner>>(saved);
                    return ret;
                }
                catch (Exception e)
                {
                    _logger.LogError(" GetExchangeVendors >> Error parsing cached string >> " + saved);
                }
            }

            var fromDB = await _getLoyaltyData.GetListThirdPartyVendors(new GetListThirdpartyVendorInput()
            {
                TypeFilter = "Exchange",
                SkipCount = 0,
                MaxResultCount = 100,
                StatusFilter = "A"
            });
            // Set to cache
            var xRet = fromDB.Items.Select(x => x.ThirdPartyGiftVendor).ToList();
            await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(xRet), new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromDays(1)));
            return xRet;
        }
        private void GetErrorValidation(string errorCode, string errorMessage)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = errorMessage
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }

        private async Task retryRevertToken(RevertExchangeLynkiDToPartnerPointInput request)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await revertToken(request);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<Boolean> revertToken(RevertExchangeLynkiDToPartnerPointInput request)
        {
            try
            {
                await _rewardMemberService.RevertExchangeLynkiDToPartnerPoint(request);
                return true;
            }
            catch
            {
                return false;
            }
        }

    }

    public class DetailExchangeAndRedeem
    {
        public decimal CurrentBalance { get; set; }
        public decimal ExchangeBalance { get; set; }
        public decimal RedeemBalance { get; set; }
    }
}
