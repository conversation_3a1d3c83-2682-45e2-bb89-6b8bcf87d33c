﻿using AKC.MobileAPI.DTO.Reward.GiveToken;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Reward
{
    public interface IRewardGiveTokenService
    {
        Task<RewardGiveTokenUserVerifyingOutput> UserVerifying(RewardGiveTokenUserVerifyingInput input);
        Task<RewardGiveTokenTransferTransactionOutput> TransferTransaction(RewardGiveTokenTransferTransactionInput input);
    }
}
