﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Order
{
    public class LoyaltyPurchaseAgentInput
    {
        public PaymentTransactionMemberAgent Member { get; set; }
        public PaymentTransactionOrderAgent Order { get; set; }
        public List<PaymentTransactionDetailAgent> OrdersDetail { get; set; }
    }

    public class PaymentTransactionMemberAgent
    {
        public string MemberCode { get; set; }
        public string StandardMemberCode { get; set; }
        public string RankTypeCode { get; set; }
        public string FullMemberTypeCode { get; set; }
        public string FullRegionCode { get; set; }
        public string FullSalesRegionCode { get; set; }
        public string FullChannelTypeCode { get; set; }
        public string MLMDealerChannel { get; set; }
        public string MLMDistributionChannel { get; set; }
        public bool MLMApplied { get; set; }
        public bool SpecialRewarding { get; set; }
        public string RankCode { get; set; }
    }

    public class PaymentTransactionOrderAgent
    {
        public int TenantId { get; set; }
        public int MerchantId { get; set; }
        public string Reason { get; set; }
        public DateTime BusinessTime { get; set; }
        public string OriginalOrders { get; set; }
        public string OrderCode { get; set; }
        public decimal TotalAmountIncludeTax { get; set; }
        public decimal TotalAmountBeforeTax { get; set; }
        public decimal TotalCalcAmount { get; set; }
        public decimal TotalTaxAmount { get; set; }
        public string Source { get; set; }
        public int TotalDetailLine { get; set; }
        public string SourceType { get; set; }
        public decimal TotalLoyaltyDiscount { get; set; }
        public string DistributorCode { get; set; }
        public string FullDistributionChannelCode { get; set; }
    }

    public class PaymentTransactionDetailAgent
    {
        public int SeqNo { get; set; }
        public string ProductCode { get; set; }
        public string FullDeptCode { get; set; }
        public string FullClassCode { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal AmountBeforeTax { get; set; }
        public decimal AmountIncludeTax { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal CalcAmount { get; set; }
        public bool HotDeal { get; set; }
        public string OriginalProductCode { get; set; }
        public decimal BeforeDiscount { get; set; }
        public decimal Discount { get; set; }
        public decimal LoyaltyDiscount { get; set; }
        public decimal RedemptionAmount { get; set; }
        public bool AllowIncentive { get; set; }
    }
}
