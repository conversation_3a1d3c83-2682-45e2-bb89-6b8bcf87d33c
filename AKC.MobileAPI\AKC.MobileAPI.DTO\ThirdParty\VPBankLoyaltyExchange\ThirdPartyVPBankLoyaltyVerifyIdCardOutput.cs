﻿using AKC.MobileAPI.DTO.Loyalty;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.ThirdParty.VPBankLoyaltyExchange
{
    public class ThirdPartyVPBankLoyaltyVerifyIdCardOutput
    {
        public string OtpCode { get; set; }
    }

    public class ThirdPartyVPBankLoyaltyVerifyIdCardDto
    {
        public bool IsChangedLoyalty { get; set; }
        public LoyaltyResponse<ThirdPartyVPBankLoyaltyVerifyIdCardOutput> ResponseLoyalty { get; set; }
    }
}
