﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Payment
{
    public class GetFeeCashOutOutput
    {
        public FeeCashOut Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class FeeCashOut
    {
        public decimal Fee { get; set; }
    }
}
