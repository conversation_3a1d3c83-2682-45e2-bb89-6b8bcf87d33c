using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Localization;

namespace AKC.MobileAPI.Controllers.Webstore
{
    [ApiController]
    [Route("api")]
    [ApiExplorerSettings(GroupName = "Captcha", IgnoreApi = false)]
    public class WebstoreCaptchaController : ControllerBase
    {
        private readonly IDistributedCache _cache;
        private readonly IStringLocalizer<WebstoreCaptchaController> _localizer;

        public WebstoreCaptchaController(IDistributedCache cache, IStringLocalizer<WebstoreCaptchaController> localizer)
        {
            _cache = cache;
            _localizer = localizer;
        }

        [HttpGet("captcha/generate")]
        public async Task<IActionResult> GenerateCaptcha()
        {
            var ip = HttpContext.Connection.RemoteIpAddress?.ToString();
            var throttleKey = $"captcha:throttle:{ip}";
            var lastRequest = await _cache.GetStringAsync(throttleKey);

            if (lastRequest != null && (DateTime.UtcNow - DateTime.Parse(lastRequest)).TotalSeconds < 5)
                return StatusCode(429, "Please wait 5 seconds before generating a new CAPTCHA");

            await _cache.SetStringAsync(throttleKey, DateTime.UtcNow.ToString(),
                new DistributedCacheEntryOptions { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1) });

            var random = new Random();
            int num1, num2, answer;
            string operatorText;

            // Random chọn phép cộng hoặc trừ
            var isAddition = random.Next(2) == 0; // 0: cộng, 1: trừ
            if (isAddition)
            {
                num1 = random.Next(1, 10);
                num2 = random.Next(1, 5);
                answer = num1 + num2;
                operatorText = _localizer["Plus"].Value;
            }
            else
            {
                num2 = random.Next(1, 5); // num2 nhỏ để trừ
                num1 = random.Next(num2 + 1, 10); // num1 > num2 để không ra số âm
                answer = num1 - num2;
                operatorText = _localizer["Minus"].Value;
            }

            // Lấy danh sách Items từ resource file
            var items = _localizer["Items"].Value.Split(',');
            var randomPick = items[random.Next(items.Length)];

            // Tạo câu hỏi theo ngôn ngữ
            var question =
                $"{num1} {randomPick} {operatorText} {num2} {randomPick} {_localizer["Equals"]} {randomPick}?";

            var captchaKey = $"captcha:{Guid.NewGuid().ToString()}";
            await _cache.SetStringAsync(captchaKey, answer.ToString(),
                new DistributedCacheEntryOptions { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5) });

            return Ok(new { captchaKey, question });
        }
    }
}