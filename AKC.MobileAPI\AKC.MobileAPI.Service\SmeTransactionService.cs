﻿using AKC.MobileAPI.DTO.Webstore;
using AKC.MobileAPI.Service.Abstract;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.Service.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.IdentityModel.Tokens;
using System.Threading;
using System.Net;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using System.Linq;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.Rewards;
using AKC.MobileAPI.DTO.Reward.CashoutTransaction;
using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.GiftStoreExtends;
using Microsoft.AspNetCore.Http;

namespace AKC.MobileAPI.Service
{
    public class SmeTransactionService : BaseLoyaltyService, ISmeTransactionService
    {
        private readonly IConfiguration _configuration;
        private readonly IDistributedCache _cache;
        private readonly IRewardMemberService _rewardService;
        private readonly ILogger<SmeTransactionService> _logger;
        public readonly ILoyaltyGiftTransactionsService _loyaltyGiftTransactionsService;
        private ILinkIdLoyaltyGiftStoreExtendsService _merchantGiftStoreService;
        private readonly ILoyaltyRewardsService _loyaltyRewardServc;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly decimal _amounConfig;
        private readonly string _secretKey;
        private readonly IUserService _userService;

        public SmeTransactionService(IConfiguration configuration,
            IDistributedCache cache,
            ILinkIdLoyaltyGiftStoreExtendsService l,
            ILoyaltyRewardsService l2,
            IRewardMemberService rewardService,
            ILogger<SmeTransactionService> logger,
            ILoyaltyGiftTransactionsService loyaltyGiftTransactionsService,
            IExceptionReponseService exceptionReponseService,
            IUserService userService): base(configuration, cache)
        {
            _configuration = configuration;
            _cache = cache;
            _rewardService = rewardService;
            _logger = logger;
            _loyaltyGiftTransactionsService = loyaltyGiftTransactionsService;
            _exceptionReponseService = exceptionReponseService;
            _amounConfig = _configuration.GetSection("SME_THRESHOLD_MONEY").Value == null ? 200 : Convert.ToInt32(_configuration.GetSection("SME_THRESHOLD_MONEY").Value);
            _secretKey = _configuration.GetSection("WebStoreSecretKey").Value == null ? "zXq9vzGORhNClxV3NaK4ubkFdkAI0HpLw0jZPXwR" : _configuration.GetSection("WebStoreSecretKey").Value;
            _userService = userService;
            _merchantGiftStoreService = l;
            _loyaltyRewardServc = l2;
        }

        public async Task<WebStoreCreateRedeemGiftTransactionDto> CreateRedeemGiftTransaction(WebStoreCreateRedeemGiftTransactionInput input)
        {
            try
            {
                _logger.LogInformation($"SmeTransactionService_CreateRedeemGiftTransaction Input: {JsonConvert.SerializeObject(input)} ");
                var smeObj = await _rewardService.WebstoreGetFullSmeInfo(new WebstoreGetFullSmeInfoRequest
                {
                    SmeCode = input.MemberCode
                });
                _logger.LogInformation($"SmeTransactionService_CreateRedeemGiftTransaction smeObj: {JsonConvert.SerializeObject(input)} ");
                // check tồn tại
                if (smeObj == null || (smeObj != null && smeObj.Item == null))
                {
                    return new WebStoreCreateRedeemGiftTransactionDto { Code = WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotFound, Message = "Not found" };
                }
                if (input.TotalAmount > 0)
                {
                    var memberBalance = await _rewardService.WebstoreGetSmeBalance(new WebstoreGetSmeBalanceRequest()
                    {
                        SmeCode = input.MemberCode
                    });
                    if (memberBalance?.Item == null)
                    {
                        return new WebStoreCreateRedeemGiftTransactionDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REDEEM_INTERNAL_SERVER, Message = "Internal Server" };
                    }
                    if (memberBalance.Item.Balance < input.TotalAmount)
                    {
                        return new WebStoreCreateRedeemGiftTransactionDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REDEEM_BALANCE_INSUFFICIENT, Message = "Insufficient funds" };
                    }
                }
                var merchantIdFromGiftCode = await _loyaltyGiftTransactionsService.GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
                {
                    GiftCode = input.GiftCode
                });
                _logger.LogInformation($"GetMerchantIdFromGiftCode({input.GiftCode})___Result:{JsonConvert.SerializeObject(merchantIdFromGiftCode)}");
                var merchantId = Convert.ToInt32(_configuration.GetSection("Reward" + MerchantNameConfig.VPID + ":MerchantId").Value);
                if (merchantIdFromGiftCode == null || !merchantIdFromGiftCode.Success || merchantIdFromGiftCode.Result == null || !merchantIdFromGiftCode.Result.MerchantId.HasValue)
                {
                    _logger.LogError($"SmeTransactionService_CreateRedeemGiftTransaction Error: Cannot find merchant ");
                    throw new Exception("Cannot find merchant");
                }
                var orderCode = genOrderCode(input.GiftCode, input.MemberCode);
                // gọi lên verify
                var loyaltyRedeemRequest = new MerchantGiftCreateRedeemInWebInputDto()
                {
                    Description = input.Description,
                    GiftCode = input.GiftCode,
                    MemberCode = input.MemberCode,
                    Quantity = input.Quantity.Value,
                    TotalAmount = input.TotalAmount.Value,
                    Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    TransactionCode = orderCode,
                    OtpSession = input.SessionId,
                    VpoCifCode = smeObj.Item.Code,
                    RedeemSource = "LynkiDWebApp",
                    MerchantIdRedeem = merchantIdFromGiftCode.Result.MerchantId
                };
                var createRedeemRequest = await _loyaltyGiftTransactionsService.VerifyOrCreateRedeemOrder(loyaltyRedeemRequest);
                _logger.LogInformation($"SmeTransactionService_CreateRedeemGiftTransaction respone {JsonConvert.SerializeObject(createRedeemRequest)}");
                if ((!createRedeemRequest.Success || (createRedeemRequest.Success && !createRedeemRequest.Result.IsSuccess)))
                {
                    _logger.LogError(" >> SmeTransactionService_CreateRedeemGiftTransaction createRedeemRequest: " + JsonConvert.SerializeObject(createRedeemRequest));
                    // Can not redeem at this time
                    return new WebStoreCreateRedeemGiftTransactionDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REDEEM_CANT_REDEEM_THIS_TIME, Message = "Can't redeem this time" };
                }
                bool isSendOtp = false;
                if (input.TotalAmount > _amounConfig)
                {
                    // đoạn này check xem có lớn hơn giá trị amounConfig không thì gửi OTP nè
                    await _rewardService.SendOtp(new RewardMemberSendOtpInput()
                    {
                        PhoneNumber = smeObj.Item.RepPhone,
                        SessionId = input.SessionId,
                        SmsType = "RedeemGift"
                    });
                    isSendOtp = true;
                }
                // ghi lại cache redeem.
                var requestInCache = new MerchantGiftCreateRedeemInWebCache()
                {
                    Description = input.Description,
                    GiftCode = input.GiftCode,
                    MemberCode = input.MemberCode,
                    Quantity = input.Quantity.Value,
                    TotalAmount = input.TotalAmount.Value,
                    Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    TransactionCode = orderCode,
                    OtpSession = input.SessionId,
                    VpoCifCode = smeObj.Item.Code,
                    RedeemSource = "LynkiDWebApp",
                    MerchantIdRedeem = merchantIdFromGiftCode.Result.MerchantId,
                    Rephone = smeObj.Item.RepPhone,
                    SmeId = smeObj.Item.SmeId
                };
                await _cache.SetStringAsync("RequestRedeem_" + input.MemberCode + "_" + input.SessionId,
                    JsonConvert.SerializeObject(requestInCache), new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromMinutes(10)));
                return new WebStoreCreateRedeemGiftTransactionDto { IsSendOtp = isSendOtp, IsVerifyPassWord = !isSendOtp, SessionId = input.SessionId };
            }
            catch (Exception ex)
            {
                _logger.LogError($"SmeTransactionService_CreateRedeemGiftTransaction Error: {ex.Message} __ STACKE: {ex.StackTrace} ");
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return new WebStoreCreateRedeemGiftTransactionDto { Code = res.Code,  Message = res.Message };
                }
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    if ("MaxOtpSendInput".Equals(res.Code))
                    {
                        return new WebStoreCreateRedeemGiftTransactionDto
                        {
                            Code = res.Code,
                            Message = "Thao tác gián đoạn do bạn đã thực hiện gửi OTP quá nhiều lần. Vui lòng chờ và thực hiện lại sau nhé."
                        };
                    }
                    return new WebStoreCreateRedeemGiftTransactionDto { Code = res.Code, Message = res.Message };
                }
                return new WebStoreCreateRedeemGiftTransactionDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REDEEM_INTERNAL_SERVER, Message = "Internal Server" };
            }
        }
        private string genOrderCode(string orderCode, string memberCode)
        {
            return LoyaltyHelper.GenTransactionCode("WebStore" + orderCode + memberCode + DateTime.Now.Ticks);
        }

        public async Task<SmeRedeemGiftDto> VerifyRedeemGiftTransaction(VerifyRedeemGiftTransactionInput input, HttpContext context)
        {
            var savedString = await _cache.GetStringAsync("RequestRedeem_" + input.MemberCode + "_" + input.SessionId);
            if (string.IsNullOrWhiteSpace(savedString))
            {
                return new SmeRedeemGiftDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REQUEST_REDEEM_INVALID, Message = "Thông tin không hợp lệ hoặc hết hạn. Vui lòng thực hiện lại từ đầu." };
            }
            var requestRedeem = JsonConvert.DeserializeObject<MerchantGiftCreateRedeemInWebCache>(savedString);
            if (requestRedeem.TotalAmount > _amounConfig)
            {
                if(string.IsNullOrEmpty(input.OtpCode))
                {
                    return new SmeRedeemGiftDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REQUEST_OTP_INVALID, Message = "Vui lòng điền vào OTP hợp lệ." };
                }    
                _logger.LogInformation($" {input.SessionId} SmeTransactionService_VerifyRedeemGiftTransaction redeem with otp ");
                var checkOtp = await _rewardService.VerityOtp(new RewardMemberVerifyOtpInput
                {
                    OtpCode = input.OtpCode,
                    PhoneNumber = requestRedeem.Rephone,
                    SessionId = input.SessionId,
                    SmsType = "RedeemGift"
                });
                if (checkOtp.result != 200)
                {
                    return new SmeRedeemGiftDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REQUEST_OTP_INVALID, Message = "OTP không chính xác" };
                }
            }
            else
            {
                _logger.LogInformation($" {input.SessionId} SmeTransactionService_VerifyRedeemGiftTransaction redeem with AccessToken ");
                var checkAccessTokenRedeem = ValidateAccessTokenRedeem(input.AccessTokenRedeem);
                if (!string.IsNullOrEmpty(checkAccessTokenRedeem.Code))
                {
                    return checkAccessTokenRedeem;
                }
            }
            var memberBalance = await _rewardService.WebstoreGetSmeBalance(new WebstoreGetSmeBalanceRequest()
            {
                SmeCode = input.MemberCode
            });
            if (memberBalance?.Item == null)
            {
                return new SmeRedeemGiftDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REDEEM_INTERNAL_SERVER, Message = "Có lỗi xảy ra" };
            }
            if (memberBalance.Item.Balance < requestRedeem.TotalAmount)
            {
                return new SmeRedeemGiftDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REDEEM_BALANCE_INSUFFICIENT, Message = "Khách hàng không đủ số dư để thực hiện giao dịch này" };
            }
            //Lấy từ cache hoặc gọi xuống loyalty để check category type của quà
            var categoryTypeCode = string.Empty;
            try
            {
                var keyOfCategoryType = "MobileApi_GiftCategoryType_" + requestRedeem.GiftCode;
                categoryTypeCode = await _cache.GetStringAsync(keyOfCategoryType);
                //Không lấy đc từ cache thì get từ loyalty
                if (string.IsNullOrEmpty(categoryTypeCode))
                {
                    var inputGetGiftCategoryType = new LoyaltyGetGiftCategoryTypeInput()
                    {
                        GiftCode = requestRedeem.GiftCode
                    };
                    _logger.LogInformation($" {input.SessionId} GET_GIFT_CATEGORY_TYPE_CODE start");
                    var getGiftCategoryTypeResult = await GetLoyaltyAsync<LoyaltyGetGiftCategoryTypeOutput>(LoyaltyApiUrl.GET_GIFT_CATEGORY_TYPE_CODE, inputGetGiftCategoryType);
                    _logger.LogInformation($" {input.SessionId} GET_GIFT_CATEGORY_TYPE_CODE end");
                    if (getGiftCategoryTypeResult.Success)
                    {
                        categoryTypeCode = getGiftCategoryTypeResult.Result.GiftCategoryTypeCode;
                        await SetCacheByKeyValueExpiredInMinute(keyOfCategoryType, getGiftCategoryTypeResult.Result.GiftCategoryTypeCode, 30);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($" {input.SessionId} GET_GIFT_CATEGORY_TYPE_CODE error: " + ex.Message);
                var result = new SmeRedeemGiftDto()
                {
                    Code = "CantGetGiftCategoryType",
                    Message = "Can't get gift category type"
                };
                return result;
            }
            // LINKID-3864 Đổi quà TopupPhone hoặc TopupData thì không cho nhập Quantity > 1
            if (!string.IsNullOrEmpty(categoryTypeCode)
                && (categoryTypeCode.ToUpper() == GiftCategoryTypeCode.TopUpData || categoryTypeCode.ToUpper() == GiftCategoryTypeCode.TopUpPhone)
                && requestRedeem.Quantity > 1)
            {
                var result = new SmeRedeemGiftDto()
                {
                    Code = "QuantityMustBeOne",
                    Message = "Quantity must be one with TopUpPhone or TopUpData"
                };

                return result;
            }
            //  Check xem có Creditbalance ko, nếu có thì khôgn cho redeem. FIXME TODO Uncomment after Manh done the update in Operator side
            // var checkCreditBalanceOutput =
            //     await _rewardService.GetCreditBalanceByMemberCode(new GetCreditBalanceByMemberCodeInput()
            //     {
            //         MemberCode = input.MemberCode
            //     });
            // if (checkCreditBalanceOutput is { Items: { } } && checkCreditBalanceOutput.Items.CreditBalance > 0 && requestRedeem.TotalAmount > 0)
            // {
            //     _logger.LogError($" {input.SessionId}  >> CreateRedeemTransaction >> Member: " + input.MemberCode + " >> GiftCode: " + requestRedeem.GiftCode + " >> Cannot Redeem Because User has CreditBalance");
            //     var result = new SmeRedeemGiftDto()
            //     {
            //         Code = "BalanceNotEnough",
            //         Message = "Token Balance is not enough to make this transaction"
            //     };
            //     return result;
            // }
            var merchantId = requestRedeem.MerchantIdRedeem;

            var cts = new CancellationTokenSource();

            //Nếu là đổi quà cashout => check điểm cash out, nếu ko => check token balance
            if (!string.IsNullOrEmpty(categoryTypeCode) && categoryTypeCode.ToUpper() == GiftCategoryTypeCode.CashOut)
            {
                
                var result = new SmeRedeemGiftDto()
                {
                    Code = "UnableToPurchaseCashoutGifts",
                    Message = "Hiện tại không hỗ trợ redeem quà Cashout"
                };

                return result;
            }
            else
            {
                // TODO: when KHCN onboard, need adapt this point
                _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> GetBalanceMember start");
                var resultBalance = await _rewardService.WebstoreGetSmeBalance(new WebstoreGetSmeBalanceRequest()
                {
                    SmeCode = input.MemberCode
                });
                _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> GetBalanceMember end");
                if (resultBalance.Item.Balance < requestRedeem.TotalAmount)
                {
                    var result = new SmeRedeemGiftDto()
                    {
                        Code = "BalanceNotEnough",
                        Message = "Token Balance is not enough to make this transaction"
                    };

                    return result;
                }
            }

            var request = new LoyaltyCreateRedeemTransactionDto()
            {
                Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"), //input.Date,
                Description = requestRedeem.Description,
                GiftCode = requestRedeem.GiftCode,
                MemberCode = input.MemberCode,
                Quantity = requestRedeem.Quantity,
                TotalAmount = requestRedeem.TotalAmount,
                TransactionCode = requestRedeem.TransactionCode,
                MerchantIdRedeem = merchantId,
                FlashSaleProgramCode = ""
            };

            // Kiểm tra quà bên loyalty và tạo đơn hàng redeem tạm. nếu tạo thành công thì mới process thanh toán (gọi sang reward)
            //try
            //{
            //    _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> VERIFY_OR_CREATE_REDEEM_ORDER start");
            //    var checkTransactionPending = await PostLoyaltyAsync<VerifyAndCreateRedeemOrderOutput>(LoyaltyApiUrl.VERIFY_OR_CREATE_REDEEM_ORDER, request, context);
            //    _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> VERIFY_OR_CREATE_REDEEM_ORDER end >> " + JsonConvert.SerializeObject(checkTransactionPending));
            //    if ((!checkTransactionPending.Success || (checkTransactionPending.Success && !checkTransactionPending.Result.IsSuccess)))
            //    {
            //        var result = new SmeRedeemGiftDto()
            //        {
            //            Code = "1022",
            //            Message = "Cannot Redeem At This Time"
            //        };
            //        return result;
            //    }
            //}
            //catch (WebException ex)
            //{
            //    throw ex;
            //}
            //catch (TaskCanceledException ex)
            //{
            //    throw ex;
            //}
            //catch (Exception ex)
            //{
            //    if (ex.GetType() == typeof(LoyaltyException))
            //    {
            //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);

            //        var result = new SmeRedeemGiftDto()
            //        {
            //            Code = res.Code,
            //            Message = res.Message
            //        };
            //        return result;
            //    }
            //    throw ex;
            //}

            var successPaymentToken = false;
            var originalTokenTransID = "";
            try
            {
                var q = new WebstoreCallRedwardRedeemInput
                {
                    MemberId = requestRedeem.SmeId,
                    MerchantId = requestRedeem.MerchantIdRedeem.Value,
                    OrderCode = requestRedeem.TransactionCode,
                    TotalRequestedAmount = requestRedeem.TotalAmount
                };
                _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> Call Reward: CreateRedeem start >> " + JsonConvert.SerializeObject(q));
                var resultRedeemReward = await _rewardService.WebstoreCallRedwardRedeem(q);
                if (resultRedeemReward.result == 202)
                {
                    successPaymentToken = true;
                    _logger.LogError($" {input.SessionId}  >> WebstoreRedeem >> Call Reward rs: Create redeem reward with status 202 {JsonConvert.SerializeObject(resultRedeemReward)}");
                    var ex = new RewardException();
                    var error = new RewardDataExceptionResponse()
                    {
                        result = new RewardDataExceptionResultItem()
                        {
                            code = "RedeemRewardTransactionStatus202",
                            message = "Create redeem reward with status 202"
                        },
                        status = 500
                    };
                    ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
                    ex.Data.Add("StatusCode", 400);
                    throw ex;
                }
                _logger.LogInformation("CreateRedeem end");

                successPaymentToken = true;

                var result = new SmeRedeemGiftDto()
                {
                    Code = "00"
                };
                _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> Call Loyalty Start..." + JsonConvert.SerializeObject(request));
                var loyRes = await PostLoyaltyAsync<LoyaltyCreateRedeemTransactionOutput>(LoyaltyApiUrl.GIFTTRANSACTION_CREATEREDEEMTRANSACTION, request, context);
                _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> Call Loyalty end");
                if (!string.IsNullOrWhiteSpace(loyRes.Result.Exception) || !loyRes.Result.SuccessedRedeem)
                {
                    if (!loyRes.Result.Timeout)
                    {
                        _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> retryRevertToken start");
                        var requestRevert = new RewardMemberRevertRedeemInput()
                        {
                            MerchantId = requestRedeem.MerchantIdRedeem ?? 0,
                            OrderCode = requestRedeem.TransactionCode,
                            TokenAmount = requestRedeem.TotalAmount,
                            MemberId = requestRedeem.SmeId,
                        };

                        await _loyaltyGiftTransactionsService.retryWebstoreRevertToken(requestRevert, RewardApiUrl.SME_REDEEM_GIFT_TRANSACTION_REVERT);
                        _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> retryRevertToken end");
                    }

                    var ex = new LoyaltyException();
                    var errorMsg = new 
                    {
                        error = new RewardDataExceptionResultItem()
                        {
                            code = loyRes.Result.Exception,
                            message = loyRes.Result.Messages
                        },
                        status = 500
                    };
                    
                    ex.Data.Add("ErrorData", JsonConvert.SerializeObject(errorMsg));
                    throw ex;
                }

                if (loyRes.Success && loyRes.Result.SuccessedRedeem)
                {
                    result.TransactionCode = requestRedeem.TransactionCode;
                    result.Egifts = loyRes.Result.Items.Select(x =>
                    {
                        if (x.EGift != null)
                        {
                            return new SmeRedeemGiftOutputInner()
                            {
                                Description = x.Description, Status = x.Status, UsedStatus = x.EGift.usedStatus,
                                ExpiredDate = x.EGift?.ExpiredDate, EGiftCode = x.EGift.Code, QRCode = x.EGift.QRCode
                            };
                        } else return new SmeRedeemGiftOutputInner();
                    }).ToList();
                    // Ghi nhận action cho các hành động nạp viễn thông IMEDIA
                    if (!string.IsNullOrEmpty(loyRes.Result.ActionCode) && !string.IsNullOrEmpty(loyRes.Result.ActionFilter))
                    {
                        try
                        {
                            var inputActionRequest = new LoyaltyInputActionInput()
                            {
                                Actions = new List<ActionRequestDto>()
                                {
                                    new ActionRequestDto()
                                    {
                                        TransactionCode = requestRedeem.TransactionCode,
                                        Type = 1,
                                        OriginalTransactionCode = "",
                                        ActionCode = loyRes.Result.ActionCode,
                                        ActionFilter = new ActionListValueFilterReward()
                                        {
                                            ListCodeFilter = loyRes.Result.ActionFilter,
                                        },
                                        MemberCode = input.MemberCode,
                                        Value = (int)Decimal.Truncate(requestRedeem.TotalAmount),
                                        ActionTime = DateTime.UtcNow,
                                        MlmApplied = false,
                                        MLMDealerChannel = null,
                                        MLMDistributionChannel = null,
                                        ReferenceCode = null,
                                        Tag = "",
                                        RankCode = ""
                                    }
                                }
                            };
                            // Send to LinkID
                            await _loyaltyRewardServc.InputAction(inputActionRequest);
                        }
                        catch (Exception e)
                        {
                            _logger.LogError(" >> Error while sending InputAction to LinkId tenant for Topup Transaction");
                            _logger.LogError(e.StackTrace);
                        }
                    }
                }
                return result;
            }
            catch (WebException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError")
                    {
                        _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> retryRevertToken start");
                        var requestRevert = new RewardMemberRevertRedeemInput()
                        {
                            MerchantId = requestRedeem.MerchantIdRedeem ?? 0,
                            OrderCode = requestRedeem.TransactionCode,
                            TokenAmount = requestRedeem.TotalAmount,
                            MemberId = requestRedeem.SmeId,
                        };

                        await _loyaltyGiftTransactionsService.retryWebstoreRevertToken(requestRevert, RewardApiUrl.SME_REDEEM_GIFT_TRANSACTION_REVERT);
                        _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> retryRevertToken end");
                    }
                    await _loyaltyGiftTransactionsService.UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = res.Code == "SystemError" ? "504" : res.Code,
                        ErrorMessage = res.Code != "SystemError" ? res.Message?.ToString() : res.MessageDetail?.ToString(),
                    }, successPaymentToken);
                    throw ex;
                }
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError")
                    {
                        _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> retryRevertToken start");
                        var requestRevert = new RewardMemberRevertRedeemInput()
                        {
                            MerchantId = requestRedeem.MerchantIdRedeem ?? 0,
                            OrderCode = requestRedeem.TransactionCode,
                            TokenAmount = requestRedeem.TotalAmount,
                            MemberId = requestRedeem.SmeId,
                        };

                        await _loyaltyGiftTransactionsService.retryWebstoreRevertToken(requestRevert, RewardApiUrl.SME_REDEEM_GIFT_TRANSACTION_REVERT);
                        _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> retryRevertToken end");
                    }
                    await _loyaltyGiftTransactionsService.UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = res.Code == "SystemError" ? "504" : res.Code,
                        ErrorMessage = res.Code != "SystemError" ? res.Message?.ToString() : res.MessageDetail?.ToString(),
                    }, successPaymentToken);
                    throw ex;
                }
                if (!cts.Token.IsCancellationRequested)
                {
                    await _loyaltyGiftTransactionsService.UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = "408",
                        ErrorMessage = "Timed Out with: " + ex.Message,
                    }, successPaymentToken);
                    throw new Exception("Timed Out with: ", ex);
                }
                else
                {
                    await _loyaltyGiftTransactionsService.UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = "504",
                        ErrorMessage = "Cancelled for some other reason: " + ex.Message,
                    }, successPaymentToken);
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError")
                    {
                        _logger.LogInformation("retryRevertToken start");
                        var requestRevert = new RewardMemberRevertRedeemInput()
                        {
                            MerchantId = requestRedeem.MerchantIdRedeem ?? 0,
                            OrderCode = requestRedeem.TransactionCode,
                            TokenAmount = requestRedeem.TotalAmount,
                            MemberId = requestRedeem.SmeId,
                        };

                        await _loyaltyGiftTransactionsService.retryWebstoreRevertToken(requestRevert, RewardApiUrl.SME_REDEEM_GIFT_TRANSACTION_REVERT);
                        _logger.LogInformation($" {input.SessionId}  >> WebstoreRedeem >> retryRevertToken end");
                    }
                    await _loyaltyGiftTransactionsService.UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = res.Code == "SystemError" ? "504" : res.Code,
                        ErrorMessage = res.Code != "SystemError" ? res.Message?.ToString() : res.MessageDetail?.ToString(),
                    }, successPaymentToken);
                    throw ex;
                }
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    if (res.Code == "RedeemRewardTransactionStatus202")
                    {
                        await _loyaltyGiftTransactionsService.UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                        {
                            TransactionCode = request.TransactionCode,
                            ErrorCode = "202",
                            ErrorMessage = "" + res.MessageDetail,
                        }, successPaymentToken);
                        throw new Exception("" + res.MessageDetail);
                    }
                }
                await _loyaltyGiftTransactionsService.UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = request.TransactionCode,
                    ErrorCode = "504",
                    ErrorMessage = "Cancelled for some other reason: " + ex.Message,
                }, successPaymentToken);
                throw ex;
            }
                
        }

        private SmeRedeemGiftDto ValidateAccessTokenRedeem(string accessToken)
        {
            if (string.IsNullOrEmpty(accessToken))
                return new SmeRedeemGiftDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REQUEST_ACCESSTOKEN_INVALID, Message = "Token not null" };
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(_secretKey)),
                    ValidateIssuer = false,
                    ValidateAudience = true,
                    ValidAudience = "LynkiD",
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };
                var token = accessToken.Replace("Bearer ", "").Replace("bearer ", "");
                SecurityToken validatedToken;
                tokenHandler.ValidateToken(token, validationParameters, out validatedToken);
                return new SmeRedeemGiftDto { };

            }
            catch (SecurityTokenExpiredException ex)
            {
                _logger.LogError($"ValidateAccessTokenRedeem_Error:{ex.Message}-{ex.StackTrace}");
                return new SmeRedeemGiftDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REQUEST_ACCESSTOKEN_INVALID, Message = "Token has expired" };
            }
            catch (SecurityTokenInvalidSignatureException ex)
            {
                _logger.LogError($"CustomTokenAuthenticationService_Error:{ex.Message}-{ex.StackTrace}");
                return new SmeRedeemGiftDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REQUEST_ACCESSTOKEN_INVALID, Message = "Token is incorrect" };
            }
            catch (Exception ex)
            {
                _logger.LogError($"CustomTokenAuthenticationService_Error:{ex.Message}-{ex.StackTrace}");
                return new SmeRedeemGiftDto { Code = WebstoreConstants.RedeemGiftErrorCodes.REQUEST_ACCESSTOKEN_INVALID, Message = "Token is incorrect" };
            }
        }

        public async Task<GetOTPOutput> ResendOtp(GetOTPInput input)
        {
            _userService.checkPhone(input.PhoneNumber);
            await _rewardService.SendOtp(new RewardMemberSendOtpInput()
            {
                PhoneNumber = input.PhoneNumber,
                SessionId = input.SessionId,
                SmsType = "RedeemGift",
            });
            return new GetOTPOutput()
            {
                result = 200,
                message = "Success",
                messageDetail = null,
            };
        }
    }
}
