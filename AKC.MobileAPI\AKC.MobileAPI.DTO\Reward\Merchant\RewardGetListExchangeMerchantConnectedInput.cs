﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Merchant
{
    public class RewardGetListExchangeMerchantConnectedInput
    {
        public string sorting { get; set; }
        public int maxResultCount { get; set; }
        public int skipCount { get; set; }
        public string MemberCode { get; set; }
        public string BindingStatusFilter { get; set; }
        public int PriorityFilter { get; set; }
        public string MerchantNameFilter { get; set; }
    }

    public class RewardGetListExchangeMerchantConnectedDto
    {
        public string Sorting { get; set; }
        public int MaxResultCount { get; set; }
        public int SkipCount { get; set; }
        public string NationalId { get; set; }
        public int PriorityFilter { get; set; }
        public string BindingStatusFilter { get; set; }
        public string MerchantNameFilter { get; set; }
    }
}
