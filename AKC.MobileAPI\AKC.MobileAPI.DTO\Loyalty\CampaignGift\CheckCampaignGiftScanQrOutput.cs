﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.CampaignGift
{
    public class CheckCampaignGiftScanQrOutput
    {
        public CheckCampaignGiftScanQrResponse Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class CheckCampaignGiftScanQrResponse
    {
        public int Id { get; set; }
        public string CampaignGiveGiftCode { get; set; }
        public string PopUpImageLink { get; set; }
        public string Description { get; set; }
        public string ActionCode { get; set; }
        public int? TypeOfUse { get; set; }
    }
}
