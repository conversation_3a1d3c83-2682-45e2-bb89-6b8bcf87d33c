﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Gamification.Exchange
{
    public class GetDetailsExchangeRuleOutput
    {
        public int result { get; set; }
        public List<GetDetailsExchangeRuletDto> item { get; set; }
        public int totalCount { get; set; }
        public string message { get; set; }
        public string messageDetail { get; set; }
    }
    public class GetDetailsExchangeRuletDto
    {
        public int? ID { get; set; }
        public int? RuleID { get; set; }
        public int? ItemID { get; set; }
        public int? Quantity { get; set; }
        public int? CreatorUserID { get; set; }
        public DateTime? CreateDate { get; set; }
        public int? LastModifierUserID { get; set; }
        public DateTime? LastModificationDate { get; set; }
        public int? DeletorUserID { get; set; }
        public DateTime? DeletionTime { get; set; }
        public int? Type { get; set; }
    }
}
