﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.Service.Constants
{
    public class RewardApiUrl
    {
        public const string MEMBER_VIEW = "Integration/Member/View";
        public const string MERCHANT_GETINFO = "Integration/Merchant/GetInfo";
        public const string MEMBER_GET_NAME_BY_PHONE = "Integration/Member/GetNameByPhoneNumber";
        public const string MEMBER_UPDATE = "Integration/Member/Update";
        public const string MEMBER_UPDATE_DEVICEID = "Integration/Member/UpdateDeviceId";
        public const string MEMBER_VIEWPOINT = "Integration/Member/ViewPoint";
        public const string MEMBER_CREATE = "Integration/Member/Create";
        public const string MEMBER_VERIFY_REFERRAL_CODE = "Integration/Member/VerifyReferralCode";
        public const string MEMBER_GET_TEMP_POINT_BY_ID = "Integration/TempPointTrans/GetByMemberID";
        public const string MEMBER_GET_TOKEN_TRANS_BY_MEMBER_ID = "Integration/TokenTrans/GetByMemberID";
        public const string MEMBER_GET_TOKEN_TRANS_BY_ID = "Integration/TokenTrans/GetTokenTransById";
        public const string MEMBER_GET_EXPIRED_TOKEN_TRANS_BY_ID = "Integration/TokenTrans/GetTokenTransExpiredById";
        public const string MERCHANT_EXCHANGE_GET_ALL = "Integration/Merchant/GetAll";
        public const string EXCHANGE_TRANSACTION_INTEGRATION_CREATE = "Integration/ExchangeIntergrationTransaction/Create";
        public const string EXCHANGE_TRANSACTION_INTEGRATION_REVERT = "Integration/ExchangeIntergrationTransaction/Revert";
        public const string EXCHANGE_TRANSACTION_INTEGRATION_CHECK_PHONE_NUMBER = "Integration/ExchangeIntergrationTransaction/CheckPhoneNumber";
        public const string MEMBER_GET_INFO = "Integration/Member/GetInfo";
        public const string MEMBER_ADD_POINT_USING_ORDINARY = "Integration/Member/AddPointUsingOrdinary";
        public const string MEMBER_GET_LIST_MERCHANT_CONNECTED = "Integration/Member/GetListExchangeMerchantConnected";
        public const string GIVE_TOKEN_USER_VERIFYING = "Integration/GiveToken/UserVerifying";
        public const string GIVE_TOKEN_TRANSFER_TRANSACTION = "Integration/GiveToken/TransferTransaction";
        public const string EXCHANGE_VERIFITION_OTP = "Integration/Verification/ExchangeVerifyOTP";
        public const string MEMBER_GET_TOKEN_BALANCE = "Integration/Member/GetBalanceByNationalId";
        public const string REDEEM_GIFT_TRANSACTION_CREATE = "Integration/RedeemTransaction/Create";
        public const string REDEEM_GIFT_TRANSACTION_REVERT = "Integration/RedeemTransaction/Return";
        public const string TOPUP_TRANSACTION_CREATE = "Integration/TopUpTransaction/Create";
        public const string PAY_BY_TOKEN_TRANSACTION_CREATE = "Integration/PayByTokenTransaction/Create";
        public const string PARTNER_POINT_CACHING_REQUEST_UPDATE = "Integration/PartnerPointCaching/RequestUpdateIntegration";
        public const string GET_ALL_MERCHANT = "Integration/Merchant/GetAllWithStore";
        public const string MERCHANT_GetInfoFullData = "Integration/Merchant/GetInfoFullData";
        public const string GET_ALL_MERCHANT_PAYMENT = "Integration/Merchant/GetAllMerchantPayment";
        public const string MEMBER_VERIFY_IDCARD = "Integration/Member/IsIdCardVerified";
        public const string MEMBER_VERIFY_PINCODE = "Integration/Member/VerifyPinCode";
        public const string MEMBER_HAS_PINCODE = "Integration/Member/HasPinCode";
        public const string CHECK_MEMBER_DELETED_HISTORY = "Integration/Member/CheckDeletedHistory";
        public const string MEMBER_CREATE_OR_UPDATE_PINCODE = "Integration/Member/CreateOrUpdatePinCode";
		public const string VERIFY_PROVIDER_ID_BY_PHONE_NUMBER = "Integration/Member/VerifyProviderIdByPhoneNumber";
        public const string UPDATE_PROVIDER = "Integration/Member/UpdateProvider";
        public const string MEMBER_VERIFY_OR_CREATE = "Integration/Member/VerifyOrCreate";
        public const string MEMBER_GET_REFRESH_TOKEN = "Integration/Member/GetRefreshToken";
        public const string MEMBER_SAVE_REFRESH_TOKEN = "Integration/Member/SaveRefreshToken";
        public const string MEMBER_SAVE_REFRESH_TOKEN_MOBIAPIAPPOTA = "Integration/Member/SaveRefreshTokenMobileAPIAppota";
        public const string MEMBER_VERIFY_AND_UPDATE_PROVIDER = "Integration/Member/VerifyCreateMemberAndUpdateProvider";
        public const string MEMBER_CREATE_REGISTER_LOG = "Integration/Member/CreateRegisterLogs";
        public const string MEMBER_UPDATE_PHONE_NUMBER = "Integration/Member/UpdatePhoneNumber";
        public const string MEMBER_ACCOUNT_HAVE_PHONE_NUMBER = "Integration/Member/AccountHavePhoneNumber";
        public const string MEMBER_FIRST_ACTION_MEMBER = "Integration/CommonFunction/FirstActionMember";
        public const string MEMBER_GET_LOGIN_BY_FIREBASE_ID = "Integration/Member/GetMemberLoginByFirebaseId";
        public const string MEMBER_GET_USAGE_PRIORITY = "Integration/Member/GetUsagePriority";
        public const string MEMBER_UPDATE_USAGE_PRIORITY = "Integration/Member/UpdateUsagePriority";
        public const string MEMBER_UPDATE_POINT_USAGE_TYPE = "Integration/Member/UpdatePointUsageType";
        public const string TOPUP_CASHOUT_CREATE = "Integration/IncentiveCashOut";
        public const string MEMBER_GET_CASHOUT_AND_TOPUP_INFO = "Integration/Member/GetCashoutAndTopupInfo";
        public const string TOPUP_CASHOUT_REVERT = "Integration/IncentiveCashOut/Revert";
        public const string PAYMENT_FAIL_CREATE = "Integration/PaymentFail/Create";
        public const string PAYMENT_GENERATE_PAYMENT_LINK = "Integration/Payment/GeneratePaymentLink";
        public const string PAYMENT_CHECK_PAYMENT_RETURN_LINK = "Integration/Payment/CheckPaymentReturn";
        public const string PAYMENT_RETURN_URL = "/api/Payment/GetDataReturnUrl";
        public const string PAYMENT_BACK_URL = "/api/Payment/GetDataBackUrl";
        public const string PAYMENT_CHECK_GENERATE_PAYMENT_LINK = "Integration/Payment/CheckGeneratePaymentLinkByMember";
        public const string MEMBER_GET_REFERENCE_DATA_CONNECTED = "Integration/Member/GetReferenceDataConnected";
        public const string MEMBER_CONFIRM_CONNECT_MERCHANT = "Integration/Member/ConfirmConnectMerchant";
        public const string MEMBER_INFO_CONFIRM_CONNECT_MERCHANT = "Integration/Member/GetInfoConfirmConnectMerchant";
        public const string MERCHANT_GET_LIST_BY_IDS = "Integration/MasterData/GetListMerchantByIds";
        public const string MEMBER_VERIFY_AUTHEN_MEMBER_CODE = "Integration/Member/VerifyAuthenWithMemberCode";
        public const string MEMBER_TOPUP_EVOUCHER_CREATE_TRANS = "Integration/TopUpTransactionLinkId/Create";
        public const string MEMBER_SEND_OTP = "Integration/SmsProvider/SendOtpConfirm";
        public const string MEMBER_CONFIRM_OTP = "Integration/SmsProvider/VerifyOtpConfirm";
        public const string COMMON_GET_USING_FIREBASE = "Integration/Common/GetUsingFirebaseOtp";
        public const string MEMBER_SEND_OTP_ACTION = "Integration/Member/RequestOtpAction";
        public const string MEMBER_VERIFY_OTP_ACTION = "Integration/Member/VerifyOtpAction";
        public const string MEMBER_UPDATE_PHONE_VALIDATION = "Integration/Member/UpdatePhoneNumberValidation";
        public const string MEMBER_UPDATE_PIN_CODE_VALIDATION = "Integration/Member/UpdatePinCodeValidation";
        public const string MEMBER_CheckCifExistWithMerchant = "Integration/Member/CheckIfCifInAConfirmedConnection";
        public const string MEMBER_GET_INFO_BY_ID_PHONE_IDCARD = "Integration/Member/GetMemberByIdCardOrPhoneNumber";
        public const string EXCHANGE_TRANSACTION_TRACKING_INTEGRATION_CREATE = "Integration/ExchangeTransactionTracking/Create";
        public const string EXCHANGE_TRANSACTION_TRACKING_INTEGRATION_UPDATE = "Integration/ExchangeTransactionTracking/Update";
        public const string DELETE_MEMBER = "Integration/Member/DeleteMember";
        public const string CreateLoyaltyMemberOfCustomMerchant = "Integration/Member/CreateLoyaltyMemberOfCustomMerchant";
        public const string GetCreditBalanceByMemberCode = "Integration/Member/GetCreditBalanceByMemberCode";
        public const string DisconnectMerchant = "Integration/Member/DeletePartner";
        public const string ValidateBalanceAbleToCashout = "Integration/Member/ValidateBalanceAbleToCashout";
        public const string GetGlobalSettingInfo = "Integration/GlobalSetting/GetAll";

        // SmartOTP
        public const string ACTIVE_SMART_OTP = "Integration/ActivateSmartOTP";
        public const string DEACTIVATE_SMART_OTP = "Integration/DeactivateSmartOTP";
        public const string REGISTER_SMART_OTP = "Integration/RegisterSmartOTP";
        public const string VALIDATE_SMART_OTP = "Integration/ValidateSmartOTP";
        public const string SYNC_TIME_SMART_OTP = "Integration/SyncTimeSmartOTP";
        public const string REGISTER_SMART_OTP_STATUS = "Integration/RegisterSmartOTPStatus";
        public const string GET_STATUS_SMART_OTP = "Integration/GetStatusOTPProvider";

        public const string GET_CIF_BY_NATIONAL_ID = "Integration/Member/GetCifByNationalId";

        public const string MERCHANT_GET_CURRENT_MERCHANT = "Integration/Merchant/GetCurrentMerchant";
        public const string GiveFeedback = "Integration/Member/Feedback";
        public const string GET_ALL_FEEDBACK_SUGGESSTION = "Integration/Suggestion/GetAll";
        public const string RewardSendAckAfterConnected = "Integration/RewardSendAckAfterConnected";
        public const string RewardSendAckAfterDisconnected = "Integration/RewardSendAckAfterDisconnected";
        // Endpoint để đổi điểm LynkiD sang điểm của đối tác
        public const string MEMBER_EXCHANGE_LINKIDTOKEN_TO_PARTNER_POINT = "Integration/PayByTokenTransaction/Exchange";
        public const string MEMBER_EXCHANGE_LINKIDTOKEN_TO_PARTNER_POINT_Revert = "Integration/PayByTokenTransaction/RevertExchange";
        public const string UPDATE_EXCHANGE_PARTNERBINDINGTXID = "Integration/RedeemExchangeTransaction/Update";
        
        // Endpoint for Webstore
        public const string WebstoreSmeLogin = "WebstoreIntegration/Sme/Login";
        public const string WebstoreGetSmeInfo = "WebstoreIntegration/Sme/GetSmeInfoForWebStore";
        public const string WebstoreGetSmeFullInfo = "WebstoreIntegration/Sme/GetFullInfoForWebstore";
        public const string WebstoreSetSmePasword = "WebstoreIntegration/Sme/SetPassword";
        public const string WebstoreGetSmeBalance = "WebstoreIntegration/Sme/GetBalance";
        public const string WebstoreValidateAccessToken = "WebstoreIntegration/ValidateAccessToken";
        public const string WebstoreRefreshToken = "WebstoreIntegration/RefreshToken";
        public const string WebstoreCheckPassword = "WebstoreIntegration/Sme/CheckPassword";

        public const string GetALLTokenTrans = "Integration/TokenTrans/GetByMemberID";
        public const string GetALLTokenTransBySmeCif = "Integration/TokenTrans/GetBySmeCif";
        public const string CheckPassWord = "WebstoreIntegration/CheckPassword";
        public const string SME_REDEEM_GIFT_TRANSACTION_CREATE = "Integration/SmeRedeemTransaction/Create";
        public const string SME_REDEEM_GIFT_TRANSACTION_REVERT = "Integration/SmeRedeemTransaction/Return";
        
        // Endpoint for Money Card
        public const string GetMoneyCardByMember = "Integration/CardTransaction/GetAllMoneyCard";
        public const string RedeemUsingMoneyCard = "Integration/CardTransaction/Redeem";
        public const string RevertRedeemUsingMoneyCard = "Integration/CardTransaction/Return";
        public const string GetCardTransactionByMember = "Integration/CardTransaction/GetAllTransaction";
        public const string GetCardTransaction_REVERT_MULTICARD_SAMEORDERs = "Integration/CardTransaction/RevertTransaction";
        public const string GetPaymentMethod = "Integration/Member/GetPaymentMethod";


        public const string MEMBER_VALIDATE_MEMBER_FOR_INTEGRATIONPARTNER = "Integration/Member/ValidateMemberForIntegrationPartner";
        public const string MEMBER_VALIDATE_OTP_FOR_INTEGRATIONPARTNER = "Integration/Member/ValidateOTPMemberForIntegrationPartner";
        public const string MEMBER_SAVE_REFRESH_TOKEN_V2 = "Integration/Member/SaveRefreshTokenV2";
        public const string WebstoreIntegration_SaveTANDC = "WebstoreIntegration/AgreeTAndC";
        public const string INTEGRATION_CREATE_SME = "Integration/Member/CreateSme";
        // SKY JOY
        public const string GET_PARTNER_MEMBER_BY_CONNECTINFO = "Integration/Member/GetCifByMember";
    }

    public class MerchantNameConfig
    {
        public const string VPID = "VPID";
        public const string VPBank = "VPBank";
    }
}
