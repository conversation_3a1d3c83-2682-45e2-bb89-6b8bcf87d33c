<?xml version="1.0"?>
<doc>
    <assembly>
        <name>AKC.MobileAPI</name>
    </assembly>
    <members>
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.Gift.GiftRecommendationController.GetRecommendedCategories(AKC.MobileAPI.DTO.Loyalty.Gift.GetRecommendedCategoriesInput)">
            Get các categories được recommended cho từng member. Cached lại trong vòng 5 phút để tránh flood Redis.
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.Gift.GiftRecommendationController.GetRecommendedGifts(AKC.MobileAPI.DTO.Loyalty.Gift.GetRecommendedGiftsInput)">
            Get các Gifts được recommended cho từng member. Cached lại trong vòng 5 phút để tránh flood Redis.
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.Gift.LoyaltyGiftInforController.GetGiftGroupForHomePageV1Dot1(AKC.MobileAPI.DTO.Loyalty.Gift.GetGiftGroupForHomePageV1Dot1Input)">
            LINKID-1352: API trả về giftgroup chỉ trả 1 cái active và kèm theo 4 quà thuộc group đó...
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.Gift.LoyaltyGiftInforController.GetGiftsByBrand(AKC.MobileAPI.DTO.Loyalty.Gift.GiftInforByBrandInput)">
            LINKID-1352: API trả về giftgroup chỉ trả 1 cái active và kèm theo 4 quà thuộc group đó...
        </member>
        <!-- Badly formed XML comment ignored for member "M:AKC.MobileAPI.Controllers.Loyalty.LoyaltyArticleController.GetTermsAndConditions" -->
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.LoyaltyArticleController.GetSecurityPolicy">
            Allow this method to be called anonymously. SecPolicy are simply Articles with CategoryTypeFilter = 39.
            So this method will query all the Active Articles having CategoryTypeFilter = 39
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.LoyaltyGiftTransactionsController.RedeemWithMoneyCard(AKC.MobileAPI.DTO.Reward.RedeemWithMoneyCardInput)">
            <summary>
            Thực hiện Redeem quà với money card.
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:AKC.MobileAPI.Controllers.Loyalty.LoyaltyInsuranceController">
            <summary>
            Bộ API tích hợp phần BẢO HIỂM
            </summary>
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.LoyaltyInsuranceController.GetListProgram(AKC.MobileAPI.DTO.Loyalty.Insurance.GetListProgramsInput)">
            <summary>
            Xem danh sách các chương trình bảo hiểm
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.LoyaltyInsuranceController.GetListProduct(AKC.MobileAPI.DTO.Loyalty.Insurance.GetListProductsInput)">
            <summary>
            Get danh sách các sản phẩm bảo hiểm
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.LoyaltyInsuranceController.GetListPackage(AKC.MobileAPI.DTO.Loyalty.Insurance.GetListPackagesInput)">
            <summary>
            Get danh sách các gói bảo hiểm
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.LoyaltyInsuranceController.GetListInsuranceContracts(AKC.MobileAPI.DTO.Loyalty.Insurance.GetListContractInput)">
            <summary>
            Get danh sách các Hợp đồng bảo hiểm đã mua
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.LoyaltyInsuranceController.PurchasePackage(AKC.MobileAPI.DTO.Loyalty.Insurance.PurchasePackageInput)">
            <summary>
            Hành động mua gói bảo hiểm
            Gọi sang bên đối tác rồi trả về đối tượng Hợp đồng cho Mobile View
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.LoyaltyInsuranceController.GetInsuranceDetail(AKC.MobileAPI.DTO.Loyalty.Insurance.InsuranceDetailInput)">
            <summary>
            Get thông tin chi tiết gói bảo hiểm
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Loyalty.LoyaltyThirdPartyPointsController.ExchangeLynkiDToPartnerPoint(AKC.MobileAPI.DTO.Loyalty.ThirdParty.ExchangeLynkiDToPartnerPointReq)">
            Entry point (level controller) để cho đổi điểm LynkiD Ra khỏi tài khoản LynkiD.
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Webstore.WebstoreGiftTransactionController.CreateRedeemGiftTransaction(AKC.MobileAPI.DTO.Webstore.WebStoreCreateRedeemGiftTransactionInput)">
            <summary>
            API thực hiện khởi tạo việc redeem gift
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Webstore.WebstoreGiftTransactionController.VerifyRedeemGiftTransaction(AKC.MobileAPI.DTO.Webstore.VerifyRedeemGiftTransactionInput)">
            <summary>
            API xác nhận redeem. Nếu mức coin nhỏ hơn config, thì cần password token, còn lớn hơn sẽ dùng SMS OTP
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:AKC.MobileAPI.Controllers.Webstore.WebstoreMemberController.ChangePassword(AKC.MobileAPI.DTO.Webstore.ChangePasswordInput)">
            <summary>
            API cho phép User chủ động đổi password. Authorization header required.
            </summary>
        </member>
    </members>
</doc>
