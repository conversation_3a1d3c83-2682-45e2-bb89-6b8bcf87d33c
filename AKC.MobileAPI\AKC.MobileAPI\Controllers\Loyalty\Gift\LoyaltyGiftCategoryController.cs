﻿using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.DTO.Loyalty.GiftCategories;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.Service;

namespace AKC.MobileAPI.Controllers.Loyalty.Gift
{
    [Route("api/GiftCategory")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyGiftCategoryController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyGiftService _loyaltyGiftService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        public LoyaltyGiftCategoryController
        (
              ILogger<LoyaltyGiftCategoryController> logger,
              ILoyaltyGiftService loyaltyGiftService,
              IExceptionReponseService exceptionReponseService,
              ICommonHelperService commonHelperService
        )
        {
            _logger = logger;
            _loyaltyGiftService = loyaltyGiftService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
        }

        [HttpGet]
        [Route("GetAll")]
        public async Task<ActionResult<LoyaltyGiftCategoryGetAllOutput>> GetALL([FromQuery] LoyaltyGiftCategoryGetAllInput input)
        {
            try
            {
                var result = await _loyaltyGiftService.GetAll(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GettAll GiftCategory Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        [HttpGet]
        [Route("GetAllGiftCategoriesAndInfo")]
        public async Task<ActionResult<GetGiftCategoryAndInfoForView>> GetAllGiftCategoriesAndInfo([FromQuery] GetAllGiftCategoriesAndInfoInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var listExclude = new List<string>()
                {
                    "ab978ff5-427f-42cc-958a-8c8241870164", "c3f8b851-2fc0-418a-bf22-3234ce4cdbd7"
                };
                var result = await _loyaltyGiftService.GetAllGiftCategoriesAndInfo(input);
                if (result != null && result.Result != null && result.Result.Items != null && result.Result.Items.Count > 0)
                {
                    // Thay đổi category của Topup trên môi trường Production khi golive
                    if (!input.ShowTopupCategory)
                    {
                        listExclude.Add("81044caf-eddb-40ee-a7c7-b862be9818e0");
                        listExclude.Add("84afa499-7e53-4cbc-b442-2286ee7ff545");
                        var containIt = result.Result.Items.Any(x => listExclude.Contains(x.GiftCategory.Code));
                        if (containIt)
                        {
                            var x = result.Result.Items.Where(x => !listExclude.Contains(x.GiftCategory.Code)).Select(x => x).ToList();
                            result.Result.Items = x;
                            result.Result.TotalCount = x.Count;
                        }
                    }
                    else
                    {
                        var listInclude = new List<string>()
                        {
                            "81044caf-eddb-40ee-a7c7-b862be9818e0", "84afa499-7e53-4cbc-b442-2286ee7ff545"
                        };
                        var containIt = result.Result.Items.Any(x => listInclude.Contains(x.GiftCategory.Code));
                        if (containIt)
                        {
                            var x = result.Result.Items.Where(x => listInclude.Contains(x.GiftCategory.Code)).Select(x => x).ToList();
                            result.Result.Items = x;
                            result.Result.TotalCount = x.Count;
                        }
                    }

                }
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GettAll GiftCategory Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllGiftCategoriesAndInfo_V1")]
        public async Task<ActionResult<GetGiftCategoryAndInfoForView>> GetAllGiftCategoriesAndInfo_V1([FromQuery] GetAllGiftCategoriesAndInfoInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetAllGiftCategoriesAndInfo_V1(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GettAll GiftCategory v1 Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllByMemberCode")]
        public async Task<ActionResult<GetAllByMemberCodeOutput>> GetAllByMemberCode([FromQuery] GetAllByMemberCodeGiftCategoriesInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetAllByMemberCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GettAll GiftCategory ByMemberCode Error - " + JsonConvert.SerializeObject(res));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GiftListCategories")]
        public async Task<ActionResult<GiftCategoriesForView>> GiftListCategories([FromQuery] GetAllGiftCategoriesAndInfoInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var listExclude = new List<string>()
                {
                    "ab978ff5-427f-42cc-958a-8c8241870164", "c3f8b851-2fc0-418a-bf22-3234ce4cdbd7"
                };
                var result = await _loyaltyGiftService.GiftListCategories(input);
                if (result != null && result.Result != null && result.Result.Items != null && result.Result.Items.Count > 0)
                {
                    // Thay đổi category của Topup trên môi trường Production khi golive
                    if (!input.ShowTopupCategory)
                    {
                        listExclude.Add("d3f8c0cb-c08e-4fd1-9dfa-15c274c2c15c");
                        listExclude.Add("7c9dbc97-bc38-47e0-8122-cab0d6dc03eb");
                        var containIt = result.Result.Items.Any(x => listExclude.Contains(x.Code));
                        if (containIt)
                        {
                            var x = result.Result.Items.Where(x => !listExclude.Contains(x.Code)).Select(x => x).ToList();
                            result.Result.Items = x;
                            result.Result.TotalCount = x.Count;
                        }
                    }
                    else
                    {
                        var listInclude = new List<string>()
                        {
                            "d3f8c0cb-c08e-4fd1-9dfa-15c274c2c15c", "7c9dbc97-bc38-47e0-8122-cab0d6dc03eb"
                        };
                        var containIt = result.Result.Items.Any(x => listInclude.Contains(x.Code));
                        if (containIt)
                        {
                            var x = result.Result.Items.Where(x => listInclude.Contains(x.Code)).Select(x => x).ToList();
                            result.Result.Items = x;
                            result.Result.TotalCount = x.Count;
                        }
                    }

                }
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GettAll GiftCategory Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GiftListCategories_v1")]
        public async Task<ActionResult<GiftCategoriesForView>> GiftListCategories_v1([FromQuery] GetAllGiftCategoriesAndInfoInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                // HACK for golive UIUXV1.1 
                var configuration = AccessConfigurationService.Instance.GetConfiguration();
                var cateCodeOfMaxNapDiem = configuration.GetSection("CateCodeOfNapDiem").Value ?? "";

                var result = await _loyaltyGiftService.GiftListCategories_v1(input);
                if ("next" != input.Ver && !string.IsNullOrEmpty(cateCodeOfMaxNapDiem) && result.Result.Items.Select(x => x.Code == cateCodeOfMaxNapDiem).Any())
                {
                    result.Result.TotalCount = result.Result.TotalCount - 1;
                    result.Result.Items = result.Result.Items.Where(x => x.Code != cateCodeOfMaxNapDiem).ToList();
                }
                
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GettAll GiftCategory Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        [HttpGet]
        [Route("GiftListCategoriesInTwoRows")]
        public async Task<ActionResult<GiftListCategoriesInTwoRowsOutput>> GiftListCategoriesInTwoRows([FromQuery] GiftListCategoriesInTwoRowsInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GiftListCategoriesInTwoRows(input);
                var res = new GiftListCategoriesInTwoRowsOutputWrapper
                {
                    Result = result,
                    Success = true,
                    Error = null,
                    UnAuthorizedRequest = false,
                    __abp = true
                };
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GiftListCategoriesInTwoRows Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}
