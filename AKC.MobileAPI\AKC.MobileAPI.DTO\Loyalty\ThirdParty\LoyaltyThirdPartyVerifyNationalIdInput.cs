﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.ThirdParty
{
    public class LoyaltyThirdPartyVerifyNationalIdInput
    {
        [Required]
        public string PhoneNumber { get; set; }
        [Required]
        public string IdNumber { get; set; }
        [Required]
        [Range(1, Double.MaxValue)]
        public int MerchantId { get; set; }
        public string OtpSession { get; set; }
        public string IsResendOTP { get; set; }
        //[Required]
        public string MemberCode { get; set; }
    }
}
