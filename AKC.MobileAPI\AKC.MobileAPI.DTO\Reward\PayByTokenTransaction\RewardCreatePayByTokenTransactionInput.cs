﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.PayByTokenTransaction
{
   public class RewardCreatePayByTokenTransactionInput
   {
        public string NationalId { get; set; }
        public string WalletAddress { get; set; }
        public int StoreId { get; set; }
        public DateTime Time { get; set; }
        public double TotalRequestedAmount { get; set; }
   }

    public class RewardCreatePayByTokenTransactionDto
    {
        public string NationalId { get; set; }
        public string WalletAddress { get; set; }
        public int StoreId { get; set; }
        public string OrderCode { get; set; }
        public DateTime Time { get; set; }
        public double TotalRequestedAmount { get; set; }
    }
}
