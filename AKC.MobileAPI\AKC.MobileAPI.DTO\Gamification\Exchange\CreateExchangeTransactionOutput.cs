﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Gamification.Exchange
{
    public class CreateExchangeTransactionOutput
    {
        public int result { get; set; }
        public List<ExchangeTransactionOutputDto> item { get; set; }
        public int totalCount { get; set; }
        public string message { get; set; }
        public string messageDetail { get; set; }
    }
    public class ExchangeTransactionOutputDto
    {
        public int? MemberID { get; set; }
        public string TransactionCode { get; set; }
        public string MemberCode { get; set; }
        public int? ItemID { get; set; }
        public decimal? ItemQuantity { get; set; }
    }
}
