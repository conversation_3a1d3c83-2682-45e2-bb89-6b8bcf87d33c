﻿// using System;
// using System.Threading.Tasks;
// using AKC.MobileAPI.DTO.Loyalty.Adjust;
// using AKC.MobileAPI.Service.Abstract.Loyalty;
// using AKC.MobileAPI.Service.Common;
// using AKC.MobileAPI.Service.Exceptions;
// using AKC.MobileAPI.Service.Helpers;
// using Microsoft.AspNetCore.Authorization;
// using Microsoft.AspNetCore.Mvc;
// using Microsoft.Extensions.Logging;
// using Newtonsoft.Json;
//
// namespace AKC.MobileAPI.Controllers.Loyalty
// {
//     [Route("api/Gamification")]
//     [ApiController]
//     [ApiConventionType(typeof(DefaultApiConventions))]
//     [Authorize]
//     public class LoyaltyAdjustController : ControllerBase
//     {
//         private readonly ILogger _logger;
//         private readonly IExceptionReponseService _exceptionReponseService;
//         private readonly ILoyaltyAdjustService _loyaltyAdjustService;
//         private readonly ICommonHelperService _commonHelperService;
//         public LoyaltyAdjustController(
//         ILogger<LoyaltyAdjustController> logger,
//         IExceptionReponseService exceptionReponseService,
//         ILoyaltyAdjustService loyaltyAdjustService,
//         ICommonHelperService commonHelperService
//         )
//         {
//             _loyaltyAdjustService = loyaltyAdjustService;
//             _logger = logger;
//             _exceptionReponseService = exceptionReponseService;
//             _commonHelperService = commonHelperService;
//         }
//
//         [HttpPut]
//         [Route("UpdateMemberBalance")]
//         public async Task<ActionResult<UpdateMemberBalanceOutput>> UpdateMemberBalance(UpdateMemberBalanceInput input)
//         {
//             try
//             {
//                 var authorization = Request.Headers["Authorization"].ToString();
//                 var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
//                 if (checkAuthen != null)
//                 {
//                     return StatusCode(401, checkAuthen);
//                 }
//                 var result = await _loyaltyAdjustService.UpdateMemberBalance(input);
//                 return StatusCode(200, result);
//             }
//             catch (Exception ex)
//             {
//                 var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
//                 _logger.LogError(ex, "UpdateMemberBalance Error - " + JsonConvert.SerializeObject(ex));
//
//                 return StatusCode(400, res);
//             }
//         }
//
//     }
// }
