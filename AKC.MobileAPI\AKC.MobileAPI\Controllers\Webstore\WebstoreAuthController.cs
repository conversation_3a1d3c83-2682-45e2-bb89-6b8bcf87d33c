using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Webstore;
using AKC.MobileAPI.Helper;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using AKC.MobileAPI.Service.Webstore;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;
using Microsoft.IdentityModel.Tokens;
using AKC.MobileAPI.DTO.Downstream;

namespace AKC.MobileAPI.Controllers.Webstore
{
    [ApiController]
    [Route("api/webstore/auth")]
    [ApiConventionType(typeof(DefaultApiConventions))]
    public class WebstoreAuthController : ControllerBase
    {
        private readonly IDistributedCache _cache;
        private readonly IConfiguration _config;
        private readonly ILoyaltyAuditLogService _loggingService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IRewardMemberService _rewardService;
        private readonly ILogger<WebstoreAuthController> _logger;
        private readonly IStringLocalizer<WebstoreAuthController> _localizer;
        private readonly string _secretKey;

        public WebstoreAuthController(
            IRewardMemberService rewardService,
            IDistributedCache cache, IConfiguration config,
            ILoyaltyAuditLogService loggingService,
            ILogger<WebstoreAuthController> lg,
            IExceptionReponseService ex,
            IStringLocalizer<WebstoreAuthController> l)
        {
            _rewardService = rewardService;
            _cache = cache;
            _config = config;
            _loggingService = loggingService;
            _logger = lg;
            _exceptionReponseService = ex;
            _localizer = l;
            _secretKey = _config.GetSection("WebStoreSecretKey").Value;
            
        }

        // Trả ra 1 temp password để sử dụng (ActionKey, như các luồng khác
        [HttpPost("sme/send-otp-register")]
        public async Task<ActionResult<WebstoreBaseOutputDto<RequestOtpRegisterSMEOutput>>> RequestOtpRegisterSME([FromBody] RequestOtpRegisterSMERequest input)
        {
            var ipAddress = GetIp();
            try
            {
                _logger.LogInformation(" >> RequestOtpRegisterSME >> " + JsonConvert.SerializeObject(input));
                if (input == null || string.IsNullOrEmpty(input.SessionId) || string.IsNullOrEmpty(input.RepresentativePhoneNumber) || string.IsNullOrEmpty(input.SMELicenseCode))
                {
                    return BadRequest(WebstoreBaseOutputDto<RequestOtpRegisterSMEOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_INVALIDINPUT, "Invalid Input"));
                }
                if ("LYNKID" != input.SMEType)
                {
                    return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_INVALIDINPUT, "SME Type Must Be LYNKID"));
                }
                var cacheKey = "WebStore:OtpRegisterSme:" + input.SessionId;
                var cacheString = await _cache.GetStringAsync(cacheKey);
                if (!string.IsNullOrEmpty(cacheString))
                {
                    _logger.LogError(" >> RequestOtpRegisterSME >> " + input.SMELicenseCode + " >> duplicate sessionid >> " + input.SessionId);
                    return BadRequest(WebstoreBaseOutputDto<RequestOtpRegisterSMEOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_SessionId_Duplicated, "Session Duplicated"));
                }
                // Kiểm tra SĐT
                if (!WebstoreHelpers.IsValidPhoneNumber(input.RepresentativePhoneNumber))
                {
                    return BadRequest(WebstoreBaseOutputDto<RequestOtpRegisterSMEOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_PhonePlus84Required, "Phone Number is in invalid format"));
                }
                // Kiểm tra bên OPERATOR xem có tài khoản active chưa
                var checkOpereator = await _rewardService.WebstoreGetSmeInfo(new WebstoreGetSmeInfoRequest()
                {
                    SmeLicenseCode = input.SMELicenseCode, RepPhone = input.RepresentativePhoneNumber
                });
                if (checkOpereator != null && checkOpereator.Item.HasPassword)
                {
                    _logger.LogError(" >> RequestOtpRegisterSME >> " + input.SMELicenseCode + " >> Đã có thông tin login rồi và đã set password rồi");
                    return BadRequest(WebstoreBaseOutputDto<RequestOtpRegisterSMEOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_LicenseCodeAlreadyHasAccount, "Thông tin đăng ký đã tồn tại. Vui lòng thử đăng nhập"));
                }

                // CHƯA CÓ MẬT KHẨU VÀ LẠI ĐIỀN INPUT.PHONE KHAC VỚI PHIÊN BẢN DB THÌ SẼ END, BÁO LỖI RA NGOÀI FE
                if (checkOpereator != null && checkOpereator.Item.IsExisting && !checkOpereator.Item.HasPassword && checkOpereator.Item.RepPhone != input.RepresentativePhoneNumber)
                {
                    _logger.LogError(" >> RequestOtpRegisterSME >> " + input.SMELicenseCode + " >> LicenseCode và PhoneNumber ở đầu vào không match với LicenseCode và Phone ở CSDL. LicenseCode ở DB chưa set password");
                    return BadRequest(WebstoreBaseOutputDto<RequestOtpRegisterSMEOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_LICENSECODE_WITH_DIFF_PHONE_ALREADYEXISTS, 
                        "Thông tin đăng ký không hợp lệ, vui lòng kiểm tra lại ĐKKD và SĐT"));
                }
                
                _logger.LogInformation(" >> RequestOtpRegisterSME >> " + input.SMELicenseCode + " >> done check in dataservice, now sending SMS OTP...");
                // Send SMS OTP
                await _rewardService.SendOtp(new RewardMemberSendOtpInput()
                {
                    PhoneNumber = input.RepresentativePhoneNumber, SessionId = input.SessionId, SmsType = "RegisterOnWeb",
                });
                await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(new 
                {
                    RepPhone = input.RepresentativePhoneNumber, SMELicenseCode = input.SMELicenseCode
                }), new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(10)));
                _logger.LogInformation(" >> RequestOtpRegisterSME >> " + input.SMELicenseCode + " >> done check in dataservice, Done sent otp to " + input.RepresentativePhoneNumber);
                return Ok(WebstoreBaseOutputDto<RequestOtpRegisterSMEOutput>.Success(new RequestOtpRegisterSMEOutput()
                {
                    IsOtpSent = true
                }));
            }
            catch (RewardException ex)
            {
                var rewardExRes = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                _logger.LogWarning(
                    "Login failed: SMECode={SMECode}, Details: {Details}",
                    input.SMELicenseCode, JsonConvert.SerializeObject(rewardExRes));
                if (rewardExRes.Code == "InvalidCredentials")
                {
                    return BadRequest(new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.LOGIN_INVALID_CREDENTIALS, "Thông tin đăng nhập không chính xác"));
                }
                if (rewardExRes.Code == "SMEMemberNotExist")
                {
                    return BadRequest(new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.LOGIN_INVALID_CREDENTIALS, "Thông tin đăng nhập không chính xác"));
                }
                if (rewardExRes.Code == "PhoneNumberBlocked")
                {
                    return BadRequest(new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.COMMON_PhoneBlockedFromOtp, "Số điện thoại không thể nhận OTP lúc này. Vui lòng thử lại sau."));
                }
                if (rewardExRes.Code == "MaxOtpSendInput")
                {
                    return BadRequest(new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.COMMON_PhoneReachMaxOtpAttempts, 
                        "Số điện thoại không thể nhận OTP lúc này. Vui lòng chờ ít phút."));
                }

                throw new Exception("UnknownRewardException");
            }
            catch (Exception ex)
            {
                _logger.LogError(" >> RequestOtpRegisterSME >> Exception > " + ex.Message + " - " + ex.StackTrace);
                if (ex.GetType() == typeof(RewardException))
                {
                    var rewardExRes = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(rewardExRes.Code, 
                        rewardExRes.Message));
                }
                else
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR, 
                        "Unknown Issue"));
                }
            }
        }

        [HttpPost("sme/validate-otp-register")]
        public async Task<ActionResult<WebstoreBaseOutputDto<ValidateOtpRegisterSMEOutput>>>
            ValidateOtpRegisterSME(ValidateOtpRegisterSMERequest input)
        {
            try
            {
                _logger.LogInformation(" >> ValidateOtpRegisterSME >> " + JsonConvert.SerializeObject(input));
                if (input == null)
                {
                    return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_INVALIDINPUT, "Invalid Input"));
                }

                if ("LYNKID" != input.SMEType)
                {
                    return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_INVALIDINPUT, "SME Type Must Be LYNKID"));
                }
                var cacheKey = "WebStore:OtpRegisterSme:" + input.SessionId;
                var cacheString = await _cache.GetStringAsync(cacheKey);
                if (string.IsNullOrEmpty(cacheString))
                {
                    _logger.LogError(" >> ValidateOtpRegisterSME >> " + input.SessionId + " >> SessionID Expired");
                    return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_SESSIONOTP_EXPIRED, "Session Expired"));
                }

                dynamic cached = JsonConvert.DeserializeObject(cacheString);
                if (cached == null)
                {
                    _logger.LogError(" >> ValidateOtpRegisterSME >> " + input.SessionId + " >> SessionID in cache cannot parse: " + cacheString);
                    return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_SESSIONOTP_EXPIRED, "Session Expired"));
                }
                await _rewardService.VerityOtp(new RewardMemberVerifyOtpInput()
                {
                    PhoneNumber = cached.RepPhone, SessionId = input.SessionId, SmsType = "RegisterOnWeb",
                    OtpCode = input.OtpNumber
                });
                var stringPhone = cached.RepPhone.ToString();
                var stringSMELicenseCode = cached.SMELicenseCode.ToString();
                var claims = new[]
                {
                    new Claim("RepresentativePhoneNumber", stringPhone),
                    new Claim("SMELicenseCode", stringSMELicenseCode),
                    new Claim("MemberType", "SME"),
                    new Claim("SessionId", input.SessionId),
                    new Claim("ActionType", "RegisterSME"),
                };

                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_secretKey);

                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(claims),
                    Expires = DateTime.UtcNow.AddMinutes(3),
                    SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                    Audience = "LynkiD"
                };

                var token = tokenHandler.CreateToken(tokenDescriptor);
                var tokenString = tokenHandler.WriteToken(token);
                return Ok(WebstoreBaseOutputDto<ValidateOtpRegisterSMEOutput>.Success(new ValidateOtpRegisterSMEOutput()
                {
                    ActionKey = tokenString
                }));
            }
            catch (Exception ex)
            {
                _logger.LogError(" >> ValidateOtpRegisterSME >> Exception > " + ex.Message + " - " + ex.StackTrace);
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                    if ("InvalidOtp".Equals(res.Code))
                    {
                        return StatusCode(400, new RewardInvalidResponse()
                        {
                            Code = WebstoreConstants.LoginErrorCodes.COMMON_InvalidOTP, Message = "OTP không chính xác"
                            , numberOfRetries = res.numberOfRetries
                        });
                    }
                    if ("OtpExpired".Equals(res.Code))
                    {
                        return StatusCode(400, new RewardInvalidResponse()
                        {
                            Code = WebstoreConstants.LoginErrorCodes.COMMON_SESSIONOTP_EXPIRED, Message = "OTP đã hết hạn"
                            , numberOfRetries = res.numberOfRetries
                        });
                    }
                    if ("PhoneNumberReachMaxFailure".Equals(res.Code))
                    {
                        return StatusCode(400, new RewardInvalidResponse()
                        {
                            Code = WebstoreConstants.LoginErrorCodes.COMMON_TooManyWrongOtpAttempts, Message = "Bạn đã điền sai OTP quá nhiều lần. Hãy chờ và thử lại."
                            , numberOfRetries = res.numberOfRetries
                        });
                    }
                    if ("PhoneNumberBlocked".Equals(res.Code))
                    {
                        return StatusCode(400, new RewardInvalidResponse()
                        {
                            Code = WebstoreConstants.LoginErrorCodes.COMMON_PhoneBlockedFromOtp, Message = "Số điện thoại tạm thời bị chặn khỏi tính năng OTP. Vui lòng thử lại sau."
                            , numberOfRetries = res.numberOfRetries
                        });
                    }
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(res.Code, 
                        res.Message));
                }
                else
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR, 
                        "Unknown Issue"));
                }
                
            }
        }
        [HttpPost("sme/set-password-after-otp-of-register")]
        public async Task<ActionResult<WebstoreBaseOutputDto<SmeChangePasswordAfterVerificationOutput>>> SmeSetPasswordAfterOTPRegiser(SmeSetPasswordAfterOTPRegiseRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.ActionKey) ||
                    string.IsNullOrEmpty(request.SessionId))
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeChangePasswordAfterVerificationOutput>.Error(
                        WebstoreConstants.LoginErrorCodes.COMMON_INVALIDINPUT,
                        "Tham số đầu vào không hợp lệ"));
                }

                _logger.LogInformation(" >> SmeSetPasswordAfterOTPRegiser >> " + request.SessionId + " - " +
                                       request.ActionKey[..4] + "...");
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_secretKey); // same secret used when issuing token

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false, // adjust if you used Issuer
                    ValidateAudience = true,
                    ValidAudience = "LynkiD",
                    ClockSkew = TimeSpan.Zero // remove default 5 mins skew
                };
                
                var token = request.ActionKey.Replace("Bearer ", "").Replace("bearer ", "");
                SecurityToken validatedToken;
                var claimsPrincipal = tokenHandler.ValidateToken(token, validationParameters, out validatedToken);
                if (claimsPrincipal == null)
                {
                    _logger.LogError(" >> SmeVerifyOtpChangePass >> Exception >  Not found principle");
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_WRONGACTIONKEY, 
                                                            "Action Key invalid"));
                }
                if (string.IsNullOrEmpty(request.Password))
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_NEWPASSWORD_INVALID, "New Password Is Invalid"));
                }
                if (!string.Empty.Equals(CommonHelper.WebstoreValidatePasswordFormat(request.Password)))
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_NEWPASSWORD_INVALID_FORMAT, "New Password Is Invalid Format"));
                }
                var RepresentativePhoneNumber = claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "RepresentativePhoneNumber")?.Value;
                var SMELicenseCode = claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "SMELicenseCode")?.Value;
                var actionType = claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "ActionType")?.Value;
                var sessionId = claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "SessionId")?.Value;
                if (string.IsNullOrEmpty(SMELicenseCode) || string.IsNullOrEmpty(sessionId) || !"RegisterSME".Equals(actionType) || !sessionId.Equals(request.SessionId))
                {
                    _logger.LogError(" >> SmeVerifyOtpChangePass >> Exception >  memberCode not found found in principle claim or missing aciton type and membertype >> " + SMELicenseCode 
                                      + " - " + RepresentativePhoneNumber + " - " + actionType + " Input session: " + request.SessionId + " - Token Session: " + sessionId);
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_WRONGACTIONKEY, 
                        "Action Key invalid"));
                }
                // Get sme code from license
                var memberCode = "";
                try
                {
                    var fullInfo = await _rewardService.WebstoreGetFullSmeInfo(new WebstoreGetFullSmeInfoRequest()
                    {
                        LicenseCode = SMELicenseCode
                    });
                    if (fullInfo != null && fullInfo.Item != null && !string.IsNullOrEmpty(fullInfo.Item.Code))
                    {
                        memberCode = fullInfo.Item.Code;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(" >> Member license not yet exist");
                }
                if (string.IsNullOrEmpty(memberCode))
                {
                    // Chua co acc thi create new one
                    memberCode = "SME" + Guid.NewGuid().ToString().ToUpper().Replace("-", "");
                    var resCreate = await _rewardService.CreateSmeMember(new 
                    {
                        Code = memberCode,
                        LicenseNumber = SMELicenseCode,
                        ContactPhoneNumber = RepresentativePhoneNumber,
                        IsDeleted = false, Status = "A",
                        LongName = "CTY " + SMELicenseCode,
                    });
                    _logger.LogInformation(" >> Ket qua cua Create Sme Member >> " + JsonConvert.SerializeObject(resCreate));
                    if (resCreate.Items == null || string.IsNullOrEmpty(resCreate.Items.UserAddress))
                    {
                        return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR, 
                            "Lỗi xảy ra khi tạo member mới. Vui lòng thử lại sau."));
                    }
                }
                // Done verify OTP, now set password:
                var req = new WebstoreSetSmePasswordRequest()
                {
                    SmeCode = memberCode,
                    Password = request.Password
                };
                var ret = await this._rewardService.WebstoreSetSmePassword(req);
                return Ok(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Success(new SmeRequestOtpChangePasswordResponse()));
            }
            catch (SecurityTokenExpiredException ex)
            {
                _logger.LogError(" >> SmeVerifyOtpChangePass >> Exception > " + ex.Message + " - " + ex.StackTrace);
                return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_WRONGACTIONKEY, 
                                        "Action Key invalid or expired"));
            }
            catch (Exception ex)
            {
                _logger.LogError(" >> SmeVerifyOtpChangePass >> Exception > " + ex.Message + " - " + ex.StackTrace);
                if (ex.GetType() == typeof(RewardException))
                {
                    var rewardExRes = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(rewardExRes.Code, 
                        rewardExRes.Message));
                }
                else
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR, 
                        "Unknown Issue"));
                }
            }
        }

        [HttpPost("check-sme-login-info")]
        public async Task<ActionResult<WebstoreBaseOutputDto<CheckSmeLoginInfoOutput>>> CheckSmeLoginInfo([FromBody] SMELoginRequest request)
        {
            var ipAddress = GetIp();
            if (string.IsNullOrEmpty(request.SmeLicenseCode) ||
                string.IsNullOrEmpty(request.RepPhone))
            {
                _logger.LogWarning(
                    "Login failed: Invalid input for SmeLicenseCode={SmeLicenseCode}, Reason={Message}",
                    request.SmeLicenseCode ?? "null", "Vui lòng nhập đầy đủ thông tin");
                return BadRequest(new WebstoreErrorResponse(
                    WebstoreConstants.LoginErrorCodes.LOGIN_INVALID_CREDENTIALS,
                    "Vui lòng nhập đầy đủ thông tin"));
            }
            if ("LYNKID" != request.SMEType)
            {
                return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_INVALIDINPUT, "SME Type Must Be LYNKID"));
            }
            // Kiểm tra SĐT
            if (!WebstoreHelpers.IsValidPhoneNumber(request.RepPhone))
            {
                return BadRequest(WebstoreBaseOutputDto<WebstoreCheckSmeLoginInfoOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_PhonePlus84Required, "Số điện thoại sai định dạng"));
            }
            try
            {
                var operatorRes = await _rewardService.WebstoreGetSmeInfo(new WebstoreGetSmeInfoRequest()
                {
                    SmeLicenseCode = request.SmeLicenseCode, RepPhone = request.RepPhone
                });
                if (operatorRes == null ||  operatorRes.Item == null)
                {
                    return BadRequest(new WebstoreErrorResponse(
                        WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR,
                        "Có lỗi xảy ra. Vui lòng kiểm tra lại."));
                }

                if ("CheckForgetPassword".Equals(request.CheckMode) && operatorRes.Item.RepPhone != request.RepPhone)
                {
                    return BadRequest(new WebstoreErrorResponse(
                        WebstoreConstants.LoginErrorCodes.LOGIN_INVALID_CREDENTIALS,
                        "Thông tin ĐKKD và Số điện thoại không đúng"));
                }

                return Ok(WebstoreBaseOutputDto<CheckSmeLoginInfoOutput>.Success(new CheckSmeLoginInfoOutput()
                {
                    IsExisting = operatorRes.Item.IsExisting, MemberCode = operatorRes.Item.SmeCode,
                    Name = operatorRes.Item.SmeName,
                    WalletAddress = operatorRes.Item.SmeWalletAddress, HasPassword = operatorRes.Item.HasPassword
                }));

            }
            catch (RewardException ex)
            {
                var rewardExRes = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                return BadRequest(new WebstoreErrorResponse(rewardExRes.Code, rewardExRes.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during SME login for SMECode: {SMECode}", request.SmeLicenseCode);
                return StatusCode(500, new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR, "Có lỗi xảy ra, vui lòng thử lại sau"));
            }
        }

        [HttpPost("sme-login")]
        public async Task<ActionResult<WebstoreBaseOutputDto<LoginResponse>>> SMELogin([FromBody] SMELoginRequest request)
        {
            var ipAddress = GetIp();
            try
            {
                if (string.IsNullOrEmpty(request.SmeLicenseCode) || string.IsNullOrEmpty(request.Password) ||
                    string.IsNullOrEmpty(request.RepPhone))
                {
                    _logger.LogWarning(
                        "Login failed: Invalid input for SmeLicenseCode={SmeLicenseCode}, Reason={Message}, IP={IPAddress}",
                        request.SmeLicenseCode ?? "null", "Vui lòng nhập đầy đủ thông tin đăng nhập", ipAddress);
                    return BadRequest(new WebstoreErrorResponse(
                        WebstoreConstants.LoginErrorCodes.LOGIN_INVALID_CREDENTIALS,
                        "Vui lòng nhập đầy đủ thông tin đăng nhập"));
                }

                // Kiểm tra SĐT
                if (!WebstoreHelpers.IsValidPhoneNumber(request.RepPhone))
                {
                    return BadRequest(WebstoreBaseOutputDto<LoginResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_PhonePlus84Required, "Phone Number is in invalid format"));
                }
                // Kiểm tra SME tồn tại trên OPERATOR
                var smeInfoWrapper = await _rewardService.WebstoreGetSmeInfo(new WebstoreGetSmeInfoRequest()
                {
                    SmeLicenseCode = request.SmeLicenseCode,
                    RepPhone = request.RepPhone
                });
                if (smeInfoWrapper == null || smeInfoWrapper.Result != 200)
                {
                    _logger.LogWarning(
                        "Login failed: SmeLicenseCode={SmeLicenseCode} got issue when trying to login, Reason={Message}, IP={IPAddress}",
                        request.SmeLicenseCode, "Có lỗi xảy ra khi thực hiện đăng nhập", ipAddress);
                    return BadRequest(new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR,
                        "Có lỗi xảy ra khi thực hiện đăng nhập"));
                }

                var smeInfo = smeInfoWrapper.Item;
                if (smeInfo == null || smeInfo.IsExisting == false)
                {
                    _logger.LogWarning("Login failed: SmeLicenseCode={SmeLicenseCode} not found, Reason={Message}, IP={IPAddress}",
                        request.SmeLicenseCode, "Thông tin đăng nhập không chính xác", ipAddress);
                    return BadRequest(new WebstoreErrorResponse(
                        WebstoreConstants.LoginErrorCodes.LOGIN_INVALID_CREDENTIALS,
                        "Thông tin đăng nhập không chính xác"));
                }

                // Kiểm tra trạng thái Password: khi reward check được SmeCode và RepPhone thấy có data và đang cchưa setup mật khẩu, thì báo để vào luồng.
                if (!smeInfo.HasPassword)
                {
                    _logger.LogWarning(
                        "Login failed: SMECode={SmeLicenseCode} has no password setup, Reason={Message}, IP={IPAddress}",
                        request.SmeLicenseCode, "Tài khoản doanh nghiệp chưa được đăng ký. Vui lòng hoàn tất đăng ký trước khi đăng nhập.", ipAddress);
                    return BadRequest(new WebstoreErrorResponse(
                        WebstoreConstants.LoginErrorCodes.LOGIN_REGISTRATIONREQUIRED,
                        "Tài khoản doanh nghiệp chưa được đăng ký. Vui lòng hoàn tất đăng ký trước khi đăng nhập."));
                }

                // Xác thực Password
                var validationResult = await _rewardService.WebstoreLogin(new WebstoreCallRewardLoginRequest()
                {
                    SmeLicenseCode = request.SmeLicenseCode,
                    RepPhone = request.RepPhone,
                    Password = request.Password
                });
                var item = validationResult.Item;

                _logger.LogInformation(
                    "Login successful for SMECode={SMECode}, MemberCode={MemberCode}, IP={IPAddress}",
                    request.SmeLicenseCode, smeInfo.SmeCode, ipAddress);

                return Ok(WebstoreBaseOutputDto<LoginResponse>.Success(new LoginResponse()
                {
                    AccessToken = item.AccessToken, RefreshToken = item.RefreshToken, ExpiresAt = item.ExpiresAt,
                    MemberCode = item.SmeCode, MemberType = "SME"
                }));
            }
            catch (RewardException ex)
            {
                var rewardExRes = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                if (rewardExRes.Code == "InvalidCredentials")
                {
                    _logger.LogWarning(
                        "Login failed: SMECode={SMECode}, Invalid password, Reason={Message}, IP={IPAddress}",
                        request.SmeLicenseCode, "InvalidCredentials", ipAddress);
                    return BadRequest(new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.LOGIN_INVALID_CREDENTIALS, "Thông tin đăng nhập không chính xác"));
                }
                if (rewardExRes.Code == "AccountTemporarilyLocked")
                {
                    _logger.LogWarning(
                        "Login failed: SMECode={SMECode}, Invalid password, Reason={Message}, IP={IPAddress}",
                        request.SmeLicenseCode, "AccountTemporarilyLocked", ipAddress);
                    return BadRequest(new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.LOGIN_AccountTemporarilyLocked, "Tài khoản của bạn đã bị khóa tạm thời do nhập sai quá nhiều lần. Vui lòng thử lại sau 15 phút."));
                }
                if (rewardExRes.Code == "SMEMemberNotExist")
                {
                    _logger.LogWarning(
                        "Login failed: SMECode={SMECode}, Invalid info, Reason={Message}, IP={IPAddress}",
                        request.SmeLicenseCode, "SMEMemberNotExist", ipAddress);
                    return BadRequest(new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.LOGIN_INVALID_CREDENTIALS, "Thông tin đăng nhập không chính xác"));
                }

                if (rewardExRes.Code == "NotYetRegister")
                {
                    _logger.LogWarning(
                        "Login failed: SMECode={SmeLicenseCode}, Reason={Message}, IP={IPAddress}",
                        request.SmeLicenseCode, "NotYetRegister", ipAddress);
                    return BadRequest(new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.LOGIN_REGISTRATIONREQUIRED, "Tài khoản doanh nghiệp chưa được đăng ký. Vui lòng hoàn tất đăng ký trước khi đăng nhập."));
                }

                throw new Exception("UnknownRewardException");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during SME login for SMECode: {SMECode}", request.SmeLicenseCode);
                return StatusCode(500, new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR, "Có lỗi xảy ra, vui lòng thử lại sau"));
            }
        }
        private string GetIp()
        {
            var ip = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ip))
            {
                ip = HttpContext.Connection.RemoteIpAddress?.ToString();
            }
            else
            {
                // Nếu X-Forwarded-For chứa nhiều IP (qua nhiều proxy), lấy IP đầu tiên (client IP)
                ip = ip.Split(',')[0].Trim();
            }

            return ip;
        }
        [HttpPost("sme/request-otp-change-password")]
        public async Task<ActionResult<WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>>> SmeRequestOtpChangePassword(SmeRequestOtpChangePasswordRequest request)
        {
            try
            {
                _logger.LogInformation(" >> SmeRequestOtpChangePassword >> " + JsonConvert.SerializeObject(request));
                if (request == null || string.IsNullOrEmpty(request.SessionId) ||
                    string.IsNullOrEmpty(request.LicenseCode) || string.IsNullOrEmpty(request.RepPhone))
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_INVALIDINPUT, "Dữ liệu đầu vào không hợp lệ"));
                }
                // Kiểm tra SĐT
                if (!WebstoreHelpers.IsValidPhoneNumber(request.RepPhone))
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_PhonePlus84Required, "Phone Number không đúng định dạng"));
                }
                // Kiểm tra SME tồn tại trên OPERATOR
                var smeInfoWrapper = await _rewardService.WebstoreGetSmeInfo(new WebstoreGetSmeInfoRequest()
                {
                    SmeLicenseCode = request.LicenseCode, RepPhone = request.RepPhone
                });
                if (smeInfoWrapper == null || smeInfoWrapper.Result != 200)
                {
                    _logger.LogWarning(
                        "Check info failed: SmeLicenseCode={SmeLicenseCode} got issue when trying to login, Reason={Message}",
                        request.LicenseCode, "Có lỗi xảy ra khi thực hiện đăng nhập");
                    return BadRequest(WebstoreBaseOutputDto<GetAllTokenTransRespone>.Error(WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR, 
                        "Có lỗi xảy ra khi thực hiện đăng nhập"));
                }

                var smeInfo = smeInfoWrapper.Item;
                if (smeInfo == null || smeInfo.IsExisting == false)
                {
                    _logger.LogWarning("Check info failed: SmeLicenseCode={SmeLicenseCode} not found, Reason={Message}",
                        request.LicenseCode, "Thông tin đăng nhập không chính xác");
                    return BadRequest(WebstoreBaseOutputDto<GetAllTokenTransRespone>.Error(WebstoreConstants.LoginErrorCodes.LOGIN_INVALID_CREDENTIALS, 
                        "Thông tin không chính xác"));
                }

                // Kiểm tra trạng thái Password: khi reward check được SmeCode và RepPhone thấy có data và đang cchưa setup mật khẩu, thì báo để vào luồng.
                if (!smeInfo.HasPassword)
                {
                    _logger.LogWarning(
                        "Check info failed: SMECode={SmeLicenseCode} has no password setup, Reason={Message}",
                        request.LicenseCode, "Tài khoản doanh nghiệp chưa được đăng ký. Vui lòng hoàn tất đăng ký trước khi đăng nhập.");
                    return BadRequest(WebstoreBaseOutputDto<GetAllTokenTransRespone>.Error(WebstoreConstants.LoginErrorCodes.LOGIN_REGISTRATIONREQUIRED,
                        "Tài khoản doanh nghiệp chưa được đăng ký. Vui lòng hoàn tất đăng ký trước khi đăng nhập."));
                }
                // Như vậy là đã có set password. Giờ request OTP change password
                await _rewardService.SendOtp(new RewardMemberSendOtpInput()
                {
                    PhoneNumber = request.RepPhone, SessionId = request.SessionId, SmsType = "ChangePassword",
                });
                var cacheKey = "SME_CHANGE_PASSWORD_" + request.SessionId;
                await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(new SmeForCacheChangePasswordDTO
                    {
                        RepPhone = request.RepPhone, SmeCode = smeInfo.SmeCode,
                    }), new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(6)));
                return Ok(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Success(new SmeRequestOtpChangePasswordResponse()));
            }
            catch (Exception ex)
            {
                _logger.LogError(" >> SmeRequestOtpChangePass >> Exception > " + ex.Message + " - " + ex.StackTrace);
                var rewardExRes = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                if (rewardExRes.Code == "PhoneNumberBlocked")
                {
                    return BadRequest(new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.COMMON_PhoneBlockedFromOtp, "Số điện thoại không thể nhận OTP lúc này. Vui lòng thử lại sau."));
                }
                if (rewardExRes.Code == "MaxOtpSendInput")
                {
                    return BadRequest(new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.COMMON_PhoneReachMaxOtpAttempts, 
                        "Số điện thoại không thể nhận OTP lúc này. Vui lòng chờ ít phút."));
                }
                return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(rewardExRes.Code, 
                    rewardExRes.Message));
            }
        }
        [HttpPost("sme/verify-otp-change-password")]
        public async Task<ActionResult<WebstoreBaseOutputDto<WebStoreVerifyOtpOutput>>> SmeVerifyOtpChangePassword(SmeVerifyOtpChangePasswordRequest request)
        {
            try
            {
                _logger.LogInformation(" >> SmeVerifyOtpChangePassword >> " + JsonConvert.SerializeObject(request));
                var cacheKey = "SME_CHANGE_PASSWORD_" + request.SessionId;
                var cachedString = await _cache.GetStringAsync(cacheKey);
                var ipAddress = GetIp();
                if (string.IsNullOrEmpty(cachedString))
                {
                    _logger.LogError(" >> SmeVerifyOtpChangePassword >> SESSION EXPIRED");
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_SESSIONOTP_EXPIRED, 
                        "OTP Session không đúng hoặc đã hết hạn"));
                }
                var parsed = JsonConvert.DeserializeObject<SmeForCacheChangePasswordDTO>(cachedString);
                // Như vậy là đã có set password. Giờ request OTP change password
                await _rewardService.VerityOtp(new RewardMemberVerifyOtpInput()
                {
                    PhoneNumber = parsed.RepPhone, SessionId = request.SessionId, SmsType = "ChangePassword", OtpCode = request.OtpCode
                });
                try
                {
                    var claims = new[]
                    {
                        new Claim("SessionId", request.SessionId),
                        new Claim("MemberCode", parsed.SmeCode),
                        new Claim("MemberType", "SME"),
                        new Claim("ActionType", "ChangePassword"),
                    };

                    var tokenHandler = new JwtSecurityTokenHandler();
                    var key = Encoding.ASCII.GetBytes(_secretKey);

                    var tokenDescriptor = new SecurityTokenDescriptor
                    {
                        Subject = new ClaimsIdentity(claims),
                        Expires = DateTime.UtcNow.AddMinutes(3),
                        SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                        Audience = "LynkiD"
                    };

                    var token = tokenHandler.CreateToken(tokenDescriptor);
                    var tokenString = tokenHandler.WriteToken(token);
                    return Ok(WebstoreBaseOutputDto<WebStoreVerifyOtpOutput>.Success(new WebStoreVerifyOtpOutput()
                    {
                        ActionKey = tokenString
                    }));
                }
                catch(Exception ex)
                {
                    _logger.LogError(ex, "Error during Generating temp token for OTP request, IP={IPAddress}", ipAddress);
                    return StatusCode(500, new WebstoreErrorResponse(
                        WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR,
                        _localizer["InternalServerError"]));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(" >> SmeVerifyOtpChangePass >> Exception > " + ex.Message + " - " + ex.StackTrace);
                var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                if ("InvalidOtp".Equals(res.Code))
                {
                    return StatusCode(400, new RewardInvalidResponse()
                    {
                        Code = WebstoreConstants.LoginErrorCodes.COMMON_InvalidOTP, Message = "OTP không chính xác"
                        , numberOfRetries = res.numberOfRetries
                    });
                }
                if ("OtpExpired".Equals(res.Code))
                {
                    return StatusCode(400, new RewardInvalidResponse()
                    {
                        Code = WebstoreConstants.LoginErrorCodes.COMMON_SESSIONOTP_EXPIRED, Message = "OTP đã hết hạn"
                        , numberOfRetries = res.numberOfRetries
                    });
                }
                if ("PhoneNumberReachMaxFailure".Equals(res.Code))
                {
                    return StatusCode(400, new RewardInvalidResponse()
                    {
                        Code = WebstoreConstants.LoginErrorCodes.COMMON_TooManyWrongOtpAttempts, Message = "Bạn đã điền sai OTP quá nhiều lần. Hãy chờ và thử lại."
                        , numberOfRetries = res.numberOfRetries
                    });
                }
                if ("PhoneNumberBlocked".Equals(res.Code))
                {
                    return StatusCode(400, new RewardInvalidResponse()
                    {
                        Code = WebstoreConstants.LoginErrorCodes.COMMON_PhoneBlockedFromOtp, Message = "Số điện thoại tạm thời bị chặn khỏi tính năng OTP. Vui lòng thử lại sau."
                        , numberOfRetries = res.numberOfRetries
                    });
                }
                return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(res.Code, 
                    res.Message));
            }
        }
        [HttpPost("sme/change-password-after-otp-verification")]
        public async Task<ActionResult<WebstoreBaseOutputDto<SmeChangePasswordAfterVerificationOutput>>> SmeChangePasswordAfterVerification(SmeChangePasswordAfterVerificationRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.ActionKey) ||
                    string.IsNullOrEmpty(request.SessionId))
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeChangePasswordAfterVerificationOutput>.Error(
                        WebstoreConstants.LoginErrorCodes.COMMON_INVALIDINPUT,
                        "Tham số đầu vào không hợp lệ"));
                }

                _logger.LogInformation(" >> SmeChangePasswordAfterVerification >> " + request.SessionId + " - " +
                                       request.ActionKey.Substring(0, 4) + "...");
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_secretKey); // same secret used when issuing token

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false, // adjust if you used Issuer
                    ValidateAudience = true,
                    ValidAudience = "LynkiD",
                    ClockSkew = TimeSpan.Zero // remove default 5 mins skew
                };
                
                var token = request.ActionKey.Replace("Bearer ", "").Replace("bearer ", "");
                SecurityToken validatedToken;
                var claimsPrincipal = tokenHandler.ValidateToken(token, validationParameters, out validatedToken);
                if (claimsPrincipal == null)
                {
                    _logger.LogError(" >> SmeVerifyOtpChangePass >> Exception >  Not found principle");
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_WRONGACTIONKEY, 
                                                            "Action Key invalid"));
                }
                if (string.IsNullOrEmpty(request.NewPassword))
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_NEWPASSWORD_INVALID, "New Password Is Invalid"));
                }
                if (!string.Empty.Equals(CommonHelper.WebstoreValidatePasswordFormat(request.NewPassword)))
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_NEWPASSWORD_INVALID_FORMAT, "New Password Is Invalid Format"));
                }
                var memberCode = claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "MemberCode")?.Value;
                var memberType = claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "MemberType")?.Value;
                var actionType = claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "ActionType")?.Value;
                var sessionId = claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "SessionId")?.Value;
                if (string.IsNullOrEmpty(memberCode) || string.IsNullOrEmpty(sessionId) || !"SME".Equals(memberType) || !"ChangePassword".Equals(actionType) || !sessionId.Equals(request.SessionId))
                {
                    _logger.LogError(" >> SmeVerifyOtpChangePass >> Exception >  memberCode not found found in principle claim or missing aciton type and membertype >> "
                                     + memberCode + " - " + memberType + " - " + actionType + " Input session: " + request.SessionId + " - Token Session: " + sessionId);
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_WRONGACTIONKEY, 
                        "Action Key invalid"));
                }
                // Done verify OTP, now set password:
                var req = new WebstoreSetSmePasswordRequest()
                {
                    SmeCode = memberCode,
                    Password = request.NewPassword
                };
                var ret = await this._rewardService.WebstoreSetSmePassword(req);
                return Ok(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Success(new SmeRequestOtpChangePasswordResponse()));
            }
            catch (SecurityTokenExpiredException ex)
            {
                _logger.LogError(" >> SmeVerifyOtpChangePass >> Exception > " + ex.Message + " - " + ex.StackTrace);
                return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.COMMON_WRONGACTIONKEY, 
                                        "Action Key invalid or expired"));
            }
            catch (Exception ex)
            {
                _logger.LogError(" >> SmeVerifyOtpChangePass >> Exception > " + ex.Message + " - " + ex.StackTrace);
                if (ex.GetType() == typeof(RewardException))
                {
                    var rewardExRes = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(rewardExRes.Code, 
                        rewardExRes.Message));
                }
                else
                {
                    return BadRequest(WebstoreBaseOutputDto<SmeRequestOtpChangePasswordResponse>.Error(WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR, 
                        "Unknown Issue"));
                }
            }
        }
        
        [HttpPost("refresh-token")]
        public async Task<ActionResult<WebstoreBaseOutputDto<RefreshTokenRes>>> SmeRefreshToken([FromBody] WebstoreRefreshTokenRequest request)
        {
            var ipAddress = GetIp();
            try
            {
                if (string.IsNullOrEmpty(request.RefreshToken))
                {
                    _logger.LogWarning(
                        "Refresh token failed: Invalid input, Reason={Message}, IP={IPAddress}",
                        _localizer["InvalidRefreshToken"], ipAddress);
                    return BadRequest(new WebstoreErrorResponse(
                        WebstoreConstants.LoginErrorCodes.INVALID_REFRESH_TOKEN,
                        _localizer["InvalidRefreshToken"]));
                }

                // Call reward service to refresh the token
                var refreshResult = await _rewardService.WebstoreRefreshToken(new WebstoreRefreshTokenRequest()
                {
                    RefreshToken = request.RefreshToken,
                    MemberCode = request.MemberCode,
                });

                if (refreshResult == null || refreshResult.Result != 200)
                {
                    _logger.LogWarning(
                        "Refresh token failed: Invalid or expired token, Reason={Message}, IP={IPAddress}",
                        _localizer["InvalidOrExpiredRefreshToken"], ipAddress);
                    return BadRequest(new WebstoreErrorResponse(
                        WebstoreConstants.LoginErrorCodes.INVALID_REFRESH_TOKEN,
                        _localizer["InvalidOrExpiredRefreshToken"]));
                }

                var item = refreshResult.Item;
                if (item == null || string.IsNullOrEmpty(item.AccessToken))
                {
                    _logger.LogWarning(
                        "Refresh token failed: No valid token returned, Reason={Message}, IP={IPAddress}",
                        _localizer["InvalidOrExpiredRefreshToken"], ipAddress);
                    return BadRequest(new WebstoreErrorResponse(
                        WebstoreConstants.LoginErrorCodes.INVALID_REFRESH_TOKEN,
                        _localizer["InvalidOrExpiredRefreshToken"]));
                }

                _logger.LogInformation(
                    "Refresh token successful, New AccessToken issued, IP={IPAddress}",
                    ipAddress);

                return Ok(WebstoreBaseOutputDto<RefreshTokenRes>.Success(new RefreshTokenRes()
                {
                    AccessToken = item.AccessToken,
                    RefreshToken = item.RefreshToken,
                    ExpiresAt = item.ExpiresAt,
                    MemberCode = request.MemberCode,
                    MemberType = item.MemberType
                }));
            }
            catch (RewardException ex)
            {
                var rewardExRes = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                if (rewardExRes.Code == "MemberNotExist")
                {
                    _logger.LogWarning(
                        "Refresh token failed: Reason={Message}, IP={IPAddress}",
                        rewardExRes.Code, ipAddress);
                    return BadRequest(new WebstoreErrorResponse(
                        WebstoreConstants.LoginErrorCodes.REFRESHTOKEN_MEMBER_NOTACTIVEORNOTEXIST,
                        _localizer["MemberNotExistOrInactive"]));
                }
                if (rewardExRes.Code == "RefreshTokenAndMemberNotValid" || rewardExRes.Code == "RefreshTokenExpireOrInactive")
                {
                    _logger.LogWarning(
                        "Refresh token failed: Reason={Message}, IP={IPAddress}",
                        rewardExRes.Code, ipAddress);
                    return BadRequest(new WebstoreErrorResponse(
                        WebstoreConstants.LoginErrorCodes.INVALID_REFRESH_TOKEN,
                        _localizer["InvalidOrExpiredRefreshToken"]));
                }
                throw new Exception("UnknownRewardException");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during refresh token request, IP={IPAddress}", ipAddress);
                return StatusCode(500, new WebstoreErrorResponse(
                    WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR,
                    _localizer["InternalServerError"]));
            }
        }

        [HttpPost("verify-password")]
        public async Task<ActionResult<WebStoreVerifyPassWordOutput>> VerifyPassWord(WebStoreVerifyPassWordInput input)
        {
            try
            {
                var memberCode = HttpContext.Items["MemberCode"]?.ToString();
                if (string.IsNullOrEmpty(memberCode) || memberCode != input.MemberCode)
                {
                    return BadRequest(WebstoreBaseOutputDto<WebStoreVerifyPassWordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotMatch_Token, "Access Token không hợp lệ"));
                }
                // Kiểm tra SĐT
                if (!WebstoreHelpers.IsValidPhoneNumber(input.RepPhone))
                {
                    return BadRequest(WebstoreBaseOutputDto<WebStoreVerifyPassWordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_PhonePlus84Required, "Phone Number không đúng định dạng"));
                }
                var checkPassWord = await _rewardService.CheckSmePassword(new CheckSmePasswordRewardInput
                {
                    Password = input.PassWord,
                    RepPhone = input.RepPhone,
                    SmeLicenseCode = input.SmeLicenseCode
                });
                if(checkPassWord.Result != 200)
                {
                    return BadRequest(new WebstoreErrorResponse(WebstoreConstants.LoginErrorCodes.LOGIN_INVALID_CREDENTIALS, "Thông tin đăng nhập không chính xác"));
                }    
                var ipAddress = GetIp();
                try
                {
                    var claims = new[]
                    {
                        new Claim("MemberCode", input.MemberCode),
                        new Claim("ActionType", input.ActionType),
                    };

                    var tokenHandler = new JwtSecurityTokenHandler();
                    var key = Encoding.ASCII.GetBytes(_secretKey);

                    var tokenDescriptor = new SecurityTokenDescriptor
                    {
                        Subject = new ClaimsIdentity(claims),
                        Expires = DateTime.UtcNow.AddMinutes(3),
                        SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                        Audience = "LynkiD"
                    };

                    var token = tokenHandler.CreateToken(tokenDescriptor);
                    var tokenString = tokenHandler.WriteToken(token);
                    return Ok(WebstoreBaseOutputDto<WebStoreVerifyPassWordOutput>.Success(new WebStoreVerifyPassWordOutput()
                    {
                        ActionKey = tokenString
                    }));
                }
                catch(Exception ex)
                {
                    _logger.LogError(ex, "Error during verify password request, IP={IPAddress}", ipAddress);
                    return StatusCode(500, new WebstoreErrorResponse(
                        WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR,
                        _localizer["InternalServerError"]));
                }
            }
            catch (Exception e)
            {
                if (e.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(e);
                    if ("AccountTemporarilyLocked".Equals(res.Code))
                    {
                        return StatusCode(400, new RewardInvalidResponse()
                        {
                            Code = WebstoreConstants.LoginErrorCodes.LOGIN_AccountTemporarilyLocked, Message = "Tài khoản của bạn đã bị khóa tạm thời do nhập sai quá nhiều lần. Vui lòng thử lại sau 15 phút."
                            , remainingTime = res.remainingTime
                        });
                    }
                    if ("InvalidCredentials".Equals(res.Code))
                    {
                        return StatusCode(400, new RewardInvalidResponse()
                        {
                            Code = WebstoreConstants.LoginErrorCodes.LOGIN_INVALID_CREDENTIALS, Message = "Thông tin xác thực không chính xác"
                            , numberOfRetries = res.numberOfRetries
                        });
                    }
                }
            }
            return StatusCode(500, new WebstoreErrorResponse(
                                        WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR,
                                        _localizer["InternalServerError"]));
        }
    }

}