﻿using System.Text.RegularExpressions;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.Service.Exceptions;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.Helpers
{
    public class CommonHelper
    {
        public static RewardDataExceptionResponse GetErrorValidation(string errorCode, string errorMessage)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = errorMessage,
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }

        public static string GetCodeRedeemFromCode(string code)
        {
            //601 Address not found
            //602 City code not found
            //603 The province you entered does not exist
            //604 District code not found
            //605 The district you entered does not exist
            //606 Districts that are not in the city of your choice
            //1031 Connection error. Please try again
            //1032 There was an error in the order processing, the order result will be sent to you as soon as possible
            //2009 Can not update status through urbox

            switch (code)
            {
                case "601":
                case "602":
                case "603":
                case "604":
                case "605":
                case "606":
                    return "1029";
                case "1019":
                case "1024":
                    return "1023";
                case "1028":
                    return "1025";
                case "1037":
                case "1038":
                    return "1022";
                case "1031":
                case "1032":
                case "2009":
                    return "SystemError";
                case "MemberNotExitsOrNotActive":
                    return "MemberNotExist";
                case "BalanceNotEnough":
                    return "MemberNotEnoughToken";
                default:
                    return code;
            }
        }

        public static string GetMessageRedeemFromCode(string code)
        {
            var codeCheck = GetCodeRedeemFromCode(code);
            switch (codeCheck)
            {
                case "MemberNotEnoughToken":
                    return "Member not enough token";
                case "MemberNotExist":
                    return "LinkID member not exist";
                case "1022":
                    return "Can not redeem at this time";
                case "1023":
                    return "Gift not exist";
                case "1025":
                    return "Out of gift";
                case "1026":
                    return "Gift required coin of request is not valid";
                case "1028":
                    return "Out of gift (Egift)";
                case "1029":
                    return "Address redeem not found";
                case "225":
                    return "Gift is out of stock, please remove product from the cart.";
                case "410":
                    return "Your order is exceeding the allowed quantity (30 products/order). Please try again.";
                default:
                    return "System error";
            }
        }
        /**
         * Làm sạch các thông số của SĐT đưa về 1 chuẩn kiểu 84900111222 để làm key redis hoắc các task tương đương.
         */
        public static string NormalizePhoneNumberNoPlusSign(string phone)
        {
            if (string.IsNullOrWhiteSpace(phone))
            {
                return "";
            }

            var ret = phone.Trim();
            ret = ret.Replace(" ", "");

            if (ret.StartsWith("+84"))
            {
                ret = ret.Substring(1);
            }
            else if (ret.StartsWith("0"))
            {
                ret = "84" + ret.Substring(1);
            }
            else if (!ret.StartsWith("84"))
            {
                ret = "84" + ret;
            }
            return ret;
        }

        public static string WebstoreGetLoyaltyMessageByCode(string code)
        {
            switch (code)
            {
                case "1023": return "Quà không khả dụng vào lúc này";
                    default:
                    return "";
            }
        }

        public static string WebstoreValidatePasswordFormat(string password)
        {
            if (string.IsNullOrEmpty(password))
            {
                return "Password cannot be empty";
            }

            // Check length (8-50 characters)
            if (password.Length < 8 || password.Length > 64)
            {
                return "Password must be between 8 and 64 characters";
            }

            // Allows: a-z, A-Z, 0-9, and common special characters (!@#$%^&*()_+-=[]{}|;:,.<>?)
            string pattern = @"^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{}|;:,.<>?]+$";
            if (!Regex.IsMatch(password, pattern))
            {
                return "Password contains invalid characters. Only letters, numbers, and common special characters are allowed";
            }

            // Check for at least 1 uppercase, 1 lowercase, 1 digit, and 1 special character
            if (!Regex.IsMatch(password, @"[A-Z]"))
            {
                return "Password must contain at least one uppercase letter";
            }
            if (!Regex.IsMatch(password, @"[a-z]"))
            {
                return "Password must contain at least one lowercase letter";
            }
            if (!Regex.IsMatch(password, @"[0-9]"))
            {
                return "Password must contain at least one digit";
            }
            if (!Regex.IsMatch(password, @"[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]"))
            {
                return "Password must contain at least one special character";
            }

            return string.Empty;
        }

        public static string ConvertTo09XXX(string phoneNo)
        {
            if (string.IsNullOrEmpty(phoneNo))
            {
                return string.Empty;
            }

            var xphone = phoneNo.Trim();
            if (xphone.StartsWith("+84"))
            {
                xphone = "0" + xphone.Substring(3);
            }

            if (!xphone.StartsWith("0"))
            {
                xphone = "0" + xphone;
            }

            return xphone;
        }
    }
}
