﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.ExchangeTransaction;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.PartnerPointCaching;
using AKC.MobileAPI.DTO.ThirdParty.Dummy;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Abstract.ThirdParty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using AKC.MobileAPI.Service.ThirdParty.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.ThirdParty
{
    public class ThirdPartyDummyService : BaseThirdPartyDummyService, IThirdPartyDummyService
    {
        private readonly IRewardExchangeTransactionService _rewardExchangeTransactionService;
        private readonly IRewardMemberService _rewardMemberService;
        public ThirdPartyDummyService(IConfiguration configuration,
            IDistributedCache cache,
            IRewardMemberService rewardMemberService,
            IRewardExchangeTransactionService rewardExchangeTransactionService,
            ILogger<ThirdPartyDummyService> logger) : base(configuration, cache, logger)
        {
            _rewardExchangeTransactionService = rewardExchangeTransactionService;
            _rewardMemberService = rewardMemberService;
        }

        public async Task<LoyaltyThirdPartyPointExchangeOutput> PointExchange(LoyaltyThirdPartyPointExchangeInput input, string orderCode)
        {
            var transactionCode = string.Empty;
            if (string.IsNullOrWhiteSpace(orderCode))
            {
                transactionCode = LoyaltyHelper.GenTransactionCode("PointExchange");
            }
            else
            {
                transactionCode = orderCode;
            }
            var dummyRequest = new LoyaltyThirdPartyDummyExchangePointInput()
            {
                ExchangeAmount = input.ExchangeAmount,
                MemberCode = input.IdNumber,
                MerchantId = input.MerchantId,
                TransactionCode = transactionCode,
            };

            var dummyResponse = await PostLoyaltyAsync<LoyaltyResponse<LoyaltyThirdPartyDummyExchangePointOutput>>(LoyaltyApiUrl.THIRD_PARTY_POINT_EXCHANGE, dummyRequest);
            var requestReward = new RewardCreateExchangeTransactionInput()
            {
                TransactionCode = transactionCode,
                ExchangeAmount = input.ExchangeAmount,
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                PartnerBindingTxId = dummyResponse.Result.Transaction.TransactionId.ToString(),
            };

            try
            {
                var rewardExchange = await _rewardExchangeTransactionService.CreateExchangeTransactionIntegration(requestReward);

                return new LoyaltyThirdPartyPointExchangeOutput()
                {
                    Success = true,
                    Result = 200,
                    Items = new LoyaltyThirdPartyPointExchangeResult()
                    {
                        Transaction = new LoyaltyThirdPartyPointExchangeItem()
                        {
                            EquivalentTokenAmount = rewardExchange.Items.EquivalentTokenAmount,
                            ExchangeAmount = input.ExchangeAmount,
                            PartnerBindingTxId = rewardExchange.Items.PartnerBindingTxId,
                        }
                    },
                };
            }
            catch (Exception e)
            {
                var revertPoint = new LoyaltyThirdPartyDummyRevertPointInput()
                {
                    MemberCode = input.IdNumber,
                    TransactionId = dummyResponse.Result.Transaction.TransactionId
                };
                //await PostLoyaltyAsync<LoyaltyResponse<LoyaltyThirdPartyDummyRevertPointOutput>>(LoyaltyApiUrl.THIRD_PARTY_REVERT_POINT, revertPoint);
                // Revert point
                await retryRevertToken(revertPoint);
                throw e;
            }
        }

        public async Task<LoyaltyThirdPartyPointViewOutput> PointView(LoyaltyThirdPartyPointViewInput input)
        {
            var request = new LoyaltyThirdPartyDummyPointViewInput()
            {
                MemberCode = input.IdNumber,
            };
            var response = await PostLoyaltyAsync<LoyaltyResponse<LoyaltyThirdPartyDummyPointViewOutput>>(LoyaltyApiUrl.THIRD_PARTY_POINT_VIEW, request);
            return new LoyaltyThirdPartyPointViewOutput()
            {
                Success = true,
                Result = new LoyaltyThirdPartyPointViewResult()
                {
                    Member = new LoyaltyThirdPartyPointViewItem()
                    {
                        MemberCode = response.Result.Member.MemberCode,
                        CoinBalance = response.Result.Member.CoinBalance,
                    }
                }
            };
        }

        public async Task<RewardPartnerPoingCachingItems> UpdatePartnerCaching(LoyaltyThirdPartyDummyUpdatePartnerCachingInput input)
        {
            try
            {
                var request = new LoyaltyThirdPartyPointViewInput()
                {
                    IdNumber = input.IdNumber,
                    MerchantId = input.MerchantId,
                };
                var response = await PointView(request);
                return new RewardPartnerPoingCachingItems()
                {
                    PointBalance = response.Result.Member.CoinBalance,
                    Status = "S",
                    MerchantId = input.MerchantId
                };
            } catch
            {
                return new RewardPartnerPoingCachingItems()
                {
                    PointBalance = 0,
                    Status = "F",
                    MerchantId = input.MerchantId
                };
            }
        }

        public async Task<LoyaltyThirdPartyVerifyNationalIdOutput> VerifyNationalId(LoyaltyThirdPartyVerifyNationalIdInput input)
        {
            var request = new LoyaltyThirdPartyDummyVerifyNationalIdInput()
            {
                IdNumber = input.IdNumber,
                MerchantId = input.MerchantId,
                PhoneNumber = input.PhoneNumber
            };
            var resultDummy = await PostLoyaltyAsync<LoyaltyResponse<LoyaltyThirdPartyDummyVerifyNationalIdOutput>>(LoyaltyApiUrl.THIRD_PARTY_VERIFY_NATIONAL_ID, request);
            return new LoyaltyThirdPartyVerifyNationalIdOutput()
            {
                Result = new LoyaltyThirdPartyVerifyNationalIdResult()
                {
                    Otp = resultDummy.Result.Otp,
                },
                Success = true,
                IsChangedLoyalty = false,
            };
        }

        public async Task<LoyaltyThirdPartyVerifyOTPOutput> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input)
        {
            await _rewardMemberService.SaveRefreshToken(new RewardMemberSaveRefreshTokenInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                RefreshToken = "RefreshToken",
                IsChangedLoyalty = false,
            });
            var request = new LoyaltyThirdPartyDummyVerifyOTPInput()
            {
                IdNumber = input.IdNumber,
                Otp = input.OtpNumber,
                PhoneNumber = input.PhoneNumber,
            };
            var resultDummy = await PostLoyaltyAsync<LoyaltyResponse<LoyaltyThirdPartyDummyVerifyOTPOutput>>(LoyaltyApiUrl.THIRD_PARTY_VERIFY_OTP, request);
            return new LoyaltyThirdPartyVerifyOTPOutput()
            {
                Success = true,
                Result = 200,
                Items = new LoyaltyThirdPartyVerifyOTPResult()
                {
                    Member = new LoyaltyThirdPartyVerifyOTPItem()
                    {
                        Name = resultDummy.Result.Member.Name,
                        PartnerBalance = resultDummy.Result.Member.PartnerBalance,
                    }
                }
            };
        }

        public async Task<LoyaltyThirdPartyRevertPointOutput> RevertPoint(LoyaltyThirdPartyRevertPointInput input, HttpContext context)
        {
            var revertPoint = new LoyaltyThirdPartyDummyRevertPointInput()
            {
                MemberCode = input.MemberCode, // Get IdNumber from request exchange
                TransactionId = Convert.ToInt64(input.LoyaltlyTransactionId)
            };
            await PostLoyaltyAsync<LoyaltyResponse<LoyaltyThirdPartyDummyRevertPointOutput>>(LoyaltyApiUrl.THIRD_PARTY_REVERT_POINT, revertPoint);
            return new LoyaltyThirdPartyRevertPointOutput()
            {
                Error = null,
                Success = true,
                Message = "Success",
            };
        }

        private async Task<bool> RevertPointInternal(LoyaltyThirdPartyDummyRevertPointInput input)
        {
            try
            {
                await PostLoyaltyAsync<LoyaltyResponse<LoyaltyThirdPartyDummyRevertPointOutput>>(LoyaltyApiUrl.THIRD_PARTY_REVERT_POINT, input);
                return true;
            } catch
            {
                return false;
            }
        }

        private async Task retryRevertToken(LoyaltyThirdPartyDummyRevertPointInput input)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await RevertPointInternal(input);
                if (result == true)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        public async Task<LoyaltyThirdPartyPointExchangeOutput> PointExchangeIntegration(LoyaltyThirdPartyPointExchangeInput input, string orderCode = null)
        {
            var transactionCode = string.Empty;
            if (string.IsNullOrWhiteSpace(orderCode))
            {
                transactionCode = LoyaltyHelper.GenTransactionCode("PointExchange");
            }
            else
            {
                transactionCode = orderCode;
            }
            var dummyRequest = new LoyaltyThirdPartyDummyExchangePointInput()
            {
                ExchangeAmount = input.ExchangeAmount,
                MemberCode = input.IdNumber,
                MerchantId = input.MerchantId,
                TransactionCode = transactionCode,
            };

            try
            {
                var dummyResponse = await PostLoyaltyAsync<LoyaltyResponse<LoyaltyThirdPartyDummyExchangePointOutput>>(LoyaltyApiUrl.THIRD_PARTY_POINT_EXCHANGE, dummyRequest);

                return new LoyaltyThirdPartyPointExchangeOutput()
                {
                    Success = true,
                    Result = 200,
                    Items = new LoyaltyThirdPartyPointExchangeResult()
                    {
                        Transaction = new LoyaltyThirdPartyPointExchangeItem()
                        {
                            EquivalentTokenAmount = input.ExchangeAmount,
                            ExchangeAmount = input.ExchangeAmount,
                            PartnerBindingTxId = dummyResponse.Result.Transaction.TransactionId.ToString(),
                        }
                    },
                };
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
