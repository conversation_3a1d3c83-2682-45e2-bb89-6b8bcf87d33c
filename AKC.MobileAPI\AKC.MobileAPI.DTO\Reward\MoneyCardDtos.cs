using System;
using System.Collections.Generic;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;

namespace AKC.MobileAPI.DTO.Reward
{
    public class GetAvailableBalanceOfMoneyCardInput
    {
        public string MemberCode { get; set; }
        public string MemberType { get; set; }
        public List<string> MoneyCardCodes { get; set; }
    }
    public class GetMoneyCardOfMemberInput
    {
        public string MemberCode { get; set; }
        public string CardCode { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
        public string Status { get; set; }
        public bool? AvailableRedeem { get; set; }
    }

    public class GetAvailableBalanceOfMoneyCardOutput
    {
        public GetAvailableBalanceOfMoneyCardOutputInner Items { get; set; }
    }

    public class GetAvailableBalanceOfMoneyCardOutputInner
    {
        public decimal AvailableBalance { get; set; }
    }

    public class GetMoneyCardOfMemberOutput
    {
        public List<GetMoneyCardOfMemberOutputInner> Items { get; set; }
        
        public int TotalCount { get; set; }
    }
    public class GetMoneyCardOfMemberOutputInner
    {
        public int Id { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool? IsDeleted { get; set; }
        public string MerchantID { get; set; }
        public string MemberCode { get; set; }
        public DateTime? BusinessTime { get; set; }
        public int? CampaignID { get; set; }
        public string PhoneNumber { get; set; }
        public string CardCode { get; set; }
        public string RefCode { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public decimal? TokenAmount { get; set; }
        public decimal? RemainingAmount { get; set; }
        public string Status { get; set; }
        public string Note { get; set; }
        public string GiftGroup { get; set; }
        public string MerchantPhoto { get; set; }
    }

    public class RedeemWithMoneyCardInput : LoyaltyCreateRedeemTransactionInput
    {
        public List<string> MoneyCardCodes { get; set; }
        public string PinCode { get; set; }
        public string DeviceId { get; set; }
    }
    public class RedeemWithMoneyCardOutput
    {
        public RedeemWithMoneyCardOutputInner Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }
    public class RedeemWithMoneyCardOutputInner
    {
        public List<RedeemWithMoneyCardOutputInner2> Items { get; set; }
        public int TotalCount { get; set; }
        public string Exception { get; set; }
        public string Messages { get; set; }
        public bool SuccessedRedeem { get; set; }
        public bool IsNotEnoughBalance { get; set; }

        public bool Timeout { get; set; }
        public string ActionFilter { get; set; }
        public string ActionCode { get; set; }
        public string TransactionCode { get; set; }

    }

    public class RedeemWithMoneyCardOutputInner2
    {
        public string Code { get; set; }
        public decimal TotalCoin { get; set; }
        public decimal RequiredCoin { get; set; }
        public string Status { get; set; }
        public DateTime Date { get; set; }
        public string Description { get; set; }
        public EGiftData EGift { get; set; }
    }

    public class RevertRedeemUsingMoneyCardRequest
    {
        public string OrderCode { get; set; }
        public string MemberCode { get; set; }
        public string Reason { get; set; }
    }

    public class RevertRedeemUsingMoneyCardOutput
    {
    }

    public class RedeemUsingMoneyCardOutput
    {
        public string Message { get; set; }
        public int Result { get; set; }
        public List<RedeemUsingMoneyCardOutputItem> Item { get; set; }
    }

    public class RedeemUsingMoneyCardOutputItem
    {
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool IsDeleted { get; set; }
        public int CardId { get; set; }
        public string FromWalletAddress { get; set; }
        public string ToWalletAddress { get; set; }
        public decimal? TokenAmount { get; set; }
        public string ActionType { get; set; }
        public string OrderCode { get; set; }
        public string Status { get; set; }
        public string Reason { get; set; }
        public int UserId { get; set; }
        public int Id { get; set; }
    }
    public class RedeemUsingMoneyCardRequest
    {
        public string OrderCode { get; set; }
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
        public string Reason { get; set; }
        public List<string> MoneyCardIds { get; set; }
        public decimal TokenAmount { get; set; }
        public int MerchantId { get; set; }
    }
    public class GetCardTransactionOfMemberInput
    {
        public string MemberCode { get; set; }

        public string MemberId { get; set; }

        public int? MerchantId { get; set; }

        public string OrderCode { get; set; }

        public string CardCode { get; set; }

        public string ActionType { get; set; }

        public int? CardTransactionId { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
    }

    public class GetCardTransactionOfMemberDto
    {
        public string MemberCode { get; set; }

        public string MemberId { get; set; }

        public int? MerchantId { get; set; }

        public string OrderCode { get; set; }

        public string CardCode { get; set; }

        public string ActionType { get; set; }

        public int skipCount { get; set; }
        public int maxResultCount { get; set; }
        public int? CardTransactionId { get; set; }
    }

    public class GetCardTransactionOfMemberOutput
    {
        public int TotalCount { get; set; }

        public List<GetCardTransactionOfMemberOutputInner> Items { get; set; }
    }

    public class GetCardTransactionOfMemberOutputInner
    {
        public int Id { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public DateTime ExpiryDate { get; set; }
        public int CardId { get; set; }
        public string ActionType { get; set; }
        public string UserAddress { get; set; }
        public string OrderCode { get; set; }
        public string FromWalletAddress { get; set; }
        public double TokenAmount { get; set; }
        public string ToWalletAddress { get; set; }
        public string Reason { get; set; }
        public string OriginalId { get; set; }
        public string Status { get; set; }
        public string BaselineStatus { get; set; }
        public string MemberCode { get; set; }
        public string MerchantId { get; set; }
        public string PhoneNumber { get; set; }
        public string CardCode { get; set; }
        public string RefCode { get; set; }
        public double RemainingAmount { get; set; }
        public int CampaignID { get; set; }
    }

    public class GetGroupCodesOfGiftOutput
    {
        public GetGroupCodesOfGiftOutputInner Result { get; set; }
    }

    public class GetGroupCodesOfGiftOutputInner
    {
        public int TotalCount { get; set; }
        public List<string> Items { get; set; }
    }

}