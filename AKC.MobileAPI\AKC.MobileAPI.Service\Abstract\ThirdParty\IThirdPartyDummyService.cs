﻿using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.PartnerPointCaching;
using AKC.MobileAPI.DTO.ThirdParty.Dummy;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.ThirdParty
{
    public interface IThirdPartyDummyService
    {
        Task<LoyaltyThirdPartyPointViewOutput> PointView(LoyaltyThirdPartyPointViewInput input);
        Task<LoyaltyThirdPartyVerifyNationalIdOutput> VerifyNationalId(LoyaltyThirdPartyVerifyNationalIdInput input);
        Task<LoyaltyThirdPartyVerifyOTPOutput> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input);
        Task<LoyaltyThirdPartyPointExchangeOutput> PointExchange(LoyaltyThirdPartyPointExchangeInput input, string orderCode = null);
        Task<RewardPartnerPoingCachingItems> UpdatePartnerCaching(LoyaltyThirdPartyDummyUpdatePartnerCachingInput input);
        Task<LoyaltyThirdPartyRevertPointOutput> RevertPoint(LoyaltyThirdPartyRevertPointInput input, HttpContext context);
        Task<LoyaltyThirdPartyPointExchangeOutput> PointExchangeIntegration(LoyaltyThirdPartyPointExchangeInput input, string orderCode = null);
    }
}
