﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberUpdateUsagePriorityOutput
    {
        public int Result { get; set; }
        public string Message { get; set; }
        public RewardMemberUpdateUsagePriorityResult Item { get; set; }
    }

    public class RewardMemberUpdateUsagePriorityResult
    {
        public string MemberCode { get; set; }
        public List<RewardMemberUpdateUsagePriorityOutputItem> Items { get; set; }
    }

    public class RewardMemberUpdateUsagePriorityOutputItem
    {
        public int MerchantId { get; set; }
        public int Priority { get; set; }
    }
}
