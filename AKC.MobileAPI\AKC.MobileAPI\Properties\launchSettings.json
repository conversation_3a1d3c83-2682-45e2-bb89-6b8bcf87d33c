{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:54201", "sslPort": 44365}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "AKC.MobileAPI": {"commandName": "Project", "launchBrowser": true, "launchUrl": "", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:5001;http://localhost:5000"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "environmentVariables": {"ASPNETCORE_URLS": "https://+:443;http://+:80", "ASPNETCORE_HTTPS_PORT": "44366"}, "httpPort": 54202, "useSSL": true, "sslPort": 44366}}}