﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class LoyaltyGetAllGiftGroupByMemberInput
    {
        [Required]
        public string MemberCode { get; set; }

        public string Sorting { get; set; }

        [Range(0, int.MaxValue)]
        public int SkipCount { get; set; }

        [Range(0, int.MaxValue)]
        public int MaxResultCount { get; set; }
    }
}
