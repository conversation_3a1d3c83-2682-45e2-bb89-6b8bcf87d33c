﻿using System;
using System.Collections.Generic;


namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{

    public class GetListQuestByActivityOutputDto
    {
        public ListResultQuestByActivity Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }

    }
    public class ListResultQuestByActivity
    {
        public int TotalCount { get; set; }

        public List<ListItemsQuestByActivity> Items { get; set; }
    }

    public class ListItemsQuestByActivity
    {
        public DateTime? CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string Code { get; set; }

        public string Name { get; set; }
        public virtual string Description { get; set; }

        public virtual string LinkAvatar { get; set; }

        public string Status { get; set; }

        public DateTime FromDate { get; set; }

        public DateTime ToDate { get; set; }

        public string Tag { get; set; }

        public string State { get; set; }
        public List<MissionDto> Missions { get; set; }
        public int? MissionCount { get; set; }
        public DateTime? Date { get; set; }

        public int? Id { get; set; }

        public int? QuestBaselineId { get; set; }
        public decimal Point { get; set; }
        public decimal Coin { get; set; }
        public decimal Actual { get; set; }
        public decimal Target { get; set; }
    }


}
