﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftSearch
{
    public class SearchGiftAndGiftGroupAndBrandForView
    {
        public SearchBrandForView ListBrand { get; set; } = new SearchBrandForView();
        public GiftGroupForViewMB ListGiftGroup { get; set; } = new GiftGroupForViewMB();

        public GiftForViewMB ListGiftForView { get; set; } = new GiftForViewMB();
    }

    public class BrandDTO
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string LinkLogo { get; set; }
        public bool? IsFavourite { get; set; } = false;
        public string CoverPhoto { get; set; }
        public int? DisplayOrder { get; set; }
        public int totalCountVoucher { get; set; } = 0;
    }

    public class Count
    {
        public int totalCount { get; set; }
    }

    public class SearchBrandForView
    {
        public List<BrandDTO> ListBranDTO { get; set; } = new List<BrandDTO>();
        public int totalCount { get; set; }
    }

    public class GiftGroupForViewMB
    {
        public List<GiftGroupDTO> ListGiftGroup { get; set; } = new List<GiftGroupDTO>();
        public int totalCount { get; set; } = 0;

    }

    public class GiftGroupDTO
    {
        public List<GiftDTO> ListGift { get; set; } = new List<GiftDTO>();
        public string GroupName { get; set; }
        public string GroupCode { get; set; }
    }


    public class GiftDTO
    {
        public int ID { get; set; }
        public string Code { get; set; }

        public string Name { get; set; }

        public decimal RequiredCoin { get; set; }
        public decimal DiscountPrice { get; set; }
        public decimal FullPrice { get; set; }
        public string GiftImageLink { get; set; }
    }

    public class GiftForViewMB
    {
        public List<GiftDTO> ListGift { get; set; } = new List<GiftDTO>();
        public int totalCount { get; set; } = 0;
    }

    public class SearchGiftAndGiftGroupAndBrandForViewOutput
    {
        public SearchGiftAndGiftGroupAndBrandForView Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }
    public class LoyaltyGetGiftInfoInput
    {
        public string MemberCode { get; set; }
        public string OrderCode { get; set; }
    }
    
    public class GiftInfoDto
    {
        public string GiftCode { get; set; }
        public string GiftName { get; set; }
        public decimal RequiredCoin { get; set; }
        public List<string> GiftPhoto { get; set; }
        public string BrandName { get; set; }

        public string BrandPhoto { get; set; }
        public int BrandId { get; set; }
    }

    public class LoyaltyGetGiftInfoOutput
    {
        public GiftInfoDto Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }
}
