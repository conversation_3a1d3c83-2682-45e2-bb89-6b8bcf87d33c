﻿using AKC.MobileAPI.DTO.Loyalty.Brand;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/Brand")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    //[AllowAnonymous]
    public class LoyaltyBrandController : ControllerBase
    {
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILogger _logger;
        private readonly ILoyaltyBrandService _loyaltyBrandService;
        private readonly ICommonHelperService _commonHelperService;
        public LoyaltyBrandController(
            IExceptionReponseService exceptionReponseService,
            ILogger<LoyaltyBrandController> logger,
            ILoyaltyBrandService loyaltyBrandService,
            ICommonHelperService commonHelperService)
        {
            _exceptionReponseService = exceptionReponseService;
            _logger = logger;
            _loyaltyBrandService = loyaltyBrandService;
            _commonHelperService = commonHelperService;
        }

        [HttpGet]
        [Route("GetAll")]
        public async Task<ActionResult<GetBrandOutput>> GetAll([FromQuery] GetBrandInput input)
        {
            try
            {
                var result = await _loyaltyBrandService.GetAll(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Brand GetAll Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllBrandByCategoryCode")]
        public async Task<ActionResult<GetBrandByCategoryOutput>> GetAllBrandByCategoryCode([FromQuery] GetBrandInput input)
        {
            try
            {
                input.MaxResultCount = input.MaxResultCount > 0 ? input.MaxResultCount : 50;
                var result = await _loyaltyBrandService.GetAllBrandByCategoryCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Brand GetAllBrandByCategoryCode Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetListProminentBrand")]
        public async Task<ActionResult<GetBrandByCategoryOutput>> GetListProminentBrand([FromQuery] GetProminentBrandInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }

                if (!"next".Equals(input.Ver))
                {
                    return StatusCode(200, new GetBrandByCategoryOutput()
                    {
                        Success = true, Error = null, Result = new GetListBrandByCategoryOutput()
                        {
                            Items = new List<Item>(),
                            TotalCount = 0
                        },
                        TargetUrl = "", UnAuthorizedRequest = false
                    });
                }
                var result = await _loyaltyBrandService.GetProminentBrand(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetAllByMemberCode Infor Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }
    }
}
