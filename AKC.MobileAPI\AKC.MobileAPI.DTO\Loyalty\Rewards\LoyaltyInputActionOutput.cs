﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Rewards
{
    public class LoyaltyInputActionOutput
    {
        public List<InputActionOutput> Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class InputActionOutput
    {
        public decimal RewardedPoint { get; set; }
        public decimal RewardedCoin { get; set; }
    }

    public class LoyaltyInputActionDtoOutput
    {
        public List<InputActionOutputDto> Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class InputActionOutputDto
    {
        public InputActionRewardOutputDto Member { get; set; }
        public InputActionMlmOutputDto MlmResult { get; set; }
    }

    public class InputActionRewardOutputDto
    {
        public decimal RewardedPoint { get; set; }
        public decimal RewardedCoin { get; set; }
    }

    public class InputActionMlmOutputDto
    {
        public List<InputActionMlmItemDto> MLMDealerChannel { get; set; }
        public List<InputActionMlmItemDto> MLMDistributionChannel { get; set; }
    }

    public class InputActionMlmItemDto
    {
        public bool Success { get; set; }
        public int Level { get; set; }
        public int ErrorCode { get; set; }
        public string ErrorMessages { get; set; }
    }
}
