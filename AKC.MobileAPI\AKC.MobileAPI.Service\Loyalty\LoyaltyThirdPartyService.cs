﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory.V2;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.Merchant;
using AKC.MobileAPI.DTO.Reward.PartnerPointCaching;
using AKC.MobileAPI.DTO.ThirdParty.Appota;
using AKC.MobileAPI.DTO.ThirdParty.Dummy;
using AKC.MobileAPI.DTO.ThirdParty.VPBank;
using AKC.MobileAPI.Service.Abstract;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Abstract.ThirdParty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyThirdPartyService : BaseLoyaltyService, ILoyaltyThirdPartyService
    {
        private readonly IThirdPartyVPBankService _thirdPartyVPBankService;
        private readonly IThirdPartyVPBankSecuritiesService _vpBankSecuritiesService;
        private readonly IThirdPartyAppotaService _thirdPartyAppotaService;
        private readonly ISkyJoyIntegrationService _skyJoyIntegrationService;
        private readonly IRewardPartnerPointCachingService _rewardPartnerPointCachingService;
        private readonly IThirdPartyDummyService _thirdPartyDummyService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IRewardExchangeTransactionService _rewardExchangeTransactionService;
        private readonly IStorageS3Service _storageS3;
        private readonly ILogger<LoyaltyThirdPartyService> _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IRewardMerchantService _rewardMerchantService;
        public LoyaltyThirdPartyService(
            IConfiguration configuration,
            IDistributedCache cache,
            IThirdPartyVPBankService thirdPartyVPBankService,
            IThirdPartyAppotaService apt,
            IRewardPartnerPointCachingService rewardPartnerPointCachingService,
            IThirdPartyDummyService thirdPartyDummyService,
            IRewardMemberService rewardMemberService,
            IRewardExchangeTransactionService rewardExchangeTransactionService,
            IStorageS3Service storageS3,
            IRewardMerchantService rewardMerchantService,
            IThirdPartyVPBankSecuritiesService vps,
            ISkyJoyIntegrationService sk,
            ILogger<LoyaltyThirdPartyService> lg,
            IExceptionReponseService ie
        ) : base(configuration, cache)
        {
            _vpBankSecuritiesService = vps;
            _thirdPartyVPBankService = thirdPartyVPBankService;
            _rewardPartnerPointCachingService = rewardPartnerPointCachingService;
            _thirdPartyDummyService = thirdPartyDummyService;
            _rewardMemberService = rewardMemberService;
            _rewardExchangeTransactionService = rewardExchangeTransactionService;
            _storageS3 = storageS3;
            _thirdPartyAppotaService = apt;
            _rewardMerchantService = rewardMerchantService;
            _skyJoyIntegrationService = sk;
            _logger = lg;
            _exceptionReponseService = ie;
        }

        public async Task<LoyaltyThirdPartyPointViewOutput> PointView(LoyaltyThirdPartyPointViewInput input, HttpContext context)
        {
            var merchant = getMerchantConfigById(input.MerchantId);
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.PointView(input, context);
                case "Appota":
                    return await _thirdPartyAppotaService.PointView(input, context);
                default:
                    // For dump data
                    return await _thirdPartyDummyService.PointView(input);
            }
            throw new ArgumentException("System error");
        }

        public Task<LoyaltyThirdPartyRemoveConnectedMerchantOutput> RemoveConnectedMerchant(RewardMemberUpdateOutputDto member,
            LoyaltyThirdPartyRemoveConnectedMerchant input, GetPartnerMemberInfoByConnectionOutput cif)
        {
            var memberInfo = member.Item;
            if (memberInfo.ListMerchantRemovedConnectManual != null && memberInfo.ListMerchantRemovedConnectManual.Count > 0)
            {
                var taskListMerchantRemove = new List<Task>();
                var listMerchantRemoveResult = new List<LoyaltyThirdPartyRemoveConnectedMerchantOutput>();
                Task taskRunAllExchange = Task.Run(() =>
                {
                    var optionsLoop = new ParallelOptions
                    {
                        MaxDegreeOfParallelism = 10
                    };
                    Parallel.ForEach(memberInfo.ListMerchantRemovedConnectManual, optionsLoop, async merchantId =>
                    {
                        var merchant = getMerchantConfigById(merchantId);
                        switch (merchant)
                        {
                            // For vpbank
                            case "VPBank":
                                var request = new LoyaltyThirdPartyRemoveConnectedMerchant()
                                {
                                    MemberCode = memberInfo.MemberCode,
                                    LinkID_MemberID = memberInfo.MemberId,
                                };
                                var merchantRemove = _thirdPartyVPBankService.RemoveConnectedMerchant(member, request).Result;
                                listMerchantRemoveResult.Add(merchantRemove);
                                break;
                            case "VPBankSecurities":
                                request = new LoyaltyThirdPartyRemoveConnectedMerchant()
                                {
                                    MemberCode = memberInfo.MemberCode,
                                    LinkID_MemberID = memberInfo.MemberId,
                                };
                                merchantRemove = _vpBankSecuritiesService.RemoveConnectedMerchant(member, request).Result;
                                listMerchantRemoveResult.Add(merchantRemove);
                                break;
                            case "SkyJoy":
                                try
                                {
                                    _logger.LogInformation(" >> unlink sky joy: got data in operator: " + JsonConvert.SerializeObject(cif));
                                    if (cif != null && cif.Item != null && cif.Item.HasConnection)
                                    {
                                        var sjUnlinkMemberOutput = _skyJoyIntegrationService.UnlinkMember(new SJUnlinkMemberInput()
                                        {
                                            partnerMemberId = memberInfo.MemberCode, skyjoyId = cif.Item.RelatedCif
                                        }).Result;
                                        _logger.LogInformation(" >> unlink sky joy: result when call to skyjoy: " + JsonConvert.SerializeObject(sjUnlinkMemberOutput));

                                        var removeConnectRequest = new RemoveConnectionRequest()
                                        {
                                            PartnerMemberCode = cif.Item.RelatedCif,
                                            LynkiDMemberCode = memberInfo.MemberCode,
                                            ConnectedToMerchantId = merchantId,
                                            LynkiDMemberId = memberInfo.MemberId
                                        };
                                        _logger.LogInformation($"RemoveConnection___Request: {JsonConvert.SerializeObject(removeConnectRequest)}");
                                        var resultRemoveConnect = _rewardMemberService.RemoveConnection(removeConnectRequest).Result;
                                        _logger.LogInformation($"RemoveConnection___Response: {JsonConvert.SerializeObject(resultRemoveConnect)}");
                                    }
                                }
                                catch (Exception e)
                                {
                                    _logger.LogError(" >> Unlink in skyjoy side: " + input.MemberCode + " - " + e.Message + " - " + e.StackTrace);
                                }
                                break;
                            default:
                                break;
                        }
                    });
                });
                taskListMerchantRemove.Add(taskRunAllExchange);
                Task.WaitAll(taskListMerchantRemove.ToArray());
            }
            return Task.FromResult(new LoyaltyThirdPartyRemoveConnectedMerchantOutput()
            {
                Status = true
            });
        }

        public async Task<LoyaltyThirdPartyVerifyNationalIdOutput> VerifyNationalId(LoyaltyThirdPartyVerifyNationalIdInput input, HttpContext context, string authorization)
        {
            var isCheckPhoneNumberExchangeList = _configuration.GetSection("IsCheckPhoneNumberExchangeList").Value;
            if (isCheckPhoneNumberExchangeList == "true")
            {
                //var memberInfo = await _rewardMemberService.GetInfo(new RewardMemberGetInfoInput()
                //{
                //    NationalId = input.MemberCode
                //});

                var keyName = _configuration.GetSection("StoreFiles:AWSS3:KeyName").Value;
                var phoneNumberListString = await _storageS3.DownloadFileAndGetData(keyName);
                var phoneNumberList = phoneNumberListString.Replace("\r", "").Split("\n").ToList();
                var flag = false;

                if (phoneNumberList != null && phoneNumberList.Count > 0)
                {
                    foreach (var item in phoneNumberList)
                    {
                        if (item.Trim() == input.PhoneNumber)
                        {
                            flag = true;
                            break;
                        }
                    }
                }

                if (!flag)
                {
                    throw new Exception("YouAreNotInTheTestList");
                }
            }

            var merchant = getMerchantConfigById(input.MerchantId);
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var IsEnabledVPBankNew = configuration.GetSection("IsUsingVPBankLoyalty").Value;
            if (!bool.Parse(IsEnabledVPBankNew))
            {
                await _rewardMemberService.VerifyIsIdCardVerified(new RewardMemberVerifyIsIdCardVerifiedInput()
                {
                    MemberCode = input.MemberCode,
                    IdCard = input.IdNumber,
                });
            }
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.VerifyNationalId(input, context, authorization);
                case "VPBankSecurities":
                    var verifyRe =  await _vpBankSecuritiesService.VerifyNationalId(input, context, authorization);
                    return new LoyaltyThirdPartyVerifyNationalIdOutput()
                    {
                        Result = null,
                        Success = verifyRe.Success,
                        IsChangedLoyalty = true,
                    };
                default:
                    return await _thirdPartyDummyService.VerifyNationalId(input);
            }
            throw new ArgumentException("System error");
        }

        public async Task<LoyaltyThirdPartyVerifyOTPOutput> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input, HttpContext context, string authorization)
        {
            var merchant = getMerchantConfigById(input.MerchantId);
            var firstAction = new RewardMemberFirstActionMemberInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                Type = "ConnectMerchant",
                IsFirst = true,
            };
            switch (merchant)
            {
                case "VPBank":
                    var result = await _thirdPartyVPBankService.VerifyOTP(input, context, authorization);
                    await _rewardMemberService.FirstActionMember(firstAction);
                    return result;
                case "VPBankSecurities":
                    var verifyRe =  await _vpBankSecuritiesService.VerifyOTP(input, context, authorization);
                    return verifyRe;
                default:
                    result = await _thirdPartyDummyService.VerifyOTP(input);
                    await _rewardMemberService.FirstActionMember(firstAction);
                    return result;
            }
            throw new ArgumentException("System error");
        }

        /**
         * Entry point (level service) để cho đổi điểm LynkiD Ra khỏi tài khoản LynkiD.
         */
        public async Task<ExchangeLynkiDToPartnerPointRes> ExchangeLynkiDToPartnerPoint(
            ExchangeLynkiDToPartnerPointReq input)
        {
            _logger.LogInformation(" >>[Service] Exchange LynkiD token To Partner >> " + JsonConvert.SerializeObject(input));
            // validation:
            if (string.IsNullOrEmpty(input.TransactionCode))
            {
                return new ExchangeLynkiDToPartnerPointRes()
                {
                    Code = "EX001", Message = "Mã giao dịch không thể để trống"
                };
            }
            if (input.MerchantId <= 0)
            {
                return new ExchangeLynkiDToPartnerPointRes()
                {
                    Code = "EX002", Message = "Merchant Id phải là số nguyên dương"
                };
            }

            if (input.Amount <= 0 || input.Amount > 100000000)
            {
                return new ExchangeLynkiDToPartnerPointRes()
                {
                    Code = "EX003", Message = "Amount phải là số nguyên dương không quá 100.000.000"
                };
            }

            var cif = new GetPartnerMemberInfoByConnectionOutput();
            GetFullInfoMerchantByIdOutput merchantObj = null;
            RewardMemberGetInfoOutput memberInfo = null;
            var calculatedAmountToSendToPartner = 0;
            try
            {
                memberInfo = await _rewardMemberService.GetInfo(new RewardMemberGetInfoInput()
                {
                    NationalId = input.MemberCode
                });
                cif = await _rewardMemberService.GetPartnerMemberInfoByConnection(
                    new GetCifByMemberRequest()
                    {
                        MemberId = memberInfo.Id, MerchantId = input.MerchantId
                    });
                if (cif == null || string.IsNullOrWhiteSpace(cif.Item.RelatedCif))
                {
                    return new ExchangeLynkiDToPartnerPointRes()
                    {
                        Code = "EX011", Message = "Bạn chưa liên kết với merchant, vui lòng kiểm tra lại"
                    };
                }

                merchantObj = await _rewardMerchantService.GetFullInfoMerchantById(new GetFullInfoMerchantByIdInput
                {
                    MerchantId = input.MerchantId
                });
                if (merchantObj == null || merchantObj.Id == 0)
                {
                    return new ExchangeLynkiDToPartnerPointRes()
                    {
                        Code = "EX012", Message = "Merchant Id không hợp lệ"
                    };
                }
                if (!merchantObj.RedeemExchangeRate.HasValue || merchantObj.MerchantUnit <= 0 || merchantObj.PartnerPointExchangeType != "POINT")
                {
                    _logger.LogError(" >>[Service] Exchange LynkiD token To Partner >> MerchantId#" + input.MerchantId + " - Merchant Misconfigured");
                    return new ExchangeLynkiDToPartnerPointRes()
                    {
                        Code = "EX013", Message = "Merchant Config chưa đúng"
                    };
                }
                calculatedAmountToSendToPartner = (int)((input.Amount * merchantObj.RedeemExchangeRate.Value) / merchantObj.MerchantUnit);
                if (calculatedAmountToSendToPartner <= 0)
                {
                    _logger.LogError(" >>[Service] Exchange LynkiD token To Partner >> Member: " + input.MemberCode + " >> " + input.MerchantId + " - Calculated Amount smaller or < 0. Cannot proceed");
                    var result = new CreateExchangeMilesAirlineOutput()
                    {
                        ErrorCode = AirlineIntegrationErrorCodes.MilesAndTotalAmountNotMatch,
                        Message = "Số điểm LynkiD và điểm nhận bên đối tác không khớp",
                    };
                    return new ExchangeLynkiDToPartnerPointRes()
                    {
                        Code = "EX014", Message = "Số điểm tính ra không hợp lệ để cộng bên đối tác"
                    };
                }
            }
            catch (RewardException e)
            {
                var rewardExRes = await _exceptionReponseService.GetExceptionRewardReponse(e);
                _logger.LogError(" >> Error Getting Member Info >> " + e.Message + "-" + e.StackTrace  + " - " + JsonConvert.SerializeObject(rewardExRes));
                if (rewardExRes.Code == "MemberNotExitsOrNotActive")
                {
                    return new ExchangeLynkiDToPartnerPointRes()
                    {
                        Code = "EX004", Message = "Member Code có trạng thái không hợp lệ để thực thi action này"
                    };
                }
                return new ExchangeLynkiDToPartnerPointRes()
                {
                    Code = "100", Message = "Có lỗi xảy ra khi truy vấn thông tin member"
                };
            }
            // Step 1: call rewardnetwork để thực hiện trừ $LYNKID của khách
            var lynkidTransactionCode = string.IsNullOrEmpty(input.TransactionCode) 
                ? LoyaltyHelper.GenAirlineTxCode("SKJ")
                : input.TransactionCode.Trim();
            try
            {
                var exchangeInOperatorResponse = await _rewardMemberService.ExchangeLynkiDToPartnerPoint(new ExchangeLynkiDToPartnerPointInput()
                {
                    MerchantId = input.MerchantId,
                    MemberCode = input.MemberCode,
                    TransactionCode = lynkidTransactionCode,
                    TokenAmount = Convert.ToDouble(input.Amount),
                    Cif = cif.Item.RelatedCif,
                    SourceExchange = "LynkIDApp",
                    ExtraData = JsonConvert.SerializeObject(new {
                        Cif = cif.Item.RelatedCif,
                        SourceExchange = "LynkIDApp",
                    }),
                    PartnerBindingTxID = null
                });
                _logger.LogInformation(" >> _rewardMemberService.ExchangeLynkiDToPartnerPoint >> " + JsonConvert.SerializeObject(exchangeInOperatorResponse));
            }
            catch (RewardException e)
            {
                var rewardExRes = await _exceptionReponseService.GetExceptionRewardReponse(e);
                _logger.LogError(" >> Error ExchangeLynkiDToPartnerPoint >> " + e.Message + "-" + e.StackTrace  + " - " + JsonConvert.SerializeObject(rewardExRes));
                if (rewardExRes.Code == "BalanceNotEnough")
                {
                    return new ExchangeLynkiDToPartnerPointRes()
                    {
                        Code = "EX005", Message = "Bạn không đủ số dư để thực hiện giao dịch"
                    };
                }
                if (rewardExRes.Code == "MerchantTypeMustBeExchange" || rewardExRes.Code == "MerchantExchangeRateNotConfig")
                {
                    return new ExchangeLynkiDToPartnerPointRes()
                    {
                        Code = "EX006", Message = "Có lỗi xảy ra, vui lòng thử lại hoặc liên hệ CSKH"
                    };
                }
                if (rewardExRes.Code == "MemberCannotConnectMerchant")
                {
                    return new ExchangeLynkiDToPartnerPointRes()
                    {
                        Code = "EX007", Message = "Bạn chưa liên kết với merchant, vui lòng kiểm tra lại"
                    };
                }
                if (rewardExRes.Code == "MerchantExchangeConfigLimitPerTime")
                {
                    return new ExchangeLynkiDToPartnerPointRes()
                    {
                        Code = "EX008", Message = "Bạn đổi quá giới hạn một lần đổi, vui lòng thử số nhỏ hơn"
                    };
                }
                if (rewardExRes.Code == "MerchantExchangeConfigMinPerTime")
                {
                    return new ExchangeLynkiDToPartnerPointRes()
                    {
                        Code = "EX009", Message = "Bạn đổi điểm với amount nhỏ quá, vui lòng thử số lớn hơn"
                    };
                }
                if (rewardExRes.Code == "MerchantExchangeConfigLimitPerMonth")
                {
                    return new ExchangeLynkiDToPartnerPointRes()
                    {
                        Code = "EX010", Message = "Merchant đã vượt quá hạn mức tháng này, vui lòng thử lại sau"
                    };
                }
                if (rewardExRes.Code == "MerchantExchangeConfigLimitPerYear" || rewardExRes.Code == "MerchantExchangeConfigLimitTotal")
                {
                    return new ExchangeLynkiDToPartnerPointRes()
                    {
                        Code = "EX015", Message = "Merchant đã vượt quá hạn mức năm, vui lòng thử lại sau"
                    };
                }
            }
            PartnerExchangeOutput partnerExchangeOutput = null;
            _logger.LogInformation(" >>[Service] Exchange LynkiD token To Partner >> Call to Partner To exchange  for #" + input.MemberCode + " - and " + input.MerchantId);
            if (string.Equals(merchantObj.MerchantName, "SkyJoy", StringComparison.OrdinalIgnoreCase))
            {
                var skjOutput = await _skyJoyIntegrationService.PointAccrual(new SJPointAccrualInput()
                {
                    MemberCode = input.MemberCode, skyjoyId = cif.Item.RelatedCif
                    , MerchantId = input.MerchantId, transactionId = lynkidTransactionCode
                    , extraData = new SJPointAccrualInputExtraData()
                    {
                        partnerPoint = input.Amount, points = calculatedAmountToSendToPartner, LynkidId = input.MemberCode
                    }
                });
                partnerExchangeOutput = new PartnerExchangeOutput()
                {
                    Code = skjOutput.Code, Message = skjOutput.Message
                };
                if (skjOutput.Code == "00")
                {
                    partnerExchangeOutput.TransactionId = skjOutput.Data.BitReference;
                }
            }
            else
            {
                throw new ApplicationException("MerchantNotSupported");
            }
            _logger.LogInformation(" >>[Service] Exchange LynkiD token To Partner >> Done Call to SkyJoy for #"
                                   + input.MemberCode + " - and " + input.MerchantId + " - Response: " + JsonConvert.SerializeObject(partnerExchangeOutput));

            if (partnerExchangeOutput.Code == "00")
            {
                // Cap nhat PartnerBindingTxId
                try
                {
                    _logger.LogInformation(" >>[Service] Exchange LynkiD token To Partner >> Updating PartnerBindingTxId " + partnerExchangeOutput.TransactionId);
                    await _rewardMemberService.UpdatePartnerBindingTransactionId(new UpdatePartnerBindingTransactionIdInput()
                    {
                        TransactionCode = lynkidTransactionCode,
                        PartnerBindingTxID = partnerExchangeOutput.TransactionId
                    });
                }
                catch (Exception e)
                {
                    _logger.LogError(" Error Update PartnerBindingTxId " + partnerExchangeOutput.TransactionId + " to TransactionCode#" + lynkidTransactionCode);
                }
                return new ExchangeLynkiDToPartnerPointRes
                {
                    Code = partnerExchangeOutput.Code,
                    Message = partnerExchangeOutput.Message, 
                    ExchangeAmount = (long)input.Amount, EquivalentTokenAmount = 0,
                    PartnerBindingTxId = partnerExchangeOutput.TransactionId,
                    LinkIDOrderCode = lynkidTransactionCode
                };
            }
            if (partnerExchangeOutput.Code == "TIMEOUT" || partnerExchangeOutput.Code == "100")
            {
                _logger.LogError(" >>[Service] Exchange LynkiD token To Partner >> Timeout Or Unknown error >> " + JsonConvert.SerializeObject(partnerExchangeOutput));
                var result = new ExchangeLynkiDToPartnerPointRes()
                {
                    Code = partnerExchangeOutput.Code,
                    Message = partnerExchangeOutput.Message
                };
                return result;
            }
            //  Nếu mã lỗi ko phải là success, ko phải timeout và không phải unknown, thì revert giao dịch
            _logger.LogError(" >>[Service] Exchange LynkiD token To Partner >> Not successful >> " + JsonConvert.SerializeObject(partnerExchangeOutput));
            _logger.LogInformation(" >>[Service] Exchange LynkiD token To Partner >> retryRevertToken start");
            var requestRevert = new RevertExchangeLynkiDToPartnerPointInput()
            {
                MerchantId = input.MerchantId,
                MemberCode = input.MemberCode,
                TransactionCode = lynkidTransactionCode,
                Message = JsonConvert.SerializeObject(partnerExchangeOutput),
                MemberId = memberInfo.Id
            };
            await retryRevertToken(requestRevert);
            return new ExchangeLynkiDToPartnerPointRes()
            {
                Code = partnerExchangeOutput.Code,
                Message = partnerExchangeOutput.Message
            };
        }

        public async Task<LoyaltyThirdPartyPointExchangeOutput> PointExchange(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null)
        {

            var checkMerchant = await _rewardMerchantService.GetCurrentMerchantRequest(new RewardGetCurrentMerchantRequest()
            {
                MerchantId = input.MerchantId,
            });

            if (checkMerchant.TokenAmount <= checkMerchant.MinBalanceWhenExchangeOrGrant)
            {
                var ex = new RewardException();
                var error = new RewardDataExceptionResponse()
                {
                    result = new RewardDataExceptionResultItem()
                    {
                        code = "MerchantNotExceedMinBalance",
                        message = "Merchant Not Exceed Min Balance"
                    },
                    status = 500
                };
                ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
                ex.Data.Add("StatusCode", 400);
                throw ex;
            }

            var merchant = getMerchantConfigById(input.MerchantId);
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.PointExchange(input, context, orderCode);
                case "Appota":
                    return await _thirdPartyAppotaService.PointExchange(input, context, orderCode);
                default:
                    return await _thirdPartyDummyService.PointExchange(input, orderCode);
            }
            throw new ArgumentException("System error");
        }

        public async Task<LoyaltyThirdPartyPointExchangeOutput> PointExchangeIntegration(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null)
        {
            var merchant = getMerchantConfigById(input.MerchantId);
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.PointExchangeIntegration(input, context, orderCode);
                default:
                    return await _thirdPartyDummyService.PointExchangeIntegration(input, orderCode);
            }
            throw new ArgumentException("System error");
        }

        public async Task<LoyaltyThirdPartyRevertPointOutput> RevertPoint(LoyaltyThirdPartyRevertPointInput input, HttpContext context)
        {
            var merchant = getMerchantConfigById(input.MerchantId);
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.RevertPoint(input, context);
                case "Appota":
                    return await _thirdPartyAppotaService.RevertPoint(input, context);
                default:
                    return await _thirdPartyDummyService.RevertPoint(input, context);
            }
            throw new ArgumentException("System error");
        }


        public async Task<LoyaltyThirdPartyRequestAccessTokenOutput> RequestAccessToken(LoyaltyThirdPartyRequestAccessTokenInput input, HttpContext context)
        {
            var merchant = getMerchantConfigById(input.MerchantId);
            if (string.IsNullOrWhiteSpace(merchant))
            {
                throw new ArgumentException("Merchant invalid");
            }
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.RequestAccessToken(input, context);
                case "Appota":
                    return await _thirdPartyAppotaService.RequestAccessToken(input, context);
                default:
                    break;
            }
            throw new ArgumentException("System error");
        }

        public async Task<LoyaltyThirdPartyConfirmConnectOutput> ConfirmConnect(LoyaltyThirdPartyConfirmConnectInput input, string authorization)
        {
            var merchant = getMerchantConfigById(input.MerchantId);
            if (string.IsNullOrWhiteSpace(merchant))
            {
                throw new ArgumentException("Merchant invalid");
            }
            // Kiểm tra Action, nếu là Confirm thì phải yêu cầu có OTP/Session
            if (CommonConstants.MERCHANT_CONNECTION_STATUS_CONFIRMED.Equals(input.Action))
            {
                if (string.IsNullOrWhiteSpace(input.OptCode) || string.IsNullOrWhiteSpace(input.SessionId))
                {
                    return await Task.FromResult(new LoyaltyThirdPartyConfirmConnectOutput()
                    {
                        Message = "Khi xác nhận liên kết cần phải có OTP và Session ID",
                        Result = 400
                    });
                }
            }
            
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.ConfirmConnect(input, authorization);
                default:
                    break;
            }
            throw new ArgumentException("System error");
        }

        public async Task<LoyaltyThirdPartyUpdatePartnerCachingOutput> UpdatePartnerCaching(LoyaltyThirdPartyUpdatePartnerCachingInput input, HttpContext context)
        {
            var requestPartnerPoint = new RewardPartnerPoingCachingInput()
            {
                NationalId = input.MemberCode,
                Items = new List<RewardPartnerPoingCachingItems>()
            };
            foreach (var item in input.Items)
            {
                var merchant = getMerchantConfigById(item.MerchantId);
                switch (merchant)
                {
                    case "VPBank":
                        var requestVPBank = new LoyaltyThirdPartyVPBankUpdatePartnerCachingInput()
                        {
                            AccessToken = item.AccessToken,
                            IdNumber = item.IdNumber,
                            MerchantId = item.MerchantId,
                            MemberCode = input.MemberCode
                        };
                        var responseVPBank = await _thirdPartyVPBankService.UpdatePartnerCaching(requestVPBank, context);
                        requestPartnerPoint.Items.Add(responseVPBank);
                        break;
                    case "Appota":
                        var requestAppota = new LoyaltyThirdPartyAppotaUpdatePartnerCachingInput()
                        {
                            AccessToken = item.AccessToken,
                            IdNumber = item.IdNumber,
                            MerchantId = item.MerchantId,
                            MemberCode = input.MemberCode
                        };
                        var responseAppota = await _thirdPartyAppotaService.UpdatePartnerCaching(requestAppota, context);
                        requestPartnerPoint.Items.Add(responseAppota);
                        break;
                    default:
                        var requestDummy = new LoyaltyThirdPartyDummyUpdatePartnerCachingInput()
                        {
                            IdNumber = item.IdNumber,
                            MerchantId = item.MerchantId,
                        };
                        var responseDummy = await _thirdPartyDummyService.UpdatePartnerCaching(requestDummy);
                        requestPartnerPoint.Items.Add(responseDummy);
                        break;
                }
            }

            int result = 200;

            if (requestPartnerPoint.Items.Count == 0)
            {
                throw new ArgumentException("Please input items data for request");
            }
            else
            {
                await _rewardPartnerPointCachingService.RequestUpdateIntegration(requestPartnerPoint);

                foreach (var item in requestPartnerPoint.Items)
                {
                    if (item.HaveException)
                    {
                        result = 400;
                        break;
                    }
                }
            }

            return new LoyaltyThirdPartyUpdatePartnerCachingOutput()
            {
                Result = result,
                Message = result == 200 ? "Success" : "Error",
                Items = requestPartnerPoint.Items
            };
        }

        private string getMerchantConfigById(int id)
        {
            try
            {
                var flag = "";
                var listThirdPartyMerchant = _configuration.GetSection("ThirdPartyMerchant").GetChildren();
                foreach (var item in listThirdPartyMerchant)
                {
                    var merchantId = _configuration.GetSection(item.Path + ":" + "MerchantId").Value;
                    if (merchantId != null && merchantId == id.ToString())
                    {
                        flag = item.Key;
                        break;
                    }
                }
                return flag;
            }
            catch
            {
                return "";
            }

        }

        public Task<LoyaltyThirdPartyUpdatePhoneNumberOutput> UpdatePhoneNumber(LoyaltyThirdPartyUpdatePhoneNumberInput input)
        {
            if (input.MerchantIds.Count > 0)
            {
                var taskListMerchantRemove = new List<Task>();
                var listMerchantUpdatePhoneResult = new List<LoyaltyThirdPartyUpdatePhoneNumberOutput>();
                var linkIdMemberCode = input.LinkIdMemberCode;
                var linkIdMemberId = input.LinkIdMemberId;
                var phoneNumber = input.PhoneNumber;
                Task taskRunAllExchange = Task.Run(() =>
                {
                    var optionsLoop = new ParallelOptions
                    {
                        MaxDegreeOfParallelism = 10
                    };
                    Parallel.ForEach(input.MerchantIds, optionsLoop, merchantId =>
                    {
                        var merchant = getMerchantConfigById(merchantId);
                        switch (merchant)
                        {
                            // For vpbank
                            case "VPBank":
                                var request = new UpdatePhoneNumberInput()
                                {
                                    MemberCode = linkIdMemberCode,
                                    PhoneNumber = phoneNumber,
                                    LinkID_MemberID = linkIdMemberId
                                };
                                var merchantUpdatePhone = _thirdPartyVPBankService.UpdatePhoneNumber(request).Result;
                                listMerchantUpdatePhoneResult.Add(merchantUpdatePhone);
                                break;
                            case "VPBankSecurities":
                                var requestForVPS = new UpdatePhoneNumberInput()
                                {
                                    MemberCode = linkIdMemberCode,
                                    PhoneNumber = phoneNumber,
                                    LinkID_MemberID = linkIdMemberId
                                };
                                var merchantVpbankSUpdatePhone =  _vpBankSecuritiesService.UpdatePhoneNumber(requestForVPS).Result;
                                listMerchantUpdatePhoneResult.Add(merchantVpbankSUpdatePhone);
                                break;
                            default:
                                break;
                        }
                    });
                });
                taskListMerchantRemove.Add(taskRunAllExchange);
                Task.WaitAll(taskListMerchantRemove.ToArray());
            }
            return Task.FromResult(new LoyaltyThirdPartyUpdatePhoneNumberOutput()
            {
                Status = true
            });
        }

        public async Task<LoyaltyThirdPartySendOtpConfirmConnectOutput> SendOtpConfirmConnect(LoyaltyThirdPartySendOtpConfirmConnectInput input, string authorization)
        {
            var merchant = getMerchantConfigById(input.MerchantId);
            if (string.IsNullOrWhiteSpace(merchant))
            {
                throw new ArgumentException("Merchant invalid");
            }
            switch (merchant)
            {
                case "VPBank":
                    return await _thirdPartyVPBankService.SendOtpConfirmConnect(input, authorization);
                default:
                    break;
            }
            throw new ArgumentException("System error");
        }

        public async Task<RemoveConnectRespone> RemoveConnect(RemoveConnectInput Input)
        {
            var result = await _vpBankSecuritiesService.RemoveConnect(Input);
            return result.Result;
        }
        private async Task retryRevertToken(RevertExchangeLynkiDToPartnerPointInput request)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await revertToken(request);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<Boolean> revertToken(RevertExchangeLynkiDToPartnerPointInput request)
        {
            try
            {
                await _rewardMemberService.RevertExchangeLynkiDToPartnerPoint(request);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
