﻿using AKC.MobileAPI.DTO.Loyalty.Quest;
using AKC.MobileAPI.Service.Abstract.Loyalty.Quest;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty.Quest
{
    public class LoyaltyQuestService : BaseLoyaltyService, ILoyaltyQuestService
    {
        public LoyaltyQuestService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<ListGmQuestV2Output> GetListQuestActive(SearchGmQuestV2Request request)
        {
            return await GetLoyaltyAsync<ListGmQuestV2Output>(LoyaltyApiUrl.GET_LIST_QUEST_ACTIVE, request);
        }

        public async Task<GmQuestV2Output> GetQuestById(int id, string memberCode)
        {
            return await GetLoyaltyAsync<GmQuestV2Output>(LoyaltyApiUrl.GET_QUEST_BY_ID, new { id = id, memberCode = memberCode });
        }

        public async Task<ViewMemberProgessQuestOutput> ViewMemberProgressOnQuest(ViewMemberProgessQuestInput request)
        {
            return await PostLoyaltyAsync<ViewMemberProgessQuestOutput>(LoyaltyApiUrl.VIEW_MEMBER_PROGRESS_ON_QUEST, request);
        }

        public async Task<PerformActionOutput> PerformAction(PerformActionInput request)
        {
            return await PostLoyaltyAsync<PerformActionOutput>(LoyaltyApiUrl.PERFORM_ACTION_QUEST, request);
        }

        public async Task<GmQuestV2InforOutput> GetQuestActiveByCode(GmQuestV2InforInput request)
        {
            return await GetLoyaltyAsync<GmQuestV2InforOutput>(LoyaltyApiUrl.GET_QUEST_ACTIVE_BY_CODE, new { code = request.QuestCode, memberCode = request.MemberCode });
        }

        public async Task<GmQuestEnrolmentV2Output> MemberEnrolmentQuest(GmQuestEnrolmentV2Request request)
        {
            return await PostLoyaltyAsync<GmQuestEnrolmentV2Output>(LoyaltyApiUrl.MEMBER_ENROLMENT_QUEST, request);
        }

        public async Task<PerformActionOutput> MemberClaimMission(MemberClaimMissionInput input)
        {
            return await PostLoyaltyAsync<PerformActionOutput>(LoyaltyApiUrl.MEMBER_CLAIM_MISSION, input);
        }
    }
}
