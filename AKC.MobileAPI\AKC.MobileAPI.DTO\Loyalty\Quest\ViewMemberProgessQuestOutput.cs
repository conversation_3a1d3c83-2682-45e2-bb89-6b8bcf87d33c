﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Quest
{
    public class ViewMemberProgessQuestOutput
    {
        public ViewMemberProgessQuestResponse result { get; set; }
        public object targetUrl { get; set; }
        public bool success { get; set; }
        public object error { get; set; }
        public bool unAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class ViewMemberProgessQuestResponse
    {
        public GmQuestEnrolmentV2Dto gmQuestEnrolmentV2Dto { get; set; }
        public List<GmMissionClaimV2Infor> gmMissionClaimV2Infors { get; set; }
    }
    public class GmQuestEnrolmentV2Dto
    {
        public int Id { get; set; }
        public int? TenantId { get; set; }
        public string QuestCode { get; set; }
        public string MemberCode { get; set; }
        public DateTime? EnrolmentDate { get; set; }
        public int CurrentCount { get; set; }
        public int TotalCount { get; set; }
        public DateTime? LastValidActionTime { get; set; }
        public long? CreatorUserId { get; set; }
        public DateTime CreationTime { get; set; }
        public string CreatorUserName { get; set; }
        public long? LastModifierUserId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string LastModifierUserName { get; set; }
    }

    public class GmMissionClaimV2Infor
    {
        public string MissionCode { get; set; }
        public bool Claimed { get; set; }
        public DateTime? ClaimedTime { get; set; }
    }
}
