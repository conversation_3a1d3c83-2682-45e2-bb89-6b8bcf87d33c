﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Merchant
{
    public class RewardGetAllMerchantPaymentInput
    {
        public string Sorting { get; set; }
        public int MaxResultCount { get; set; }
        public int SkipCount { get; set; }
        public string MerchantNameFilter { get; set; }
        public string PaymentType { get; set; }
    }
}
