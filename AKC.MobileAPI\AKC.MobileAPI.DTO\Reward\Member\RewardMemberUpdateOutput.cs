﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberUpdateOutput
    {
        public int Result { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }

    public class RewardMemberUpdateOutputDto
    {
        public int Result { get; set; }
        public RewardMemberUpdateItemDto Item { get; set; }
        public string Message { get; set; }
    }

    public class RewardMemberUpdateItemDto
    {
        public int MemberId { get; set; }
        public string MemberCode { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public string PhoneNumber { get; set; }
        public string IdCard { get; set; }
        public List<int> ListMerchantRemovedConnectManual { get; set; }
        public bool IsChangePhoneNumber { get; set; }
        public List<int> ListMerchantNeedUpdatePhone { get; set; }
    }

    public class RewardMemberUpdatePhoneDto
    {
        public int Result { get; set; }
        public RewardMemberUpdatePhoneItemDto Item { get; set; }
        public string Message { get; set; }
    }

    public class RewardMemberUpdatePhoneItemDto
    {
        public int MemberId { get; set; }
        public string MemberCode { get; set; }
        public List<int> ListMerchantNeedUpdatePhone { get; set; }
    }
}
