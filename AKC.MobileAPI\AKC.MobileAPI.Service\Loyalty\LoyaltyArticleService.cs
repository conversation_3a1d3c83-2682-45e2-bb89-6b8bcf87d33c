﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Article;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyArticleService : BaseLoyaltyService, ILoyaltyArticleService
    {
        private readonly IDistributedCache _cache;
        public LoyaltyArticleService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
            _cache = cache;
        }

        public async Task<LoyaltyResponseList<ArticleDto>> GetBanners(int SkipCount, int MaxResultCount)
        {
            return await GetLoyaltyAsync<LoyaltyResponseList<ArticleDto>>(LoyaltyApiUrl.ARTICLE_GETALL_BANNERS, new {SkipCount, MaxResultCount});
        }
        public async Task<LoyaltyArticleGetAllOutput> GetAll(LoyaltyArticleGetAllInput input)
        {
            return await GetLoyaltyAsync<LoyaltyArticleGetAllOutput>(LoyaltyApiUrl.ARTICLE_GETALL, input);
        }
        public async Task<GetArticleForEditOutput> GetArticleByIdAndRelatedNews(GetArticleByIdAndRelatedNewsInput input)
        {
            return await GetLoyaltyAsync<GetArticleForEditOutput>(LoyaltyApiUrl.GET_ARTICLE_BY_ID, input);
        }
        public async Task<GetArticleForEditOutput> GetArticleByIdByMemberCode(GetArticleByIdByMemberCodeInput input)
        {
            return await GetLoyaltyAsync<GetArticleForEditOutput>(LoyaltyApiUrl.GET_ARTICLE_BY_ID_BY_MEMBERCODE, input);
        }
        public async Task<GetAllArticleByMemberCodeOutPut> GetAllArticleByMemberCode(GetAllArticleByMemberCodeInput input)
        {
            return await GetLoyaltyAsync<GetAllArticleByMemberCodeOutPut>(LoyaltyApiUrl.GET_ALL_ARTICLE_BY_MEMBERCODE, input);
        }
        public async Task<GetAllArticleAndRelatedNewsOutput> GetAllArticleAndRelatedNews(GetAllArticleAndRelatedNewsInput input)
        {
            return await GetLoyaltyAsync<GetAllArticleAndRelatedNewsOutput>(LoyaltyApiUrl.GET_ALL_ARTICLE_AND_RELATED_NEWS, input);
        }
        public async Task<GetAllArticleAndRelatedNewsOutput_Optimize> GetAllArticleAndRelatedNews_Optimize(GetAllArticleAndRelatedNewsInput_Optimize input)
        {
            return await GetLoyaltyAsync<GetAllArticleAndRelatedNewsOutput_Optimize>(LoyaltyApiUrl.GET_ALL_ARTICLE_AND_RELATED_NEWS_OPTIMIZE, input);
        }
        public async Task<GetAllArticlePopupOutput> GetPopUpOnApp(GetPopupOnAppInput input)
        {
            var realOne = await GetLoyaltyAsync<GetAllArticlePopupOutput>(LoyaltyApiUrl.GET_ONE_ARTICLE_POPUP, input);
            return realOne;
        }

        public async Task<GetAllArticleAndRelatedNewsOutput_Optimize> GetAllArticleAndRelatedNews_Optimize_V1(GetAllArticleAndRelatedNewsInput_Optimize_New input)
        {

            return await GetLoyaltyAsync<GetAllArticleAndRelatedNewsOutput_Optimize>(LoyaltyApiUrl.GetAllArticleAndRelatedNews_Optimize_NEW, input);
        }
    }
}
