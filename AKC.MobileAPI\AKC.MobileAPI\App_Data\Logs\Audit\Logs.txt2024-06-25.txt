INFO  2024-06-25 16:37:26,461 [49   ]            - Http Request Information: {"ControllerName":"RewardMember","ActionName":"ViewPoint","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/Member/ViewPoint","QueryString":"{\"MemberCode\":[\"U3d8uZSaK8YMI85aTpNzgReKOEr1\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2024-06-25 16:37:58,143 [49   ] Controller - View point Member User Error - {"ClassName":"System.AggregateException","Message":"One or more errors occurred.","Data":null,"InnerException":{"ClassName":"System.ArgumentException","Message":"ID token must not be null or empty.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at FirebaseAdmin.Auth.FirebaseTokenVerifier.VerifyTokenAsync(String token, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, Boolean checkRevoked, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken)\r\n   at AKC.MobileAPI.Service.Common.ValidationMemberCode.ValidateToken(String authToken, String memberCode) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Common\\ValidationMemberCode.cs:line 27","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147024809,"Source":"FirebaseAdmin","WatsonBuckets":null,"ParamName":null},"HelpURL":null,"StackTraceString":"   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)\r\n   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)\r\n   at System.Threading.Tasks.Task`1.get_Result()\r\n   at AKC.MobileAPI.Service.Common.CommonHelperService.GetResponseInvalidToken(String authorization, String memberCodeInput) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Common\\CommonHelperService.cs:line 29\r\n   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Reward\\RewardMemberController.cs:line 246","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-**********,"Source":"System.Private.CoreLib","WatsonBuckets":null,"InnerExceptions":[{"ClassName":"System.ArgumentException","Message":"ID token must not be null or empty.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at FirebaseAdmin.Auth.FirebaseTokenVerifier.VerifyTokenAsync(String token, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, Boolean checkRevoked, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken)\r\n   at AKC.MobileAPI.Service.Common.ValidationMemberCode.ValidateToken(String authToken, String memberCode) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Common\\ValidationMemberCode.cs:line 27","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147024809,"Source":"FirebaseAdmin","WatsonBuckets":null,"ParamName":null}]}
System.AggregateException: One or more errors occurred. (ID token must not be null or empty.)
 ---> System.ArgumentException: ID token must not be null or empty.
   at FirebaseAdmin.Auth.FirebaseTokenVerifier.VerifyTokenAsync(String token, CancellationToken cancellationToken)
   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, Boolean checkRevoked, CancellationToken cancellationToken)
   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, CancellationToken cancellationToken)
   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken)
   at AKC.MobileAPI.Service.Common.ValidationMemberCode.ValidateToken(String authToken, String memberCode) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Common\ValidationMemberCode.cs:line 27
   --- End of inner exception stack trace ---
   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)
   at System.Threading.Tasks.Task`1.get_Result()
   at AKC.MobileAPI.Service.Common.CommonHelperService.GetResponseInvalidToken(String authorization, String memberCodeInput) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Common\CommonHelperService.cs:line 29
   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\Reward\RewardMemberController.cs:line 246
INFO  2024-06-25 16:38:26,209 [52   ]            - Http Request Information: {"ControllerName":"RewardMember","ActionName":"ViewPoint","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/Member/ViewPoint","QueryString":"{\"MemberCode\":[\"U3d8uZSaK8YMI85aTpNzgReKOEr1\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2024-06-25 16:38:35,613 [62   ] Controller - View point Member User Error - {"StackTrace":"   at AKC.MobileAPI.Service.Reward.RewardBaseService.GetRewardAsync[T](String apiURL, Object query, String rewardType) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardBaseService.cs:line 113\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.ViewPoint(RewardMemberRequestInput input, String authorization) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 177\r\n   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request)","Message":"The requested name is valid, but no data of the requested type was found.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"The requested name is valid, but no data of the requested type was found.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":11004},"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-2147467259}
System.Net.Http.HttpRequestException: The requested name is valid, but no data of the requested type was found.
 ---> System.Net.Sockets.SocketException (11004): The requested name is valid, but no data of the requested type was found.
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at AKC.MobileAPI.Service.Reward.RewardBaseService.GetRewardAsync[T](String apiURL, Object query, String rewardType) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardBaseService.cs:line 113
   at AKC.MobileAPI.Service.Reward.RewardMemberService.ViewPoint(RewardMemberRequestInput input, String authorization) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardMemberService.cs:line 177
   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request)
INFO  2024-06-25 16:38:52,900 [61   ]            - Http Request Information: {"ControllerName":"RewardMember","ActionName":"ViewPoint","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/Member/ViewPoint","QueryString":"{\"MemberCode\":[\"U3d8uZSaK8YMI85aTpNzgReKOEr1\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-25 16:40:53,934 [34   ]            - Http Request Information: {"ControllerName":"RewardMember","ActionName":"ViewPoint","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/Member/ViewPoint","QueryString":"{\"MemberCode\":[\"U3d8uZSaK8YMI85aTpNzgReKOEr1\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2024-06-25 16:41:22,882 [7    ] Controller - View point Member User Error - {"ClassName":"StackExchange.Redis.RedisConnectionException","Message":"No connection is available to service this operation: EVAL; UnableToConnect on localhost:6379/Interactive, Initializing/NotStarted, last: NONE, origin: BeginConnectAsync, outstanding: 0, last-read: 2s ago, last-write: 2s ago, keep-alive: 60s, state: Connecting, mgr: 10 of 10 available, last-heartbeat: never, global: 0s ago, v: 2.0.593.37019; IOCP: (Busy=1,Free=1999,Min=1000,Max=2000), WORKER: (Busy=1,Free=32766,Min=1000,Max=32767), Local-CPU: n/a","Data":{"redis-command":"EVAL","request-sent-status":1},"InnerException":{"ClassName":"StackExchange.Redis.RedisConnectionException","Message":"UnableToConnect on localhost:6379/Interactive, Initializing/NotStarted, last: NONE, origin: BeginConnectAsync, outstanding: 0, last-read: 2s ago, last-write: 2s ago, keep-alive: 60s, state: Connecting, mgr: 10 of 10 available, last-heartbeat: never, global: 0s ago, v: 2.0.593.37019","Data":{"Redis-FailureType":"UnableToConnect","Redis-EndPoint":"localhost:6379","Redis-Origin":"BeginConnectAsync","Redis-Outstanding-Responses":"0","Redis-Last-Read":"2s ago","Redis-Last-Write":"2s ago","Redis-Keep-Alive":"60s","Redis-Previous-Physical-State":"Connecting","Redis-Manager":"10 of 10 available","Redis-Last-Heartbeat":"never","Redis-Last-Global-Heartbeat":"0s ago","Redis-Version":"2.0.593.37019"},"InnerException":null,"HelpURL":null,"StackTraceString":"   at StackExchange.Redis.TaskExtensions.TimeoutAfter(Task task, Int32 timeoutMs) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\TaskExtensions.cs:line 49\r\n   at StackExchange.Redis.ConnectionMultiplexer.WaitAllIgnoreErrorsAsync(Task[] tasks, Int32 timeoutMilliseconds, TextWriter log, String caller, Int32 callerLineNumber) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\ConnectionMultiplexer.cs:line 705","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-**********,"Source":"System.Private.CoreLib","WatsonBuckets":null,"failureType":9,"commandStatus":0},"HelpURL":null,"StackTraceString":"   at StackExchange.Redis.ConnectionMultiplexer.ExecuteSyncImpl[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\ConnectionMultiplexer.cs:line 2215\r\n   at StackExchange.Redis.RedisBase.ExecuteSync[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\RedisBase.cs:line 54\r\n   at StackExchange.Redis.RedisDatabase.ScriptEvaluate(String script, RedisKey[] keys, RedisValue[] values, CommandFlags flags) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\RedisDatabase.cs:line 1134\r\n   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisExtensions.HashMemberGet(IDatabase cache, String key, String[] members)\r\n   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.GetAndRefresh(String key, Boolean getData)\r\n   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.Get(String key)\r\n   at Microsoft.Extensions.Caching.Distributed.DistributedCacheExtensions.GetString(IDistributedCache cache, String key)\r\n   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetAccessToken(Boolean mustResetCache) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\BaseLoyaltyService.cs:line 413\r\n   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetLoyaltyAsync[T](String apiURL, Object query) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\BaseLoyaltyService.cs:line 73\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltySecondaryCustomersService.GetMemberInfoByCode(String code) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltySecondaryCustomersService.cs:line 35\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.ViewPoint(RewardMemberRequestInput input, String authorization) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 180\r\n   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Reward\\RewardMemberController.cs:line 252","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-**********,"Source":"StackExchange.Redis","WatsonBuckets":null,"failureType":1,"commandStatus":1}
StackExchange.Redis.RedisConnectionException: No connection is available to service this operation: EVAL; UnableToConnect on localhost:6379/Interactive, Initializing/NotStarted, last: NONE, origin: BeginConnectAsync, outstanding: 0, last-read: 2s ago, last-write: 2s ago, keep-alive: 60s, state: Connecting, mgr: 10 of 10 available, last-heartbeat: never, global: 0s ago, v: 2.0.593.37019; IOCP: (Busy=1,Free=1999,Min=1000,Max=2000), WORKER: (Busy=1,Free=32766,Min=1000,Max=32767), Local-CPU: n/a
 ---> StackExchange.Redis.RedisConnectionException: UnableToConnect on localhost:6379/Interactive, Initializing/NotStarted, last: NONE, origin: BeginConnectAsync, outstanding: 0, last-read: 2s ago, last-write: 2s ago, keep-alive: 60s, state: Connecting, mgr: 10 of 10 available, last-heartbeat: never, global: 0s ago, v: 2.0.593.37019
   at StackExchange.Redis.TaskExtensions.TimeoutAfter(Task task, Int32 timeoutMs) in C:\projects\stackexchange-redis\src\StackExchange.Redis\TaskExtensions.cs:line 49
   at StackExchange.Redis.ConnectionMultiplexer.WaitAllIgnoreErrorsAsync(Task[] tasks, Int32 timeoutMilliseconds, TextWriter log, String caller, Int32 callerLineNumber) in C:\projects\stackexchange-redis\src\StackExchange.Redis\ConnectionMultiplexer.cs:line 705
   --- End of inner exception stack trace ---
   at StackExchange.Redis.ConnectionMultiplexer.ExecuteSyncImpl[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\projects\stackexchange-redis\src\StackExchange.Redis\ConnectionMultiplexer.cs:line 2215
   at StackExchange.Redis.RedisBase.ExecuteSync[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\projects\stackexchange-redis\src\StackExchange.Redis\RedisBase.cs:line 54
   at StackExchange.Redis.RedisDatabase.ScriptEvaluate(String script, RedisKey[] keys, RedisValue[] values, CommandFlags flags) in C:\projects\stackexchange-redis\src\StackExchange.Redis\RedisDatabase.cs:line 1134
   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisExtensions.HashMemberGet(IDatabase cache, String key, String[] members)
   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.GetAndRefresh(String key, Boolean getData)
   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.Get(String key)
   at Microsoft.Extensions.Caching.Distributed.DistributedCacheExtensions.GetString(IDistributedCache cache, String key)
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetAccessToken(Boolean mustResetCache) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 413
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetLoyaltyAsync[T](String apiURL, Object query) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 73
   at AKC.MobileAPI.Service.Loyalty.LoyaltySecondaryCustomersService.GetMemberInfoByCode(String code) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltySecondaryCustomersService.cs:line 35
   at AKC.MobileAPI.Service.Reward.RewardMemberService.ViewPoint(RewardMemberRequestInput input, String authorization) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardMemberService.cs:line 180
   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\Reward\RewardMemberController.cs:line 252
INFO  2024-06-25 16:41:35,468 [39   ]            - Http Request Information: {"ControllerName":"RewardMember","ActionName":"ViewPoint","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/Member/ViewPoint","QueryString":"{\"MemberCode\":[\"U3d8uZSaK8YMI85aTpNzgReKOEr1\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2024-06-25 16:41:38,379 [42   ] Controller - View point Member User Error - {"ClassName":"StackExchange.Redis.RedisConnectionException","Message":"No connection is available to service this operation: EVAL; UnableToConnect on localhost:6379/Interactive, Initializing/NotStarted, last: NONE, origin: BeginConnectAsync, outstanding: 0, last-read: 2s ago, last-write: 2s ago, keep-alive: 60s, state: Connecting, mgr: 10 of 10 available, last-heartbeat: never, global: 0s ago, v: 2.0.593.37019; IOCP: (Busy=1,Free=1999,Min=1000,Max=2000), WORKER: (Busy=1,Free=32766,Min=1000,Max=32767), Local-CPU: n/a","Data":{"redis-command":"EVAL","request-sent-status":1},"InnerException":{"ClassName":"StackExchange.Redis.RedisConnectionException","Message":"UnableToConnect on localhost:6379/Interactive, Initializing/NotStarted, last: NONE, origin: BeginConnectAsync, outstanding: 0, last-read: 2s ago, last-write: 2s ago, keep-alive: 60s, state: Connecting, mgr: 10 of 10 available, last-heartbeat: never, global: 0s ago, v: 2.0.593.37019","Data":{"Redis-FailureType":"UnableToConnect","Redis-EndPoint":"localhost:6379","Redis-Origin":"BeginConnectAsync","Redis-Outstanding-Responses":"0","Redis-Last-Read":"2s ago","Redis-Last-Write":"2s ago","Redis-Keep-Alive":"60s","Redis-Previous-Physical-State":"Connecting","Redis-Manager":"10 of 10 available","Redis-Last-Heartbeat":"never","Redis-Last-Global-Heartbeat":"0s ago","Redis-Version":"2.0.593.37019"},"InnerException":null,"HelpURL":null,"StackTraceString":"   at StackExchange.Redis.TaskExtensions.TimeoutAfter(Task task, Int32 timeoutMs) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\TaskExtensions.cs:line 49\r\n   at StackExchange.Redis.ConnectionMultiplexer.WaitAllIgnoreErrorsAsync(Task[] tasks, Int32 timeoutMilliseconds, TextWriter log, String caller, Int32 callerLineNumber) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\ConnectionMultiplexer.cs:line 705","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-**********,"Source":"System.Private.CoreLib","WatsonBuckets":null,"failureType":9,"commandStatus":0},"HelpURL":null,"StackTraceString":"   at StackExchange.Redis.ConnectionMultiplexer.ExecuteSyncImpl[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\ConnectionMultiplexer.cs:line 2215\r\n   at StackExchange.Redis.RedisBase.ExecuteSync[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\RedisBase.cs:line 54\r\n   at StackExchange.Redis.RedisDatabase.ScriptEvaluate(String script, RedisKey[] keys, RedisValue[] values, CommandFlags flags) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\RedisDatabase.cs:line 1134\r\n   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisExtensions.HashMemberGet(IDatabase cache, String key, String[] members)\r\n   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.GetAndRefresh(String key, Boolean getData)\r\n   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.Get(String key)\r\n   at Microsoft.Extensions.Caching.Distributed.DistributedCacheExtensions.GetString(IDistributedCache cache, String key)\r\n   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetAccessToken(Boolean mustResetCache) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\BaseLoyaltyService.cs:line 413\r\n   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetLoyaltyAsync[T](String apiURL, Object query) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\BaseLoyaltyService.cs:line 73\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltySecondaryCustomersService.GetMemberInfoByCode(String code) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltySecondaryCustomersService.cs:line 35\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.ViewPoint(RewardMemberRequestInput input, String authorization) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 180\r\n   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Reward\\RewardMemberController.cs:line 252","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-**********,"Source":"StackExchange.Redis","WatsonBuckets":null,"failureType":1,"commandStatus":1}
StackExchange.Redis.RedisConnectionException: No connection is available to service this operation: EVAL; UnableToConnect on localhost:6379/Interactive, Initializing/NotStarted, last: NONE, origin: BeginConnectAsync, outstanding: 0, last-read: 2s ago, last-write: 2s ago, keep-alive: 60s, state: Connecting, mgr: 10 of 10 available, last-heartbeat: never, global: 0s ago, v: 2.0.593.37019; IOCP: (Busy=1,Free=1999,Min=1000,Max=2000), WORKER: (Busy=1,Free=32766,Min=1000,Max=32767), Local-CPU: n/a
 ---> StackExchange.Redis.RedisConnectionException: UnableToConnect on localhost:6379/Interactive, Initializing/NotStarted, last: NONE, origin: BeginConnectAsync, outstanding: 0, last-read: 2s ago, last-write: 2s ago, keep-alive: 60s, state: Connecting, mgr: 10 of 10 available, last-heartbeat: never, global: 0s ago, v: 2.0.593.37019
   at StackExchange.Redis.TaskExtensions.TimeoutAfter(Task task, Int32 timeoutMs) in C:\projects\stackexchange-redis\src\StackExchange.Redis\TaskExtensions.cs:line 49
   at StackExchange.Redis.ConnectionMultiplexer.WaitAllIgnoreErrorsAsync(Task[] tasks, Int32 timeoutMilliseconds, TextWriter log, String caller, Int32 callerLineNumber) in C:\projects\stackexchange-redis\src\StackExchange.Redis\ConnectionMultiplexer.cs:line 705
   --- End of inner exception stack trace ---
   at StackExchange.Redis.ConnectionMultiplexer.ExecuteSyncImpl[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\projects\stackexchange-redis\src\StackExchange.Redis\ConnectionMultiplexer.cs:line 2215
   at StackExchange.Redis.RedisBase.ExecuteSync[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\projects\stackexchange-redis\src\StackExchange.Redis\RedisBase.cs:line 54
   at StackExchange.Redis.RedisDatabase.ScriptEvaluate(String script, RedisKey[] keys, RedisValue[] values, CommandFlags flags) in C:\projects\stackexchange-redis\src\StackExchange.Redis\RedisDatabase.cs:line 1134
   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisExtensions.HashMemberGet(IDatabase cache, String key, String[] members)
   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.GetAndRefresh(String key, Boolean getData)
   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.Get(String key)
   at Microsoft.Extensions.Caching.Distributed.DistributedCacheExtensions.GetString(IDistributedCache cache, String key)
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetAccessToken(Boolean mustResetCache) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 413
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetLoyaltyAsync[T](String apiURL, Object query) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 73
   at AKC.MobileAPI.Service.Loyalty.LoyaltySecondaryCustomersService.GetMemberInfoByCode(String code) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltySecondaryCustomersService.cs:line 35
   at AKC.MobileAPI.Service.Reward.RewardMemberService.ViewPoint(RewardMemberRequestInput input, String authorization) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardMemberService.cs:line 180
   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\Reward\RewardMemberController.cs:line 252
INFO  2024-06-25 16:41:40,547 [39   ]            - Http Request Information: {"ControllerName":"RewardMember","ActionName":"ViewPoint","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/Member/ViewPoint","QueryString":"{\"MemberCode\":[\"U3d8uZSaK8YMI85aTpNzgReKOEr1\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-25 16:46:47,846 [30   ]            - Http Request Information: {"ControllerName":"RewardMember","ActionName":"ViewPoint","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/Member/ViewPoint","QueryString":"{\"MemberCode\":[\"U3d8uZSaK8YMI85aTpNzgReKOEr1\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-25 16:48:28,640 [28   ]            - Http Request Information: {"ControllerName":"RewardMember","ActionName":"ViewPoint","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/Member/ViewPoint","QueryString":"{\"MemberCode\":[\"U3d8uZSaK8YMI85aTpNzgReKOEr1\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-25 16:50:02,044 [36   ]            - Http Request Information: {"ControllerName":"RewardMember","ActionName":"ViewPoint","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/Member/ViewPoint","QueryString":"{\"MemberCode\":[\"U3d8uZSaK8YMI85aTpNzgReKOEr1\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2024-06-25 16:50:18,530 [23   ] Controller - View point Member User Error - {"StackTrace":"   at AKC.MobileAPI.Service.Reward.RewardBaseService.GetRewardAsync[T](String apiURL, Object query, String rewardType) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardBaseService.cs:line 116\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.ViewPoint(RewardMemberRequestInput input, String authorization) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 177\r\n   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Reward\\RewardMemberController.cs:line 252","Message":"Exception of type 'AKC.MobileAPI.Service.Exceptions.RewardException' was thrown.","Data":{"ErrorData":"{\"status\":500,\"result\":{\"code\":\"Account\",\"message\":\"Account\"}}","StatusCode":400},"InnerException":null,"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-**********}
AKC.MobileAPI.Service.Exceptions.RewardException: Exception of type 'AKC.MobileAPI.Service.Exceptions.RewardException' was thrown.
   at AKC.MobileAPI.Service.Reward.RewardBaseService.GetRewardAsync[T](String apiURL, Object query, String rewardType) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardBaseService.cs:line 116
   at AKC.MobileAPI.Service.Reward.RewardMemberService.ViewPoint(RewardMemberRequestInput input, String authorization) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardMemberService.cs:line 177
   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\Reward\RewardMemberController.cs:line 252
INFO  2024-06-25 16:51:15,126 [29   ]            - Http Request Information: {"ControllerName":"RewardMember","ActionName":"ViewPoint","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/Member/ViewPoint","QueryString":"{\"MemberCode\":[\"U3d8uZSaK8YMI85aTpNzgReKOEr1\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2024-06-25 16:51:19,521 [22   ] Controller - View point Member User Error - {"StackTrace":"   at AKC.MobileAPI.Service.Reward.RewardBaseService.GetRewardAsync[T](String apiURL, Object query, String rewardType) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardBaseService.cs:line 116\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.ViewPoint(RewardMemberRequestInput input, String authorization) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 177\r\n   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Reward\\RewardMemberController.cs:line 252","Message":"Exception of type 'AKC.MobileAPI.Service.Exceptions.RewardException' was thrown.","Data":{"ErrorData":"{\"status\":500,\"result\":{\"code\":\"1Account\",\"message\":\"2Account\"}}","StatusCode":400},"InnerException":null,"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-**********}
AKC.MobileAPI.Service.Exceptions.RewardException: Exception of type 'AKC.MobileAPI.Service.Exceptions.RewardException' was thrown.
   at AKC.MobileAPI.Service.Reward.RewardBaseService.GetRewardAsync[T](String apiURL, Object query, String rewardType) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardBaseService.cs:line 116
   at AKC.MobileAPI.Service.Reward.RewardMemberService.ViewPoint(RewardMemberRequestInput input, String authorization) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardMemberService.cs:line 177
   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\Reward\RewardMemberController.cs:line 252
INFO  2024-06-25 16:52:22,348 [27   ]            - Http Request Information: {"ControllerName":"RewardMember","ActionName":"ViewPoint","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"GET","ServerHost":"localhost:44365","Path":"/api/Member/ViewPoint","QueryString":"{\"MemberCode\":[\"U3d8uZSaK8YMI85aTpNzgReKOEr1\"]}","Body":"","TrueClientIp":"","XForwardedFor":""}
ERROR 2024-06-25 16:52:53,094 [8    ] Controller - View point Member User Error - {"ClassName":"System.NullReferenceException","Message":"Object reference not set to an instance of an object.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at AKC.MobileAPI.Service.Reward.RewardBaseService.GetRewardAsync[T](String apiURL, Object query, String rewardType) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardBaseService.cs:line 116\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.ViewPoint(RewardMemberRequestInput input, String authorization) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 177\r\n   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Reward\\RewardMemberController.cs:line 252","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467261,"Source":"AKC.MobileAPI.Service","WatsonBuckets":null}
System.NullReferenceException: Object reference not set to an instance of an object.
   at AKC.MobileAPI.Service.Reward.RewardBaseService.GetRewardAsync[T](String apiURL, Object query, String rewardType) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardBaseService.cs:line 116
   at AKC.MobileAPI.Service.Reward.RewardMemberService.ViewPoint(RewardMemberRequestInput input, String authorization) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardMemberService.cs:line 177
   at AKC.MobileAPI.Controllers.Reward.RewardMemberController.ViewPoint(RewardMemberRequestInput request) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\Reward\RewardMemberController.cs:line 252
