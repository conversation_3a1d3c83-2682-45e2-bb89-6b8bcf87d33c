﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Languages;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyLanguageService
    {
        Task<LoyaltyResponse<List<LoyaltyMultiLanguageTextListOutput>>> GetListLanguageTexts(LoyaltyMultiLanguageTextListInput input);
        Task<object> GetListMessagesByKey(string key);
    }
}
