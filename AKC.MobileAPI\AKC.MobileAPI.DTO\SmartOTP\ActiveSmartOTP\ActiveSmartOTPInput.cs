﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.SmartOTP.ActiveSmartOTP
{
    public class ActiveSmartOTPInput
    {
        public string MemberCode { get; set; }
        public string DeviceId { get; set; }
        public string DeviceOS { get; set; }
        public string AppVersion { get; set; }
        public string ActivationKey { get; set; }
        public string Token { get; set; }
    }

    public class ActiveSmartOTPRequest
    {
        public string partner_id { get; set; }
        public string partner_key { get; set; }
        public string user_id { get; set; }
        public string device_id { get; set; }
        public string device_os { get; set; }
        public string app_version { get; set; }
        public string registration_id { get; set; }
        public string activation_key { get; set; }
        public string token { get; set; }
    }
}
