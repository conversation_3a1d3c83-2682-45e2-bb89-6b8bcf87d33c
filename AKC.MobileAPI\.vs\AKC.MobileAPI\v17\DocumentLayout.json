{"Version": 1, "WorkspaceRootPath": "D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5C96D664-62A4-42C3-A887-FDF1A5F52DB7}|AKC.MobileAPI\\AKC.MobileAPI.csproj|d:\\working\\vpid-mobile-api\\akc.mobileapi\\akc.mobileapi\\controllers\\loyalty\\loyaltygifttransactionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5C96D664-62A4-42C3-A887-FDF1A5F52DB7}|AKC.MobileAPI\\AKC.MobileAPI.csproj|solutionrelative:akc.mobileapi\\controllers\\loyalty\\loyaltygifttransactionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24485C90-CF47-4BCF-A81C-7E958BA80397}|AKC.MobileAPI.Service\\AKC.MobileAPI.Service.csproj|d:\\working\\vpid-mobile-api\\akc.mobileapi\\akc.mobileapi.service\\loyalty\\baseloyaltyservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24485C90-CF47-4BCF-A81C-7E958BA80397}|AKC.MobileAPI.Service\\AKC.MobileAPI.Service.csproj|solutionrelative:akc.mobileapi.service\\loyalty\\baseloyaltyservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24485C90-CF47-4BCF-A81C-7E958BA80397}|AKC.MobileAPI.Service\\AKC.MobileAPI.Service.csproj|d:\\working\\vpid-mobile-api\\akc.mobileapi\\akc.mobileapi.service\\exceptions\\loyaltytimeoutexception.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24485C90-CF47-4BCF-A81C-7E958BA80397}|AKC.MobileAPI.Service\\AKC.MobileAPI.Service.csproj|solutionrelative:akc.mobileapi.service\\exceptions\\loyaltytimeoutexception.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24485C90-CF47-4BCF-A81C-7E958BA80397}|AKC.MobileAPI.Service\\AKC.MobileAPI.Service.csproj|d:\\working\\vpid-mobile-api\\akc.mobileapi\\akc.mobileapi.service\\loyalty\\loyaltygifttransactionsservice.ext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24485C90-CF47-4BCF-A81C-7E958BA80397}|AKC.MobileAPI.Service\\AKC.MobileAPI.Service.csproj|solutionrelative:akc.mobileapi.service\\loyalty\\loyaltygifttransactionsservice.ext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5C96D664-62A4-42C3-A887-FDF1A5F52DB7}|AKC.MobileAPI\\AKC.MobileAPI.csproj|d:\\working\\vpid-mobile-api\\akc.mobileapi\\akc.mobileapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{5C96D664-62A4-42C3-A887-FDF1A5F52DB7}|AKC.MobileAPI\\AKC.MobileAPI.csproj|solutionrelative:akc.mobileapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 28, "Children": [{"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:139:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Bookmark", "Name": "ST:0:0:{004be353-6879-467c-9d1e-9ac23cdf6d49}"}, {"$type": "Bookmark", "Name": "ST:6:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:130:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:133:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:136:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:133:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:134:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:135:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Bookmark", "Name": "ST:131:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:136:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:134:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:139:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:138:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:137:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:140:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:138:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:1:0:{dcc4ea97-1c0c-482b-b205-e541c0df9728}"}, {"$type": "Bookmark", "Name": "ST:128:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 4, "Title": "appsettings.json", "DocumentMoniker": "D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\appsettings.json", "RelativeDocumentMoniker": "AKC.MobileAPI\\appsettings.json", "ToolTip": "D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\appsettings.json", "RelativeToolTip": "AKC.MobileAPI\\appsettings.json", "ViewState": "AgIAADAAAAAAAAAAAAAAADMAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-05T03:24:30.186Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "LoyaltyTimeoutException.cs", "DocumentMoniker": "D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Exceptions\\LoyaltyTimeoutException.cs", "RelativeDocumentMoniker": "AKC.MobileAPI.Service\\Exceptions\\LoyaltyTimeoutException.cs", "ToolTip": "D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Exceptions\\LoyaltyTimeoutException.cs", "RelativeToolTip": "AKC.MobileAPI.Service\\Exceptions\\LoyaltyTimeoutException.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T03:17:45.355Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "BaseLoyaltyService.cs", "DocumentMoniker": "D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\BaseLoyaltyService.cs", "RelativeDocumentMoniker": "AKC.MobileAPI.Service\\Loyalty\\BaseLoyaltyService.cs", "ToolTip": "D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\BaseLoyaltyService.cs", "RelativeToolTip": "AKC.MobileAPI.Service\\Loyalty\\BaseLoyaltyService.cs", "ViewState": "AgIAACIBAAAAAAAAAAAEwD0BAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T03:07:06.784Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "LoyaltyGiftTransactionsService.ext.cs", "DocumentMoniker": "D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyGiftTransactionsService.ext.cs", "RelativeDocumentMoniker": "AKC.MobileAPI.Service\\Loyalty\\LoyaltyGiftTransactionsService.ext.cs", "ToolTip": "D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyGiftTransactionsService.ext.cs", "RelativeToolTip": "AKC.MobileAPI.Service\\Loyalty\\LoyaltyGiftTransactionsService.ext.cs", "ViewState": "AgIAACYBAAAAAAAAAAAAADUAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T03:06:40.637Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "LoyaltyGiftTransactionsController.cs", "DocumentMoniker": "D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Loyalty\\LoyaltyGiftTransactionsController.cs", "RelativeDocumentMoniker": "AKC.MobileAPI\\Controllers\\Loyalty\\LoyaltyGiftTransactionsController.cs", "ToolTip": "D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Loyalty\\LoyaltyGiftTransactionsController.cs", "RelativeToolTip": "AKC.MobileAPI\\Controllers\\Loyalty\\LoyaltyGiftTransactionsController.cs", "ViewState": "AgIAAGYBAAAAAAAAAAAowKoBAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T03:06:23.086Z", "EditorCaption": ""}]}, {"DockedWidth": 200, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:137:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:130:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}]}]}]}