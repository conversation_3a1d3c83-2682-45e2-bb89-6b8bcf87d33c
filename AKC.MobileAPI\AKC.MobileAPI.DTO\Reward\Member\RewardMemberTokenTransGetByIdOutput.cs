﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberTokenTransGetByIdOutput
    {
        public int Result { get; set; }
        public List<RewardMemberTokenTransGetByIdItems> Items { get; set; }
        public int TotalCount { get; set; }
        public string Message { get; set; }
    }

    public class RewardMemberTokenTransGetByIdItems
    {
        public string TokenTransID { get; set; }
        public int MemberId { get; set; }
        public DateTime? Time { get; set; }
        public DateTime? BusinessTime { get; set; }
        public DateTime? ValidDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string OrderCode { get; set; }
        public string ActionCode { get; set; }
        public string ActionCodeDetail { get; set; }        
        public string ActionName { get; set; }
        public string FromWalletAddress { get; set; }
        public string FromMerchantNameOrMember { get; set; }
        public string ToWalletAddress { get; set; }
        public string ToMerchantNameOrMember { get; set; }
        public int StoreId { get; set; }
        public string StoreName { get; set; }
        public string NationalId { get; set; }
        public string UserAddress { get; set; }
        public string ActionType { get; set; }
        public decimal TokenAmount { get; set; }
        public decimal PartnerPointAmount { get; set; }
        public decimal PointExchangeRate { get; set; }
        public decimal CurrencyExchangeRate { get; set; }
        public string GrantType { get; set; }
        public string UsagePriority { get; set; }
        public string PartnerBindingTxId { get; set; }
        public string Reason { get; set; }
        public int MerchantId { get; set; }
        public decimal BaseUnit { get; set; }
        public string TopUpVendorName { get; set; }
        public string TopUpVendorAvatar { get; set; }

        public List<RewardMemberTokenTransGetByIdItems> AdjustTransactionList { get; set; }
    }
}
