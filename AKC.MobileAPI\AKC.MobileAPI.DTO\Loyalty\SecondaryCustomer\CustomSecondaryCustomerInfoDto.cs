﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer
{
    public class CustomSecondaryCustomerInfoDto
    {
        public string MemberCode { get; set; }
        public decimal Point { get; set; }
        public decimal Coin { get; set; }
        public int? RankId { get; set; }
        public CustomRankDto Rank { get; set; }
        public int? NextRankId { get; set; }
        public CustomRankDto NextRank { get; set; }
        public int? TempRankId { get; set; }
        public CustomRankDto TempRank { get; set; }
        public decimal? ExpiringCoin { get; set; }
        public decimal? PointToNextTempRank { get; set; }

        public decimal? PointToKeepRank { get; set; }
    }
}
