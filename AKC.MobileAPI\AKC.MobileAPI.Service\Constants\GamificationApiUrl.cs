﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.Service.Constants
{
    public class GamificationApiUrl
    {
        public const string VIEW_GAME_LIST = "api/GameMgr/GameQuest/GetQuestByTenantID";
        public const string CREATE_EXCHANGE_TRANSACTION = "api/ExchangeMgr/ExchangeTransaction";
        public const string EXCHANGE_TRANSACTION_HISTORY = "api/ExchangeMgr/ExchangeTransaction";
        public const string VIEW_GAME_ITEM = "api/MemberMgr/GameMaster/GetMemberDetail";
        public const string GET_EXCHANGE_RULE_TYPE = "api/ItemMgr/ExchangeRuleType";
        public const string GET_DETAILS_EXCHANGE_RULE = "api/ItemMgr/ExchangeRule";

        public const string MEMBER_PLAY_GAME = "api/MemberMgr/MemberPlayGame";
        public const string CREATE_MEMBER = "api/MemberMgr/GameMaster";
        public const string GAMIFICATION_LINK = "https://list-game.akcada-hcm.com/dashboard?tenantId=";
    }
}
