INFO  2025-08-05 10:26:31,114 [26   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-05 10:26:33,819 [26   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-05 10:26:34,729 [26   ] Controller - redeem RedeemWithMoneyCard Fzw4wS04C4P2JBWbGRbT93lHXMK2_Error{"ClassName":"System.AggregateException","Message":"One or more errors occurred.","Data":null,"InnerException":{"ClassName":"System.ArgumentException","Message":"ID token must not be null or empty.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at FirebaseAdmin.Auth.FirebaseTokenVerifier.VerifyTokenAsync(String token, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, Boolean checkRevoked, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken)\r\n   at AKC.MobileAPI.Service.Common.ValidationMemberCode.ValidateToken(String authToken, String memberCode) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Common\\ValidationMemberCode.cs:line 27","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147024809,"Source":"FirebaseAdmin","WatsonBuckets":null,"ParamName":null},"HelpURL":null,"StackTraceString":"   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)\r\n   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)\r\n   at System.Threading.Tasks.Task`1.get_Result()\r\n   at AKC.MobileAPI.Service.Common.CommonHelperService.GetResponseInvalidToken(String authorization, String memberCodeInput) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Common\\CommonHelperService.cs:line 29\r\n   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyGiftTransactionsController.RedeemWithMoneyCard(RedeemWithMoneyCardInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Loyalty\\LoyaltyGiftTransactionsController.cs:line 423","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2146233088,"Source":"System.Private.CoreLib","WatsonBuckets":null,"InnerExceptions":[{"ClassName":"System.ArgumentException","Message":"ID token must not be null or empty.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at FirebaseAdmin.Auth.FirebaseTokenVerifier.VerifyTokenAsync(String token, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, Boolean checkRevoked, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken)\r\n   at AKC.MobileAPI.Service.Common.ValidationMemberCode.ValidateToken(String authToken, String memberCode) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Common\\ValidationMemberCode.cs:line 27","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147024809,"Source":"FirebaseAdmin","WatsonBuckets":null,"ParamName":null}]}
INFO  2025-08-05 10:26:44,680 [11   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-05 10:26:46,165 [11   ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-05 10:27:19,382 [4    ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"RedeemWithMoneyCard","Browser":"PostmanRuntime/7.45.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/RedeemWithMoneyCard","QueryString":"{}","Body":"{\r\n    \"MoneyCardCodes\": [\r\n        \"cde77a70621e4f719a0ab1390c864be4\"\r\n    ],\r\n    \"PinCode\": \"111111\",\r\n    \"DeviceId\": \"2146BD1B-E74A-4E0C-9895-180FD5135958\",\r\n    \"MemberCode\": \"Fzw4wS04C4P2JBWbGRbT93lHXMK2\",\r\n    \"GiftCode\": \"GiftInfor_20250804041222018_5858\",\r\n    \"Quantity\": 1,\r\n    \"TotalAmount\": 50000,\r\n    \"Description\": null,\r\n    \"MerchantId\": null,\r\n    \"FlashSaleProgramCode\": null,\r\n    \"FlashSaleProgramName\": null,\r\n    \"MemberId\": null,\r\n    \"UserAddress\": null\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2025-08-05 10:27:21,330 [4    ] Controller - RedeemWithMoneyCard___Input: {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-05 10:27:27,527 [32   ] onsService -  638899612475243329Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard start >> {"MoneyCardCodes":["cde77a70621e4f719a0ab1390c864be4"],"PinCode":"111111","DeviceId":"2146BD1B-E74A-4E0C-9895-180FD5135958","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","GiftCode":"GiftInfor_20250804041222018_5858","Quantity":1,"TotalAmount":50000.0,"Description":null,"MerchantId":null,"FlashSaleProgramCode":null,"FlashSaleProgramName":null,"MemberId":null,"UserAddress":null}
INFO  2025-08-05 10:27:27,532 [9    ] onsService -  638899612475243329Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard GET_GIFT_CATEGORY_TYPE_CODE startGiftInfor_20250804041222018_5858
INFO  2025-08-05 10:27:27,916 [31   ] onsService -  638899612475243329Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetMerchantIdFromGiftCode(GiftInfor_20250804041222018_5858)___Result:{"Result":{"GiftCode":"GiftInfor_20250804041222018_5858","MerchantId":217,"BrandId":5,"VendorId":14,"GiftCategory":null,"Vendor":null,"Brand":null,"FullPrice":0.0,"RequiredCoin":0.0,"Merchant":null},"TargetUrl":null,"Success":true,"Error":null,"UnAuthorizedRequest":false,"__abp":true}
INFO  2025-08-05 10:27:27,921 [31   ] onsService -  638899612475243329Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard check the moneycard in operator...
INFO  2025-08-05 10:27:28,097 [31   ] onsService -  638899612475243329Fzw4wS04C4P2JBWbGRbT93lHXMK2 - GetBalanceMember end {"Items":[{"Id":1893,"CreatedAt":"2025-07-31T07:45:03.315Z","UpdatedAt":"2025-08-05T02:57:55.389Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-28T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"cde77a70621e4f719a0ab1390c864be4","RefCode":"ORDER_CODE_000Z_03105","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":2000000.0000000000000000000000,"RemainingAmount":657000.00000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null},{"Id":1892,"CreatedAt":"2025-07-31T07:45:01.982Z","UpdatedAt":"2025-08-01T03:25:22.323Z","IsDeleted":false,"MerchantID":"362","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","BusinessTime":"2025-07-31T12:02:12.199Z","CampaignID":40,"PhoneNumber":"+***********","CardCode":"7ce2d107548b4211bc83b6d96772490b","RefCode":"ORDER_CODE_000Z_03107","ExpiryDate":"2025-08-28T16:59:59Z","TokenAmount":1000000.0000000000000000000000,"RemainingAmount":977900.0000000000000000000000,"Status":"A","Note":"Tặng điểm tích lũy điểm thưởng, đổi quà thịnh vượn","GiftGroup":"GiftGroup_20250707080044269_4837","MerchantPhoto":null}],"TotalCount":2}
INFO  2025-08-05 10:27:28,100 [31   ] onsService -  638899612475243329Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2025-08-05 10:27:36,896 [31   ] onsService -  638899612475243329Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2025-08-05 10:27:36,902 [31   ] onsService -  638899612475243329Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemWithMoneyCard >> Call Reward RedeemUsingMoneyCard start... {"OrderCode":"100C2DB07A17584F378347BFA6A66160D12727","MemberCode":"Fzw4wS04C4P2JBWbGRbT93lHXMK2","PhoneNumber":null,"Reason":"REDEEM","MoneyCardIds":["cde77a70621e4f719a0ab1390c864be4"],"TokenAmount":50000.0,"MerchantId":217}
INFO  2025-08-05 10:27:37,381 [31   ] onsService -  638899612475243329Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard CreateRedeem end
INFO  2025-08-05 10:27:38,681 [31   ] onsService -  638899612475243329Fzw4wS04C4P2JBWbGRbT93lHXMK2 - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION start
INFO  2025-08-05 10:28:15,111 [35   ] onsService - RedeemWithMoneyCard___ 638899612475243329Fzw4wS04C4P2JBWbGRbT93lHXMK2 - LoyaltyTimeoutException - retryRevertToken start
INFO  2025-08-05 10:28:19,141 [35   ] onsService - RedeemWithMoneyCard___ 638899612475243329Fzw4wS04C4P2JBWbGRbT93lHXMK2 - LoyaltyTimeoutException - retryRevertToken end
INFO  2025-08-05 10:28:42,422 [36   ] Controller - redeem RedeemWithMoneyCard Fzw4wS04C4P2JBWbGRbT93lHXMK2_Error{"ApiUrl":"https://vpid-loyalty-api-uat.linkid.vn/api/services/app/GiftTransactions/CreateRedeemTransaction","StackTrace":"   at AKC.MobileAPI.Service.Loyalty.LoyaltyGiftTransactionsService.RedeemWithMoneyCard(RedeemWithMoneyCardInput input, HttpContext context) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyGiftTransactionsService.ext.cs:line 475\r\n   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyGiftTransactionsController.RedeemWithMoneyCard(RedeemWithMoneyCardInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Loyalty\\LoyaltyGiftTransactionsController.cs:line 509","Message":"Timed Out with API: https://vpid-loyalty-api-uat.linkid.vn/api/services/app/GiftTransactions/CreateRedeemTransaction","Data":{},"InnerException":{"ClassName":"System.Threading.Tasks.TaskCanceledException","Message":"The operation was canceled.","Data":null,"InnerException":{"ClassName":"System.IO.IOException","Message":"Unable to read data from the transport connection: The I/O operation has been aborted because of either a thread exit or an application request..","Data":null,"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"The I/O operation has been aborted because of either a thread exit or an application request.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":null,"RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":null,"WatsonBuckets":null,"NativeErrorCode":995},"HelpURL":null,"StackTraceString":"   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.GetResult(Int16 token)\r\n   at System.Net.Security.SslStream.<FillBufferAsync>g__InternalFillBufferAsync|215_0[TReadAdapter](TReadAdapter adap, ValueTask`1 task, Int32 min, Int32 initial)\r\n   at System.Net.Security.SslStream.ReadAsyncInternal[TReadAdapter](TReadAdapter adapter, Memory`1 buffer)\r\n   at System.Net.Http.HttpConnection.SendAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2146232800,"Source":"System.Net.Sockets","WatsonBuckets":null},"HelpURL":null,"StackTraceString":"   at System.Net.Http.HttpConnection.SendAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithNtConnectionAuthAsync(HttpConnection connection, HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)\r\n   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.PostLoyaltyAsync[T](String apiURL, Object body, HttpContext request) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\BaseLoyaltyService.cs:line 271","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2146233029,"Source":"System.Net.Http","WatsonBuckets":null},"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-2146233088}
