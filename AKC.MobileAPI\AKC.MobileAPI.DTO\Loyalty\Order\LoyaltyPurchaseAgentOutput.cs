﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Order
{
    public class LoyaltyPurchaseAgentOutput
    {
        public LoyaltyPurchaseAgent Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }
    public class LoyaltyPurchaseAgent
    {
        public PaymentTransactionAgentOutput Member { get; set; }
        public MlmValidationRequestOutput MlmResult { get; set; }
    }

    public class PaymentTransactionAgentOutput
    {
        public int TransactionId { get; set; }
        public string MemberCode { get; set; }
        public string OrderCode { get; set; }
        public decimal RewardedPoint { get; set; }
        public decimal RewardedCoin { get; set; }
    }

    public class MlmValidationRequestOutput
    {
        public List<MlmValidationRequestItemDto> MLMDealerChannel { get; set; }
        public List<MlmValidationRequestItemDto> MLMDistributionChannel { get; set; }
    }

    public class MlmValidationRequestItemDto
    {
        public bool Success { get; set; }
        public int Level { get; set; }
        public int ErrorCode { get; set; }
        public string ErrorMessages { get; set; }
    }
}
