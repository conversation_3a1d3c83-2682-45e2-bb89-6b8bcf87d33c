﻿using AKC.MobileAPI.DTO.Loyalty.ThirdPartyBrandMapping;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class ThirdPartyBrandMappingService : BaseLoyaltyService, IThirdPartyBrandMappingService
    {
        public ThirdPartyBrandMappingService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<GetAllThirdPartyBrandMappingByVendorNameOutput> GetAllThirdPartyBrandByVendorName(GetAllThirdPartyBrandMappingByVendorNameInput input)
        {
            return await GetLoyaltyAsync<GetAllThirdPartyBrandMappingByVendorNameOutput>(LoyaltyApiUrl.GET_ALL_THIRD_PARTY_BRAND_BY_VENDOR_NAME, input);
        }

        public async Task<GetAllThirdPartyBrandMappingByVendorNameOutput> GetAllThirdPartyBrandByVendorType(GetAllThirdPartyBrandMappingByVendorTypeInput input)
        {
            return await GetLoyaltyAsync<GetAllThirdPartyBrandMappingByVendorNameOutput>(LoyaltyApiUrl.GET_ALL_THIRD_PARTY_BRAND_BY_VENDOR_TYPE, input);
        }
    }
}
