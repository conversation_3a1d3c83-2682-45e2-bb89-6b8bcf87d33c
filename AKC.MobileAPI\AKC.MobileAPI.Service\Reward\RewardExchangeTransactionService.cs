﻿using AKC.MobileAPI.DTO.Reward.ExchangeTransaction;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardExchangeTransactionService : RewardBaseService, IRewardExchangeTransactionService
    {
        public RewardExchangeTransactionService(IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<RewardCreateExchangeTransactionOutput> CreateExchangeTransactionIntegration(RewardCreateExchangeTransactionInput input, string rewardType = null)
        {
            var request = new RewardCreateExchangeTransactionDto()
            {
                NationalId = input.MemberCode,
                ExchangeAmount = input.ExchangeAmount,
                MerchantId = input.MerchantId,
                TransactionCode = input.TransactionCode,
                PartnerBindingTxId = input.PartnerBindingTxId,
            };
            var apiSite = MerchantNameConfig.VPBank;
            if (rewardType == MerchantNameConfig.VPID)
            {
                apiSite = MerchantNameConfig.VPID;
            }
            return await PostRewardExchangeAsync<RewardCreateExchangeTransactionOutput>(RewardApiUrl.EXCHANGE_TRANSACTION_INTEGRATION_CREATE, request, apiSite);
        }

        public async Task<RewardRevertExchangeTransactionOutput> RevertExchangeTransactionIntegration(RewardRevertExchangeTransactionInput input, string rewardType = null)
        {
            var apiSite = MerchantNameConfig.VPBank;
            if (rewardType == MerchantNameConfig.VPID)
            {
                apiSite = MerchantNameConfig.VPID;
            }
            return await PostRewardAsync<RewardRevertExchangeTransactionOutput>(RewardApiUrl.EXCHANGE_TRANSACTION_INTEGRATION_REVERT, input, apiSite);
        }

        public async Task<RewardExchangeVerifyOTPOutput> ExchangeVerifyOTP(RewardExchangeVerifyOTPInput input)
        {
            return await PostRewardAsync<RewardExchangeVerifyOTPOutput>(RewardApiUrl.EXCHANGE_VERIFITION_OTP, input);
        }

        public async Task<CheckPhoneNumberOutput> CheckPhoneNumber(CheckPhoneNumberInput input)
        {
            return await GetRewardAsync<CheckPhoneNumberOutput>(RewardApiUrl.EXCHANGE_TRANSACTION_INTEGRATION_CHECK_PHONE_NUMBER, input, MerchantNameConfig.VPBank);
        }
    }
}
