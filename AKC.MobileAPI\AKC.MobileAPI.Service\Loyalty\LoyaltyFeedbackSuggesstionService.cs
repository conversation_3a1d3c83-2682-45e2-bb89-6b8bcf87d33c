﻿using AKC.MobileAPI.DTO.Loyalty.FAQ;
using AKC.MobileAPI.DTO.Loyalty.FeedbackSuggesstion;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service
{
    public class LoyaltyFeedbackSuggesstionService : BaseLoyaltyService, ILoyaltyFeedbackSuggesstionService
    {
        public LoyaltyFeedbackSuggesstionService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<GetAllFeedbackSuggesstionOutputDto> GetAll(GetAllFeedbackSuggesstionInputDto input)
        {
            return await GetLoyaltyAsync<GetAllFeedbackSuggesstionOutputDto>(LoyaltyApiUrl.GET_ALL_FEEDBACK_SUGGESSTION, input);
        }
    }
}
