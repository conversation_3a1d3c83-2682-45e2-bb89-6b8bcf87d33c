﻿using System;
using System.Collections.Generic;


namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class ViewActualQuestOutputDto
    {
        public ListResultViewActual Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }
    public class ListResultViewActual
    {
        public int TenantId { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Tag { get; set; }
        public string LinkAvatar { get; set; }
        public string RewardCompleteQuest { get; set; }
        public decimal Point { get; set; }
        public decimal Coin { get; set; }
        public string RewardCompleteMission { get; set; }
        public decimal TargetAudienceId { get; set; }
        public string TargetAudienceCode { get; set; }
        public string FrequencyType { get; set; }
        public string AutoJoin { get; set; }
        public string Approver { get; set; }
        public decimal EstimatedMembers { get; set; }
        public decimal EstimatedBudget { get; set; }
        public decimal EstimatedRevenue { get; set; }
        public decimal RequireCompletedMission { get; set; }
        public List<QuestActualBaselineDto> QuestActualBaseline { get; set; }
        public List<ViewActualMissionDto> Mission { get; set; }
    }

    public class QuestActualBaselineDto
    {
        public int QuestBaselineId { get; set; }
        public int PeriodId { get; set; }
        public string MemberCode { get; set; }
        public DateTime StartPeriod { get; set; }
        public DateTime EndPeriod { get; set; }
        public decimal Actual { get; set; }
        public decimal Target { get; set; }
    }

    public class ViewActualMissionDto
    {
        public int? TenantId { get; set; }
        public DateTime Date { get; set; }
        public string MemberCode { get; set; }
        public string QuestCode { get; set; }
        public string MissionCode { get; set; }
        public string ActionCode { get; set; }
        public List<ViewActualMissionBaselineDto> MissionBaseline { get; set; }
    }

    public class ViewActualMissionBaselineDto
    {
        public int MissionBaselineId { get; set; }
        public int PeriodId { get; set; }
        public DateTime StartPeriod { get; set; }
        public DateTime EndPeriod { get; set; }
        public string Actual { get; set; }
        public string Target { get; set; }
    }
}
