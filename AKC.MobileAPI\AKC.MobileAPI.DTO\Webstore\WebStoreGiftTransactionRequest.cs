﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;

namespace AKC.MobileAPI.DTO.Webstore
{
    public class WebStoreGetAllMyGiftRequest
    {
        [Required]
        public string MemberCode { get; set; }
        public string TransactionCode { get; set; }
        public string GiftTransactionCode { get; set; }
        public string StatusFilter { get; set; }
        public string EGiftStatusFilter { get; set; }
        public DateTime? FromDateFilter { get; set; }
        public DateTime? ToDateFilter { get; set; }
        [Range(0, int.MaxValue)]
        public int MaxResultCount { get; set; }

        [Range(0, int.MaxValue)]
        public int SkipCount { get; set; }
    }
    
    public class WebStoreGetAllMyGiftResponse
    {
        public int TotalCount { get; set; }
        public List<WebStoreGetAllMyGiftResponseItem> Items { get; set; }
    }
    public class WebStoreGetAllMyGiftResponseItem
    {
        public int GiftId { get; set; }
        public string GiftName { get; set; }
        public string GiftCode { get; set; }
        public string BrandImage { get; set; }
        public string BrandName { get; set; }
        public string GiftTransactionCode { get; set; }
        public string ExpiredDate { get; set; }
        public string WhyHaveIt { get; set; }
        public string EGiftCode { get; set; }
        public string Description { get; set; }
        public string GiftIntroduce { get; set; }
        public string GiftDescription { get; set; }
        public string ShipAddress { get; set; }
        public string UsedStatus { get; set; }
        public bool IsEGift { get; set; }
        public bool UsageCheck { get; set; }
        public string Status { get; set; }
        public DateTime? PurchaseDate { get; set; }
        public string ReceiveGiftFrom { get; set; }
        public bool IsAvailableToRedeemAgain { get; set; }
        public string SerialNo { get; set; }
        public DateTime? EGiftUsedAt { get; set; }
        public int Quantity { get; set; }
        public string RejectReason { get; set; }
    }
    public class MerchantGiftTransactionHistoryOutputDto
    {
        public GetAllWithEGifResult Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }
    public class MerchantGiftTransactionDetailInputDto
    {
        public string OwnerCodeFilter { get; set; }
        public string TransactionCode { get; set; }
        public string StatusFilter { get; set; }
        public string EGiftStatusFilter { get; set; }
        public DateTime? FromDateFilter { get; set; }
        public DateTime? ToDateFilter { get; set; }
        public string GiftTransactionCode { get; set; }
        public int MaxResultCount { get; set; }
        public int SkipCount { get; set; }
        public string Sorting { get; set; }
        public string MerchantName { get; set; }
        public string GetBrandInfo { get; set; }
        public string ConnectSource { get; set; }
    }

    public class WebstoreQuaVLAddressDto
    {
        public string name { get; set; }
        public string phone { get; set; }
        public string email { get; set; }
        public string note { get; set; }
        public string gender { get; set; }
        public string shipAddress { get; set; }
        public int cityId { get; set; }
        public int districtId { get; set; }
        public int wardId { get; set; }
    }

    public class WebStoreMarkUseInput
    {
        public string MemberCode { get; set; }
        public string GiftRedeemTransaction { get; set; }
    }

    public class WebStoreMarkUseOutput
    {
    }

    public class WebStoreCreateRedeemGiftTransactionInput
    {
        [Required]
        public string MemberCode { get; set; }
        public string SessionId { get; set; }
        public string GiftCode { get; set; }
        public int? Quantity { get; set; }
        public decimal? TotalAmount { get; set; }
        public string Description { get; set; }
    }

    public class WebStoreCreateRedeemGiftTransactionOutput
    {
        public bool? IsSendOtp { get; set; }
        public bool? IsVerifyPassWord { get; set; }
        public string SessionId { get; set; }
    }

    public class WebStoreCreateRedeemGiftTransactionDto
    {
        public string Code { get; set; }
        public string Message { get; set; }
        public bool? IsSendOtp { get; set; }
        public bool? IsVerifyPassWord { get; set; }
        public string SessionId { get; set; }
    }

    public class MerchantGiftCreateRedeemInWebInputDto
    {
        public string MemberCode { get; set; }
        public string GiftCode { get; set; }
        public int Quantity { get; set; }
        public decimal TotalAmount { get; set; }
        public string Description { get; set; }
        public string Date { get; set; }
        public string TransactionCode { get; set; }
        public string VpoCifCode { get; set; }
        public string OtpSession { get; set; }
        public string RedeemSource { get; set; }
        public int? MerchantIdRedeem { get; set; }
    }

    public class MerchantGiftCreateRedeemInWebCache : MerchantGiftCreateRedeemInWebInputDto
    {
        public string Rephone { get; set; }
        public int SmeId { get; set; }
    }

    public class VerifyRedeemGiftTransactionInput
    {
        public string MemberCode { get; set; }
        public string SessionId { get; set; }
        public string OtpCode { get; set; }
        public string AccessTokenRedeem { get; set; }
    }

    public class SmeRedeemGiftOutputInner
    {
        public string EGiftCode { get; set; }
        public string UsedStatus { get; set; }
        public bool UsageCheck { get; set; }
        public string QRCode { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public DateTime? ExpiredDate { get; set; }
    }

    public class SmeRedeemGiftOutput
    {
        public string TransactionCode { get; set; }
        public List<SmeRedeemGiftOutputInner> Egifts { get; set; }
    }

    public class SmeRedeemGiftDto : SmeRedeemGiftOutput
    {
        public string Code { get; set; }
        public string Message { get; set; }
    }

    public class GetOTPOutput
    {
        public int result { get; set; }
        public string message { get; set; }
        public string messageDetail { get; set; }
    }

    public class GetOTPInput
    {
        public string PhoneNumber { get; set; }
        public string SessionId { get; set; }
    }
}
