using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory.V2;
using AKC.MobileAPI.Service.Abstract.NotiV2;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.NotiV2
{
    public class NotificationHistoryV2Service: BaseNotificationV2Service, INotificationHistoryV2Service
    {
        private readonly ILogger<NotificationHistoryV2Service> _logger;
        public NotificationHistoryV2Service(IConfiguration configuration,
            ILogger<NotificationHistoryV2Service> lg,
            IDistributedCache cache) : base(configuration, cache)
        {
            _logger = lg;
        }


        public async Task<GetByMemberOutput> GetByMember(GetByMemberInput input)
        {
           _logger.LogInformation(" >> GetByMember >> " + JsonConvert.SerializeObject(input)); 
            var notiCate = input.NotiCate;
            if (string.IsNullOrEmpty(notiCate))
            {
                notiCate = "";
            }
            var searchInput = new GetByMemberInput()
            {
                MemberCode = input.MemberCode,
                NotiCate = notiCate
            };

            return await GetAsync<GetByMemberOutput>("NotificationHistory/GetByMember", searchInput);
        }

        public async Task<MarkAsReadOutput> MarkAsRead(MarkAsReadInput input)
        {
            _logger.LogInformation(" >> MarkAsRead >> " + JsonConvert.SerializeObject(input)); 
            return await PostAsync<MarkAsReadOutput>("NotificationHistory/MarkAsRead", input);
        }

        public async Task<TokenTransDetailOutput> TokenTransDetail(TokenTransDetailInput input)
        {
            _logger.LogInformation(" >> TokenTransDetail >> " + JsonConvert.SerializeObject(input)); 
            return await GetAsync<TokenTransDetailOutput>("NotificationHistory/TokenTransDetail", input);
        }
    }
}