﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Gamification.Exchange
{
    public class GetAllGetExchangeRuleListInput
    {
        public int? ID { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int? GameMasterID { get; set; }
        public int? GameID { get; set; }
        public int? Offset { get; set; }
        public int? Limit { get; set; }
    }
}
