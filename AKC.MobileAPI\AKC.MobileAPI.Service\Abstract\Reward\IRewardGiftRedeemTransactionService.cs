﻿using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Webstore;
using AKC.MobileAPI.DTO.Reward;

namespace AKC.MobileAPI.Service.Abstract.Reward
{
    public interface IRewardGiftRedeemTransactionService
    {
        Task<RewardCreateGiftRedeemTransactionResponse> CreateRedeem(RewardCreateGiftRedeemTransactionRequest request);
        Task<RewardRevertGiftRedeemTransactionResponse> RevertRedeem(RewardRevertGiftRedeemTransactionRequest request);
        Task<RedeemUsingMoneyCardOutput> RedeemUsingMoneyCard(RedeemUsingMoneyCardRequest request);
        Task<RevertRedeemUsingMoneyCardOutput> RevertRedeemUsingMoneyCard(RevertRedeemUsingMoneyCardRequest request);

        Task<RewardNapEvoucherOutputSuccess> CreateTransForTopupVoucher(RewardNapEvoucherInput request);

        Task<RewardRevertGiftRedeemTransactionResponse> RevertRedeen_V2(RewardMemberRevertRedeemInput request, string url = null);
    }
}
