﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.Reward;
using Microsoft.Extensions.Caching.Distributed;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/LoyaltyCampaignGiveCoin")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyCampaignGiveCoinController : ControllerBase
    {
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILogger _logger;
        private readonly ILoyaltyCampaignGiveCoinService _loyaltyCampaignGiveCoinService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IDistributedCache _cache;
        public LoyaltyCampaignGiveCoinController(
            IExceptionReponseService exceptionReponseService,
            ILogger<LoyaltyCampaignGiveCoinController> logger,
            IRewardMemberService reward,
            ILoyaltyCampaignGiveCoinService loyaltyCampaignGiveCoinService,
            IDistributedCache cache,
            ICommonHelperService commonHelperService)
        {
            _exceptionReponseService = exceptionReponseService;
            _logger = logger;
            _loyaltyCampaignGiveCoinService = loyaltyCampaignGiveCoinService;
            _commonHelperService = commonHelperService;
            this._cache = cache;
            _rewardMemberService = reward;
        }

        [HttpGet]
        [Route("GetActiveCampaign")]
        public async Task<ActionResult<GetActiveCampaignOutput>> GetActiveCampaign()
        {
            try
            {
                _logger.LogDebug(" >> GetActiveCampaign >> Start...");
                // Check cached obj. if not exist then query from DB
                var cacheKey = "LoyaltyCampaignGiveCoin_ActiveCamp";
                var strObj = await _cache.GetStringAsync(cacheKey);
                if (!string.IsNullOrEmpty(strObj))
                {
                    try
                    {
                        var ret = JsonConvert.DeserializeObject<GetActiveCampaignOutput>(strObj);
                        _logger.LogDebug(" >> GetActiveCampaign >> CACHE HIT");
                        return StatusCode(200, ret);
                    }
                    catch (Exception e)
                    {
                        await _cache.RemoveAsync(cacheKey);
                    }
                }
                _logger.LogDebug(" >> GetActiveCampaign >> CACHE HIT. Query from DB");
                
                var result = await _loyaltyCampaignGiveCoinService.GetActiveCampaign();
                
                await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(result),
                                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(5)));
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "LoyaltyCampaignGiveCoin GetActiveCampaign Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        [HttpGet]
        [Route("GetListRegistrationByMemberCode")]
        public async Task<ActionResult<GetListRegistrationByMemberCodeOutput>> GetListRegistrationByMemberCode([FromQuery] GetListRegistrationByMemberCodeInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyCampaignGiveCoinService.GetListRegistrationByMemberCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "LoyaltyCampaignGiveCoin GetMemberByCif Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        
        [HttpPost]
        [Route("MemberRejectCampaign")]
        public async Task<ActionResult<GiveFeedbackOutput>> MemberRejectCampaign([FromBody] MemberRejectCampaignInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _rewardMemberService.GiveFeedback(new GiveFeedbackInput()
                {
                    Content = "User Rejected", MemberCode = input.MemberCode, ReferenceCode = input.CampaignCode, Title = "User Rejected CASA Campaign",
                    Type = "UserRejectCampaignGiveCoin", StarRate = 0
                });
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "LoyaltyCampaignGiveCoin GetMemberByCif Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetMemberByCif")]
        public async Task<ActionResult<GetMemberByCifOutput>> GetMemberByCif([FromQuery] GetMemberByCifInput input)
        {
            try
            {
                var result = await _loyaltyCampaignGiveCoinService.GetMemberByCif(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "LoyaltyCampaignGiveCoin GetMemberByCif Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("Create")]
        public async Task<ActionResult<CreateOrEditMemberCampaignGiveOutput>> Create([FromBody] CreateOrEditMemberCampaignGiveCoinDto input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.NationalId);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyCampaignGiveCoinService.Create(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "LoyaltyCampaignGiveCoin Create Error - " + JsonConvert.SerializeObject(ex));
        
                return StatusCode(400, res);
            }
        }


        [HttpGet]
        [Route("GetTransactionForCampaign")]
        public async Task<ActionResult<GetTransactionForCampaignOutput>> GetTransactionForCampaign([FromQuery] GetTransactionForCampaignInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCodeFilter);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyCampaignGiveCoinService.GetTransactionForCampaign(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "LoyaltyCampaignGiveCoin GetTransactionForCampaign Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

    }
}
