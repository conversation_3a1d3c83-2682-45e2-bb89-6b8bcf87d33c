// using AKC.MobileAPI.DTO.Downstream;
// using AKC.MobileAPI.DTO.Webstore;
// using AKC.MobileAPI.Helper;
// using AKC.MobileAPI.Service.Constants;
// using AKC.MobileAPI.Service.Downstream;
// using Microsoft.AspNetCore.Http;
// using Microsoft.Extensions.Caching.Distributed;
// using Microsoft.Extensions.Configuration;
// using System;
// using System.Collections.Generic;
// using System.Threading.Tasks;
// using AKC.MobileAPI.Service.Helpers;
// using Microsoft.Extensions.Logging;
// using Newtonsoft.Json;
//
// namespace AKC.MobileAPI.Service.Webstore
// {
//     
//     public class WebstorePartnerOrgToCheckSmeService : BaseDownstreamService
//     {
//         private readonly IDistributedCache _cache;
//         private readonly IConfiguration _config;
//         private int maxRequestPerMinutePerIpWhenLogin = 100000;
//         private readonly ILogger<WebstorePartnerOrgToCheckSmeService> _logger;
//         public WebstorePartnerOrgToCheckSmeService(
//             IConfiguration conf,
//             ILogger<WebstorePartnerOrgToCheckSmeService> lg,
//             IDistributedCache c) : base(conf, c)
//         {
//             _cache = c;
//             _logger = lg;
//             _config = conf;
//             var limit = _config.GetSection("WebstoreLimitLoginPerIpPerMin")?.Value ?? "100";
//             int.TryParse(limit.ToString(), out maxRequestPerMinutePerIpWhenLogin) ;
//         }
//         public async Task<WebstoreExternalSmeCheckOutput> CheckSmeInfo(WebstoreExternalSmeCheckInput input, HttpContext context, string callerSource = "OTHERS")
//         {
//             var time = DateTime.UtcNow.ToString("yyyyMMddHHmm");
//             var cacheKey = "Webstore:PartnerOrg:CheckSmeInfo:" + IpAddressHelper.GetClientIp(context) + ":" + time;
//             var cacheKey2 = "Webstore:EachSmeCodeOncePer2Min:" + input.LicenseCode;
//             var newVal = 1;
//             if (callerSource == "LOGIN")
//             {
//                 var checkValue = await _cache.GetStringAsync(cacheKey);
//                 if (!string.IsNullOrEmpty(checkValue))
//                 {
//                     var parsed = int.Parse(checkValue);
//                     if (parsed + 1 > maxRequestPerMinutePerIpWhenLogin)
//                     {
//                         return new WebstoreExternalSmeCheckOutput()
//                         {
//                             Code = "429",
//                             Message = "Too Many Requests"
//                         };
//                     }
//
//                     newVal = parsed+1;
//                 }
//                 
//                 var checkvalue2 = await _cache.GetStringAsync(cacheKey2);
//                 if (checkvalue2 == "YES")
//                 {
//                     return new WebstoreExternalSmeCheckOutput()
//                     {
//                         Code = "RateLimitPerUser",
//                         Message = "Too Many Requests"
//                     };
//                 }
//             }
//             // MAKE API CALL TO DOWNSTREAM HERE
//
//             if (callerSource == "LOGIN")
//             {
//                 await _cache.SetStringAsync(cacheKey2,  "YES",
//                     new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(2)));
//                 await _cache.SetStringAsync(cacheKey, newVal + "",
//                     new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(1)));
//             }
//
//             var resultCheck = await CheckDataSME(new CheckDataSMERequest()
//                 { Contact_num = CommonHelper.ConvertTo09XXX(input.RepresentativePhoneNumber), Legal_id = input.LicenseCode });
//             if (resultCheck.Code == "00")
//             {
//                 if (resultCheck.result != null && resultCheck.result.data != null && resultCheck.result.data.Count > 0)
//                 {
//                     // Co du lieu
//                     return new WebstoreExternalSmeCheckOutput()
//                     {
//                         Code = "00",
//                         Message = "Success"
//                     };
//                 }
//                 else
//                 {
//                     return new WebstoreExternalSmeCheckOutput()
//                     {
//                         Code = WebstoreConstants.LoginErrorCodes.LOGIN_INVALID_CREDENTIALS,
//                         Message = "Không tìm thấy dữ liệu ở Downstream Service"
//                     };
//                 }
//             }
//             else
//             {
//                 return new WebstoreExternalSmeCheckOutput()
//                 {
//                     Code = resultCheck.Code,
//                     Message = resultCheck.Message
//                 };
//             }
//         }
//
//         private async Task<CheckDataSMEResponse> CheckDataSME(CheckDataSMERequest request)
//         {
//             var requestId = Guid.NewGuid().ToString();
//             if (!string.IsNullOrEmpty(requestId))
//             {
//                 return new CheckDataSMEResponse()
//                 {
//                     Code = "00", Message = "Success", result = new SMEData()
//                     {
//                         
//                         data = new List<SMEInfor>()
//                         {
//                             new SMEInfor()
//                             {
//                                 STATUS = "X", CUS_NAME = "X", EMAIL_ADDR = "X"
//                             }
//                         }
//                     }
//                 };
//             }
//             try
//             {
//                 _logger.LogInformation($" {requestId} >> Calling to downstream >> " + JsonConvert.SerializeObject(request));
//                 var x = await GetDownstreamAsync<CheckDataSMEResponse>(DownstreamApiUrl.CHECK_DATA_SME, request);
//                 _logger.LogInformation($" {requestId} >> RS of calling to downstream >> " + JsonConvert.SerializeObject(x));
//                 x.Code = "00";
//                 x.Message = "Success";
//                 return x;
//             }
//             catch (Exception e)
//             {
//                 _logger.LogInformation($" {requestId} >> Error calling to downstream >> " + e.Message + " - " + e.StackTrace);
//                 return new CheckDataSMEResponse()
//                 {
//                     Code = "100", Message = "Error Happened when calling Downstream",
//                     result = new SMEData(){data = new List<SMEInfor>()}
//                 };
//             }
//         }
//     }
// }