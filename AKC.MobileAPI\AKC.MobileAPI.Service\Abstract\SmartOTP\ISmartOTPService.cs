﻿using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.SmartOTP.ActiveSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.DeactivateSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.RegisterSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.RegisterSmartOTPStatus;
using AKC.MobileAPI.DTO.SmartOTP.StatusSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.SyncTimeSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.ValidateSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.VerifySmartOTP;
using AKC.MobileAPI.DTO.User;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.SmartOTP
{
    public interface ISmartOTPService
    {
        Task<ActiveSmartOTPOutput> ActiveSmartOTP(ActiveSmartOTPInput input);
        Task<DeactivateSmartOTPOutput> DeactivateSmartOTP(DeactivateSmartOTPRequest input);
        Task<RegisterSmartOTPOutput> RegisterSmartOTP(RegisterSmartOTPInput input);
        Task<ValidateSmartOTPOutput> ValidateSmartOTP(ValidateSmartOTPInput input);
        Task<VerifyOTPOutput> VerifySmartOTP(CreateUserMobileDTO input);
        Task<RewardMemberSendOtpOutput> GetOTP(RewardMemberSendOtpInput input);
        Task<SyncTimeSmartOTPOutput> SyncTimeSmartOTP(SyncTimeSmartOTPInput input);
        Task<RegisterSmartOTPStatusOutput> RegisterSmartOTPStatus(RegisterSmartOTPStatusInput input);
        Task<RewardMemberVerifyOtpOutput> VerifyOTP(RewardMemberVerifyOtpInput input);
        Task<StatusSmartOTPOutput> GetStatusSmartOTP(StatusSmartOTPInput input);
    }
}
