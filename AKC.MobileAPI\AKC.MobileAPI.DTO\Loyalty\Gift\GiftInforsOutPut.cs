﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
	public class GiftInforsOutPut
	{
		public ListResultGiftInfors Result { get; set; }
		public string TargetUrl { get; set; }
		public bool Success { get; set; }
		public string Error { get; set; }
		public bool UnAuthorizedRequest { get; set; }
		public bool __abp { get; set; }
	}
	public class ListResultGiftInfors
	{
		public int TotalCount { get; set; }

		public List<GetAllGiftInforsIteams> Items { get; set; }
	}
	public class GetAllGiftInforsIteams
	{
		public GetGiftGroupForView GiftGroup { get; set; }
		public List<GiftShortInforForView> Gifts { get; set; }
	}

	public class GetGiftGroupForView
	{
		public GiftGroupDto GiftGroup { get; set; }

		public ImageLinkDto ImageLink { get; set; }
		
		public int? NumberOfGifts { get; set; }
	}
	public class GiftShortInforForView
	{
		public GiftShortInforDto GiftInfor { get; set; }

		public List<ImageLinkDto> ImageLink { get; set; }
		public FlashSaleProgramDto FlashSaleProgramInfor { get; set; }
		public GiftDiscountDto GiftDiscountInfor { get; set; }

		public GiftShortInforForView()
		{
			ImageLink = new List<ImageLinkDto>();
		}

	}

	public class GiftShortInforDto
	{
		public string Code { get; set; }

		public string Name { get; set; }

		public string Description { get; set; }

		public string Introduce { get; set; }

		public string FullGiftCategoryCode { get; set; }

		public string ThirdPartyBrandName { get; set; }

		public string Vendor { get; set; }

		public DateTime EffectiveFrom { get; set; }

		public DateTime EffectiveTo { get; set; }

		public decimal RequiredCoin { get; set; }

		public string Status { get; set; }

		public decimal TotalQuantity { get; set; }

		public decimal UsedQuantity { get; set; }

		public decimal RemainingQuantity { get; set; }

		public decimal FullPrice { get; set; }
		public decimal DiscountPrice { get; set; }

		public bool IsEGift { get; set; }

		//public SettingParamDto TargetAudience { get; set; }
		//public int? TargetAudienceId { get; set; }

		//public DateTime? LastModificationTime { get; set; }
		//public DateTime CreationTime { get; set; }
		//public string CreatedByUser { get; set; }
		//public string UpdatedByUser { get; set; }

		public string Tag { get; set; }

		public bool IsInWishlist { get; set; }

		public int Id { get; set; }

		public string RegionCode { get; set; }

		public string Office { get; set; }
		public string ExpireDuration { get; set; }

		public int TotalWish { get; set; }
		
		public string VendorHotline { get; set; }

		// for sorting
		//public int? BrandOrdinal { get; set; }
		//public int? VendorOrdinal { get; set; }
		//public int? MerchantOrdinal { get; set; }
		public int? TotalRedeem { get; set; }
		public DateTime? GiftUpdatedTime { get; set; }
		public DateTime? GiftCreatedTime { get; set; }
		// info data gift
		public string VendorName { get; set; }
		public string VendorType { get; set; }
		public string VendorImage { get; set; }
		public string VendorDescription { get; set; }
		public int? MerchantId { get; set; }
		public string MerchantName { get; set; }
		public string MerchantAvatar { get; set; }
		public string MerchantDescription { get; set; }
		public int? BrandId { get; set; }
		public string BrandName { get; set; }
		public string BrandLinkLogo { get; set; }
		public string BrandDescription { get; set; }
		public string BrandAddress { get; set; }
		public string ThirdPartyBrandId { get; set; }
		public string ThirdPartyCategoryName { get; set; }
		public string ThirdPartyCategoryId { get; set; }
		public string ThirdPartyGiftCode { get; set; }
		public int? ThirdPartyBrandMappingId { get; set; }
		public double CommisPercentCategory { get; set; }
		public decimal? PriceLasted { get; set; }
		public string Condition { get; set; }
		public string OfficeAddress { get; set; }
        public int? TotalRedeemedOfUser { get; set; }
        public int? MaxAllowedRedemptionOfUser { get; set; }
        public int? MaxQuantityPerRedemptionOfUser { get; set; }
        public string ContactEmail { get; set; }
        public string ContactHotline { get; set; }
        public bool? IsBrandFavourite { get; set; } = false;
        public bool IsTypeCashout { get; set; }
        public int? DisplayOrder { get; set; }
		public int? EffectiveFromRedeem { get; set; }
		public DateTime? ExpiryDate { get; set; }
	}

	public class GiftDiscountDto
	{
		public decimal? SalePrice { get; set; }

		public int? RemainingQuantityFlashSale { get; set; }

		public float? ReductionRate { get; set; }

		public bool WarningOutOfStock { get; set; }
		public int? UsedQuantityFlashSale { get; set; }
		public string Status { get; set; }
		public int ReductionRateDisplay
		{
			get
			{
				if (ReductionRate.HasValue)
				{
					if (ReductionRate > 0 && ReductionRate < 1)
					{
						return 1;
					}
					else if (ReductionRate > 99 && ReductionRate < 100)
					{
						return 99;
					}
					else
					{
						return (int)Math.Round((double)ReductionRate);
					}
				}
				return 0;
			}
		}
		public int? RedeemGiftQuantity { get; set; }
		public int? RedeemFlashSaleQuantity { get; set; }
		public int? MaxAmountRedeem { get; set; }

		public int? MaxAmount { get; set; }
        public bool IsSoldOut { get; set; }
    }
}
