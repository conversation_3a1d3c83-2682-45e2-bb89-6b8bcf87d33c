using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.DTO.Loyalty.GiftCategories;
using AKC.MobileAPI.DTO.Reward.CashoutTransaction;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Loyalty.Gift
{
    [Route("api/recommendation")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class GiftRecommendationController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyGiftService _loyaltyGiftService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IRewardCashoutTransactionService _rewardCashoutTransactionService;
        protected readonly IConfiguration _configuration;
        private string PersonalizationPilotMode = "";
        private string PersonalizationPilotList = "";
        private List<string> ListAllowed = new List<string>();

        public GiftRecommendationController
        (
            ILogger<GiftRecommendationController> logger,
            ILoyaltyGiftService loyaltyGiftService,
            IExceptionReponseService exceptionReponseService,
            ICommonHelperService commonHelperService,
            IConfiguration cf,
            IRewardCashoutTransactionService x)
        {
            _logger = logger;
            _loyaltyGiftService = loyaltyGiftService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
            _rewardCashoutTransactionService = x;
            _configuration = cf;
            PersonalizationPilotMode = _configuration.GetSection("PersonalizationPilotMode").Value;
            PersonalizationPilotList = _configuration.GetSection("PersonalizationPilotList").Value;
            _logger.LogInformation(" >> PersonalizationPilotMode " +  PersonalizationPilotMode + " - PersonalizationPilotList = " + PersonalizationPilotList);
            if (string.IsNullOrEmpty(PersonalizationPilotMode))
            {
                PersonalizationPilotMode = "ON";
                // Khi chưa có set ở appsetting, thì mặc định bằng ON để test pilot
                // Khi có nhu cầu off, thì set vào appsetting PersonalizationPilotMode = "OFF"
            }
            if (PersonalizationPilotList != null)
            {
                ListAllowed = PersonalizationPilotList.Split(",").ToList();
            }

            if ("ON" == PersonalizationPilotMode && ListAllowed.Count == 0)
            {
                ListAllowed.Add("BelLsLy8iIzta1braaWC3WgjArZ"); // TRAN UAT
                ListAllowed.Add("bUQqe92KL3UOUscYu74jcHbFPKw1"); // TRAN UAT
                ListAllowed.Add("pSCKmj3sEJPuEypcA5rMgjRUxRn2"); // LA UAT
            }
        }


        /**
         * Get các categories được recommended cho từng member. Cached lại trong vòng 5 phút để tránh flood Redis.
         */
        [HttpGet]
        [Route("get-categories")]
        public async Task<ActionResult<GetRecommendedCategoriesOutput>> GetRecommendedCategories(
            [FromQuery] GetRecommendedCategoriesInput input)
        {
            if ("ON" == PersonalizationPilotMode && !ListAllowed.Contains(input.MemberCode))
            {
                // PersonalizationPilotMode = OFF thì sẽ không vào đây
                // Khi PersonalizationPilotMode = ON nhưng membercode nằm ngoài whitelist thì sẽ bị trả ra rỗng.
                _logger.LogInformation(" >> Pilot On, Member not in the test list");
                return StatusCode(200, new GiftCategoriesForView()
                {
                    Success = true, Result = new ListResultGiftCategoriesForMB()
                    {
                        TotalCount = 0, Items = new List<GiftCategoriesForMB>()
                    }, 
                });
            }
            var requestId = input.MemberCode + "_" + DateTime.UtcNow.Ticks;
            try
            {
                _logger.LogInformation($"{requestId} - GetRecommendedCategories >> " +
                                       JsonConvert.SerializeObject(input));
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }

                var result = await _loyaltyGiftService.GetRecommendedCategories(input);

                var result2 = new GiftCategoriesForView()
                {
                    Success = true, Result = new ListResultGiftCategoriesForMB()
                    {
                       TotalCount = result.Result.TotalCount, Items = result.Result.Items.Select(x => new GiftCategoriesForMB()
                       {
                           Code = x.GiftCategory.Code, Name = x.GiftCategory.Name, CategoryTypeCode = "", FullLink = x.GiftCategory.ImageLink?.FullLink ?? ""
                       }).ToList()
                    }, 
                };

                return StatusCode(200, result2);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{requestId} GetRecommendedCategories >> Error >> " + ex.Message + " -- " + ex.StackTrace);
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        /**
         * Get các Gifts được recommended cho từng member. Cached lại trong vòng 5 phút để tránh flood Redis.
         */
        [HttpGet]
        [Route("get-gifts")]
        public async Task<ActionResult<GetRecommendedGiftsOutput>> GetRecommendedGifts(
            [FromQuery] GetRecommendedGiftsInput input)
        {
            if ("ON" == PersonalizationPilotMode && !ListAllowed.Contains(input.MemberCode))
            {
                // PersonalizationPilotMode = OFF thì sẽ không vào đây
                // Khi PersonalizationPilotMode = ON nhưng membercode nằm ngoài whitelist thì sẽ bị trả ra rỗng.
                _logger.LogInformation(" >> Pilot On, Member not in the test list");
                return StatusCode(200, new GetRecommendedGiftsOutput()
                {
                    Success = true, Result = new GetRecommendedGiftsOutputResult()
                    {
                        TotalCount = 0, Items = new List<GetRecommendedGiftsOutputResultElement>()
                    }, 
                });
            }
            var requestId = input.MemberCode + "_" + DateTime.UtcNow.Ticks;
            try
            {
                _logger.LogInformation($"{requestId} - GetRecommendedGifts >> " +
                                                       JsonConvert.SerializeObject(input));
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }

                var result = await _loyaltyGiftService.GetRecommendedGifts(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{requestId} - GetRecommendedGifts Error >> " + ex.Message + " -- " + ex.StackTrace);
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }
    }
}