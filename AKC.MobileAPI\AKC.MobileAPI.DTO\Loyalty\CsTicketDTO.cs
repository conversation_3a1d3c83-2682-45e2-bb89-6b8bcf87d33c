using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace AKC.MobileAPI.DTO.Loyalty
{
    public class UploadImageInput
    {
        public IFormFile File { get; set; }
        public string Type { get; set; }
    }

    public class UploadImageOutput
    {
        public string FileName { get; set; }
        public string Link { get; set; }
    }

    public class CreateCSTicketOutput
    {
        public int Id { get; set; }
        public string TicketCode { get; set; }
        public DateTime CreatedTime { get; set; }
    }

    public class SyncStringeeXOutput
    {
        public string OldStatus { get; set; }
        public string NewStatus { get; set; }
        public bool IsSuccessful { get; set; }
        public string Message { get; set; }
    }
    public class SyncStringeeXInput
    {
        public string Code { get; set; }
        public string MemberCode { get; set; }
        public int? Id { get; set; }
    }
    public class CreateCSTicketInput
    {
        public string Code { get; set; }
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
        public string MemberName { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }

        public string Status { get; set; }
        public string TicketType { get; set; }
        public string Priority { get; set; }
        public List<string> AttachmentList { get; set; }
    }

    public class GetOneCSTicketOutput
    {
        [JsonProperty("code")]
        public string Code { get; set; }
    
        [JsonProperty("title")]
        public string Title { get; set; }
    
        [JsonProperty("content")]
        public string Content { get; set; }
    
        [JsonProperty("status")]
        public string Status { get; set; }
    
        [JsonProperty("memberCode")]
        public string MemberCode { get; set; }
    
        [JsonProperty("memberName")]
        public string MemberName { get; set; }
    
        [JsonProperty("phoneNumber")]
        public string PhoneNumber { get; set; }
    
        [JsonProperty("attachments")]
        public List<string> Attachments { get; set; }
    
        [JsonProperty("comments")]
        public List<CSTicketComment> Comments { get; set; }
    
        [JsonProperty("creationTime")]
        public DateTime CreationTime { get; set; }

        [JsonProperty("isReacted")]
        public bool isReacted { get; set; }
    }

    public class CSTicketComment
    {
        [JsonProperty("ticketCode")]
        public string TicketCode { get; set; }
    
        [JsonProperty("code")]
        public string Code { get; set; }
    
        [JsonProperty("content")]
        public string Content { get; set; }
    
        [JsonProperty("creationTime")]
        public DateTime CreationTime { get; set; }
    
        [JsonProperty("attachments")]
        public List<string> Attachments { get; set; }
    }

    public class GetOneCSTicketInput
    {
        public string Code { get; set; }
        public string MemberCode { get; set; }
        public int? Id { get; set; }
    }
    public class GetListCSTicketOutput
    {
        public int TotalCount { get; set; }
        public List<GetListCSTicketOutputInner> Items { get; set; }
    }

    public class GetListCSTicketOutputInner
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Title { get; set; }
        public string Status { get; set; }
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
        public string Content { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string StringeeXCode { get; set; }
    }
    public class GetListCSTicketInput
    {
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
        public DateTime? FromDateFilter { get; set; }
        public DateTime? ToDateFilter { get; set; }
        public string TicketType { get; set; }
        public string TitleFilter { get; set; }
        public string StatusFilter { get; set; }
    }

    public class UploadImageCSTicketInput
    {
        public IFormFile File { get; set; }
    }
}