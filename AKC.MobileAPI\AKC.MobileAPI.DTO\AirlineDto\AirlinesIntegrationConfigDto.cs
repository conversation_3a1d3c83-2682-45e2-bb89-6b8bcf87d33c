﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.AirlineDto
{
    public class AirlinesIntegrationConfigDto
    {
        public int Id { get; set; }
        public bool IsDeleted { get; set; }
        public long? DeleterUserId { get; set; }
        public DateTime? DeletionTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public long? LastModifierUserId { get; set; }
        public DateTime CreationTime { get; set; }
        public long? CreatorUserId { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Status { get; set; }
        public string Description { get; set; }
        public int MerchantId { get; set; }
        public int? MinMiles { get; set; }
        public int? MaxMiles { get; set; }
    }

    public class AirlinesIntegrationConfigOutput
    {
        public int TotalCount { get; set; }
        public List<AirlinesIntegrationConfigDto> items { get; set; }
    }


    public class LoyaltyAirlinesIntegrationConfigInput
    {
        public string Code { get; set; }
        public string Status { get; set; }
    }
}
