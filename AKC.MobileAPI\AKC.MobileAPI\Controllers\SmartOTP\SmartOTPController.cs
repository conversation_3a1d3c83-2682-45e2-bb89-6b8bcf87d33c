﻿using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using AKC.MobileAPI.Service.Abstract.SmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.ActiveSmartOTP;
using Microsoft.AspNetCore.Authorization;
using Newtonsoft.Json;
using AKC.MobileAPI.DTO.SmartOTP.RegisterSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.ValidateSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.DeactivateSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.VerifySmartOTP;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.DTO.SmartOTP.SyncTimeSmartOTP;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.DTO.SmartOTP.GetOTP;
using AKC.MobileAPI.DTO.SmartOTP.RegisterSmartOTPStatus;
using AKC.MobileAPI.DTO.SmartOTP.VerifyOTP;
using Microsoft.Extensions.Caching.Distributed;
using AKC.MobileAPI.DTO.SmartOTP.StatusSmartOTP;

namespace AKC.MobileAPI.Controllers.SmartOTP
{
    //[Route("v1/saas-sotp")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class SmartOTPController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ISmartOTPService _smartOTPService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IDistributedCache _cache;

        public SmartOTPController(ILogger<SmartOTPController> logger, ISmartOTPService smartOTPService, IExceptionReponseService exceptionReponseService, ICommonHelperService commonHelperService, IDistributedCache cache)
        {
            _logger = logger;
            _smartOTPService = smartOTPService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
            _cache = cache;
        }

        [HttpPost("v1/saas-sotp/activate")]
        public async Task<ActionResult<ActiveSmartOTPResponse>> ActiveSmartOTP([FromBody] ActiveSmartOTPRequest request)
        {
            try
            {
                var model = new ActiveSmartOTPInput
                {
                    MemberCode = request.user_id,
                    DeviceId = request.device_id,
                    DeviceOS = request.device_os,
                    AppVersion = request.app_version,
                    ActivationKey = request.activation_key,
                    Token = request.token
                };
                var response = await _smartOTPService.ActiveSmartOTP(model);
                var data = new ActiveSmartOtpDataResponse
                {
                    Key = response.Data?.SecretKey,
                    Period = response.Data?.Period,
                    Algorithm = response.Data?.Algorithm,
                    Length = response.Data?.Length,
                    Type = response.Data.ConfigType,
                    Issuer = response.Data?.Issuer,
                    Id = response.Data?.Id
                };
                var result = new ActiveSmartOTPResponse
                {
                    Success = response.Success,
                    Code = response.Code,
                    Message = response.Message,
                    Data = data
                };
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = _exceptionReponseService.GetExceptionRewardReponse(ex).Result;
                _logger.LogError(ex, "ActiveSmartOTP Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }

        [HttpPost("v1/saas-sotp/retire")]
        public async Task<ActionResult<DeactivateSmartOTPOutput>> DeactivateSmartOTP([FromBody] RetireSmartOTPRequest request)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var model = new DeactivateSmartOTPRequest
                {
                    MemberCode = await DecodeTokenGetMemberCode(authorization),
                    Token = request.token
                };
                var result = await _smartOTPService.DeactivateSmartOTP(model);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = _exceptionReponseService.GetExceptionRewardReponse(ex).Result;
                _logger.LogError(ex, "DeactivateSmartOTP Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }

        [HttpPost("v1/saas-sotp/register")]
        public async Task<ActionResult<RegisterSmartOTPOutput>> RegisterSmartOTP([FromBody] RegisterSmartOTPInput request)
        {
            _logger.LogInformation($"RegisterSmartOTP___Request:{JsonConvert.SerializeObject(request)}");
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, request.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                _logger.LogInformation($"RegisterSmartOTP___Call___smartOTPService");
                var result = await _smartOTPService.RegisterSmartOTP(request);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = _exceptionReponseService.GetExceptionRewardReponse(ex).Result;
                _logger.LogError(ex, "RegisterSmartOTP Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }

        [HttpPost("v1/saas-sotp/validate-sotp")]
        public async Task<ActionResult<ValidateSmartOTPOutput>> ValidateSmartOTP([FromBody] ValidateSmartOTPRequest request)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var model = new ValidateSmartOTPInput
                {
                    MemberCode = await DecodeTokenGetMemberCode(authorization),
                    Flow = SmartOtpConstant.FlowReedem,
                    Token = request.token,
                    OTP = request.otp,
                    OTPType = request.otp_type,
                    SessionId = Guid.NewGuid().ToString()
                };
                var result = await _smartOTPService.ValidateSmartOTP(model);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = _exceptionReponseService.GetExceptionRewardReponse(ex).Result;
                _logger.LogError(ex, "ValidateSmartOTP Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }

        [HttpPost("v1/saas-sotp/sync-time")]
        public async Task<ActionResult<SyncTimeSmartOTPOutput>> SyncTimeSmartOTP([FromBody] SyncTimeSmartOTPRequest request)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var model = new SyncTimeSmartOTPInput
                {
                    MemberCode = await DecodeTokenGetMemberCode(authorization),
                    OTP = request.otp,
                    Token = request.token,
                    Period = request.period
                };
                var result = await _smartOTPService.SyncTimeSmartOTP(model);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = _exceptionReponseService.GetExceptionRewardReponse(ex).Result;
                _logger.LogError(ex, "ValidateSmartOTP Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }

        [HttpPost("v1/saas-sotp/get-status")]
        public async Task<ActionResult<StatusSmartOTPOutput>> GetStatusSmartOTP([FromBody] StatusSmartOTPRequest request)
        {
            try
            {
                var model = new StatusSmartOTPInput { Token = request.token };
                var result = await _smartOTPService.GetStatusSmartOTP(model);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = _exceptionReponseService.GetExceptionRewardReponse(ex).Result;
                _logger.LogError(ex, "GetStatusSmartOTP Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }

        [HttpPost("/api/SmartOTP/VerifySmartOTP")]
        public async Task<ActionResult<VerifyOTPOutput>> VerifySmartOTP([FromBody] VerifySmartOTPInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var model = new CreateUserMobileDTO
                {
                    SessionId = input.SessionId,
                    OtpCode = input.OtpCode,
                    Name = input.Name,
                    Address = input.Address,
                    PhoneNumber = input.PhoneNumber,
                    Gender = input.Gender,
                    ReferralCode = input.ReferralCode
                };
                var result = await _smartOTPService.VerifySmartOTP(model);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = _exceptionReponseService.GetExceptionRewardReponse(ex).Result;
                _logger.LogError(ex, "VerifySmartOTP Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }

        [HttpPost("/api/SmartOTP/GetOTP")]
        public async Task<ActionResult<RewardMemberSendOtpOutput>> GetOTP(GetOTPInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var model = new RewardMemberSendOtpInput
                {
                    PhoneNumber = input.PhoneNumber,
                    SessionId = input.SessionId,
                    SmsType = input.SmsType
                };
                return await _smartOTPService.GetOTP(model);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetOTP error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpPost("/api/SmartOTP/RegisterSmartOTPStatus")]
        public async Task<ActionResult<RegisterSmartOTPStatusOutput>> RegisterSmartOTPStatus(RegisterSmartOTPStatusInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                return await _smartOTPService.RegisterSmartOTPStatus(input);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "RegisterSmartOTPStatus error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpPost("/api/SmartOTP/VerifyOTP")]
        public async Task<ActionResult<RewardMemberVerifyOtpOutput>> VerifyOTP(VerifyOTPInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var model = new RewardMemberVerifyOtpInput
                {
                    OtpCode = input.OtpCode,
                    PhoneNumber = input.PhoneNumber,
                    SessionId = input.SessionId,
                    SmsType = input.SmsType
                };
                return await _smartOTPService.VerifyOTP(model);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "VerifyOTP error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        private async Task<string> DecodeTokenGetMemberCode(string authToken)
        {
            if (string.IsNullOrWhiteSpace(authToken))
            {
                return string.Empty;
            }
            var idToken = authToken.Replace("Bearer ", "").Replace("bearer ", "");
            var token = await FirebaseAdmin.Auth.FirebaseAuth.DefaultInstance.VerifyIdTokenAsync(idToken);
            if (token == null || string.IsNullOrWhiteSpace(token.Uid))
                return string.Empty;
            var memberCodeCache = await _cache.GetStringAsync(token.Uid);
            return memberCodeCache;
        }
    }
}
