﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Quest
{
    public class GmQuestV2Dto
    {
        public int Id { get; set; }
        public int? TenantId { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Status { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public int MaxActionCount { get; set; }
        public string TimeMode { get; set; }
        public bool AutoEnrolment { get; set; }
        public bool AutoReward { get; set; }
        public bool NonstopRequired { get; set; }
        public long? CreatorUserId { get; set; }
        public DateTime CreationTime { get; set; }
        public string CreatorUserName { get; set; }
        public long? LastModifierUserId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string LastModifierUserName { get; set; }
    }
}
