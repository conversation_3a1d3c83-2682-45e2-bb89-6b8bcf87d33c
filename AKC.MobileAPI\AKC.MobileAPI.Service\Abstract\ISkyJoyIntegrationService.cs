using System.Threading;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;

namespace AKC.MobileAPI.Service.Abstract
{
    public interface ISkyJoyIntegrationService
    {
        public Task<SJRequestAccessTokenOutput> ExchangeCodeForToken(SJRequestAccessTokenInput input);
        public Task<SJLinkMemberOutput> LinkMember(SJLinkMemberInput input,  CancellationToken cancellationToken = default);
        public Task<SJUnlinkMemberOutput> UnlinkMember(SJUnlinkMemberInput input, CancellationToken cancellationToken = default);
        public Task<SJPointAccrualOutput> PointAccrual(SJPointAccrualInput input, CancellationToken cancellationToken = default);
        string GetLoginUri();
    }
}