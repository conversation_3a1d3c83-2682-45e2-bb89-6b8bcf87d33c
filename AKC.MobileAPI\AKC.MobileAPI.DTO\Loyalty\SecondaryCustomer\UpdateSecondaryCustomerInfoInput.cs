﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer
{
    public class UpdateSecondaryCustomerInfoInput
    {
        public string Code { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public DateTime? Birthday { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string Gender { get; set; }
        public string IdCard { get; set; }
        public string FacebookId { get; set; }
        public string FirebaseId { get; set; }
        public string Avatar { get; set; }
    }
}
