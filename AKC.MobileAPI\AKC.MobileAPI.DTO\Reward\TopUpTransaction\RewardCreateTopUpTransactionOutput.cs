﻿using AKC.MobileAPI.DTO.Reward.Payment;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.TopUpTransaction
{
    public class RewardCreateTopUpTransactionOutput
    {
        public int Result { get; set; }
        public RewardCreateTopUpTransactionResult Items { get; set; }
        public string Message { get; set; }
    }

    public class RewardCreateTopUpTransactionResult
    {
        public string TokenTransactionId { get; set; }
        public double TokenAmount { get; set; }
    }

    public class RewardCreateTopUpTransactionPayment
    {
        public RewardCreateTopUpTransactionOutput Data { get; set; }
        public bool IsSuccess { get; set; }
        public string ErrorCode { get; set; }
    }

    public class HandlerCreateTopUpTransactionPayment
    {
        public string CacheDataTopup { get; set; }
        public string TopupCache { get; set; }
        public string Url { get; set; }
        public string IpPayment { get; set; }
        public string PaymentTransactionStatus { get; set; }
        public RewardCheckReturnPaymentLinkOutput CheckReturn { get; set; }
    }
}
