using System;
using System.Linq;
using System.Net;
using Microsoft.AspNetCore.Http;

namespace AKC.MobileAPI.Helper
{
    public class IpAddressHelper
    {
        public static string GetClientIp(HttpContext context)
        {
            // 1. Thử lấy từ X-Forwarded-For (phù hợp với K8S có Ingress/proxy)
            if (context.Request.Headers.TryGetValue("X-Forwarded-For", out var forwardedFor))
            {
                // X-Forwarded-For chứa danh sách IP: client_ip, proxy1, proxy2, ...
                // Lấy IP đầu tiên (client IP)
                var ip = forwardedFor.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .FirstOrDefault()?.Trim();
                if (!string.IsNullOrEmpty(ip) && IsValidIpAddress(ip) && !IsPrivateIp(ip))
                {
                    return ip;
                }
            }

            // 2. Fallback sang X-Real-IP (một số Ingress chỉ dùng header này)
            if (context.Request.Headers.TryGetValue("X-Real-IP", out var realIp))
            {
                var ip = realIp.ToString().Trim();
                if (!string.IsNullOrEmpty(ip) && IsValidIpAddress(ip) && !IsPrivateIp(ip))
                {
                    return ip;
                }
            }

            // 3. Fallback sang CF-Connecting-IP (nếu dùng Cloudflare)
            if (context.Request.Headers.TryGetValue("CF-Connecting-IP", out var cfIp))
            {
                var ip = cfIp.ToString().Trim();
                if (!string.IsNullOrEmpty(ip) && IsValidIpAddress(ip) && !IsPrivateIp(ip))
                {
                    return ip;
                }
            }

            // 4. Fallback sang RemoteIpAddress (có thể là IP của proxy)
            var remoteIp = context.Connection.RemoteIpAddress?.ToString();
            if (!string.IsNullOrEmpty(remoteIp) && IsValidIpAddress(remoteIp) && !IsPrivateIp(remoteIp))
            {
                return remoteIp;
            }

            // Không lấy đc IP hợp lệ
            return null;
        }

        private static bool IsValidIpAddress(string ip)
        {
            return IPAddress.TryParse(ip, out _);
        }

        private static bool IsPrivateIp(string ip)
        {
            if (string.IsNullOrEmpty(ip))
            {
                return false;
            }

            try
            {
                var ipAddress = IPAddress.Parse(ip);
                if (ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork) // IPv4
                {
                    var bytes = ipAddress.GetAddressBytes();
                    return bytes[0] == 10 || // 10.0.0.0/8
                           (bytes[0] == 172 && bytes[1] >= 16 && bytes[1] <= 31) || // **********/12
                           (bytes[0] == 192 && bytes[1] == 168); // ***********/16
                }
                else if (ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetworkV6)
                {
                    // Bỏ qua localhost (::1) hoặc IP nội bộ IPv6
                    return ipAddress.IsIPv6LinkLocal || ipAddress.IsIPv6SiteLocal || ip == "::1";
                }
            }
            catch
            {
                return false;
            }

            return false;
        }
    }
}