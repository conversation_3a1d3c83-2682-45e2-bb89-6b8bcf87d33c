﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.SmartOTP.SyncTimeSmartOTP
{
    public class SyncTimeSmartOTPOutput
    {
        public bool Success { get; set; }
        public int Code { get; set; }
        public string Message { get; set; }
        public SyncTimeSmartOTPDataResponse Data { get; set; }
    }

    public class SyncTimeSmartOTPDataResponse
    {
        public string Timestamp { get; set; }
        public int? Period { get; set; }
        public string Algorithm { get; set; }
        public int? Length { get; set; }
        public string Issuer { get; set; }
        public string ConfigType { get; set; }
    }
}
