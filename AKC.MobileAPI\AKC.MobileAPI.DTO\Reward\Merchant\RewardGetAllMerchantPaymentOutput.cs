﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Merchant
{
    public class RewardGetAllMerchantPaymentOutput
    {
        public int Result { get; set; }
        public int TotalCount { get; set; }
        public string Message { get; set; }
        public List<RewardGetAllMerchantPaymentOutputItem> Items { get; set; }
    }

    public class RewardGetAllMerchantPaymentOutputItem
    {
        public int MerchantId { get; set; }
        public string MerchantName { get; set; }
        public string Logo { get; set; }
        public decimal BaseUnit { get; set; }
        public string PaymentType { get; set; }
        public decimal PointExchangeRate { get; set; }
    }
}
