﻿using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.PartnerPointCaching;
using AKC.MobileAPI.DTO.ThirdParty.Appota;
using AKC.MobileAPI.DTO.ThirdParty.VPBank;
using Microsoft.AspNetCore.Http;

namespace AKC.MobileAPI.Service.Abstract.ThirdParty
{
    public interface IThirdPartyAppotaService
    {
        Task<LoyaltyThirdPartyPointViewOutput> PointView(LoyaltyThirdPartyPointViewInput input, HttpContext context);

        Task<LoyaltyThirdPartyPointExchangeOutput> PointExchange(LoyaltyThirdPartyPointExchangeInput input,
            HttpContext context, string orderCode = null);

        Task<LoyaltyThirdPartyRevertPointOutput> RevertPoint(LoyaltyThirdPartyRevertPointInput input,
            HttpContext context);
        
        Task<LoyaltyThirdPartyRequestAccessTokenOutput> RequestAccessToken(LoyaltyThirdPartyRequestAccessTokenInput input, HttpContext context);

        Task<RewardPartnerPoingCachingItems> UpdatePartnerCaching(
            LoyaltyThirdPartyAppotaUpdatePartnerCachingInput input, HttpContext context);
    }
}