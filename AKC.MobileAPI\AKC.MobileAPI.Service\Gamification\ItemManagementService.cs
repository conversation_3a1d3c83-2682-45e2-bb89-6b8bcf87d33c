﻿using AKC.MobileAPI.DTO.Gamification;
using AKC.MobileAPI.DTO.Gamification.Exchange;
using AKC.MobileAPI.DTO.Gamification.Game;
using AKC.MobileAPI.Service.Abstract.Gamification;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Gamification
{
    public class ItemManagementService : ItemManagementBaseService, IItemManagementService
    {
        public ItemManagementService(IConfiguration configuration)
            : base(configuration)
        {
        }

        public async Task<GetDetailsExchangeRuleOutput> GetDetailsExchangeRule(GetDetailsExchangeRuleInput input)
        {
            return await GetGamificationdAsync<GetDetailsExchangeRuleOutput>(GamificationApiUrl.GET_DETAILS_EXCHANGE_RULE, input);
        }

        public async Task<GetAllGetExchangeRuleListOutput> GetExchangeRuleType(GetAllGetExchangeRuleListInput input)
        {
            return await GetGamificationdAsync<GetAllGetExchangeRuleListOutput>(GamificationApiUrl.GET_EXCHANGE_RULE_TYPE, input);
        }
    }
}
