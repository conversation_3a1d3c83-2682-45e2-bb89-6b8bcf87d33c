﻿using AKC.MobileAPI.DTO.Loyalty.Gift;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftUsageAddress
{
    public class GiftUsageAddressOutput
    {
        public ListGiftUsageAddress Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }
    
    public class ListGiftUsageAddress
    {
        public int TotalCount { get; set; }
        public List<GiftUsageAddressDto> Items { get; set; }
    }

    public class GiftUsageAddressDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Latitude { get; set; }
        public string Longtitude { get; set; }
    }
}
