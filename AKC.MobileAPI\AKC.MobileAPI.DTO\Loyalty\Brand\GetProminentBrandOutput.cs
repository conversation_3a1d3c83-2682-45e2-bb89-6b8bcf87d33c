﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Brand
{
    public class GetProminentBrandOutput
    {
        public GetListProminentBrandOutput Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class GetListProminentBrandOutput
    {
        public int TotalCount { get; set; }
        public List<ProminentBrandOutput> Items { get; set; }
    }

    public class ProminentBrandOutput
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Logo { get; set; }
        public string Description { get; set; }
        public int? Order { get; set; }
        public string Status { get; set; }
    }
}
