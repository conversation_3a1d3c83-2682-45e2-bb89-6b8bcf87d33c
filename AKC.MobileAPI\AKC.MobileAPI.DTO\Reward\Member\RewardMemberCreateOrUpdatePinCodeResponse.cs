﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberCreateOrUpdatePinCodeResponse
    {
        public int Result { get; set; }
        public string Message { get; set; }
        public string MessageDetail { get; set; }
        public string CustomToken { get; set; }
    }

    public class RewardMemberCreateOrUpdatePinCodeSendOtp
    {
        public int Result { get; set; }
        public string Message { get; set; }
        public string MessageDetail { get; set; }
    }

    public class RewardMemberUpdatePinCodeResponse
    {
        public int Result { get; set; }
        public string Message { get; set; }
        public RewardMemberUpdatePinCodeResponseItem Item { get; set; }
    }

    public class RewardMemberUpdatePinCodeResponseItem
    {
        public string CustomToken { get; set; }
        public string MemberCode { get; set; }
    }
}
