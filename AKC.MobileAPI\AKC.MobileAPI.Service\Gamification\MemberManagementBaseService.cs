﻿using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.CronServices;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using Cronos;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.Gamification
{
    public class MemberManagementBaseService
    {
        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        protected string baseURL;
        protected int tenantId;
        private readonly IDistributedCache _cache;

        public MemberManagementBaseService(IConfiguration configuration, IDistributedCache cache)
        {
            _configuration = configuration;
            baseURL = _configuration.GetSection("Gamifications:MemberManagement:RemoteURL").Value;
            tenantId = Convert.ToInt32(_configuration.GetSection("Loyalty:TenantId").Value);
            _cache = cache;
        }

        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetGamificationMemberManagementAsync<T>(string apiURL, object query = null, string rewardType = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };
            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            var token = GetAccessToken();
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);

            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();

            //Recall API when Unauthorized
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                string accessToken = GetAccessToken(true);
                req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var reqClone = CloneHttpRequest(req);

                response = await _client.SendAsync(reqClone);
                rawData = await response.Content.ReadAsStringAsync();
            }
            //End Recall API when Unauthorized

            if (response.IsSuccessStatusCode == false)
            {
                var ex = new GamificationException();
                ex.Data.Add("ErrorData", rawData);
                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    ex.Data.Add("StatusCode", 400);
                }
                else
                {
                    ex.Data.Add("StatusCode", 500);
                }
                throw ex;
            }
            response.EnsureSuccessStatusCode();

            // Get respone result.
            var result = JsonConvert.DeserializeObject<T>(rawData);

            return result;
        }

        public async Task<T> PostGamificationMemberManagementAsync<T>(string apiURL, object body)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            var data = new StringContent(JsonConvert.SerializeObject(body), Encoding.UTF8, "application/json");

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post,
                Content = data
            };
            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri(requestURL);

            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();

            //Recall API when Unauthorized
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                string accessToken = GetAccessToken(true);
                req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var reqClone = CloneHttpRequest(req);

                response = await _client.SendAsync(reqClone);
                rawData = await response.Content.ReadAsStringAsync();
            }
            //End Recall API when Unauthorized

            if (response.IsSuccessStatusCode == false)
            {
                var ex = new GamificationException();
                ex.Data.Add("ErrorData", rawData);
                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    ex.Data.Add("StatusCode", 400);
                }
                else
                {
                    ex.Data.Add("StatusCode", 500);
                }
                throw ex;
            }
            response.EnsureSuccessStatusCode();

            // Get respone result.
            var result = JsonConvert.DeserializeObject<T>(rawData);

            return result;
        }

        /// <summary>
        /// Convert a object to query string format.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string GetQueryString(object obj)
        {
            var properties = from p in obj.GetType().GetProperties()
                             where p.GetValue(obj, null) != null
                             select p.Name + "=" + HttpUtility.UrlEncode(p.GetValue(obj, null).ToString());

            return string.Join("&", properties.ToArray());
        }
        private string GetAccessToken(bool mustResetCache = false)
        {
            var token = _cache.GetString(CommonConstants.ACCESSS_TOKEN_CACHE_KEY);
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var expression = CronExpression.Parse(configuration.GetSection("Loyalty:CronExpressionRefreshToken").Value);
            var timeZoneInfo = TimeZoneInfo.Local;

            // Request body
            if (string.IsNullOrEmpty(token) || mustResetCache)
            {
                return LoyaltyHelper.RenewAccessTokenCacheValue(_cache, CronHelper.GetDelayToNextRefreshToken(expression, timeZoneInfo), mustResetCache);
            }

            return token;
        }
        //Clone a HttpRequest
        private HttpRequestMessage CloneHttpRequest(HttpRequestMessage req)
        {
            HttpRequestMessage clone = new HttpRequestMessage(req.Method, req.RequestUri);

            clone.Content = req.Content;
            clone.Version = req.Version;

            foreach (KeyValuePair<string, object> prop in req.Properties)
            {
                clone.Properties.Add(prop);
            }

            foreach (KeyValuePair<string, IEnumerable<string>> header in req.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            return clone;
        }
    }
}
