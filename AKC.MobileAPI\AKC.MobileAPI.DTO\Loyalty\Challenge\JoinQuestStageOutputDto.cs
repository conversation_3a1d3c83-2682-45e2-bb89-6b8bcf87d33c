﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class JoinQuestStageOutputDto
    {
        public MemberJoinQuestStageOutputDto Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class MemberJoinQuestOutputDto
    {
        public string Id { get; set; }
        public string MemberCode { get; set; }
        public string QuestCode { get; set; }
        public string QuestName { get; set; }
        public DateTime Date { get; set; }
        public string JoinState { get; set; }
        public string QuestState { get; set; }
        public string ErrorMessage { get; set; }
    }

    public class MemberJoinQuestStageOutputDto
    {
        public string Id { get; set; }
        public int CurrentLevelCompleted { get; set; }
        public string MemberCode { get; set; }
        public string QuestStageCode { get; set; }
        public DateTime Date { get; set; }
        public string State { get; set; }
        public List<MemberJoinQuestOutputDto> MemberJoinedQuest { get; set; }
    }
}
