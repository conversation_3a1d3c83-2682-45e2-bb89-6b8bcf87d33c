﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.MobileAPI;
using AKC.MobileAPI.DTO.Reward.Member;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltySecondaryCustomersService
    {
        Task<LoyaltyResponse<SecondaryCustomerDto>> GetProfileByCode(string code);
        Task<LoyaltyResponse<CustomSecondaryCustomerInfoDto>> GetMemberInfoByCode(string code);
        Task<LoyaltyResponse<string>> UpdateNotificationSetting(UpdateNotificationSettingInput code);
        Task<LoyaltyResponse<LoyaltyVerifyReferralCodeOutputDto>> VerifyReferralCode(LoyaltyVerifyReferralCodeInput input);
        Task<LoyaltyMemberCheckReferralCodeExistanceOutput> CheckReferralCodeExistance(LoyaltyMemberCheckReferralCodeExistanceInput input);
        Task<LoyaltyResponse<string>> UpdatePhoneNumber(UpdatePhoneNumberInput input);
        Task<object> DeleteAccount(DeleteAccountInputForLoyLinkId input);

        Task<CreateMemberOfCustomTenantOutput> CreateMemberOfCustomTenant(
            CreateMemberOfCustomTenantInput input, string partner);
        
        // Get Member thuộc về 1 tenant tương ứng với partner
        Task<LoyaltyResponse<SecondaryCustomerDto>> GetProfileByCode(string code, string partner);

        Task<LoyaltyResponse<CheckRefCodeOutput>> CheckRefCode(CheckRefCodeInput input);

        Task<LoyaltyResponse<CheckMemberHasInvitedOutput>> CheckMemberHasInvited(CheckMemberHasInvitedInput input);

        Task<LoyaltyResponse<VerifyRefferralCode_v2Output>> VerifyReferralCode_v2(VerifyReferralCode_v2DTO input);

        Task<LoyaltyResponse<GetInforCampaignRefOutput>> GetInforCampaignRef();

        Task<LoyaltyResponse<RegisterCampaignFriendReferralOutput>> RegisterCampaignFriendReferral(RegisterCampaignFriendReferralInput input);

        Task<LoyaltyResponse<CheckMemberRegisterCampaignOutput>> CheckMemberRegisterCampaign(CheckMemberRegisterCampaignInput input);

        Task<LoyaltyResponse<CommonLoyDeleteAccountOutput>> DeleteMemberLoyaltyInCommonLoyalty(
            CommonLoyDeleteAccountInput input, int tID);

        Task<LoyaltyResponse<CreateMemberActionFirstOutput>> CreateMemberActionFirst(CreateMemberActionFirstInput input);
    }
}
