﻿using AKC.MobileAPI.DTO.Loyalty.Rewards;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/Rewards")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyRewardsController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyRewardsService _rewardsService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        public LoyaltyRewardsController(
            ILogger<LoyaltyRewardsController> logger,
            ILoyaltyRewardsService rewardsService,
            IExceptionReponseService exceptionReponseService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _rewardsService = rewardsService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
        }

        [HttpPost]
        [Route("InputAction")]
        public async Task<ActionResult<LoyaltyInputActionDtoOutput>> InputAction(LoyaltyInputActionInput input)
        {
            _logger.LogInformation("InputAction- " + JsonConvert.SerializeObject(input));
            try
            {
                if (input.Actions != null && input.Actions.Count > 0)
                {
                    var authorization = Request.Headers["Authorization"].ToString();
                    var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.Actions[0].MemberCode);
                    if (checkAuthen != null)
                    {
                        return StatusCode(401, checkAuthen);
                    }
                }
                var result = await _rewardsService.InputAction(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "InputAction Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}
