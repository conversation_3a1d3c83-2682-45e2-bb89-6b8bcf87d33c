﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Quest
{
    public class PerformActionOutput
    {
        public PerformActionResult result { get; set; }
        public object targetUrl { get; set; }
        public bool success { get; set; }
        public object error { get; set; }
        public bool unAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class PerformActionResult
    {
        public GmQuestEnrolmentV2Dto GmQuestEnrolmentV2 { get; set; }
        public List<GiftPerformActionResult> GiftPerformActionResults { get; set; }
    }

    public class GiftPerformActionResult
    {
        public string GiftCode { get; set; }
        public string GiftName { get; set; }
    }
}
