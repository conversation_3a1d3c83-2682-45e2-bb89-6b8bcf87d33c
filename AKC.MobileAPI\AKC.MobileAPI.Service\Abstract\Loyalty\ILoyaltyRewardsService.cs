﻿using AKC.MobileAPI.DTO.Loyalty.Rewards;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyRewardsService
    {
        Task<LoyaltyInputActionDtoOutput> InputAction(LoyaltyInputActionInput input);
        Task<LoyaltyInputActionDtoOutput> InputAction(LoyaltyInputActionInput input, string m);
    }
}
