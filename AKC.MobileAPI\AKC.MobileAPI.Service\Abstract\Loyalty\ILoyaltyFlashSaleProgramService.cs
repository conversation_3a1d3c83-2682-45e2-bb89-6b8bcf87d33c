﻿using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.DTO.Loyalty.Member;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyFlashSaleProgramService
    {
        Task<LoyaltyGetAllForFlashSaleProgramOutPut> GetAll(MemberInfoRequest input);
        Task<LoyaltyGiftCategoryGetAllOutput> GetAllGiftCategoryForFlashSaleProgram(LoyaltyGetAllGiftCategoryForFlashSaleProgramInput input);
    }
}
