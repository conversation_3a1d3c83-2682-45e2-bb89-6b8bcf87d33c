﻿using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.LoyaltyVpbank.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.LoyaltyVpbank;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using AKC.MobileAPI.Service.Abstract.Reward;


namespace AKC.MobileAPI.Controllers.LoyaltyVpbank
{
    [Route("api/Member")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    //[AllowAnonymous]
    public class LoyaltyMemberController : ControllerBase
    {
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILogger _logger;
        private readonly ILoyaltyMemberService _loyaltyMemberService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly ILoyaltyGiftTransactionsService _giftTransactionsService;
        private readonly IRewardMemberService _rewardMemberService;
        public LoyaltyMemberController(
            IExceptionReponseService exceptionReponseService,
            ILogger<LoyaltyMemberController> logger,
            ILoyaltyMemberService loyaltyMemberService,
            ICommonHelperService commonHelperService,
            IRewardMemberService r,
            ILoyaltyGiftTransactionsService giftTransactionsService)
        {
            _exceptionReponseService = exceptionReponseService;
            _logger = logger;
            _loyaltyMemberService = loyaltyMemberService;
            _commonHelperService = commonHelperService;
            _giftTransactionsService = giftTransactionsService;
            _rewardMemberService = r;
        }

        [HttpGet]
        [Route("GetMemberVpbankInfor")]
        public async Task<ActionResult<GetMemberVpbankInforOutput>> GetMemberVpbankInfor([FromQuery] GetMemberVpbankInforInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                _logger.LogInformation("GetMemberVpbankInfor >> getting memberid of membercode " + input.MemberCode);
                var memberIdFromMemCode = await _rewardMemberService.GetMemberIdByMemberCode(input.MemberCode, authorization);
                _logger.LogInformation("GetMemberVpbankInfor >> getting memberid of membercode " + input.MemberCode + " - Result: " + memberIdFromMemCode);
                if (memberIdFromMemCode != input.MemberId)
                {
                    throw new Exception("MemberIdAndMemCodeNotMatch");
                }
                var result = await _loyaltyMemberService.GetMemberVpbankInfor(input);

                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetMemberVpbankInfor Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetVPBankCustomerInfo")]
        public async Task<ActionResult<VPBankCustomerInfoOutput>> GetVPBankCustomerInfo([FromQuery] VPBankCustomerInfoInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                await _giftTransactionsService.UpdateRecipientLegalId(new GiftTransactionUpdateLegalIdInput
                { Code = input.Code, RecipientLegalId = input.LegalId, RecipientAddress = input.RecipientAddress, RecipientPhone = input.RecipientPhone });

                var result = await _loyaltyMemberService.GetVPBankCustomerInfo(input);

                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetVPBankCustomerInfo Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}
