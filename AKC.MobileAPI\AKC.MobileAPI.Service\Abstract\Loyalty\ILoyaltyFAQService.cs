﻿using AKC.MobileAPI.DTO.Loyalty.FAQ;
using AKC.MobileAPI.DTO.User;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyFAQService
    {
        Task<GetAllFAQOutputDto> GetAll(GetAllFAQInputDto input);

        Task<GetAllFaqAdvanceOutputWrapperDTO> GetAllAdvanceByCode(GetAllAdvancebyCode input);
    }


}
