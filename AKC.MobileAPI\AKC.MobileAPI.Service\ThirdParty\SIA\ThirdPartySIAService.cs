﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.AuditLog;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Abstract.ThirdParty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace AKC.MobileAPI.Service.ThirdParty
{
    public class ThirdPartySIAService : IThirdPartySIAService
    {
        private readonly IDistributedCache _cache;
        private readonly ILogger<ThirdPartySIAService> _logger;
        private readonly HttpClient _httpClient = new HttpClient();
        private SingaporeAirlinesCredentialsDto configDto = new SingaporeAirlinesCredentialsDto();
        private ILoyaltyAuditLogService _logService;
        private static readonly SemaphoreSlim _loggingSemaphore = new SemaphoreSlim(5);
        private readonly int tenantId;
        public ThirdPartySIAService(
            IConfiguration configuration,
            ILogger<ThirdPartySIAService> lg,
            ILoyaltyAuditLogService logSv,
            IDistributedCache cache)
        {
            _cache = cache;
            _logger = lg;
            configDto.APIUrlCreditNonAirMiles = configuration.GetSection("SingaporeAirlines:APIUrlCreditNonAirMiles").Value;
            configDto.ParticipantCode = configuration.GetSection("SingaporeAirlines:ParticipantCode").Value;
            configDto.ChannelId = configuration.GetSection("SingaporeAirlines:ChannelId").Value;
            configDto.APIKey = configuration.GetSection("SingaporeAirlines:APIKey").Value;
            configDto.ClientSecret = configuration.GetSection("SingaporeAirlines:ClientSecret").Value;
            _logService = logSv;
            tenantId = Convert.ToInt32(configuration.GetSection("Loyalty:TenantId").Value);
        }
        
       

        public async Task<PartnerExchangeOutput> CreditNonAirMiles(string memberCode, CreditNonAirMilesCallerInput input)
        {
            _logger.LogInformation(" >> ThirdPartySIAService >> CreditNonAirMiles >> Input#" + JsonConvert.SerializeObject(input));
            var siaInput = new CreditNonAirMilesSIAInput
            {
                PartnerProfile = new CreditNonAirMilesSIAInput.XPartnerProfile()
                {
                    LoyaltyProgramCode = "KF", LoyaltyParticipantCode = "SQ", LoyaltyMembershipID = input.SIAMemberCode
                },
                TransactionCode = "AC",
                FamilyName = input.LastName,
                GivenName = input.FirstName,
                Miles = input.Miles,
                PartnerTransactionCode = input.LynkiDTransactionCode,
                AwardDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                ParticipantCode = configDto.ParticipantCode
            };
            CreditNonAirMilesSIAOutput siaRes = null;
            if (!string.IsNullOrWhiteSpace(input.promotionCode))
            {
                siaInput.promotionCode = input.promotionCode;
                siaInput.promotionMileAwarded = input.promotionMileAwarded;
                siaInput.promotionAwardDate = input.promotionAwardDate;
            }

            try
            {
                siaRes = await PostAsync<CreditNonAirMilesSIAOutput>(configDto.APIUrlCreditNonAirMiles, siaInput);
                return BuildResponse(siaRes);
            }
            catch (TimeoutException e)
            {
                _logger.LogError(" >> ThirdPartySIAService >> CreditNonAirMiles >> Timeout exception: " + e.Message +
                                 " - " + e.StackTrace);
                return new PartnerExchangeOutput()
                {
                    Message = "Request Timeout", Code = AirlineIntegrationErrorCodes.Timeout, TransactionId = null
                };
            }
            catch (Exception e)
            {
                _logger.LogError(" >> ThirdPartySIAService >> CreditNonAirMiles >> Error happened: " + e.Message +
                                 " - " + e.StackTrace);
                return new PartnerExchangeOutput()
                {
                    Message = "Có lỗi xảy ra. Vui lòng thử lại sau", Code = AirlineIntegrationErrorCodes.GeneralError, TransactionId = null
                };
            }
            finally
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _loggingSemaphore.WaitAsync();
                        try
                        {
                            var operationLog = new OperationLogDto
                            {
                                TenantId = tenantId,
                                MemberId = memberCode,
                                Code = "ExchangeMile" + DateTime.Now.Ticks.ToString() + new Random().Next(4),
                                ReferenceKey = input.LynkiDTransactionCode,
                                PartnerCode = "SIA",
                                DestIP = "",
                                StartDate = DateTime.UtcNow,
                                RequestMsg = JsonConvert.SerializeObject(siaInput),
                                ServiceName = "ExchangeMiles",
                                EndDate = DateTime.UtcNow,
                                Status = (siaRes == null || siaRes.Code != "00")? true : false,
                                ErrorCode = siaRes?.Code ?? "00",
                                ResponseMsg = siaRes == null? "N-A" : JsonConvert.SerializeObject(siaRes)
                            };
                            await _logService.CreateOperationLog(new CreateOperationLogDto()
                            {
                                OperationLogs = new List<OperationLogDto>(){ operationLog }
                            });
                        }
                        finally
                        {
                            _loggingSemaphore.Release();
                        }
                    }
                    catch (Exception logEx)
                    {
                        _logger.LogError(" >> Background Logging Failed: " + logEx.Message + " - " + logEx.StackTrace);
                    }
                });
            }
        }

        private PartnerExchangeOutput BuildResponse(CreditNonAirMilesSIAOutput input)
        {
            // Dưa vào input xem thành công ko thì trả dữ liệu tương ứng. Dựa vào bảng mã lỗi SIA gửi mình.
            if (input == null)
            {
                return new PartnerExchangeOutput()
                {
                    Code = AirlineIntegrationErrorCodes.GeneralError, Message = "Có lỗi xảy ra. Vui lòng thử lại sau", TransactionId = ""
                };;
            }
            var response = new PartnerExchangeOutput
            {
                Code = "00",
                Message = "Thành công",
                TransactionId = ""
            };
            if (input.Status == "SUCCESS")
            {
                response.TransactionId = input.Response.TransactionID;
                _logger.LogInformation("XAirlinesAdapter - CreditNonAirMiles - Success: {Input}", JsonConvert.SerializeObject(input));
                return response;
            }

            var errorMappings = new Dictionary<List<string>, (string Code, string Message)>
            {
                { new List<string> { "N9500", "N9501", "N9545", "N9504", "N9506", "N9505", "N9508", "N9510", "N9548" }, (AirlineIntegrationErrorCodes.InitConnectionError, "InitConnectionError") },
                { new List<string> { "N0585", "N0696", "S1005" }, (AirlineIntegrationErrorCodes.PartnerTemporarilyNotAcceptMilesExchange, "Không thể thực hiện đổi dặm ở bên đối tác") },
                { new List<string> { "N3007", "N0015", "N0046", "W0206", "N0907", "N0252", "N3064", "N0399", "N0499", "N0733" }, (AirlineIntegrationErrorCodes.AirlineMemberHasInvalidStatus, "Trạng thái không hợp lệ của mã hội viên bên phía đối tác") },
                { new List<string> { "N0855", "W0177" }, (AirlineIntegrationErrorCodes.AirlineMemberCodeAndNameMismatch, "Mã và tên của hội viên không khớp bên đối tác") },
                { new List<string> { "N0140" }, (AirlineIntegrationErrorCodes.AirlineMemberCodeNotFound, "Mã hội viên không tìm thấy bên đối tác") },
                { new List<string> { "N2091" }, (AirlineIntegrationErrorCodes.MilesAndAmountNotValid, "Thông tin số dặm không hợp lệ") },
            };

            foreach (var mapping in errorMappings)
            {
                if (mapping.Key.Contains(input.Response.ErrorCode))
                {
                    response.Code = mapping.Value.Code;
                    response.Message = mapping.Value.Message;
                    _logger.LogError("XAirlinesAdapter - CreditNonAirMiles - Error: {Input}", JsonConvert.SerializeObject(input));
                    return response;
                }
            }

            response.Code = AirlineIntegrationErrorCodes.GeneralError;
            response.Message = "Error Happened";
            _logger.LogError("XAirlinesAdapter - CreditNonAirMiles - Unknown Issue: {Input}", JsonConvert.SerializeObject(input));
            return response;
        }
        public async Task<T> PostAsync<T>(string endpoint, object body = null, HttpContext request = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            req.Headers.Add("API_KEY", configDto.APIKey);
            req.Headers.Add("CHANNEL_ID", configDto.ChannelId);
            req.Headers.Add("x-signature", GenerateSignatureForSIA(configDto.APIKey, configDto.ClientSecret));
            req.RequestUri = new Uri($"{endpoint}");
            _logger.LogInformation(" >> CURL >> " + ToCurlCommand(req));
            var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
            try
            {
                var response = await _httpClient.SendAsync(req, cts.Token);
                if (!response.IsSuccessStatusCode)
                {
                    var rawData = await response.Content.ReadAsStringAsync();
                    throw new HttpResponseException($"HTTP Error: {response.StatusCode} - {rawData}");
                }

                var rawResponse = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<T>(rawResponse);
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new TimeoutException("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: " + ex.Message + " - " + ex.StackTrace);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        // Hàm clone 1 httpRequest để có thể retry lại
        protected HttpRequestMessage CloneHttpRequest(HttpRequestMessage req)
        {
            HttpRequestMessage clone = new HttpRequestMessage(req.Method, req.RequestUri);

            clone.Content = req.Content;
            clone.Version = req.Version;

            foreach (KeyValuePair<string, object> prop in req.Properties)
            {
                clone.Properties.Add(prop);
            }

            foreach (KeyValuePair<string, IEnumerable<string>> header in req.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            return clone;
        }
        protected string GenerateSignatureForSIA(string apiKey, string clientSecret)
        {
            // Step 1: Get current Unix timestamp (seconds since 1970)
            long timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            // Step 2: Concatenate the parameters and the timestamp
            string concatenatedString = apiKey + clientSecret + timestamp;

            // Step 3: Create SHA-256 hash from the concatenated string
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(concatenatedString));

                // Step 4: Convert hash bytes to hexadecimal string
                StringBuilder sb = new StringBuilder();
                foreach (byte b in hashBytes)
                {
                    sb.Append(b.ToString("x2"));  // Convert byte to its hexadecimal representation
                }

                return sb.ToString(); // Return the hashed signature
            }
        }
        private string ToCurlCommand(HttpRequestMessage request)
        {
            var curlCommand = new StringBuilder("curl");

            // Add method and URL
            curlCommand.Append($" -X {request.Method} \"{request.RequestUri}\"");

            // Helper method to mask sensitive headers
            string MaskSensitiveHeader(string key, string value)
            {
                if (key.Equals("CHANNEL_ID", StringComparison.OrdinalIgnoreCase) ||
                    key.Equals("API_KEY", StringComparison.OrdinalIgnoreCase))
                {
                    if (value.Length <= 3)
                    {
                        return "***"; // Mask entirely if the value has 3 or fewer characters
                    }
                    else
                    {
                        return $"********{value.Substring(value.Length - 4)}";
                    }
                }
                return value;
            }

            // Add headers
            foreach (var header in request.Headers)
            {
                foreach (var value in header.Value)
                {
                    string maskedValue = MaskSensitiveHeader(header.Key, value);
                    curlCommand.Append($" -H \"{header.Key}: {maskedValue}\"");
                }
            }

            // Handle content headers
            if (request.Content != null)
            {
                foreach (var header in request.Content.Headers)
                {
                    foreach (var value in header.Value)
                    {
                        string maskedValue = MaskSensitiveHeader(header.Key, value);
                        curlCommand.Append($" -H \"{header.Key}: {maskedValue}\"");
                    }
                }
            }

            // Add content if it exists
            if (request.Content != null)
            {
                string content;
                if (request.Content is StringContent stringContent)
                {
                    content = stringContent.ReadAsStringAsync().Result;
                }
                else
                {
                    content = request.Content.ReadAsStringAsync().Result;
                }
                curlCommand.Append($" -d \"{content}\"");
            }

            return curlCommand.ToString();
        }
        private RewardDataExceptionResponse GetErrorValidation(string errorCode, string errorMessage)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = errorMessage
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }
    }

    public class SingaporeAirlinesCredentialsDto
    {
        public string APIUrlCreditNonAirMiles { get; set; }
        public string ParticipantCode { get; set; }
        public string ChannelId { get; set; }
        public string APIKey { get; set; }
        public string ClientSecret { get; set; }
    }
    public class HttpResponseException : Exception
    {
        public HttpResponseException(string message) : base(message) { }
    }
}