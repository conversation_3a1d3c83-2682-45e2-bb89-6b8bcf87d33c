INFO  2024-06-11 17:34:57,684 [58   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"AuthenWithConnectedPhone","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/authen-with-connected-phone","QueryString":"{}","Body":"{\"connectedPhone\":\"+84559373737\",\"originalPhone\":\"+84333123323\"}","TrueClientIp":"","XForwardedFor":""}
ERROR 2024-06-11 17:35:21,256 [57   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:35:21,262 [57   ] onTokenJob - RenewAccessToken Job run successfully
INFO  2024-06-11 17:36:10,336 [64   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"AuthenWithConnectedPhone","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/authen-with-connected-phone","QueryString":"{}","Body":"{\r\n    \"connectedPhone\": \"+84559373737\",\r\n    \"originalPhone\": \"+84333123323\"\r\n}","TrueClientIp":"","XForwardedFor":""}
ERROR 2024-06-11 17:36:29,600 [65   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:36:29,618 [65   ] onTokenJob - RenewAccessToken Job run successfully
INFO  2024-06-11 17:37:05,502 [64   ] Controller - >> Partner#218 AuthenWithConnectedPhone Input - {"OriginalPhone":"+84333123323","ConnectedPhone":"+84559373737"}
INFO  2024-06-11 17:37:05,506 [64   ] Controller - >> Partner#218 AuthenWithConnectedPhone Request - {"MerchantId":218,"PhoneNumber":"+84333123323","Cif":"8991829"}
ERROR 2024-06-11 17:37:05,795 [79   ] Controller - >> Partner#218 AuthenWithConnectedPhone Error - {"StackTrace":"   at AKC.MobileAPI.Service.Reward.RewardBaseService.PostRewardAsync[T](String apiURL, Object body, String rewardType) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardBaseService.cs:line 285\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.CheckMemberAndConnection(CheckMemberAndConnectionRewardIntput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 1924\r\n   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.AuthenWithConnectedPhone(AuthenWithConnectedPhoneInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\SDKV1\\SDKV1Controller.cs:line 484","Message":"The requested name is valid, but no data of the requested type was found.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"The requested name is valid, but no data of the requested type was found.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":11004},"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-2147467259}
System.Net.Http.HttpRequestException: The requested name is valid, but no data of the requested type was found.
 ---> System.Net.Sockets.SocketException (11004): The requested name is valid, but no data of the requested type was found.
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at AKC.MobileAPI.Service.Reward.RewardBaseService.PostRewardAsync[T](String apiURL, Object body, String rewardType) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardBaseService.cs:line 285
   at AKC.MobileAPI.Service.Reward.RewardMemberService.CheckMemberAndConnection(CheckMemberAndConnectionRewardIntput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardMemberService.cs:line 1924
   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.AuthenWithConnectedPhone(AuthenWithConnectedPhoneInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\SDKV1\SDKV1Controller.cs:line 484
INFO  2024-06-11 17:37:19,622 [84   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"AuthenWithConnectedPhone","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/authen-with-connected-phone","QueryString":"{}","Body":"{\r\n    \"connectedPhone\": \"+84559373737\",\r\n    \"originalPhone\": \"+84333123323\"\r\n}","TrueClientIp":"","XForwardedFor":""}
ERROR 2024-06-11 17:37:23,271 [81   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:37:23,281 [81   ] onTokenJob - RenewAccessToken Job run successfully
INFO  2024-06-11 17:38:15,832 [84   ] Controller - >> Partner#218 AuthenWithConnectedPhone Input - {"OriginalPhone":"+84333123323","ConnectedPhone":"+84559373737"}
INFO  2024-06-11 17:38:15,835 [84   ] Controller - >> Partner#218 AuthenWithConnectedPhone Request - {"MerchantId":218,"PhoneNumber":"+84333123323","Cif":"8991829"}
ERROR 2024-06-11 17:38:15,956 [104  ] Controller - >> Partner#218 AuthenWithConnectedPhone Error - {"StackTrace":"   at AKC.MobileAPI.Service.Reward.RewardBaseService.PostRewardAsync[T](String apiURL, Object body, String rewardType) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardBaseService.cs:line 285\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.CheckMemberAndConnection(CheckMemberAndConnectionRewardIntput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 1924\r\n   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.AuthenWithConnectedPhone(AuthenWithConnectedPhoneInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\SDKV1\\SDKV1Controller.cs:line 484","Message":"The requested name is valid, but no data of the requested type was found.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"The requested name is valid, but no data of the requested type was found.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":11004},"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-2147467259}
System.Net.Http.HttpRequestException: The requested name is valid, but no data of the requested type was found.
 ---> System.Net.Sockets.SocketException (11004): The requested name is valid, but no data of the requested type was found.
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at AKC.MobileAPI.Service.Reward.RewardBaseService.PostRewardAsync[T](String apiURL, Object body, String rewardType) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardBaseService.cs:line 285
   at AKC.MobileAPI.Service.Reward.RewardMemberService.CheckMemberAndConnection(CheckMemberAndConnectionRewardIntput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardMemberService.cs:line 1924
   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.AuthenWithConnectedPhone(AuthenWithConnectedPhoneInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\SDKV1\SDKV1Controller.cs:line 484
ERROR 2024-06-11 17:38:22,304 [111  ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:38:22,307 [111  ] onTokenJob - RenewAccessToken Job run successfully
INFO  2024-06-11 17:38:28,742 [96   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"AuthenWithConnectedPhone","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/authen-with-connected-phone","QueryString":"{}","Body":"{\r\n    \"connectedPhone\": \"+84559373737\",\r\n    \"originalPhone\": \"+84333123323\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-11 17:38:34,999 [96   ] Controller - >> Partner#218 AuthenWithConnectedPhone Input - {"OriginalPhone":"+84333123323","ConnectedPhone":"+84559373737"}
INFO  2024-06-11 17:38:53,548 [96   ] Controller - >> Partner#218 AuthenWithConnectedPhone Request - {"MerchantId":218,"PhoneNumber":"+84333123323","Cif":"8991829"}
ERROR 2024-06-11 17:38:57,707 [98   ] Controller - >> Partner#218 AuthenWithConnectedPhone Error - {"StackTrace":"   at AKC.MobileAPI.Service.Reward.RewardBaseService.PostRewardAsync[T](String apiURL, Object body, String rewardType) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardBaseService.cs:line 285\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.CheckMemberAndConnection(CheckMemberAndConnectionRewardIntput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 1924\r\n   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.AuthenWithConnectedPhone(AuthenWithConnectedPhoneInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\SDKV1\\SDKV1Controller.cs:line 484","Message":"The requested name is valid, but no data of the requested type was found.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"The requested name is valid, but no data of the requested type was found.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":11004},"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-2147467259}
System.Net.Http.HttpRequestException: The requested name is valid, but no data of the requested type was found.
 ---> System.Net.Sockets.SocketException (11004): The requested name is valid, but no data of the requested type was found.
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at AKC.MobileAPI.Service.Reward.RewardBaseService.PostRewardAsync[T](String apiURL, Object body, String rewardType) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardBaseService.cs:line 285
   at AKC.MobileAPI.Service.Reward.RewardMemberService.CheckMemberAndConnection(CheckMemberAndConnectionRewardIntput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardMemberService.cs:line 1924
   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.AuthenWithConnectedPhone(AuthenWithConnectedPhoneInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\SDKV1\SDKV1Controller.cs:line 484
INFO  2024-06-11 17:39:07,123 [98   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"AuthenWithConnectedPhone","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/authen-with-connected-phone","QueryString":"{}","Body":"{\r\n    \"connectedPhone\": \"+84559373737\",\r\n    \"originalPhone\": \"+84333123323\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-11 17:39:09,037 [98   ] Controller - >> Partner#218 AuthenWithConnectedPhone Input - {"OriginalPhone":"+84333123323","ConnectedPhone":"+84559373737"}
INFO  2024-06-11 17:39:10,914 [98   ] Controller - >> Partner#218 AuthenWithConnectedPhone Request - {"MerchantId":218,"PhoneNumber":"+84333123323","Cif":"8991829"}
ERROR 2024-06-11 17:39:42,228 [106  ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:39:42,253 [106  ] onTokenJob - RenewAccessToken Job run successfully
ERROR 2024-06-11 17:39:57,792 [50   ] Controller - >> Partner#218 AuthenWithConnectedPhone Error - {"StackTrace":"   at AKC.MobileAPI.Service.Reward.RewardBaseService.PostRewardAsync[T](String apiURL, Object body, String rewardType) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardBaseService.cs:line 285\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.CheckMemberAndConnection(CheckMemberAndConnectionRewardIntput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 1924\r\n   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.AuthenWithConnectedPhone(AuthenWithConnectedPhoneInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\SDKV1\\SDKV1Controller.cs:line 484","Message":"The requested name is valid, but no data of the requested type was found.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"The requested name is valid, but no data of the requested type was found.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":11004},"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-2147467259}
System.Net.Http.HttpRequestException: The requested name is valid, but no data of the requested type was found.
 ---> System.Net.Sockets.SocketException (11004): The requested name is valid, but no data of the requested type was found.
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at AKC.MobileAPI.Service.Reward.RewardBaseService.PostRewardAsync[T](String apiURL, Object body, String rewardType) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardBaseService.cs:line 285
   at AKC.MobileAPI.Service.Reward.RewardMemberService.CheckMemberAndConnection(CheckMemberAndConnectionRewardIntput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardMemberService.cs:line 1924
   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.AuthenWithConnectedPhone(AuthenWithConnectedPhoneInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\SDKV1\SDKV1Controller.cs:line 484
ERROR 2024-06-11 17:40:00,042 [59   ] onTokenJob - Job: RefreshLoyaltyNotificationToken error{"ClassName":"StackExchange.Redis.RedisConnectionException","Message":"No connection is available to service this operation: EVAL; It was not possible to connect to the redis server(s). To create a disconnected multiplexer, disable AbortOnConnectFail. ConnectTimeout; IOCP: (Busy=0,Free=2000,Min=1000,Max=2000), WORKER: (Busy=3,Free=32764,Min=1000,Max=32767), Local-CPU: n/a","Data":{"redis-command":"EVAL","request-sent-status":1},"InnerException":{"ClassName":"StackExchange.Redis.RedisConnectionException","Message":"It was not possible to connect to the redis server(s). To create a disconnected multiplexer, disable AbortOnConnectFail. ConnectTimeout","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":null,"RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2146233088,"Source":null,"WatsonBuckets":null,"failureType":9,"commandStatus":0},"HelpURL":null,"StackTraceString":"   at StackExchange.Redis.ConnectionMultiplexer.ExecuteSyncImpl[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\ConnectionMultiplexer.cs:line 2215\r\n   at StackExchange.Redis.RedisBase.ExecuteSync[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\RedisBase.cs:line 54\r\n   at StackExchange.Redis.RedisDatabase.ScriptEvaluate(String script, RedisKey[] keys, RedisValue[] values, CommandFlags flags) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\RedisDatabase.cs:line 1134\r\n   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisExtensions.HashMemberGet(IDatabase cache, String key, String[] members)\r\n   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.GetAndRefresh(String key, Boolean getData)\r\n   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.Get(String key)\r\n   at Microsoft.Extensions.Caching.Distributed.DistributedCacheExtensions.GetString(IDistributedCache cache, String key)\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyNotificationHelper.RenewAccessTokenNotificationCacheValue(IDistributedCache cache, TimeSpan delay, Boolean isForceRenew) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyNotificationHelper.cs:line 71\r\n   at AKC.MobileAPI.Service.CronServices.RefreshLoyaltyNotificationTokenJob.DoWork(CancellationToken cancellationToken) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\CronServices\\Jobs\\RefreshLoyaltyNotificationTokenJob.cs:line 45","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2146233088,"Source":"StackExchange.Redis","WatsonBuckets":null,"failureType":1,"commandStatus":1}
ERROR 2024-06-11 17:40:21,115 [50   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:40:21,117 [50   ] onTokenJob - RenewAccessToken Job run successfully
INFO  2024-06-11 17:40:58,590 [50   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"AuthenWithConnectedPhone","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/authen-with-connected-phone","QueryString":"{}","Body":"{\r\n    \"connectedPhone\": \"+84559373737\",\r\n    \"originalPhone\": \"+84333123323\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-11 17:41:03,115 [50   ] Controller - >> Partner#218 AuthenWithConnectedPhone Input - {"OriginalPhone":"+84333123323","ConnectedPhone":"+84559373737"}
INFO  2024-06-11 17:41:03,123 [50   ] Controller - >> Partner#218 AuthenWithConnectedPhone Request - {"MerchantId":218,"PhoneNumber":"+84333123323","Cif":"8991829"}
ERROR 2024-06-11 17:41:03,277 [28   ] Controller - >> Partner#218 AuthenWithConnectedPhone Error - {"StackTrace":"   at AKC.MobileAPI.Service.Reward.RewardBaseService.PostRewardAsync[T](String apiURL, Object body, String rewardType) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardBaseService.cs:line 285\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.CheckMemberAndConnection(CheckMemberAndConnectionRewardIntput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 1924\r\n   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.AuthenWithConnectedPhone(AuthenWithConnectedPhoneInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\SDKV1\\SDKV1Controller.cs:line 484","Message":"The requested name is valid, but no data of the requested type was found.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"The requested name is valid, but no data of the requested type was found.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":11004},"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-2147467259}
System.Net.Http.HttpRequestException: The requested name is valid, but no data of the requested type was found.
 ---> System.Net.Sockets.SocketException (11004): The requested name is valid, but no data of the requested type was found.
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at AKC.MobileAPI.Service.Reward.RewardBaseService.PostRewardAsync[T](String apiURL, Object body, String rewardType) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardBaseService.cs:line 285
   at AKC.MobileAPI.Service.Reward.RewardMemberService.CheckMemberAndConnection(CheckMemberAndConnectionRewardIntput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardMemberService.cs:line 1924
   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.AuthenWithConnectedPhone(AuthenWithConnectedPhoneInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\SDKV1\SDKV1Controller.cs:line 484
INFO  2024-06-11 17:41:06,114 [28   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"AuthenWithConnectedPhone","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/authen-with-connected-phone","QueryString":"{}","Body":"{\r\n    \"connectedPhone\": \"+84559373737\",\r\n    \"originalPhone\": \"+84333123323\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-11 17:41:10,860 [28   ] Controller - >> Partner#218 AuthenWithConnectedPhone Input - {"OriginalPhone":"+84333123323","ConnectedPhone":"+84559373737"}
INFO  2024-06-11 17:41:16,065 [28   ] Controller - >> Partner#218 AuthenWithConnectedPhone Request - {"MerchantId":218,"PhoneNumber":"+84333123323","Cif":"8991829"}
ERROR 2024-06-11 17:41:29,041 [51   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:41:29,046 [51   ] onTokenJob - RenewAccessToken Job run successfully
ERROR 2024-06-11 17:43:26,538 [37   ] Controller - >> Partner#218 AuthenWithConnectedPhone Error - {"StackTrace":"   at AKC.MobileAPI.Service.Reward.RewardBaseService.PostRewardAsync[T](String apiURL, Object body, String rewardType) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardBaseService.cs:line 285\r\n   at AKC.MobileAPI.Service.Reward.RewardMemberService.CheckMemberAndConnection(CheckMemberAndConnectionRewardIntput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Reward\\RewardMemberService.cs:line 1924\r\n   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.AuthenWithConnectedPhone(AuthenWithConnectedPhoneInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\SDKV1\\SDKV1Controller.cs:line 484","Message":"The requested name is valid, but no data of the requested type was found.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"The requested name is valid, but no data of the requested type was found.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":11004},"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-2147467259}
System.Net.Http.HttpRequestException: The requested name is valid, but no data of the requested type was found.
 ---> System.Net.Sockets.SocketException (11004): The requested name is valid, but no data of the requested type was found.
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at AKC.MobileAPI.Service.Reward.RewardBaseService.PostRewardAsync[T](String apiURL, Object body, String rewardType) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardBaseService.cs:line 285
   at AKC.MobileAPI.Service.Reward.RewardMemberService.CheckMemberAndConnection(CheckMemberAndConnectionRewardIntput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Reward\RewardMemberService.cs:line 1924
   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.AuthenWithConnectedPhone(AuthenWithConnectedPhoneInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\SDKV1\SDKV1Controller.cs:line 484
INFO  2024-06-11 17:43:30,584 [141  ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"AuthenWithConnectedPhone","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/authen-with-connected-phone","QueryString":"{}","Body":"{\r\n    \"connectedPhone\": \"+84559373737\",\r\n    \"originalPhone\": \"+84333123323\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-11 17:43:38,060 [141  ] Controller - >> Partner#218 AuthenWithConnectedPhone Input - {"OriginalPhone":"+84333123323","ConnectedPhone":"+84559373737"}
INFO  2024-06-11 17:43:38,066 [141  ] Controller - >> Partner#218 AuthenWithConnectedPhone Request - {"MerchantId":218,"PhoneNumber":"+84333123323","Cif":"8991829"}
INFO  2024-06-11 17:47:19,502 [38   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"AuthenWithConnectedPhone","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/authen-with-connected-phone","QueryString":"{}","Body":"{\r\n    \"connectedPhone\": \"+84559373737\",\r\n    \"originalPhone\": \"+84333123323\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-11 17:47:29,094 [38   ] Controller - >> Partner#218 AuthenWithConnectedPhone Input - {"OriginalPhone":"+84333123323","ConnectedPhone":"+84559373737"}
INFO  2024-06-11 17:47:29,106 [38   ] Controller - >> Partner#218 AuthenWithConnectedPhone Request - {"MerchantId":218,"PhoneNumber":"+84333123323","Cif":"8991829"}
ERROR 2024-06-11 17:47:30,967 [31   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:47:30,970 [31   ] onTokenJob - RenewAccessToken Job run successfully
INFO  2024-06-11 17:47:45,239 [39   ] Controller - >> Partner#218 AuthenWithConnectedPhone Result - {"PhoneNumber":null,"Cif":null,"MerchantId":0,"IsExisting":true,"BasicInfo":{"Name":"Nguyễn Hà Thanh","MemberCode":"SDK218-6b4b88a8-649c-4d14-b12b-182ae6f09894","Balance":0.0,"AccessToken":null,"RefreshToken":null},"ConnectionInfo":{"IsExisting":true,"ConnectedToPhone":"+84559373737","ConnectedToMemberCode":"65xYoa88s0hs5soXmVLcdwDQCGm2"}}
ERROR 2024-06-11 17:48:21,248 [54   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:48:21,250 [54   ] onTokenJob - RenewAccessToken Job run successfully
ERROR 2024-06-11 17:49:21,261 [58   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:49:21,263 [58   ] onTokenJob - RenewAccessToken Job run successfully
ERROR 2024-06-11 17:50:00,039 [54   ] onTokenJob - Job: RefreshLoyaltyNotificationToken error{"ClassName":"StackExchange.Redis.RedisConnectionException","Message":"No connection is available to service this operation: EVAL; It was not possible to connect to the redis server(s). To create a disconnected multiplexer, disable AbortOnConnectFail. ConnectTimeout; IOCP: (Busy=0,Free=2000,Min=1000,Max=2000), WORKER: (Busy=1,Free=32766,Min=1000,Max=32767), Local-CPU: n/a","Data":{"redis-command":"EVAL","request-sent-status":1},"InnerException":{"ClassName":"StackExchange.Redis.RedisConnectionException","Message":"It was not possible to connect to the redis server(s). To create a disconnected multiplexer, disable AbortOnConnectFail. ConnectTimeout","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":null,"RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2146233088,"Source":null,"WatsonBuckets":null,"failureType":9,"commandStatus":0},"HelpURL":null,"StackTraceString":"   at StackExchange.Redis.ConnectionMultiplexer.ExecuteSyncImpl[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\ConnectionMultiplexer.cs:line 2215\r\n   at StackExchange.Redis.RedisBase.ExecuteSync[T](Message message, ResultProcessor`1 processor, ServerEndPoint server) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\RedisBase.cs:line 54\r\n   at StackExchange.Redis.RedisDatabase.ScriptEvaluate(String script, RedisKey[] keys, RedisValue[] values, CommandFlags flags) in C:\\projects\\stackexchange-redis\\src\\StackExchange.Redis\\RedisDatabase.cs:line 1134\r\n   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisExtensions.HashMemberGet(IDatabase cache, String key, String[] members)\r\n   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.GetAndRefresh(String key, Boolean getData)\r\n   at Microsoft.Extensions.Caching.StackExchangeRedis.RedisCache.Get(String key)\r\n   at Microsoft.Extensions.Caching.Distributed.DistributedCacheExtensions.GetString(IDistributedCache cache, String key)\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyNotificationHelper.RenewAccessTokenNotificationCacheValue(IDistributedCache cache, TimeSpan delay, Boolean isForceRenew) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyNotificationHelper.cs:line 71\r\n   at AKC.MobileAPI.Service.CronServices.RefreshLoyaltyNotificationTokenJob.DoWork(CancellationToken cancellationToken) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\CronServices\\Jobs\\RefreshLoyaltyNotificationTokenJob.cs:line 45","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2146233088,"Source":"StackExchange.Redis","WatsonBuckets":null,"failureType":1,"commandStatus":1}
ERROR 2024-06-11 17:50:21,261 [53   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:50:21,263 [53   ] onTokenJob - RenewAccessToken Job run successfully
ERROR 2024-06-11 17:51:21,298 [59   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:51:21,303 [59   ] onTokenJob - RenewAccessToken Job run successfully
ERROR 2024-06-11 17:52:21,118 [59   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:52:21,121 [59   ] onTokenJob - RenewAccessToken Job run successfully
ERROR 2024-06-11 17:53:22,301 [67   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:53:22,304 [67   ] onTokenJob - RenewAccessToken Job run successfully
ERROR 2024-06-11 17:54:21,345 [45   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:54:21,348 [45   ] onTokenJob - RenewAccessToken Job run successfully
ERROR 2024-06-11 17:55:21,130 [38   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:55:21,132 [38   ] onTokenJob - RenewAccessToken Job run successfully
ERROR 2024-06-11 17:56:21,344 [38   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:56:21,348 [38   ] onTokenJob - RenewAccessToken Job run successfully
ERROR 2024-06-11 17:57:21,257 [51   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:57:21,261 [51   ] onTokenJob - RenewAccessToken Job run successfully
ERROR 2024-06-11 17:58:21,279 [51   ] onTokenJob - Job: RenewAccessToken gift store vendor error for TenantId: One or more errors occurred. (A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.)
INFO  2024-06-11 17:58:21,284 [51   ] onTokenJob - RenewAccessToken Job run successfully
