﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberGetMemberLoginByFirebaseIdOutput
    {
        public string FirebaseId { get; set; }
        public string MemberCode { get; set; }
        public bool IsExisted { get; set; }
        public string PhoneNumber { get; set; }
        public bool HasPinCode { get; set; }
        public bool IsLocked { get; set; }
    }
}
