﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.ThirdParty
{
    public class LoyaltyThirdPartyVerifyOTPInput
    {
        public string OtpSession { get; set; }
        [Required]
        public string IdNumber { get; set; }
        [Required]
        [Range(1, Double.MaxValue)]
        public int MerchantId { get; set; }
        public string OtpNumber { get; set; }
        public string PhoneNumber { get; set; }
        //[Required]
        public string MemberCode { get; set; }
        public string NationalId { get; set; }
    }
}
