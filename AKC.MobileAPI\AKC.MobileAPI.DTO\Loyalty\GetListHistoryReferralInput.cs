﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty
{
    public class GetListHistoryReferralInput
    {
        public string InviterCode { get; set; }
        public string Sorting { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
    }

    public class HistoryReferralOutput
    {
        public string InvitedName { get; set; }
        public string InvitedPhone { get; set; }
        public string InvitedAvatar { get; set; }
        public DateTime? AcceptDate { get; set; }
        public string Status { get; set; }
        public string CampaignName { get; set; }
        public string TransactionCode { get; set; }
        public string GiftTransactionCode { get; set; }
        public ReferralInfo Detail { get; set; }
    }

    public class GetListHistoryReferralOutput
    {
        public List<HistoryReferralOutput> Items { get; set; }
        public int TotalCount { get; set; }
    }

    public class GetTransactionInforReferralInput
    {
          public string TransactionCode { get; set; }
    }

    public class GetTransactionInforReferralOutput
    {
        public string CampaignName { get; set; }
        public string InvitedMember { get; set; }
    }
    public class GetAddressBy3IdsInput
    {
        public int? CityId { get; set; }
        public int? DistrictId { get; set; }
        public int? WardId { get; set; }
    }

    public class GetAddressBy3IdsOutput
    {
        public string Province { get; set; }
        public string District { get; set; }
        public string Ward { get; set; }
    }
    public class ReferralInfo
    {
        public string Title { get; set; }
        public string Message { get; set; }
    }
}
