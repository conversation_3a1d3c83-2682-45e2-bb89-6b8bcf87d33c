﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.ThirdParty.VPBank
{
    public class ThirdPartyVPBankPointExchangeOutput
    {
        public string status { get; set; }
        public string error { get; set; }
        public string message { get; set; }
        //public ThirdPartyVPBankPointExchangeItem item { get; set; }
        public string vpidTransactionId { get; set; }
        public string memberCode { get; set; }
        public string exchangedAmount { get; set; }
        public string loyaltyTransactionId { get; set; }
    }

    public class ThirdPartyVPBankPointExchangeItem
    {
        public string vpidTransactionId { get; set; }
        public string memberCode { get; set; }
        public string exchangedAmount { get; set; }
        public string loyaltyTransactionId { get; set; }
    }
}
