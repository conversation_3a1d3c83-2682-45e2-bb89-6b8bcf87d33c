﻿using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.Merchant;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardMerchantService : RewardBaseService, IRewardMerchantService
    {
        //private readonly ILoyaltyThirdPartyService _loyaltyThirdPartyService;
        public RewardMerchantService(
            IConfiguration configuration
            //ILoyaltyThirdPartyService loyaltyThirdPartyService
            ) : base(configuration)
        {
            //_loyaltyThirdPartyService = loyaltyThirdPartyService;
        }

        public async Task<RewardGetAllMerchantOutput> GetAll(RewardGetAllMerchantInput input)
        {
            input.StatusFilter = "A";
            return await GetRewardAsync<RewardGetAllMerchantOutput>(RewardApiUrl.GET_ALL_MERCHANT, input);
        }

        public async Task<GetFullInfoMerchantByIdOutput> GetFullInfoMerchantById(GetFullInfoMerchantByIdInput input)
        {
            return await GetRewardAsync<GetFullInfoMerchantByIdOutput>(RewardApiUrl.MERCHANT_GetInfoFullData, input);
        }

        public async Task<RewardGetAllMerchantPaymentOutput> GetAllMerchantPayment(RewardGetAllMerchantPaymentInput input)
        {
            return await GetRewardAsync<RewardGetAllMerchantPaymentOutput>(RewardApiUrl.GET_ALL_MERCHANT_PAYMENT, input);
        }

        public async Task<RewardGetAllMerchantExchangeOutput> GetListExchangeMerchant(RewardGetAllMerchantExchangeInput input)
        {
            var request = new RewardGetAllMerchantExchangeDto()
            {
                StatusFilter = "A",
                TypeFilter = "Exchange",
                skipCount = input.skipCount,
                maxResultCount = input.maxResultCount,
                sorting = input.sorting,
            };
            return await GetRewardAsync<RewardGetAllMerchantExchangeOutput>(RewardApiUrl.MERCHANT_EXCHANGE_GET_ALL, request);
        }

        public async Task<RewardGetListExchangeMerchantConnectedOutput> GetListExchangeMerchantByMember(RewardGetListExchangeMerchantConnectedInput input)
        {
            var requestReward = new RewardGetListExchangeMerchantConnectedDto()
            {
                MaxResultCount = input.maxResultCount,
                NationalId = input.MemberCode,
                SkipCount = input.skipCount,
                Sorting = input.sorting,
                BindingStatusFilter = input.BindingStatusFilter,
                MerchantNameFilter = input.MerchantNameFilter,
                PriorityFilter = input.PriorityFilter,
            };

            return await GetRewardAsync<RewardGetListExchangeMerchantConnectedOutput>(RewardApiUrl.MEMBER_GET_LIST_MERCHANT_CONNECTED, requestReward);
        }

        public async Task<RewardMerchantGetListMerchantByIdsOutput> GetListMerchantByIds(RewardMerchantGetListMerchantByIdsInput input)
        {
            return await PostRewardAsync<RewardMerchantGetListMerchantByIdsOutput>(RewardApiUrl.MERCHANT_GET_LIST_BY_IDS, input);
        }

        public async Task<RewardGetCurrentMerchantResponse> GetCurrentMerchantRequest(RewardGetCurrentMerchantRequest input)
        {
            return await GetRewardAsync<RewardGetCurrentMerchantResponse>(RewardApiUrl.MERCHANT_GET_CURRENT_MERCHANT, input);
        }
    }
}
