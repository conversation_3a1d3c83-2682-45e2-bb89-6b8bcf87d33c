using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.AuditLog;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.ExchangeTransaction;
using AKC.MobileAPI.DTO.Reward.Merchant;
using AKC.MobileAPI.Service.Abstract;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using Google.Apis.Auth.OAuth2.Responses;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using JsonException = Newtonsoft.Json.JsonException;
using JsonSerializer = Newtonsoft.Json.JsonSerializer;

namespace AKC.MobileAPI.Service
{
    public class SkyJoyIntegrationService : ISkyJoyIntegrationService
    {
        private readonly string ClientId;
        private readonly string RedirectUri;
        private readonly string BaseWebViewAuthUrl;
        private readonly string LinkMemberUrl;
        private readonly string UnlinkMemberUrl;
        private readonly string PointAccrualUrl;
        private readonly string ExchangeCodeForAccessTokenUrl;
        private readonly string BeToBeLoginUrl;
        private readonly string RetrieveAccessBe2BeTokenSecret;
        private readonly string RetrieveAccessBe2BeTokenPassword;
        private readonly string RetrieveAccessBe2BeTokenUsername;
        private readonly string RetrieveAccessBe2BeTokenClientId;
        private readonly IConfiguration _configuration;
        private readonly IDistributedCache _cache;
        private readonly ILogger<SkyJoyIntegrationService> _logger;
        private readonly IRewardMemberService _memberRewardService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltyAuditLogService _loyaltyAuditLogService;
        public SkyJoyIntegrationService(IConfiguration conf, IDistributedCache cache,
            IRewardMemberService sv,
            IExceptionReponseService exceptionSv,
            ILoyaltyAuditLogService logSv,
            ILogger<SkyJoyIntegrationService> logger)
        {
            _configuration = conf;
            _cache = cache;
            ClientId = _configuration.GetSection("ThirdPartyMerchant:SkyJoy:ClientId").Value;
            RetrieveAccessBe2BeTokenSecret = _configuration.GetSection("ThirdPartyMerchant:SkyJoy:RetrieveAccessBe2BeTokenSecret").Value;
            RetrieveAccessBe2BeTokenPassword = _configuration.GetSection("ThirdPartyMerchant:SkyJoy:RetrieveAccessBe2BeTokenPassword").Value;
            RetrieveAccessBe2BeTokenUsername = _configuration.GetSection("ThirdPartyMerchant:SkyJoy:RetrieveAccessBe2BeTokenUsername").Value;
            RetrieveAccessBe2BeTokenClientId = _configuration.GetSection("ThirdPartyMerchant:SkyJoy:RetrieveAccessBe2BeTokenClientId").Value;
            // https://vpid-mobile-api-uat.linkid.vn/api/3rd/skyjoy-callback
            RedirectUri = _configuration.GetSection("ThirdPartyMerchant:SkyJoy:RedirectURI").Value;
            // https://id.uat.skyjoy.io/realms/uat-loyalty/protocol/openid-connect/auth
            BaseWebViewAuthUrl = _configuration.GetSection("ThirdPartyMerchant:SkyJoy:BaseWebViewAuthLink").Value;
            // https://api.uat.skyjoy.io/api-user/partner/v2/user/link-member
            LinkMemberUrl = _configuration.GetSection("ThirdPartyMerchant:SkyJoy:LinkMemberUrl").Value;
            // https://api.uat.skyjoy.io/api-user/partner/v1/user/unlink-member
            UnlinkMemberUrl = _configuration.GetSection("ThirdPartyMerchant:SkyJoy:UnlinkMemberUrl").Value;
            // https://api.uat.skyjoy.io/api-point/partner/v1/point-accrual
            PointAccrualUrl = _configuration.GetSection("ThirdPartyMerchant:SkyJoy:PointAccrualUrl").Value;
            // https://id.uat.skyjoy.io/realms/uat-loyalty/protocol/openid-connect/token
            ExchangeCodeForAccessTokenUrl =
                _configuration.GetSection("ThirdPartyMerchant:SkyJoy:ExchangeCodeForAccessTokenUrl").Value;
            // https://id.uat.skyjoy.io/realms/loyalty-partner/protocol/openid-connect/token
            BeToBeLoginUrl =
                _configuration.GetSection("ThirdPartyMerchant:SkyJoy:BeToBeLoginUrl").Value;
            _logger = logger;
            _memberRewardService = sv;
            _exceptionReponseService = exceptionSv;
            _loyaltyAuditLogService = logSv;
        }

        public async Task<SJRequestAccessTokenOutput> ExchangeCodeForToken(SJRequestAccessTokenInput input)
        {
            if (input == null)
                throw new ArgumentNullException(nameof(input));
            if (string.IsNullOrEmpty(input.Code))
                throw new ArgumentException("All input fields must be provided.");

            using var httpClient = new HttpClient();

            var formData = new Dictionary<string, string>
            {
                { "grant_type", "authorization_code" },
                { "client_id", ClientId },
                { "code", input.Code },
                { "redirect_uri", RedirectUri }
            };
            var httpResContent = string.Empty;
            try
            {
            
                var request = new HttpRequestMessage(HttpMethod.Post, ExchangeCodeForAccessTokenUrl)
                {
                    Content = new FormUrlEncodedContent(formData)
                };
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var response = await httpClient.SendAsync(request);

                // Read and deserialize the response
                var responseContent = await response.Content.ReadAsStringAsync();
                httpResContent = responseContent;
                // Check if the request was successful
                response.EnsureSuccessStatusCode();
                var result = JsonConvert.DeserializeObject<SJRequestAccessTokenOutput>(responseContent);
                result.code = "00";
                result.message = "Success";
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to exchange code for token." + ex.Message + " - " + ex.StackTrace);
                var parsed = JsonConvert.DeserializeObject<SJApiErrorResponse>(httpResContent);
                if (parsed.Errors != null)
                {
                    return new SJRequestAccessTokenOutput()
                    {
                        code = parsed.Errors?.Code ?? parsed.StatusCode + "", message = parsed.Errors?.Message ?? "Error Happened"
                    };
                }
                else
                {
                    // Maybe the httpResponse in this format in stead of the common format {"error":"invalid_grant","error_description":"Code not valid"}
                    var alternativeParsed = JsonConvert.DeserializeObject<SJAlternativeExchangeTokenErrorResponse>(httpResContent);
                    if (alternativeParsed != null && !string.IsNullOrEmpty(alternativeParsed.Error))
                    {
                        return new SJRequestAccessTokenOutput()
                        {
                            code = alternativeParsed.Error,
                            message = alternativeParsed.ErrorDescription ?? "Error Happened"
                        };
                    }
                    else
                    {
                        // Fallback if neither format is recognized
                        return new SJRequestAccessTokenOutput()
                        {
                            code = "500",
                            message = "Có lỗi xảy ra trong quá trình truy cập dịch vụ của đối tác. Vui lòng thử lại sau"
                        };
                    }
                }
            }
        }
        /// <summary>
        /// Call sang SkyJoy để bên họ acknowledge rằng member họ và member LynkiD thiết lập liên kết
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<SJLinkMemberOutput> LinkMember(SJLinkMemberInput input, CancellationToken cancellationToken = default)
        {
            if (input == null)
            {
                throw new ArgumentNullException(nameof(input));
            }

            var httpResContent = string.Empty;
            _logger.LogInformation(" >> LINK MEMBER RESPONSE >> " + JsonConvert.SerializeObject(input));
            try
            {
                var token = await GetBe2BeTokenAsync(cancellationToken);

                using var httpClient = new HttpClient
                {
                    Timeout = TimeSpan.FromSeconds(30)
                };

                var request = new HttpRequestMessage(HttpMethod.Post, LinkMemberUrl)
                {
                    Headers =
                    {
                        Accept = { new MediaTypeWithQualityHeaderValue("application/json") },
                        Authorization = new AuthenticationHeaderValue("Bearer", token.access_token)
                    },
                    Content = new StringContent(
                        JsonConvert.SerializeObject(new
                        {
                            sjToken = input.sjToken,
                            partnerMemberId = input.partnerMemberId,
                            profile = input.profile ?? new SJLinkMemberInputInner()
                        }),
                        Encoding.UTF8,
                        "application/json")
                };

                var response = await httpClient.SendAsync(request, cancellationToken);
                var content = await response.Content.ReadAsStringAsync();
                httpResContent = content;
                _logger.LogInformation(" >> LINK MEMBER RESPONSE >> " + content);
                response.EnsureSuccessStatusCode();

                var output = JsonConvert.DeserializeObject<SJLinkMemberOutput>(content) 
                             ?? throw new InvalidOperationException("Failed to deserialize link member response");
                output.code = "00";
                output.message = "Success";
                return output;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "Failed to link member with SkyJoy >> ");
                var parsed = JsonConvert.DeserializeObject<SJApiErrorResponse>(httpResContent);

                return new SJLinkMemberOutput()
                {
                    code = parsed.Errors?.Code ?? parsed.StatusCode + "", message = parsed.Errors?.Message ?? "Error Happened"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in LinkMember");
                var parsed = JsonConvert.DeserializeObject<SJApiErrorResponse>(httpResContent);

                return new SJLinkMemberOutput()
                {
                    code = parsed.Errors?.Code ?? parsed.StatusCode + "", message = parsed.Errors?.Message ?? "Error Happened"
                };
            }
        }

        public async Task<SJUnlinkMemberOutput> UnlinkMember(SJUnlinkMemberInput input, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Starting UnlinkMember for SkyJoy with input: {Input}", JsonConvert.SerializeObject(input));

            if (input == null)
            {
                throw new ArgumentNullException(nameof(input));
            }

            try
            {
                var token = await GetBe2BeTokenAsync(cancellationToken);

                using var httpClient = new HttpClient
                {
                    Timeout = TimeSpan.FromSeconds(30)
                };

                var request = new HttpRequestMessage(HttpMethod.Post, UnlinkMemberUrl)
                {
                    Headers =
                    {
                        Accept = { new MediaTypeWithQualityHeaderValue("application/json") },
                        Authorization = new AuthenticationHeaderValue("Bearer", token.access_token)
                    },
                    Content = new StringContent(
                        JsonConvert.SerializeObject(new
                        {
                            skyjoyId = input.skyjoyId,
                            partnerMemberId = input.partnerMemberId
                        }),
                        Encoding.UTF8,
                        "application/json")
                };

                var response = await httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<SJUnlinkMemberOutput>(responseContent) 
                    ?? throw new InvalidOperationException("Failed to deserialize unlink member response");

                _logger.LogInformation("Completed UnlinkMember for SkyJoy successfully with result: " + responseContent);
                return result;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "Failed to unlink member with SkyJoy");
                throw new ApplicationException("Failed to communicate with SkyJoy member unlinking service", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in UnlinkMember");
                throw;
            }
        }

        public async Task<SJPointAccrualOutput> PointAccrual(SJPointAccrualInput input, CancellationToken cancellationToken = default)
        {
            var op = new OperationLogDto()
            {
                PartnerCode = "SkyJoy", MemberId = input.MemberCode, ServiceName = "PointAccrual", Code = input.transactionId + "_" + DateTime.UtcNow.Millisecond
            };
            _logger.LogInformation("Starting point accrual process for SkyjoyId: {SkyjoyId}", input.skyjoyId);
            var token = await GetBe2BeTokenAsync(cancellationToken);
            _logger.LogDebug("Retrieved access token!");
            using var _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(60);
            var request = new HttpRequestMessage(HttpMethod.Post, PointAccrualUrl);

            // Set headers
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token.access_token);
            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            // Prepare payload
            var payload = new
            {
                input.skyjoyId,
                transactionId = input.transactionId,
                transactionDes = "",
                transactionDate = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                transactionAmount = 0,
                transactionCurrency = "VND",
                extraData = input.extraData
            };

            var json = JsonConvert.SerializeObject(payload);
            op.RequestMsg = json;
            _logger.LogInformation(
                "Point accrual to SkyJoy, INPUT: " + json);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                var response = await _httpClient.SendAsync(request, cancellationToken);
                var content = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(
                        "Point accrual successful for TransactionId: {TransactionId} - RES OF SKYJOY: {Response}",
                        input.transactionId, content);
                    op.Status = true;
                    op.ResponseMsg = content;
                    var successCase = JsonConvert.DeserializeObject<SJPointAccrualOutput>(content);
                    successCase.Code = "00";
                    successCase.Message = "Success";
                    return successCase;
                }
                else
                {
                    _logger.LogError(
                        "Point accrual failed for TransactionId: {TransactionId}. Status: {StatusCode}, Response: {Content}",
                        input.transactionId, response.StatusCode, content);
                    var errorResponse = JsonConvert.DeserializeObject<JPPointAccrualErrorResponse>(content);
                    op.Status = false;
                    op.ResponseMsg = content;
                    return new SJPointAccrualOutput
                    {
                        Code = errorResponse.Errors.Code,
                        Message = SkyjoyContentMessage.GetMessageFromCode(errorResponse.Errors.Code)
                    };
                }
            }
            catch (HttpRequestException ex) when (ex.InnerException is TimeoutException ||
                                                  ex.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogError(ex, "Timeout error during point accrual for TransactionId: {TransactionId}",
                    input.transactionId);
                op.Status = false;
                op.ResponseMsg = $"Timeout error communicating with Skyjoy: {ex.Message}";
                return new SJPointAccrualOutput
                {
                    Code = "TIMEOUT",
                    Message = $"Timeout error communicating with Skyjoy: {ex.Message}"
                };
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP request failed for TransactionId: {TransactionId}", input.transactionId);
                op.Status = false;
                op.ResponseMsg = $"Failed to communicate with Skyjoy: {ex.Message}";
                return new SJPointAccrualOutput
                {
                    Code = "100",
                    Message = $"Failed to communicate with Skyjoy: {ex.Message}"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during point accrual for TransactionId: {TransactionId}",
                    input.transactionId);
                op.Status = false;
                op.ResponseMsg = $"Internal error: {ex.Message}";
                return new SJPointAccrualOutput
                {
                    Code = "100",
                    Message = $"Internal error: {ex.Message}"
                };
            }
            finally
            {
                try
                {
                    var listOperationLogDto = new CreateOperationLogDto
                    {
                        OperationLogs = new List<OperationLogDto> { op }
                    };
                    await _loyaltyAuditLogService.CreateOperationLog(listOperationLogDto);
                }
                catch (Exception e)
                {
                    _logger.LogError(" >>  ERROR SAVING OPERATION LOG OF SKYJOY POINT ACCRUAL >> " + e.Message + " - " +  e.StackTrace);
                    _logger.LogError(" >>  ERROR SAVING OPERATION LOG OF SKYJOY POINT ACCRUAL >> " + JsonConvert.SerializeObject(op));
                }
            }
        }
        private RewardDataExceptionResponse GetErrorValidation(string errorCode, string errorMessage)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = errorMessage
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }
        /// <summary>
        /// build lên URl để mobile app mở ra webview
        /// </summary>
        /// <returns></returns>
        public string GetLoginUri()
        {
            var state = Guid.NewGuid().ToString();
            var nonce = Guid.NewGuid().ToString();
            var url = BaseWebViewAuthUrl + "?client_id=" + ClientId
                 + "&redirect_uri=" + RedirectUri + "&response_type=code&ui_locales=en&state=" + state + "&nonce=" + nonce;

            return url;
        }
        
        // BACK TO BACKEND LOGIN
        private async Task<GJBeToBeTokenResponse> GetBe2BeTokenAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation(" >> GetBe2BETokenAsync To SkyJoy !");
            var cacheKey = $"b2b_token__skyjoy";
            
            try
            {
                // Try get from cache
                var cachedToken = await _cache.GetStringAsync(cacheKey, cancellationToken);
                if (!string.IsNullOrEmpty(cachedToken))
                {
                    _logger.LogInformation("Cache hit for Be2Be token");
                    return JsonConvert.DeserializeObject<GJBeToBeTokenResponse>(cachedToken) 
                           ?? throw new InvalidOperationException("Failed to deserialize cached token");
                }

                _logger.LogInformation("Cache missed. Requesting new token from SkyJoy");

                // Configure HTTP client with timeout
                using var httpClient = new HttpClient
                {
                    Timeout = TimeSpan.FromSeconds(30)
                };

                var request = new HttpRequestMessage(HttpMethod.Post, BeToBeLoginUrl)
                {
                    Content = new FormUrlEncodedContent(new Dictionary<string, string>
                    {
                        ["client_id"] = RetrieveAccessBe2BeTokenClientId,
                        ["grant_type"] = "password",
                        ["username"] = RetrieveAccessBe2BeTokenUsername,
                        ["password"] = RetrieveAccessBe2BeTokenPassword,
                        ["client_secret"] = RetrieveAccessBe2BeTokenSecret
                    })
                };
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var response = await httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                var token = JsonConvert.DeserializeObject<GJBeToBeTokenResponse>(content) 
                            ?? throw new InvalidOperationException("Failed to deserialize token response");

                // Cache token with sliding expiration
                var cacheOptions = new DistributedCacheEntryOptions
                {
                    SlidingExpiration = TimeSpan.FromHours(12),
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(1)
                };

                await _cache.SetStringAsync(
                    cacheKey, 
                    JsonConvert.SerializeObject(token), 
                    cacheOptions, 
                    cancellationToken);

                return token;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "Failed to obtain Be2Be token from SkyJoy");
                throw new ApplicationException("Failed to communicate with SkyJoy authentication service", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in GetBe2BeTokenAsync");
                throw;
            }
        }
    }
}