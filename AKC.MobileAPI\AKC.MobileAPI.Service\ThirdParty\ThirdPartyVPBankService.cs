﻿using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.ExchangeTransaction;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.PartnerPointCaching;
using AKC.MobileAPI.DTO.ThirdParty.VPBank;
using AKC.MobileAPI.DTO.ThirdParty.VPBankLoyaltyExchange;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Abstract.ThirdParty;
using AKC.MobileAPI.Service.Constants.ThirdParty;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using AKC.MobileAPI.Service.ThirdParty.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.DTO.Base;

namespace AKC.MobileAPI.Service.ThirdParty
{
    public class ThirdPartyVPBankService : BaseThirdPartyVPBankService, IThirdPartyVPBankService
    {
        private readonly IRewardExchangeTransactionService _rewardExchangeTransactionService;
        private readonly ILoyaltyAuditLogService _loyaltyAuditLogService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IThirdPartyVPBankLoyaltyExchangeService _thirdPartyVPBankLoyaltyExchangeService;
        private readonly IDistributedCache _cache;
        public ThirdPartyVPBankService(
            IConfiguration configuration,
            ILogger<ThirdPartyVPBankService> logger,
            IRewardExchangeTransactionService rewardExchangeTransactionService,
            IRewardMemberService rewardMemberService,
            ILoyaltyAuditLogService loyaltyAuditLogService,
            IExceptionReponseService exceptionReponseService,
            IDistributedCache cache,
            IThirdPartyVPBankLoyaltyExchangeService thirdPartyVPBankLoyaltyExchangeService) : base(configuration, logger, loyaltyAuditLogService)
        {
            _rewardExchangeTransactionService = rewardExchangeTransactionService;
            _loyaltyAuditLogService = loyaltyAuditLogService;
            _rewardMemberService = rewardMemberService;
            _exceptionReponseService = exceptionReponseService;
            _thirdPartyVPBankLoyaltyExchangeService = thirdPartyVPBankLoyaltyExchangeService;
            _cache = cache;
        }

        public async Task<LoyaltyThirdPartyPointExchangeOutput> PointExchange(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null)
        {
            var vpidTransactionId = string.Empty;
            if (string.IsNullOrWhiteSpace(orderCode))
            {
                 vpidTransactionId = LoyaltyHelper.GenTransactionCode("PointExchange");
            } else
            {
                vpidTransactionId = orderCode;
            }
            var vpbankRequest = new ThirdPartyVPBankPointExchangeInput()
            {
                accessToken = input.AccessToken,
                exchangeAmount = input.ExchangeAmount.ToString(),
                memberCode = input.IdNumber,
                vpidTransactionId = vpidTransactionId,
            };

            var vpbankExchange = await SendPostAsync<ThirdPartyVPBankPointExchangeOutput>(VPBankApiUrl.POINT_EXCHANGE_OPERATION_NAME, vpbankRequest, context, input.MemberCode);
            var requestReward = new RewardCreateExchangeTransactionInput()
            {
                TransactionCode = vpidTransactionId,
                ExchangeAmount = input.ExchangeAmount,
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                PartnerBindingTxId = vpbankExchange.loyaltyTransactionId,
            };
            var allowRevert = true;
            try
            {
                var rewardExchange = await _rewardExchangeTransactionService.CreateExchangeTransactionIntegration(requestReward);
                if (rewardExchange.Result == 202)
                {
                    _logger.LogError($"Create exchange vpbank reward with Status 202 Accepted {JsonConvert.SerializeObject(rewardExchange)}");
                    allowRevert = false;
                    GetErrorValidation("ExchangeTransactionStatusAccepted", "Status 202 Accepted");
                }

                return new LoyaltyThirdPartyPointExchangeOutput()
                {
                    Error = null,
                    Success = true,
                    Result = 200,
                    Items = new LoyaltyThirdPartyPointExchangeResult()
                    {
                        Transaction = new LoyaltyThirdPartyPointExchangeItem()
                        {
                            EquivalentTokenAmount = rewardExchange.Items.EquivalentTokenAmount,
                            ExchangeAmount = input.ExchangeAmount,
                            PartnerBindingTxId = rewardExchange.Items.PartnerBindingTxId,
                        }
                    },
                };
            }
            catch (Exception e)
            {
                if (e.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(e);
                    if (res.Code == "ExchangeTransactionTimeout" || res.Code == "ExchangeTransactionStatusAccepted")
                    {
                        GetErrorValidation("ExchangeTransactionPending", "Transaction pending");
                    }
                }
                if (allowRevert)
                {
                    var revertPoint = new ThirdPartyVPBankPointRevertPointInput()
                    {
                        accessToken = input.AccessToken,
                        loyaltlyTransactionId = vpbankExchange.loyaltyTransactionId,
                        memberCode = input.IdNumber,
                        vpidTransactionId = vpidTransactionId,
                    };
                    await retryRevertToken(revertPoint, context);
                }
                throw e;
            }
        }

        public async Task<LoyaltyThirdPartyPointViewOutput> PointView(LoyaltyThirdPartyPointViewInput input, HttpContext context)
        {
            var vpbankRequest = new ThirdPartyVPBankPointViewInput()
            {
                accessToken = input.AccessToken,
                memberCode = input.IdNumber,
            };
            var vpbankResult = await SendPostAsync<ThirdPartyVPBankPointViewOutput>(VPBankApiUrl.POINT_VIEW_OPERATION_NAME, vpbankRequest, context, input.MemberCode);
            return new LoyaltyThirdPartyPointViewOutput()
            {
                Success = true,
                Result = new LoyaltyThirdPartyPointViewResult()
                {
                    Member = new LoyaltyThirdPartyPointViewItem()
                    {
                        CoinBalance = decimal.Parse(vpbankResult.PointBalance),
                        MemberCode = vpbankResult.MemberCode,
                    }
                }
            };
        }

        public async Task<LoyaltyThirdPartyRemoveConnectedMerchantOutput> RemoveConnectedMerchant(RewardMemberUpdateOutputDto member, LoyaltyThirdPartyRemoveConnectedMerchant input)
        {
            //var configuration = AccessConfigurationService.Instance.GetConfiguration();
            //var IsEnabledVPBankNew = configuration.GetSection("IsUsingVPBankLoyalty").Value;
            //if (!bool.Parse(IsEnabledVPBankNew))
            //{
            //    _logger.LogInformation("Not using new vpbank loyalty");
            //    return new LoyaltyThirdPartyRemoveConnectedMerchantOutput()
            //    {
            //        Status = true
            //    };
            //}
            var retry = 4;
            while (retry != 0)
            {
                try
                {
                    _logger.LogInformation("Remove connected merchant request: " + JsonConvert.SerializeObject(input));
                    await _thirdPartyVPBankLoyaltyExchangeService.RemoveConneted(new ThirdPartyVPBankLoyaltyRemoveConnetedInput()
                    {
                        LinkID_MemberID = member.Item.MemberId
                    }, input.MemberCode);
                    retry = 0;
                }
                catch (Exception ex)
                {
                    retry--;
                    _logger.LogInformation("Remove connected merchant fail: " + JsonConvert.SerializeObject(ex));
                }
            }
            return new LoyaltyThirdPartyRemoveConnectedMerchantOutput()
            {
                Status = true
            };
        }

        public async Task<LoyaltyThirdPartyVerifyNationalIdOutput> VerifyNationalId(LoyaltyThirdPartyVerifyNationalIdInput input, HttpContext context, string authorization)
        {
            //var checkChangedLoyalty = await _thirdPartyVPBankLoayltyExchangeService.CheckIdCard(new ThirdPartyVPBankLoyaltyCheckIdCardInput()
            //{
            //    IdCard = input.IdNumber,
            //    PhoneNumber = input.PhoneNumber
            //}, input.MemberCode);
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var IsEnabledVPBankNew = configuration.GetSection("IsUsingVPBankLoyalty").Value;
            // Check xem có xử dụng loyalty mới hay không ?
            if (bool.Parse(IsEnabledVPBankNew))
            {
                // Call sang vpbank loyalty để get cif code từ IDCard
                var vpbankLoyReponse = await _thirdPartyVPBankLoyaltyExchangeService.GetCifCodeByIdCard(new ThirdPartyVPBankLoyaltyGetCifCodeByIdCardInput()
                {
                    IdCard = input.IdNumber,
                }, input.MemberCode);
                await _rewardMemberService.VerifyIsIdCardVerified(new RewardMemberVerifyIsIdCardVerifiedInput()
                {
                    MemberCode = input.MemberCode,
                    IdCard = input.IdNumber,
                    MemberLoyaltyCode = vpbankLoyReponse.Result.CifCode,
                    MerchantId = input.MerchantId,
                    CheckHistory = true,
                });
                var checkChangedLoyalty = await _thirdPartyVPBankLoyaltyExchangeService.VerifyIdCard(new ThirdPartyVPBankLoyaltyVerifyIdCardInput()
                {
                    IdCard = input.IdNumber,
                    PhoneNumber = input.PhoneNumber,
                    SessionId = input.OtpSession,
                }, input.MemberCode);
                if (checkChangedLoyalty.IsChangedLoyalty)
                {
                    // Do ở vpbank bảng usermaping cần linkid phonenumber nên cần check xem member đó có số điện thoại hay chưa
                    var rewardMember = await _rewardMemberService.View(new RewardMemberRequestInput()
                    {
                        MemberCode = input.MemberCode,
                    }, authorization);
                    if (string.IsNullOrWhiteSpace(rewardMember.Items.PhoneNumber))
                    {
                        _logger.LogDebug("Member cannot phone number when connect vpbank loyalty new");
                        return new LoyaltyThirdPartyVerifyNationalIdOutput()
                        {
                            Result = null,
                            Error = "VPB0001",
                            Success = false,
                            IsChangedLoyalty = true,
                        };
                    }
                    var cacheOption = new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromSeconds(180));
                    var cacheKey = "VerifyIdCard_" + input.PhoneNumber + input.IdNumber;
                    await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(input), cacheOption);
                    return new LoyaltyThirdPartyVerifyNationalIdOutput()
                    {
                        Result = null,
                        Success = true,
                        IsChangedLoyalty = true,
                    };
                }
            }

            // Không xử dụng loyalty mới
            var vpbankRequest = new ThirdPartyVPBankVerifyNationalIdInput()
            {
                isResendOTP = input.IsResendOTP,
                memberCode = input.IdNumber,
                OTPSession = input.OtpSession,
                phoneNumber = input.PhoneNumber,
            };
            await SendPostAsync<ThirdPartyVPBankVerifyNationalIdOutput>(VPBankApiUrl.VERIFY_NATIONAL_ID_OPERATION_NAME, vpbankRequest, context, input.MemberCode);
            return new LoyaltyThirdPartyVerifyNationalIdOutput()
            {
                Result = null,
                Success = true,
                IsChangedLoyalty = false,
            };
        }

        public async Task<LoyaltyThirdPartyVerifyOTPOutput> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input, HttpContext context, string authorization)
        {
            // Nếu có xử dụng loyalty mới
            var cacheKey = "VerifyIdCard_" + input.PhoneNumber + input.IdNumber;
            var cacheDataVerify = await _cache.GetStringAsync(cacheKey);
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var IsEnabledVPBankNew = configuration.GetSection("IsUsingVPBankLoyalty").Value;
            if (bool.Parse(IsEnabledVPBankNew))
            {
                if (!string.IsNullOrWhiteSpace(cacheDataVerify))
                {
                    var rewardMember = await _rewardMemberService.View(new RewardMemberRequestInput()
                    {
                        MemberCode = input.MemberCode,
                    }, authorization);
                    var response = await _thirdPartyVPBankLoyaltyExchangeService.VerifyOtp(new ThirdPartyVPBankLoyaltyVerifyOtpInput()
                    {
                        IdCard = input.IdNumber,
                        LinkID_MemberID = rewardMember.Items.Id,
                        OtpCode = input.OtpNumber,
                        PhoneNumber = input.PhoneNumber,
                        LinkID_WalletAddress = rewardMember.Items.UserAddress,
                        LinkID_PhoneNumber = rewardMember.Items.PhoneNumber,
                        SessionId = input.OtpSession,
                    }, input.MemberCode);
                    var referenceDataLoyalty = new ThirdPartyVPBankLoyaltyReferenceData()
                    {
                        MemberCode = response.Result.MemberCode,
                        Cif = response.Result.MemberCode,
                    };
                    var memberLoyaltyInfo = response.Result;
                    await _rewardMemberService.VerifyIsIdCardVerified(new RewardMemberVerifyIsIdCardVerifiedInput()
                    {
                        MemberCode = input.MemberCode,
                        IdCard = input.IdNumber,
                        MemberLoyaltyCode = memberLoyaltyInfo.Cif,
                        MerchantId = input.MerchantId,
                        CheckHistory = true,
                    });
                    await _rewardMemberService.SaveRefreshToken(new RewardMemberSaveRefreshTokenInput()
                    {
                        MemberCode = input.MemberCode,
                        MerchantId = input.MerchantId,
                        RefreshToken = "RefreshToken",
                        IsChangedLoyalty = true,
                        ReferenceData = JsonConvert.SerializeObject(referenceDataLoyalty),
                        MemberLoyaltyInfo = new MemberConnectLoyaltyInfoInput()
                        {
                            Cif = memberLoyaltyInfo.Cif,
                            MemberLoyaltyCode = memberLoyaltyInfo.MemberCode,
                            Segment = memberLoyaltyInfo.Segment,
                            VipType = memberLoyaltyInfo.VipType,
                            Gender = memberLoyaltyInfo.Gender,
                            FullRegionCode = memberLoyaltyInfo.FullRegionCode,
                            Name = memberLoyaltyInfo.Name,
                            Address = memberLoyaltyInfo.Address,
                            Avatar = memberLoyaltyInfo.Avatar,
                            ChannelType = memberLoyaltyInfo.ChannelType,
                            Dob = memberLoyaltyInfo.Dob.HasValue ? memberLoyaltyInfo.Dob : null,
                            Email = memberLoyaltyInfo.Email,
                            FullChannelTypeCode = memberLoyaltyInfo.FullChannelTypeCode,
                            FullMemberTypeCode = memberLoyaltyInfo.FullMemberTypeCode,
                            IdCard = memberLoyaltyInfo.IdCard,
                            MemberTypeCode = memberLoyaltyInfo.MemberTypeCode,
                            PartnerPhoneNumber = input.PhoneNumber,
                            Phone = memberLoyaltyInfo.Phone,
                            RankTypeCode = memberLoyaltyInfo.Phone,
                            RegionCode = memberLoyaltyInfo.RegionCode,
                            StandardMemberCode = memberLoyaltyInfo.StandardMemberCode,
                            Type = memberLoyaltyInfo.Type,
                        }
                    });
                    await _cache.RemoveAsync(cacheKey);
                    // mark ack flag
                    await _rewardMemberService.RewardSendAckAfterConnected(new RewardSendAckAfterConnectedInput()
                    {
                        MemberId = rewardMember.Items.Id, MerchantId = input.MerchantId, NationalId = input.MemberCode
                    });
                    return new LoyaltyThirdPartyVerifyOTPOutput()
                    {
                        Success = true,
                        Result = 200,
                        Items = new LoyaltyThirdPartyVerifyOTPResult()
                        {
                            Member = new LoyaltyThirdPartyVerifyOTPItem()
                            {
                                Name = rewardMember.Items.Name,
                                PartnerBalance = rewardMember.Items.TokenBalance,
                            }
                        }
                    };
                }
            }

            // Không sử dụng loyalty mới
            var vpbankRequest = new ThirdPartyVPBankVerifyOTPInput()
            {
                memberCode = input.IdNumber,
                OTPNumber = input.OtpNumber,
                OTPSession = input.OtpSession,
            };
            if (!string.IsNullOrEmpty(input.NationalId))
                input.MemberCode = input.NationalId;

            var vpbankResult = await SendPostAsync<ThirdPartyVPBankVerifyOTPOutput>(VPBankApiUrl.VERIFY_OTP_OPERATION_NAME, vpbankRequest, context, input.MemberCode);
            var referenceData = new ThirdPartyVPBankReferenceData()
            {
                MemberCode = input.IdNumber,
                PhoneNumber = input.PhoneNumber,
                Cif = vpbankResult.CustomerInfo.Cif
            };
            await _rewardMemberService.VerifyIsIdCardVerified(new RewardMemberVerifyIsIdCardVerifiedInput()
            {
                MemberCode = input.MemberCode,
                IdCard = input.IdNumber,
                MemberLoyaltyCode = vpbankResult.CustomerInfo.Cif,
                MerchantId = input.MerchantId,
                CheckHistory = true,
            });
            await _rewardMemberService.SaveRefreshToken(new RewardMemberSaveRefreshTokenInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                RefreshToken = vpbankResult.RefreshToken,
                IsChangedLoyalty = false,
                ReferenceData = JsonConvert.SerializeObject(referenceData),
                MemberLoyaltyInfo = new MemberConnectLoyaltyInfoInput()
                {
                    Cif = vpbankResult.CustomerInfo.Cif,
                    Name = vpbankResult.CustomerInfo.FullName,
                    MemberLoyaltyCode = vpbankResult.CustomerInfo.Cif,
                }
            });
            var viewPointResult = await PointView(new LoyaltyThirdPartyPointViewInput()
            {
                AccessToken = vpbankResult.AccessToken,
                IdNumber = input.IdNumber,
                MerchantId = input.MerchantId,
                MemberCode = input.MemberCode,
            }, context);
            return new LoyaltyThirdPartyVerifyOTPOutput()
            {
                Success = true,
                Result = 200,
                Items = new LoyaltyThirdPartyVerifyOTPResult()
                {
                    AccessToken = vpbankResult.AccessToken,
                    RefreshToken = vpbankResult.RefreshToken,
                    Member = new LoyaltyThirdPartyVerifyOTPItem()
                    {
                        Name = vpbankResult.CustomerInfo.FullName,
                        PartnerBalance = viewPointResult.Result.Member.CoinBalance
                    }
                }
            };
        }

        public async Task<LoyaltyThirdPartyConfirmConnectOutput> ConfirmConnect(LoyaltyThirdPartyConfirmConnectInput input, string authorization)
        {
            if (CommonConstants.MERCHANT_CONNECTION_STATUS_CONFIRMED.Equals(input.Action))
            {
                // Chỉ verify OTP đối với case CONFIRMED, còn REMOVED thì chỉ lưu DB
                _logger.LogInformation("Verify OTP to confirm connection of " + input.MemberCode + " AND Merchant " + input.MerchantId);
                var optConfirm = await _thirdPartyVPBankLoyaltyExchangeService.VerifyOtpConfirmConnect(new ThirdPartyVPBankLoyaltyVerifyOtpConfirmConnectInput()
                {
                    OptCode = input.OptCode,
                    SessionId = input.SessionId,
                }, input.MemberCode);
                _logger.LogInformation("Verify otp confirm connect result: " + JsonConvert.SerializeObject(optConfirm));
            }
            var rewardConfirm = await _rewardMemberService.ConfirmConnect(new RewardMemberConfirmConnectInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                Action = input.Action,
            });
            _logger.LogInformation("Reward confirm result: " + JsonConvert.SerializeObject(rewardConfirm));
            var memberId = rewardConfirm.Item.MemberId;
            if (input.Action == StatusConnectMerchant.Confirmed)
            {
                await RetryConfirmConnect(memberId, input.MemberCode);
            } else
            {
                await RetryUnConfirmConnect(memberId, input.MemberCode);
            }
            return new LoyaltyThirdPartyConfirmConnectOutput()
            {
                Message = "Success",
                MessageDetail = null,
                Result = 200,
            };
        }

        public async Task<LoyaltyThirdPartyRevertPointOutput> RevertPoint(LoyaltyThirdPartyRevertPointInput input, HttpContext context)
        {
            var revertPoint = new ThirdPartyVPBankPointRevertPointInput()
            {
                accessToken = input.AccessToken,
                loyaltlyTransactionId = input.LoyaltlyTransactionId,
                memberCode = input.MemberCode,
                vpidTransactionId = input.VpidTransactionId,
            };
            await SendPostAsync<ThirdPartyVPBankPointRevertPointOutput>(VPBankApiUrl.REVERT_POINT_OPERATION_NAME, revertPoint, context, input.MemberCode);
            return new LoyaltyThirdPartyRevertPointOutput()
            {
                Error = null,
                Success = true,
                Message = "Success",
            };
        }

        private async Task<ThirdPartyVPBankPointRevertPointOutput> RevertPointInternal(ThirdPartyVPBankPointRevertPointInput input, HttpContext context)
        {
            try
            {
                var vpbankResult = await SendPostAsync<ThirdPartyVPBankPointRevertPointOutput>(VPBankApiUrl.REVERT_POINT_OPERATION_NAME, input, context, input.memberCode);
                return vpbankResult;
            }
            catch
            {
                return null;
            }
        }

        private async Task retryRevertToken(ThirdPartyVPBankPointRevertPointInput input, HttpContext context)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await RevertPointInternal(input, context);
                if (result != null)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task RetryConfirmConnect(int LinkIdMemberId, string memberCode)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await ConfirmConnect(LinkIdMemberId, memberCode);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<bool> ConfirmConnect(int LinkIdMemberId, string memberCode)
        {
            try
            {
                var result = await _thirdPartyVPBankLoyaltyExchangeService.ConfirmConnect(new ThirdPartyVPBankLoyaltyConfirmConnectInput()
                {
                    LinkID_MemberID = LinkIdMemberId,
                    ConnectSource = 1,
                }, memberCode);
                _logger.LogInformation("Loyalty confirm result: " + JsonConvert.SerializeObject(result));
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Confirm connect member loyalty error " + ex.Message);
                return false;
            }
        }

        private async Task RetryUnConfirmConnect(int LinkIdMemberId, string memberCode)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await UnConfirmConnect(LinkIdMemberId, memberCode);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<bool> UnConfirmConnect(int LinkIdMemberId, string memberCode)
        {
            try
            {
                var result = await _thirdPartyVPBankLoyaltyExchangeService.RemoveConneted(new ThirdPartyVPBankLoyaltyRemoveConnetedInput()
                {
                    LinkID_MemberID = LinkIdMemberId
                }, memberCode);
                _logger.LogInformation("Loyalty Unconfirm result: " + JsonConvert.SerializeObject(result));
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Confirm connect member loyalty error " + ex.Message);
                return false;
            }
        }

        public async Task<LoyaltyThirdPartyRequestAccessTokenOutput> RequestAccessToken(LoyaltyThirdPartyRequestAccessTokenInput input, HttpContext context)
        {
            var resultRefreshToken = await _rewardMemberService.GetRefreshToken(new RewardMemberGetRefreshTokenInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId
            });
            var vpbankRequest = new ThirdPartyVPBankRequestAccessTokenInput()
            {
                memberCode = resultRefreshToken.Items.IdCard,
                refreshToken = resultRefreshToken.Items.ItemPartner[0].RefreshToken,
            };
            var vpbankResult = await SendPostAsync<ThirdPartyVPBankRequestAccessTokenOutput>(VPBankApiUrl.REQUEST_ACCESS_TOKEN_OPERATION_NAME, vpbankRequest, context, input.MemberCode);
            return new LoyaltyThirdPartyRequestAccessTokenOutput()
            {
                Success = true,
                Result = 200,
                Message = "Success",
                Items = new LoyaltyThirdPartyRequestAccessTokenItems()
                {
                    AccessToken = vpbankResult.AccessToken,
                    IdNumber = resultRefreshToken.Items.IdCard,
                    MemberCode = resultRefreshToken.Items.MemberCode,
                }
            };
        }

        public async Task<RewardPartnerPoingCachingItems> UpdatePartnerCaching(LoyaltyThirdPartyVPBankUpdatePartnerCachingInput input, HttpContext context)
        {
            try
            {
                var request = new LoyaltyThirdPartyPointViewInput()
                {
                    AccessToken = input.AccessToken,
                    IdNumber = input.IdNumber,
                    MemberCode = input.MemberCode,
                };
                var response = await PointView(request, context);
                return new RewardPartnerPoingCachingItems()
                {
                    PointBalance = response.Result.Member.CoinBalance,
                    Status = "S",
                    MerchantId = input.MerchantId
                };
            }
            catch (Exception ex)
            {
                return new RewardPartnerPoingCachingItems()
                {
                    PointBalance = 0,
                    Status = "F",
                    MerchantId = input.MerchantId,
                    HaveException = true,
                    ExceptionCode = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result.Code
            };
            }
        }

        public async Task<LoyaltyThirdPartyPointExchangeOutput> PointExchangeIntegration(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null)
        {
            var vpidTransactionId = string.Empty;
            if (string.IsNullOrWhiteSpace(orderCode))
            {
                vpidTransactionId = LoyaltyHelper.GenTransactionCode("PointExchange");
            }
            else
            {
                vpidTransactionId = orderCode;
            }
            var vpbankRequest = new ThirdPartyVPBankPointExchangeInput()
            {
                accessToken = input.AccessToken,
                exchangeAmount = input.ExchangeAmount.ToString(),
                memberCode = input.IdNumber,
                vpidTransactionId = vpidTransactionId,
            };

            try
            {
                var vpbankExchange = await SendPostAsync<ThirdPartyVPBankPointExchangeOutput>(VPBankApiUrl.POINT_EXCHANGE_OPERATION_NAME, vpbankRequest, context, input.MemberCode);

                return new LoyaltyThirdPartyPointExchangeOutput()
                {
                    Error = null,
                    Success = true,
                    Result = 200,
                    Items = new LoyaltyThirdPartyPointExchangeResult()
                    {
                        Transaction = new LoyaltyThirdPartyPointExchangeItem()
                        {
                            EquivalentTokenAmount = input.ExchangeAmount,
                            ExchangeAmount = input.ExchangeAmount,
                            PartnerBindingTxId = vpbankExchange.loyaltyTransactionId,
                        }
                    },
                };
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<LoyaltyThirdPartyUpdatePhoneNumberOutput> UpdatePhoneNumber(UpdatePhoneNumberInput input)
        {
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var IsEnabledVPBankNew = configuration.GetSection("IsUsingVPBankLoyalty").Value;
            if (!bool.Parse(IsEnabledVPBankNew))
            {
                _logger.LogInformation("Not using new vpbank loyalty");
                return new LoyaltyThirdPartyUpdatePhoneNumberOutput()
                {
                    Status = true
                };
            }
            var retry = 4;
            while (retry != 0)
            {
                try
                {
                    _logger.LogInformation("Update phone number connect for vpbank request: " + JsonConvert.SerializeObject(input));
                    await _thirdPartyVPBankLoyaltyExchangeService.UpdatePhoneNumber(input, input.MemberCode);
                    retry = 0;
                }
                catch (Exception ex)
                {
                    retry--;
                    _logger.LogInformation("Update phone number connect for vpbank fail: " + JsonConvert.SerializeObject(ex));
                }
            }
            return new LoyaltyThirdPartyUpdatePhoneNumberOutput()
            {
                Status = true
            };
        }

        public async Task<LoyaltyThirdPartySendOtpConfirmConnectOutput> SendOtpConfirmConnect(LoyaltyThirdPartySendOtpConfirmConnectInput input, string authorization)
        {
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var IsEnabledVPBankNew = configuration.GetSection("IsUsingVPBankLoyalty").Value;
            if (!bool.Parse(IsEnabledVPBankNew))
            {
                _logger.LogInformation("Not using new vpbank loyalty");
                throw new Exception("Not using new vpbank loyalty");
            }
            var memberConnectInfo = await _rewardMemberService.GetInfoConfirmConnectMerchant(new RewardMemberGetInfoConfirmConnectMerchantInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
            });
            var memberInfo = memberConnectInfo.Item;
            await _thirdPartyVPBankLoyaltyExchangeService.SendOtpConfirmConnect(new ThirdPartyVPBankLoyaltySendOtpConfirmConnectInput()
            {
                IdCard = memberInfo.PartnerIdCard,
                LinkID_PhoneNumber = memberInfo.PhoneNumber,
                PhoneNumber = memberInfo.PartnerPhoneNumber,
                SessionId = input.SessionId,
            }, input.MemberCode);
            return new LoyaltyThirdPartySendOtpConfirmConnectOutput()
            {
                Message = "Success",
                Result = 200,
                MessageDetail = null
            };
        }

        private RewardDataExceptionResponse GetErrorValidation(string errorCode, string errorMessage)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = errorMessage
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }
    }
}
