﻿using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty.Gift
{
    public class LoyaltyFlashSaleService : BaseLoyaltyService, ILoyaltyFlashSaleProgramService
    {
        private readonly ILogger _logger;
        private IRewardGiftRedeemTransactionService _rewardGiftRedeemTransactionService;
        private readonly IExceptionReponseService _exceptionResponseService;
        public LoyaltyFlashSaleService(IConfiguration configuration, IDistributedCache cache,
            ILogger<LoyaltyFlashSaleService> lg,
            IExceptionReponseService x,
            IRewardGiftRedeemTransactionService rewardGiftRedeemTransactionService) : base(configuration, cache)
        {
            _logger = lg;
            _exceptionResponseService = x;
            _rewardGiftRedeemTransactionService = rewardGiftRedeemTransactionService;
        }

        public async Task<LoyaltyGetAllForFlashSaleProgramOutPut> GetAll(MemberInfoRequest input)
        {
            return await GetLoyaltyAsync<LoyaltyGetAllForFlashSaleProgramOutPut>(LoyaltyApiUrl.GETALL_FLASHSALE_PROGRAM, input);
        }

        public async Task<LoyaltyGiftCategoryGetAllOutput> GetAllGiftCategoryForFlashSaleProgram(LoyaltyGetAllGiftCategoryForFlashSaleProgramInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGiftCategoryGetAllOutput>(LoyaltyApiUrl.GETALL_GIFT_CATEGORY_FLASHSALE_PROGRAM, input);
        }
    }
}
