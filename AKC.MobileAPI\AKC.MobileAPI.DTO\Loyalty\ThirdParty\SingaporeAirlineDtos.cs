namespace AKC.MobileAPI.DTO.Loyalty.ThirdParty
{
    public class CreditNonAirMilesSIAInput
    {
        public class XPartnerProfile
        {
            public string LoyaltyMembershipID { get; set; }
            public string LoyaltyParticipantCode { get; set; }
            public string LoyaltyProgramCode { get; set; }
        }

        // Properties of the MyCallInput class
        public XPartnerProfile PartnerProfile { get; set; }
        public string TransactionCode { get; set; }
        public string FamilyName { get; set; }
        public string GivenName { get; set; }
        public int Miles { get; set; }
        public string PartnerTransactionCode { get; set; }
        public string AwardDate { get; set; }
        public string ParticipantCode { get; set; }
        
        public string promotionAwardDate { get; set; }
        public string promotionCode { get; set; }
        public int? promotionMileAwarded { get; set; }
    }
    public class CreditNonAirMilesSIAOutput
    {
        public class XResponse
        {
            public string TransactionID { get; set; }
            public string ErrorCode { get; set; }
            public string ErrorDescription { get; set; }
        }
        public string Status { get; set; }
        public string Code { get; set; }
        public XResponse Response { get; set; }
    }
    public class CreditNonAirMilesCallerInput
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string SIAMemberCode { get; set; }
        public string LynkiDTransactionCode { get; set; }
        public int Miles { get; set; }
        
        
        public string promotionAwardDate { get; set; }
        public string promotionCode { get; set; }
        public int? promotionMileAwarded { get; set; }
    }
    public class PartnerExchangeOutput
    {
        public string Code { get; set; }
        public string Message { get; set; }
        public string TransactionId { get; set; }
    }
}