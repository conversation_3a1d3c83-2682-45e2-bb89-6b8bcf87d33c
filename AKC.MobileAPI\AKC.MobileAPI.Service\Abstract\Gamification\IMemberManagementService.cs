﻿using AKC.MobileAPI.DTO.Gamification;
using AKC.MobileAPI.DTO.Gamification.Game;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Gamification
{
    public interface IMemberManagementService
    {
        Task<ViewGameItemsOutput> ViewGameItems(ViewGameItemsInput input);

        Task<MemberPlayGameOutputDto> MemberPlayGame(MemberPlayGameInputDto input);

        Task<MemberPlayGameOutputDto> CreateMember(GetGamificationLinkInput input);
    }
}
