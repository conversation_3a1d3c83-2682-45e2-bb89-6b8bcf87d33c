﻿using AKC.MobileAPI.DTO.Loyalty.Article;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/Article")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyArticleController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyArticleService _articleService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltySecondaryCustomersService loyaltySecondaryCustomersService;
        private readonly ICommonHelperService _commonHelperService;
        public LoyaltyArticleController(
            ILogger<LoyaltyArticleController> logger,
            ILoyaltyArticleService articleService,
            ILoyaltySecondaryCustomersService loyaltySecondaryCustomersService,
            IExceptionReponseService exceptionReponseService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _articleService = articleService;
            _exceptionReponseService = exceptionReponseService;
            this.loyaltySecondaryCustomersService = loyaltySecondaryCustomersService;
            _commonHelperService = commonHelperService;
        }

        [HttpGet]
        [Route("GetAll")]
        public async Task<ActionResult<LoyaltyArticleGetAllOutput>> GetAll([FromQuery] LoyaltyArticleGetAllInput input)
        {
            try
            {
                var result = await _articleService.GetAll(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Article GetAll Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetArticleByIdAndRelatedNews")]
        public async Task<ActionResult<GetArticleForEditOutput>> GetArticleByIdAndRelatedNews([FromQuery] GetArticleByIdAndRelatedNewsInput input)
        {
            try
            {
                var result = await _articleService.GetArticleByIdAndRelatedNews(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Article GetAllGiftCategoriesAndInfo Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }


        [HttpGet]
        [Route("GetArticleByIdByMemberCode")]
        public async Task<ActionResult<GetArticleForEditOutput>> GetArticleByIdByMemberCode([FromQuery] GetArticleByIdByMemberCodeInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _articleService.GetArticleByIdByMemberCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Article GetAllGiftCategoriesAndInfo Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllArticleAndRelatedNews")]
        public async Task<ActionResult<GetAllArticleAndRelatedNewsOutput>> GetAllArticleAndRelatedNews([FromQuery] GetAllArticleAndRelatedNewsInput input)
        {
            try
            {
                var result = await _articleService.GetAllArticleAndRelatedNews(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Article GetAllArticleAndRelatedNews Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        //PHUONGPV5
        [HttpGet]
        [Route("GetAllArticleAndRelatedNews_Optimize")]
        public async Task<ActionResult<GetAllArticleAndRelatedNewsOutput_Optimize>> GetAllArticleAndRelatedNews_Optimize([FromQuery] GetAllArticleAndRelatedNewsInput_Optimize_New input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                if (!string.IsNullOrEmpty(input.MemberCode))
                {
                    var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                    if (checkAuthen != null)
                    {
                        return StatusCode(401, checkAuthen);
                    }
                }
                var result = await _articleService.GetAllArticleAndRelatedNews_Optimize_V1(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Article GetAllArticleAndRelatedNews Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        /**
         * Allow this method to be called anonymously. T&C are simply Articles with CategoryTypeFilter = 4.
         * So this method will query all the Active Articles having CategoryTypeFilter = 4
         */
        [HttpGet]
        [AllowAnonymous]
        [Route("GetTermsAndConditions")]
        public async Task<ActionResult<GetAllArticleAndRelatedNewsOutput>> GetTermsAndConditions()
        {
            try
            {
                var input = new GetAllArticleAndRelatedNewsInput
                {
                    Filter  = "", CategoryTypeFilter = "4", MaxResultCount = 10, SkipCount = 0
                };
                var result = await _articleService.GetAllArticleAndRelatedNews(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Get Terms And Conditions with Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }
        
        /**
         * Allow this method to be called anonymously. SecPolicy are simply Articles with CategoryTypeFilter = 39.
         * So this method will query all the Active Articles having CategoryTypeFilter = 39
         */
        [HttpGet]
        [AllowAnonymous]
        [Route("GetSecurityPolicy")]
        public async Task<ActionResult<GetAllArticleAndRelatedNewsOutput>> GetSecurityPolicy()
        {
            try
            {
                var input = new GetAllArticleAndRelatedNewsInput
                {
                    Filter  = "", CategoryTypeFilter = "39", MaxResultCount = 10, SkipCount = 0
                };
                var result = await _articleService.GetAllArticleAndRelatedNews(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetSecurityPolicy with Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetPopUpOnApp")]
        public async Task<ActionResult<GetAllArticlePopupOutput>> GetPopUpOnApp([FromQuery] GetPopupOnAppInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                if (!string.IsNullOrEmpty(input.MemberCode))
                {
                    var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                    if (checkAuthen != null)
                    {
                        return StatusCode(401, checkAuthen);
                    }
                }
                var result = await _articleService.GetPopUpOnApp(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Get Pop up with Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }


        [HttpGet]
        [Route("GetAllArticleByMemberCode")]
        public async Task<ActionResult<GetAllArticleByMemberCodeOutPut>> GetAllArticleByMemberCode([FromQuery] GetAllArticleByMemberCodeInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _articleService.GetAllArticleByMemberCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Article GetAllArticleAndRelatedNews Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        private async Task<string> getNational(string tokenBearer)
        {
            var idToken = tokenBearer.Replace("Bearer ", "").Replace("bearer ", "");
            var token = await FirebaseAdmin.Auth.FirebaseAuth.DefaultInstance.VerifyIdTokenAsync(idToken);
            return token.Uid;
        }

    }
}
