﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.ExchangeTransaction
{
    public class RewardExchangeVerifyOTPOutput
    {
        public bool Success { get; set; }
        public string Error { get; set; }
        public ThirdPartyVerifyOtpResult Result { get; set; }
    }

    public class ThirdPartyVerifyOtpResult
    {
        public ThirdPartyVerifyOtpItem Member { get; set; }
    }

    public class ThirdPartyVerifyOtpItem
    {
        public string Name { get; set; }
        public decimal PartnerBalance { get; set; }
    }
}
