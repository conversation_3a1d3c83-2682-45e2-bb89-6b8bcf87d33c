﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer
{
    public class RankDto
    {
        public long Id { get; set; }
        public string Code { get; set; }

        public string Name { get; set; }

        public decimal Target { get; set; }

        public string Description { get; set; }

        public string Avatar { get; set; }

        public bool IsActive { get; set; }

        public DateTime? LastModificationTime { get; set; }

        public long? LastModifierUserId { get; set; }

        public string LastModifierUserName { get; set; }

        public long? ArticleId { get; set; }
    }
}
