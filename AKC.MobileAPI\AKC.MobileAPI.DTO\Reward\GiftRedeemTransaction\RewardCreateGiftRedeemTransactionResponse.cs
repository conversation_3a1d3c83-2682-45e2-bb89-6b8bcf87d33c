﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction
{
    public class RewardCreateGiftRedeemTransactionResponse
    {
        public string Message { get; set; }
        public string messageDetail { get; set; }
        public int Result { get; set; }
        public RewardCreateGiftRedeemTransactionItem Items { get; set; }
    }

    public class RewardCreateGiftRedeemTransactionItem
    {
        public long MemberId { get; set; }
        public string NationalId { get; set; }
        public string OrderCode { get; set; }
        public long MerchantId { get; set; }
        public decimal TokenAmount { get; set; }
    }
}
