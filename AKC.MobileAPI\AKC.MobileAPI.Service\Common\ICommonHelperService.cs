﻿using AKC.MobileAPI.DTO.Base;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;

namespace AKC.MobileAPI.Service.Common
{
    public interface ICommonHelperService
    {
        ResponseDataInvalidTokenDto GetResponseInvalidToken(string authorization, string memberCodeInput);
        ResponseDataInvalidTokenDto GetResponseEmptyDataHeader();

        Task<ExchangeCodeForTokenOutput> GetAccessTokenFromCode(string fullUrl);

        void ApplyDecimalFormatting(object obj);
    }
}
