﻿using AKC.MobileAPI.DTO.Loyalty.Brand;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyBrandService
    {
        Task<GetBrandOutput> GetAll(GetBrandInput input);
        Task<GetBrandByCategoryOutput> GetAllBrandByCategoryCode(GetBrandInput input);
        Task<GetBrandByCategoryOutput> GetProminentBrand(GetProminentBrandInput input);
    }
}
