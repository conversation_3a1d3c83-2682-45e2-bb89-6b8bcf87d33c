﻿using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Webstore;
using Microsoft.AspNetCore.Http;
using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.GiftSearch;
using AKC.MobileAPI.DTO.Reward;
using AKC.MobileAPI.DTO.Webstore;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyGiftTransactionsService
    {
        Task<LoyaltyCreateRedeemTransactionOutput> CreateRedeemTransaction(LoyaltyCreateRedeemTransactionInput input, HttpContext context);
        Task<RedeemWithMoneyCardOutput> RedeemWithMoneyCard(RedeemWithMoneyCardInput input, HttpContext context);
        Task<GetGroupCodesOfGiftOutput> GetGroupCodesOfGift(string GiftCode);

        Task<LoyaltyGetAllWithEGiftOutput> GetAllWithEGift(LoyaltyGetAllWithEGiftInput input);
        Task<MerchantGiftTransactionHistoryOutputDto> GiftTransactionHistory(MerchantGiftTransactionDetailInputDto input);
        Task<GetSingleCreateGiftRedeemTransactionOutput> GetSingleCreateGiftRedeemTransaction(GetSingleCreateGiftRedeemTransactionInput input);

        Task<GetMemberCodeOutput> UserVerifying(GetMemberCodeInput input);

        Task<GiftTransferOutput> GiftTransfer(UpdateGiftTransactionsForTransferInput input);
        Task<LoyaltyCreateRedeemTransactionOutput> PostLoyaltyRedeem(LoyaltyCreateRedeemTransactionDto input, HttpContext context);
        Task<GiftTransactionUpdateLegalIdOutput> UpdateRecipientLegalId(GiftTransactionUpdateLegalIdInput input);
        Task<LoyaltyGiftGetMerchantIdFromGiftCodeOutput> GetMerchantIdFromGiftCode(LoyaltyGiftGetMerchantIdFromGiftCodeInput input);
        Task<LoyaltyGetAllWithTopupPhoneOutput> GetAllWithTopupPhone(LoyaltyGetAllWithTopupPhoneInput input);
        Task<LoyaltyGetAllWithTopupPhone_v1Output> GetAllWithTopupPhone_v1(LoyaltyGetAllWithTopupPhoneInput input);
        Task<GetCustomerInfoFromTransactionOutput> GetCustomerInfoFromTransaction(GetCustomerInfoFromTransactionInput input);
        Task<LoyaltyResponse<GetListHistoryReferralOutput>> GetListHistoryReferral(GetListHistoryReferralInput input);
        Task<LoyaltyResponse<GetAddressBy3IdsOutput>> GetAddressBy3Ids(GetAddressBy3IdsInput input);

        Task<LoyaltyResponse<GetTransactionInforReferralOutput>> GetTransactionInforReferral(GetTransactionInforReferralInput input);

        Task<VerifyAndCreateRedeemOrderOutput> VerifyOrCreateRedeemOrder(MerchantGiftCreateRedeemInWebInputDto input);

        Task<MerchantGiftCreateRedeemOutputDto> CreateRedeemMerchantGift(MerchantGiftCreateRedeemInWebInputDto input);

        Task retryWebstoreRevertToken(RewardMemberRevertRedeemInput request, string url);

        Task UpdateErrorWhenCreateRedeem(UpdateErrorWhenCreateRedeemPayment input, bool successPaymentToken);

        Task GetErrorFromExeption(Exception ex, string transactionCode, bool successPaymentToken);

        Task<LoyaltyGetGiftInfoOutput> GetGiftInfo(LoyaltyGetGiftInfoInput input);
    }
}
