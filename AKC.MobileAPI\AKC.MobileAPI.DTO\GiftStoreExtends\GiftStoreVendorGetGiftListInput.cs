﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.GiftStoreExtends
{
    public class GiftStoreVendorGetGiftListInput
    {
        [Required]
        public int MerchantId { get; set; }
        public int? CategoryId { get; set; }
        public int? BrandId { get; set; }
        public string Sorting { get; set; }
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
    }

    public class GiftStoreVendorGetGiftListDto
    {
        public string MemberCode { get; set; }
        public int? CategoryId { get; set; }
        public int? BrandId { get; set; }
        public string Sorting { get; set; }
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
    }
}
