﻿// using AKC.MobileAPI.DTO.Downstream;
// using AKC.MobileAPI.Service.Constants;
// using AKC.MobileAPI.Service.Exceptions;
// using Microsoft.Extensions.Caching.Distributed;
// using Microsoft.Extensions.Configuration;
// using Newtonsoft.Json;
// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Net;
// using System.Net.Http;
// using System.Text;
// using System.Threading;
// using System.Threading.Tasks;
// using System.Web;
//
// namespace AKC.MobileAPI.Service.Downstream
// {
//     public class BaseDownstreamService
//     {
//         protected readonly HttpClient _client = new HttpClient();
//         protected readonly IConfiguration _configuration;
//         private readonly string authenBaseURL;
//         private readonly string apiBaseURL;
//         protected readonly IDistributedCache _cache;
//
//         public BaseDownstreamService(IConfiguration configuration, IDistributedCache cache)
//         {
//             _client.Timeout = TimeSpan.FromSeconds(300);
//             _configuration = configuration;
//             authenBaseURL = _configuration.GetSection("Downstream:Authenticate:BaseURL").Value;
//             apiBaseURL = _configuration.GetSection("Downstream:Api:BaseURL").Value;
//             _cache = cache;
//         }
//
//         /// <summary>
//         /// Convert a object to query string format.
//         /// </summary>
//         /// <param name="obj"></param>
//         /// <returns></returns>
//         public string GetQueryString(object obj)
//         {
//             var properties = from p in obj.GetType().GetProperties()
//                              where p.GetValue(obj, null) != null
//                              select p.Name + "=" + HttpUtility.UrlEncode(p.GetValue(obj, null).ToString());
//
//             return string.Join("&", properties.ToArray());
//         }
//
//         /// <summary>
//         /// Get a new accessToken form downstream.
//         /// </summary>
//         /// <returns></returns>
//         private async Task<string> GetAccessToken(bool mustResetCache = false)
//         {
//             var token = await _cache.GetStringAsync(CommonConstants.DOWNSTREAM_ACCESSS_TOKEN_CACHE_KEY);
//             var configuration = AccessConfigurationService.Instance.GetConfiguration();
//
//             // Request body
//             if (string.IsNullOrEmpty(token) || mustResetCache)
//             {
//                 try
//                 {
//                     var json = JsonConvert.SerializeObject(new
//                     {
//                         grant_type = configuration.GetSection("Downstream:Authenticate:GrantType").Value
//                     }, new JsonSerializerSettings
//                     {
//                         NullValueHandling = NullValueHandling.Ignore
//                     });
//                     var dataRequest = new StringContent(json, Encoding.UTF8, "application/json");
//                     var req = new HttpRequestMessage
//                     {
//                         Method = HttpMethod.Post,
//                         RequestUri = new Uri($"{authenBaseURL}/oauth2/token"),
//                         Content = dataRequest
//                     };
//                     req.Headers.Add("Authorization", configuration.GetSection("Downstream:Authenticate:BasicToken").Value);
//
//                     var response = await _client.SendAsync(req);
//                     var responseData = await response.Content.ReadAsStringAsync();
//                     if (response.StatusCode == HttpStatusCode.OK)
//                     {
//                         var objResponse = JsonConvert.DeserializeObject<AuthenticateResponse>(responseData);
//                         if (!string.IsNullOrWhiteSpace(objResponse.access_token))
//                         {
//                             token = objResponse.access_token;
//                             await _cache.SetStringAsync(CommonConstants.DOWNSTREAM_ACCESSS_TOKEN_CACHE_KEY, token, new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromSeconds(objResponse.expires_in)));
//                         }
//                         else
//                         {
//                             throw new Exception("Token is null");
//                         }
//                     }
//                     else
//                     {
//                         throw new Exception($"Error: {responseData}");
//                     }
//                 }
//                 catch (Exception ex)
//                 {
//                     throw ex;
//                 }
//             }
//             return token;
//         }
//
//         //Clone a HttpRequest
//         protected HttpRequestMessage CloneHttpRequest(HttpRequestMessage req)
//         {
//             HttpRequestMessage clone = new HttpRequestMessage(req.Method, req.RequestUri);
//
//             clone.Content = req.Content;
//             clone.Version = req.Version;
//
//             foreach (KeyValuePair<string, object> prop in req.Properties)
//             {
//                 clone.Properties.Add(prop);
//             }
//
//             foreach (KeyValuePair<string, IEnumerable<string>> header in req.Headers)
//             {
//                 clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
//             }
//
//             return clone;
//         }
//
//         /// <summary>
//         /// Perform a GET request to downstream server.
//         /// </summary>
//         /// <param name="apiURL"></param>
//         /// <param name="query"></param>
//         /// <returns></returns>
//         public async Task<T> GetDownstreamAsync<T>(string apiURL, object query = null)
//         {
//             if (typeof(T) == typeof(string))
//             {
//                 throw new Exception("Not allow type of T is string");
//             }
//
//             var requestURL = $"{apiBaseURL}/{apiURL}";
//
//             if (query != null)
//             {
//                 requestURL = $"{requestURL}?{GetQueryString(query)}";
//             }
//
//             var req = new HttpRequestMessage
//             {
//                 Method = HttpMethod.Get
//             };
//
//             var token = await GetAccessToken();
//             req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
//             req.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
//             req.RequestUri = new Uri(requestURL);
//             var cts = new CancellationTokenSource();
//             try
//             {
//                 var response = await _client.SendAsync(req);
//                 var rawData = await response.Content.ReadAsStringAsync();
//
//                 //Recall API when Unauthorized
//                 if (response.StatusCode == HttpStatusCode.Unauthorized)
//                 {
//                     string accessToken = await GetAccessToken(true);
//                     req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
//                     req.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
//                     var reqClone = CloneHttpRequest(req);
//
//                     response = await _client.SendAsync(reqClone);
//                     rawData = await response.Content.ReadAsStringAsync();
//                 }
//                 //End Recall API when Unauthorized
//
//                 if (response.IsSuccessStatusCode == false)
//                 {
//                     var ex = new LoyaltyException();
//                     ex.Data.Add("ErrorData", rawData);
//                     if (response.StatusCode == HttpStatusCode.BadRequest)
//                     {
//                         ex.Data.Add("StatusCode", 400);
//                     }
//                     else
//                     {
//                         ex.Data.Add("StatusCode", 500);
//                     }
//                     throw ex;
//                 }
//
//                 response.EnsureSuccessStatusCode();
//
//
//                 // Convert response to result object which is a instance of 'T'.
//                 var result = JsonConvert.DeserializeObject<T>(rawData);
//                 return result;
//             }
//             catch (WebException ex)
//             {
//                 throw ex;
//             }
//             catch (TaskCanceledException ex)
//             {
//                 if (!cts.Token.IsCancellationRequested)
//                 {
//
//                     throw new Exception("Timed Out with API: " + req.RequestUri, ex);
//                 }
//                 else
//                 {
//                     throw new Exception("Cancelled for some other reason: ", ex);
//                 }
//             }
//         }
//     }
// }
