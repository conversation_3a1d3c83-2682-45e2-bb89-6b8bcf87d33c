using System;
using System.Collections.Generic;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;

namespace AKC.MobileAPI.DTO.Loyalty.NotificationHistory.V2
{
    public class BaseNotiV2Output
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
    }
    public class GetByMemberInput
    {
        public string MemberCode { get; set; }
        public string NotiCate { get; set; }
        public string Lang { get; set; }
    }

    public class MarkAsReadInput
    {
        public string MemberCode { get; set; }
        public string IdNotification { get; set; }
    }

    public class GetByMemberOutput : BaseNotiV2Output
    {
        public int Count { get; set; }
        public List<NotificationHistoryDtoV2> NotificationHistoryList { get; set; }
    }
    public class NotificationHistoryDtoV2
    {
        public string Id { get; set; }

        public string MemberCode { get; set; }

        public int? TenantId { get; set; }

        public NotificationTypeConst Key { get; set; }

        public string Title { get; set; }

        public string Content { get; set; }

        public string Action { get; set; }

        public string Data { get; set; }

        public string ImageLink { get; set; }

        public string Status { get; set; }

        public string Language { get; set; }

        public DateTime CreationTime { get; set; }

        public string IdNotification { get; set; }
        
        public bool IsRead { get; set; } 
    }

    public class MarkAsReadOutput : BaseNotiV2Output
    {
        
    }
    public class TokenTransDetailInput
    {
        public string TokenTransId { get; set; }
        public string MemberCode { get; set; }
        public string Lang { get; set; }
        public string Type { get; set; } = "TOKEN"; // TOKEN vs MONEYCARD
        public string OrderCode { get; set; } // Cần thiết khi mà là MONEYCARD
        public int? CardTransactionId { get; set; } // CASE MONEYCARD
    }

    public class TokenTransDetailOutput: BaseNotiV2Output
    {
        public TokenTransDetailInnerContent Result { get; set; }
    }

    public class GetMerchantInfoOutput
    {
        public string Name { get; set; }
        public int Id { get; set; }
        public string Logo { get; set; }
    }
    public class GetMerchantInfoInput
    {
        public string WalletAddress { get; set; }
        public int? MerchantId { get; set; }
    }
    public class ShortMerchantDto
    {
        public string WalletAddress { get; set; }
        public string MerchantName { get; set; }
        public int MerchantId { get; set; }
        public string MerchantIcon { get; set; }
        public string PartnerPointExchangeType { get; set; }
    }

    public class TokenTransDetailInnerContent
    {
        public string? Id { get; set; }
        public string TokenTransId { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public decimal Amount { get; set; }
        public string ActionType { get; set; }
        public string ActionCode { get; set; }
        public string OrderCode { get; set; }
        public string ContentPhoto { get; set; }
        public string DescriptionPhoto { get; set; }
        public string PartnerName { get; set; }
        public string PartnerIcon { get; set; }
        public string MemberCode { get; set; }
        public string WalletAddress { get; set; }
        public string ToWalletAddress { get; set; }
        public string FromWalletAddress { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime? ExpiredTime { get; set; }
        public string RelatedTokenTransId { get; set; }
        public string ServiceName { get; set; } // Cho case nap tien
        public string PackageName { get; set; } // Cho case nap tien
        public string CardValue { get; set; } // Cho case nap tien - Giá trị thẻ nạp 
        public string ToPhoneNumber { get; set; } // Cho case nap tien 
        public string UsageAddress { get; set; }
        public DateTime? LastModificationTime { get; set; }
        
        // Normal Gift Related Fields
        public string GiftName { get; set; }
        public string GiftId { get; set; }
        public string GiftImage { get; set; }
        public float GiftPaidCoin { get; set; }
        public DateTime? EGiftExpiredDate { get; set; }
        public string BrandName { get; set; }
        public string BrandImage { get; set; }
        public ThirdPartyGiftVendorShortDto Vendor { get; set; }
        public int RedeemQuantity { get; set; }
        
        // For case Exchange
        public decimal PartnerPointAmount { get; set; }
        public string BatchNote { get; set; }
        public string CampaignName { get; set; }
        public string InvitedMember { get; set; }
        public string PartnerPointExchangeType { get; set; }
        public string PartnerBindingTxId { get; set; }
        public string PartnerMemberFirstName { get; set; }
        public string PartnerMemberLastName { get; set; }
        public string AirlineMemberCode { get; set; }
        public string PromotionCode { get; set; }
        public string PromotionDate { get; set; }
        public string PromotionValue { get; set; }
    }

    public class ImediaDescriptionDtoForParsing
    {
        public string operation { get; set; }
        public string ownerphone { get; set; }
        public string accountType { get; set; }
    }
    
    public class TokenTransDetailForWebStoreOutput : BaseNotiV2Output
    {
        public TokenTransDetailInnerContentForWebStore Result { get; set; }
    }

    public class TokenTransDetailInnerContentForWebStore
    {
        public string? Id { get; set; }
        public string TokenTransId { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public decimal Amount { get; set; }
        public string ActionType { get; set; }
        public string ActionCode { get; set; }
        public string OrderCode { get; set; }
        public string ContentPhoto { get; set; }
        public string DescriptionPhoto { get; set; }
        public string PartnerName { get; set; }
        public string PartnerIcon { get; set; }
        public string MemberCode { get; set; }
        public string WalletAddress { get; set; }
        public string ToWalletAddress { get; set; }
        public string FromWalletAddress { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime ExpiredTime { get; set; }
        public string RelatedTokenTransId { get; set; }
        public string ServiceName { get; set; } // Cho case nap tien
        public string PackageName { get; set; } // Cho case nap tien
        public string CardValue { get; set; } // Cho case nap tien - Giá trị thẻ nạp 
        public string ToPhoneNumber { get; set; } // Cho case nap tien 
        public string UsageAddress { get; set; }
        public DateTime? LastModificationTime { get; set; }

        // Normal Gift Related Fields
        public string GiftName { get; set; }
        public string GiftId { get; set; }
        public string GiftImage { get; set; }
        public float GiftPaidCoin { get; set; }
        public DateTime? EGiftExpiredDate { get; set; }
        public string BrandName { get; set; }
        public string BrandImage { get; set; }
        public ThirdPartyGiftVendorShortDto Vendor { get; set; }
        public int RedeemQuantity { get; set; }

        // For case Exchange
        public decimal PartnerPointAmount { get; set; }
        public string BatchNote { get; set; }
        public string CampaignName { get; set; }
        public string InvitedMember { get; set; }
        public string PartnerPointExchangeType { get; set; }
        public string PartnerBindingTxId { get; set; }
        public string PartnerMemberFirstName { get; set; }
        public string PartnerMemberLastName { get; set; }
        public string AirlineMemberCode { get; set; }
        public string PromotionCode { get; set; }
        public string PromotionDate { get; set; }
        public string PromotionValue { get; set; }
    }
}