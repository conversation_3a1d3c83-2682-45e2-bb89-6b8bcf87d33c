﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class GetQuestDetailInputDto
    {
        [Required]
        public string QuestCodeFilter { get; set; }
        [Required]
        public string MemberCode { get; set; }

        public string MissionCodeFilter { get; set; }
        public string MissionStatusFilter { get; set; }

        public virtual string Sorting { get; set; }

        public virtual int SkipCount { get; set; }

        public int MaxResultCount { get; set; }
    }
}
