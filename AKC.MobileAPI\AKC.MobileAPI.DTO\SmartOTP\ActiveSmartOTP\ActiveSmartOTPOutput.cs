﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.SmartOTP.ActiveSmartOTP
{
    public class ActiveSmartOTPOutput
    {
        public bool Success { get; set; }
        public int Code { get; set; }
        public string Message { get; set; }
        public ActiveSmartOTPDataOutput Data { get; set; }
    }

    public class ActiveSmartOTPDataOutput
    {
        public string Id { get; set; }
        public string SecretKey { get; set; }
        public int Period { get; set; }
        public int Length { get; set; }
        public string Issuer { get; set; }
        public string ConfigType { get; set; }
        public string Algorithm { get; set; }
    }

    public class ActiveSmartOtpDataResponse
    {
        public string Key { get; set; }
        public int? Period { get; set; }
        public string Algorithm { get; set; }
        public int? Length { get; set; }
        public string Type { get; set; }
        public string Issuer { get; set; }
        public string Id { get; set; }
    }

    public class ActiveSmartOTPResponse
    {
        public bool Success { get; set; }
        public int Code { get; set; }
        public string Message { get; set; }
        public ActiveSmartOtpDataResponse Data { get; set; }
    }
}
