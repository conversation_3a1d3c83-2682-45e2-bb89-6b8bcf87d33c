﻿using AKC.MobileAPI.DTO.Reward.Merchant;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Reward
{
    public interface IRewardMerchantService
    {
        Task<RewardGetAllMerchantExchangeOutput> GetListExchangeMerchant(RewardGetAllMerchantExchangeInput input);
        Task<RewardGetListExchangeMerchantConnectedOutput> GetListExchangeMerchantByMember(RewardGetListExchangeMerchantConnectedInput input);
        Task<RewardGetAllMerchantOutput> GetAll(RewardGetAllMerchantInput input);
        Task<GetFullInfoMerchantByIdOutput> GetFullInfoMerchantById(GetFullInfoMerchantByIdInput input);
        Task<RewardGetAllMerchantPaymentOutput> GetAllMerchantPayment(RewardGetAllMerchantPaymentInput input);
        Task<RewardMerchantGetListMerchantByIdsOutput> GetListMerchantByIds(RewardMerchantGetListMerchantByIdsInput input);
        Task<RewardGetCurrentMerchantResponse> GetCurrentMerchantRequest(RewardGetCurrentMerchantRequest input);
    }
}
