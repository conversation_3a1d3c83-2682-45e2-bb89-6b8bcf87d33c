﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.GiveToken
{
    public class RewardGiveTokenTransferTransactionOutput
    {
        public int Result { get; set; }
        public RewardGiveTokenTransferTransactionItem Items { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }

    public class RewardGiveTokenTransferTransactionItem
    {
        public string SenderCode { get; set; }
        public string ReceiverCode { get; set; }
        public DateTime Time { get; set; }
        public decimal Amount { get; set; }
        public string Message { get; set; }
    }
}
