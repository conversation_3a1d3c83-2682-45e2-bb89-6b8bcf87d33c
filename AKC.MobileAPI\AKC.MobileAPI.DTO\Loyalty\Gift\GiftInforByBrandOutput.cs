﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class GiftInforByBrandOutput
    {
        public GetGiftByBrand Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class GetGiftByBrand
    {
        public BrandInforDto BrandInfor { get; set; }
        public ListGiftInforByBrandDto Gifts { get; set; }
    }

    public class BrandInforDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string LinkLogo { get; set; }
        public bool? IsFavourite { get; set; } = false;
        public string CoverPhoto { get; set; }
        public int? DisplayOrder { get; set; }
    }

    public class ListGiftInforByBrandDto
    {
        public int TotalCount { get; set; }

        public List<GiftInforByBrandDto> Items { get; set; }
    }
    public class GiftInforByBrandDto
    {
        public long Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Introduce { get; set; }
        public string FullGiftCategoryCode { get; set; }
        public string ThirdPartyBrandName { get; set; }
        public int? BrandId { get; set; }
        public string BrandName { get; set; }
        public string Vendor { get; set; }
        public int? VendorId { get; set; }
        public decimal RequiredCoin { get; set; }
        public string Status { get; set; }
        public decimal TotalQuantity { get; set; }
        public decimal UsedQuantity { get; set; }
        public decimal RemainingQuantity { get; set; }
        public decimal FullPrice { get; set; }
        public decimal DiscountPrice { get; set; }
        public bool IsEGift { get; set; }
        public bool Is3rdPartyGift { get; set; }
        public bool Selected { get; set; }
        public string ThirdPartyCategoryCode { get; set; }
        public string ThirdPartyGiftCode { get; set; }
        public int TotalWish { get; set; }
        public string ThirdPartyCategoryId { get; set; }
        public string ThirdPartyCategoryName { get; set; }
        public string ImageLink { get; set; }
    }
}
