﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class LoyaltyGiftGetAllByMemberCodeInput
    {
        [Required]
        public string MemberCode { get; set; }

        public string GiftCategoryCodeFilter { get; set; }

        public string GiftGroupCodeFilter { get; set; }

        public int? GiftGroupTypeFilter { get; set; }

        public string StatusFilter { get; set; }

        public string ProducerFilter { get; set; }

        public string VendorFilter { get; set; }

        public bool? IsEGiftFilter { get; set; }

        public string TagFilter { get; set; }

        public decimal MaxRequiredCoinFilter { get; set; }

        public string Sorting { get; set; }
        public int SkipCount { get; set; } = 0;

        public int MaxResultCount { get; set; } = int.MaxValue;

        public bool ShowMaket { get; set; } = true;

        public bool ShowNonMaketGift { get; set; } = false;

        public bool ShowTypeTopUpPhone { get; set; } = false;

        public string ChannelCode { get; set; } = "LINKID";
    }

    public class GetRecommendedGiftsInput
    {
        [Required]
        public string MemberCode { get; set; }
        public string GiftCategoryCodeFilter { get; set; }
        public string GiftGroupCodeFilter { get; set; }
        public int? GiftGroupTypeFilter { get; set; }
        public string StatusFilter { get; set; }
        public string VendorFilter { get; set; }
        public bool? IsEGiftFilter { get; set; }
        public decimal? FromCoinFilter { get; set; }
        public decimal? ToCoinFilter { get; set; }
        public string Sorting { get; set; }
        public int SkipCount { get; set; } = 0;
        public int MaxResultCount { get; set; } = 20;
        public string ChannelCode { get; set; } = "LINKID"; //
        public string GiftNameFilter { get; set; }
        public string RegionCodeFilter { get; set; }
    } //
}
