﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/cs-ticket")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    //[Authorize]
    public class CSTicketController : ControllerBase
    {
        private readonly ILogger _logger;
        protected readonly IConfiguration _configuration;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly ILoyaltyGiftService _loyaltyGiftService;
        private readonly IUploadImageSevice _uploadImageSevice;
        public CSTicketController(
        ILogger<CSTicketController> logger,
        IExceptionReponseService exceptionReponseService,
        ILoyaltyGiftService ls,
        ICommonHelperService ch,
        IUploadImageSevice ud,
        IConfiguration cf
        )
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _loyaltyGiftService = ls;
            _commonHelperService = ch;
            _uploadImageSevice = ud;
            _configuration = cf;
        }

        [HttpPost]
        [Route("upload-image")]
        [AllowAnonymous]
        public async Task<ActionResult<CreateCSTicketOutput>> UploadImage([FromForm]UploadImageCSTicketInput input)
        {
            try
            {
                var validContentTypes = new[] { "image/jpeg", "image/png", "image/gif" };
                var validExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var file = input.File;
                if (file == null || file.Length == 0)
                {
                    return StatusCode(400, new
                    {
                        Status = "Fail", Message = "Invalid Input File"
                    });
                }

                var sizeLimitStr = _configuration.GetSection("FileUploadMaxSizeInByte").Value;
                var maxSizeInBytes = 5 * 1024 * 1024; // 5 MB
                if (sizeLimitStr == null)
                {
                    if (int.TryParse(sizeLimitStr, out var parsed))
                    {
                        maxSizeInBytes = parsed;
                    }
                }
                
                if (file.Length > maxSizeInBytes)
                {
                    return BadRequest(new { Status = "Fail", Message = "File size exceeds the maximum allowed size of 5 MB." });
                }
                if (file.ContentType == null || !file.ContentType.StartsWith("image/"))
                {
                    return StatusCode(400, new
                    {
                        Status = "Fail", Message = "Invalid Content Type"
                    });
                }

                if (!validExtensions.Any(ext => file.FileName.EndsWith(ext, StringComparison.OrdinalIgnoreCase)))
                {
                    return StatusCode(400, new
                    {
                        Status = "Fail", Message = "Invalid File Extension"
                    });
                }
                _logger.LogInformation(" >> UploadImage >> " + input.File.FileName);
                var result = await _uploadImageSevice.UploadImage(file);
                return StatusCode(200, new
                {
                    Status = "OK", Message = "Success", Url = result
                });
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "CreateCSTicketAnnonymously Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        [HttpPost]
        [Route("create-anonymously")]
        [AllowAnonymous]
        public async Task<ActionResult<CreateCSTicketOutput>> CreateCSTicketAnnonymously(CreateCSTicketInput input)
        {
            try
            {
                _logger.LogInformation(" >> CreateCSTicketAnnonymously >> " + JsonConvert.SerializeObject(input));
                var result = await _loyaltyGiftService.CreateCSTicket(input);
                return StatusCode(200, new
                {
                    Status = "OK", Message = "Success", Result = result
                });
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "CreateCSTicketAnnonymously Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        
        [HttpPost]
        [Route("create")]
        [Authorize]
        public async Task<ActionResult<CreateCSTicketOutput>> CreateCSTicket(CreateCSTicketInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.CreateCSTicket(input);
                return StatusCode(200, new
                {
                    Status = "OK", Message = "Success", Result = result
                });
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "CreateCSTicket Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        [HttpPost]
        [Route("sync-stringeex")]
        [Authorize]
        public async Task<ActionResult<SyncStringeeXOutput>> SyncStringeeX(SyncStringeeXInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                SyncStringeeXOutput result = await _loyaltyGiftService.SyncStringeeX(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "SyncStringeeX Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        [HttpGet]
        [Route("get-list")]
        [Authorize]
        public async Task<ActionResult<GetListCSTicketOutput>> GetList([FromQuery]GetListCSTicketInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetListCSTicket(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetList Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        [HttpGet]
        [Route("get-one")]
        [Authorize]
        public async Task<ActionResult<GetOneCSTicketOutput>> GetOne([FromQuery]GetOneCSTicketInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetOne(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetOne Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }


        [HttpPost]
        [Route("ReactCSTicket")]
        [Authorize]
        public async Task<ActionResult<ReactCSTicketOutput>> ReactCSTicket(ReactCSTicketInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.ReactCSTicket(new ReactCSTicketDTO
                {
                    ID = input.ID,
                    React = input.React
                }) ;
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetOne Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}
