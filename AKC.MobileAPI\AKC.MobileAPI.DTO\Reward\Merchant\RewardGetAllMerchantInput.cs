﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Merchant
{
    public class RewardGetAllMerchantInput
    {
        public string StatusFilter { get; set; }
        public string TypeFilter { get; set; }
        public string sorting { get; set; }
        public int maxResultCount { get; set; }
        public int skipCount { get; set; }
        public string PhoneNumber { get; set; }
    }

    public class GetFullInfoMerchantByIdInput
    {
        public int? MerchantId { get; set; }
        public string WalletAddress { get; set; }
    }

    public class GetFullInfoMerchantByIdOutput: RewardGetAllMerchantItems
    {
    }
}
