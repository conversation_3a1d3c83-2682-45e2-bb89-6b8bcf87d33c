﻿using Azure.Core;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Helpers
{
    public static class SignatureHelper
    {
        public static string DoSignature(dynamic body, string secretKey)
        {
            if (body != null)
            {
                var bodyString = JsonConvert.SerializeObject(body);
                var secretBytes = Encoding.UTF8.GetBytes(secretKey);
                var hash = new StringBuilder();
                byte[] inputBytes = Encoding.UTF8.GetBytes(bodyString);
                using (var hmac = new HMACSHA256(secretBytes))
                {
                    byte[] hashValue = hmac.ComputeHash(inputBytes);
                    foreach (var theByte in hashValue)
                    {
                        hash.Append(theByte.ToString("x2"));
                    }
                }
                return hash.ToString();
            }
            return string.Empty;
        }

        public static bool ValidateSignature(dynamic body, string inputHash, string secretKey)
        {
            string myChecksum = DoSignature(body, secretKey);
            return myChecksum.Equals(inputHash, StringComparison.InvariantCultureIgnoreCase);
        }

        public static Tuple<ExpandoObject, string> CreateObject(this Dictionary<string, object> dictionary)
        {
            string signature = string.Empty;
            dynamic expandoObj = new ExpandoObject();
            var expandoObjCollection = (ICollection<KeyValuePair<String, Object>>)expandoObj;

            foreach (var keyValuePair in dictionary)
            {
                if (keyValuePair.Key != null && keyValuePair.Key.ToLower() != "checksum")
                {
                    expandoObjCollection.Add(keyValuePair);
                }
                else if (keyValuePair.Key != null && keyValuePair.Key.ToLower() == "checksum")
                {
                    signature = keyValuePair.Value.ToString();
                }
            }
            dynamic eoDynamic = expandoObj;

            return Tuple.Create(eoDynamic, signature);
        }
    }
}
