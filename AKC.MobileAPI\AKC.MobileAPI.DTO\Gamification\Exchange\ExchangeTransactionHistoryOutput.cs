﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Gamification.Exchange
{
    public class ExchangeTransactionHistoryOutput
    {
        public int result { get; set; }
        public List<ExchangeTransactionHistoryOutputDto> item { get; set; }
        public int totalCount { get; set; }
        public string message { get; set; }
        public string messageDetail { get; set; }
     }
    public class ExchangeTransactionHistoryOutputDto
    {
        public int? ID { get; set; }
        public int? ExchangeRuleID { get; set; }
        public int? Repeat { get; set; }
        public int? MemberID { get; set; }
        public string MemberCode { get; set; }
        public int? TenantID { get; set; }
        public int? ItemID { get; set; }
        public int? GameMasterID { get; set; }
        public int? GameID { get; set; }
        public List<ExchangeItemDto> ItemList { get; set; }
    }
    public class ExchangeItemDto
    {
        public int? ID { get; set; }
        public int? RuleID { get; set; }
        public int? ItemID { get; set; }
        public int? Quantity { get; set; }
        public int? Type { get; set; }
        public string Icon { get; set; }
    }
}
