﻿using AKC.MobileAPI.DTO.Gamification;
using AKC.MobileAPI.DTO.Gamification.Game;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.Service.Abstract.Gamification;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Gamification
{
    public class MemberManagementService : MemberManagementBaseService, IMemberManagementService
    {
        ILoyaltySecondaryCustomersService _customersService;
        private readonly IDistributedCache _cache;

        public MemberManagementService(IConfiguration configuration,
                ILoyaltySecondaryCustomersService customersService, IDistributedCache cache
            )
            : base(configuration, cache)
        {
            _customersService = customersService;
            _cache = cache;
        }

        public async Task<ViewGameItemsOutput> ViewGameItems(ViewGameItemsInput input)
        {
            return await GetGamificationMemberManagementAsync<ViewGameItemsOutput>(GamificationApiUrl.VIEW_GAME_ITEM, input);
        }

        public async Task<MemberPlayGameOutputDto> MemberPlayGame(MemberPlayGameInputDto input)
        {
            MemberPlayGameOutputDto result;

            var customerProfile = await _customersService.GetProfileByCode(input.MemberCode);

            if (customerProfile?.Result?.MemberLoyaltyInfo != null)
            {
                // Play Game
                var playGameInput = new MemberPlayGameDto
                {
                    TenantID = tenantId,
                    GameCode = input.GameCode,
                    MemberCode = input.MemberCode,
                    Status = customerProfile.Result.Status,
                    FirstName = customerProfile.Result.FirstName,
                    LastName = customerProfile.Result.LastName,
                    Email = customerProfile.Result.Email,
                    Phone = customerProfile.Result.Phone,
                    Avatar = customerProfile.Result.Avatar
                };
                return await PostGamificationMemberManagementAsync<MemberPlayGameOutputDto>(GamificationApiUrl.MEMBER_PLAY_GAME, playGameInput);
            }
            else
            {
                result = new MemberPlayGameOutputDto
                {
                    message = "error",
                    messageDetail = "Not found user",
                    result = 404,
                    totalCount = 0,
                    item = null
                };
            }
            return result;
        }
        public async Task<MemberPlayGameOutputDto> CreateMember(GetGamificationLinkInput input)
        {
            var customerProfile = new LoyaltyResponse<SecondaryCustomerDto>();
            customerProfile = await _customersService.GetProfileByCode(input.MemberCode);

            if (customerProfile?.Result?.MemberLoyaltyInfo != null)
            {
                // Play Game
                var playGameInput = new CreateMemberDto
                {
                    TenantID = tenantId,
                    MemberCode = input.MemberCode,
                    Status = customerProfile.Result.Status,
                    FirstName = customerProfile.Result.FirstName,
                    LastName = customerProfile.Result.LastName,
                    Email = customerProfile.Result.Email,
                    Phone = customerProfile.Result.Phone,
                    Avatar = customerProfile.Result.Avatar
                };
                return await PostGamificationMemberManagementAsync<MemberPlayGameOutputDto>(GamificationApiUrl.CREATE_MEMBER, playGameInput);
            }
            else
            {
                var result = new MemberPlayGameOutputDto
                {
                    message = "error",
                    messageDetail = "Not found user",
                    result = 404,
                    totalCount = 0,
                    item = null
                };
                return result;
            }
        }
    }
}
