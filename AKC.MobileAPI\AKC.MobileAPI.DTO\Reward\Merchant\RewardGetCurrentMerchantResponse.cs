﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Merchant
{
    public class RewardGetCurrentMerchantResponse
    {
        public int MerchantId { get; set; }
        public string WalletAddress { get; set; }
        public string MerchantName { get; set; }
        public string OrgName { get; set; }
        public string Address { get; set; }
        public string TaxId { get; set; }
        public string LoyaltyProgramName { get; set; }
        public string Logo { get; set; }
        public string LogoEndUser { get; set; }
        public string Email { get; set; }
        public string IP { get; set; }
        public string Status { get; set; }
        public string AuthenIP { get; set; }
        public int TokenAmount { get; set; }
        public int GiftCardOnHold { get; set; }
        public string Type { get; set; }
        public int PointExchangeRate { get; set; }
        public int BaseUnit { get; set; }
        public double MinBalanceWhenExchangeOrGrant { get; set; }
    }
}
