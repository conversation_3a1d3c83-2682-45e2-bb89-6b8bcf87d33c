using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Merchant
{
    public class RewardGetListExchangeMerchantConnectedOutput
    {
        public int Result { get; set; }
        public string Message { get; set; }
        public List<GetListExchangeMerchantConnectedItem> Items { get; set; }
    }

    public class GetListExchangeMerchantConnectedItem
    {
        public int MerchantId { get; set; }
        public string MerchantName { get; set; }
        public string Logo { get; set; }
        public decimal BaseUnit { get; set; }
        public int? Priority { get; set; }
        public decimal PointExchangeRate { get; set; }
        public decimal Balance { get; set; }
        public bool BindingStatus { get; set; }
        public bool IsChangedLoyalty { get; set; }
        // These information will be masked
        public string PartnerPhoneNumber { get; set; }
        public string PartnerIdCard { get; set; }
        public string IsVerified { get; set; }
        public DateTime? CreatedAt { get; set; }
        public string ConnectSource { get; set; }
        public string CoinIcon { get; set; }
    }
}
