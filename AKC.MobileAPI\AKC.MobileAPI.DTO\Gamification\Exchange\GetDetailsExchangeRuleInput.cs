﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Gamification.Exchange
{
    public class GetDetailsExchangeRuleInput
    {
        public int? ID { get; set; }
        public int? RuleID { get; set; }
        public int? ItemID { get; set; }
        public int? Quantity { get; set; }
        public int? Type { get; set; }
        public int? Offset { get; set; }
        public int? Limit { get; set; }
    }
}
