﻿using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.Loyalty
{
    public class GetTransactionForCampaignOutput
    {
        public GetTransactionForCampaignResult Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class GetTransactionForCampaignResult
    {
        public List<GetTransactionForCampaignWithDate> transaction { get; set; }
    }

    public class GetTransactionForCampaignWithDate
    {
        public string CreatedDate { get; set; }
        public List<TransactionForCampaign> ListTransaction { get; set; }
    }

    public class TransactionForCampaign
    {
        public string Name { get; set; }
        public decimal Coin { get; set; }
    }
}
