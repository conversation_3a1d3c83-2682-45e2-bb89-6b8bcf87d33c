﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.ApiSMS;
using AKC.MobileAPI.Service.Abstract.ApiSMS;
using Microsoft.AspNetCore.Mvc;

namespace AKC.MobileAPI.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class ApiSMSController : ControllerBase
    {
        private readonly IApiSMSService _apiSMSService;
        public ApiSMSController(IApiSMSService apiSMSService)
        {
            _apiSMSService = apiSMSService;
        }

        [HttpPost]
        public async Task<ResponseApiSMSModel> GetOTP(List<SMSModel> input)
        {
            return await _apiSMSService.SendSMS(input);
        }
    }
}
