﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.EarningRule
{
    public class EarningRuleOutput
    {
        public List<EarningRuleResult> result { get; set; }
        public object targetUrl { get; set; }
        public bool success { get; set; }
        public object error { get; set; }
        public bool unAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class EarningRuleResult
    {
        public int? Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string MerchantName { get; set; }
        public string Description { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public string Status { get; set; }
        public string RankType { get; set; }
        public string Content { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public long? LastModifierUserId { get; set; }
        public string LastModifierUserName { get; set; }
    }
}
