﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class GetAllQuestStageOutputDto
    {
        public ListResultGetAllQuestStage Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }


    public class ListResultGetAllQuestStage
    {
        public int TotalCount { get; set; }

        public List<ListGetAllQuestStageItems> Items { get; set; }

    }

    public class ListGetAllQuestStageItems
    {
        public QuestStageDto QuestStage { get; set; }

        // public int QuestGroupId { get; set; }
    }

    public class QuestStageDto
    {
        public int Id { get; set; }

        public string Code { get; set; }

        public string Name { get; set; }

        public virtual string Description { get; set; }

        public virtual string LinkAvatar { get; set; }

        public string Status { get; set; }

        public DateTime FromDate { get; set; }

        public DateTime ToDate { get; set; }

        public decimal Point { get; set; }

        public decimal Coin { get; set; }
    }
}
