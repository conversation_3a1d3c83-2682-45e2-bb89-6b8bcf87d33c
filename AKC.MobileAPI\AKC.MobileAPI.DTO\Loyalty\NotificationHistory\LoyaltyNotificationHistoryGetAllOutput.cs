﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.NotificationHistory
{
    public class LoyaltyNotificationHistoryGetAllOutput
    {
        public ListResultGetAllNotificationHistoryOutput Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }

    public class ListResultGetAllNotificationHistoryOutput
    {
        public NotificationSettingModel NotificationSetting { get; set; }
        public int TotalUnreadCount { get; set; }
        public DataResultGetAllNotificationHistory Items { get; set; }
    }
    public class DataResultNotificationHistory
    {
        public NotificationHistoryDto Notification { get; set; }
    }
    public class DataResultGetAllNotificationHistory
    {
        public int TotalCount { get; set; }
        public List<DataResultNotificationHistory> Items { get; set; }
    }

    public class NotificationHistoryDto
    {
        public long Id { get; set; }

        public string MemberCode { get; set; }

        public int? TenantId { get; set; }

        public NotificationTypeConst Key { get; set; }

        public string Title { get; set; }

        public string Content { get; set; }

        public string Action { get; set; }

        public string Data { get; set; }

        public string ImageLink { get; set; }

        public bool Status { get; set; }

        public string Language { get; set; }

        public DateTime CreationTime { get; set; }

        public string IdNotification { get; set; }
    }

    public enum NotificationTypeConst
    {
        personal = 1,
        public_user = 2,
        public_gift = 3,
        public_quest = 4
    }

    public class NotificationSettingModel
    {
        public bool public_user { get; set; }
        public bool personal { get; set; }
        public bool public_gift { get; set; }
    }
}
