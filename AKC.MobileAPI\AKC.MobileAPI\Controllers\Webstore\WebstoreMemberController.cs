using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory.V2;
using AKC.MobileAPI.DTO.Reward;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.Merchant;
using AKC.MobileAPI.DTO.Webstore;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.NotiV2;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using AKC.MobileAPI.Service.Webstore;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Webstore
{
    [ApiController]
    [Route("api/webstore/member")]
    [ApiConventionType(typeof(DefaultApiConventions))]
    public class WebstoreMemberController : ControllerBase
    {
        private readonly IDistributedCache _cache;
        private readonly IConfiguration _config;
        private readonly ILoyaltyAuditLogService _loggingService;
        private readonly IRewardMemberService _rewardService;
        private readonly IRewardMerchantService _rewardMerchantService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly INotificationHistoryV2Service _notificationHistoryV2Service;
        private readonly ICommonHelperService _commonHelperService;
        private readonly ILoyaltyGiftTransactionsService _giftTransactionsService;
        private readonly ILogger<WebstoreMemberController> _logger;

        public WebstoreMemberController(
            IRewardMemberService rewardService,
            IDistributedCache cache, IConfiguration config,
            ILoyaltyAuditLogService loggingService,
            ILogger<WebstoreMemberController> lg,
            ICommonHelperService cm,
            INotificationHistoryV2Service nbm,
            IExceptionReponseService exs,
            ILoyaltyGiftTransactionsService loygift,
            IRewardMerchantService rms)
        {
            _rewardService = rewardService;
            _cache = cache;
            _config = config;
            _rewardMerchantService = rms;
            _loggingService = loggingService;
            _logger = lg;
            _notificationHistoryV2Service = nbm;
            _exceptionReponseService = exs;
            _commonHelperService = cm;
            _giftTransactionsService = loygift;
        }

        [HttpGet("view-point")]
        public async Task<IActionResult> SmeViewPoint([FromQuery] string SmeCode)
        {
            if (string.IsNullOrWhiteSpace(SmeCode))
            {
                return BadRequest("SmeCode is required");
            }

            var cacheKey = $"WEBSTORE_SME_BALANCE_{SmeCode}";

            var cachedBalance = await _cache.GetStringAsync(cacheKey);
            WebstoreGetSmeBalanceItem balanceItem;

            if (cachedBalance != null)
            {
                balanceItem = JsonConvert.DeserializeObject<WebstoreGetSmeBalanceItem>(cachedBalance);
            }
            else
            {
                var info = await _rewardService.WebstoreGetSmeBalance(new WebstoreGetSmeBalanceRequest()
                {
                    SmeCode = SmeCode
                });

                if (info?.Item == null)
                {
                    return BadRequest(WebstoreBaseOutputDto<WebstoreGetSmeBalanceItem>.Error("500", "Internal server error"));
                }
                balanceItem = info.Item;
                var serializedBalance = JsonConvert.SerializeObject(balanceItem);
                await _cache.SetStringAsync(cacheKey, serializedBalance, new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1)
                });
            }

            return Ok(WebstoreBaseOutputDto<WebstoreGetSmeBalanceItem>.Success(balanceItem));
        }

        [HttpGet("sme/view-full-info")]
        public async Task<ActionResult<WebstoreBaseOutputDto<WebstoreGetFullSmeInfoItem>>> SmeGetFullInfo([FromQuery] string smeCode)
        {
            try
            {
                var memberCode = HttpContext.Items["MemberCode"]?.ToString();
                if (string.IsNullOrEmpty(memberCode) || memberCode != smeCode)
                {
                    return BadRequest(WebstoreBaseOutputDto<GetByMemberOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotMatch_Token, "Invalid Token"));
                }
                var smeObj = await _rewardService.WebstoreGetFullSmeInfo(new WebstoreGetFullSmeInfoRequest()
                {
                    SmeCode = smeCode
                });
                if (smeObj != null && smeObj.Item != null)
                {
                    return Ok(WebstoreBaseOutputDto<WebstoreGetFullSmeInfoItem>.Success(smeObj.Item));
                }

                return BadRequest(WebstoreBaseOutputDto<WebstoreGetFullSmeInfoItem>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotFound, "Not found"));
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "SmeGetFullInfo Error - " + ex.Message + " - " + ex.StackTrace);
                return BadRequest(WebstoreBaseOutputDto<WebstoreGetSmeInfoResponseItem>.Error(res.Code, res.Message));
            }
        }
        
        [HttpPost]
        [Route("mark-as-read")]
        public async Task<ActionResult<MarkAsReadOutput>> MarkAsRead([FromBody] MarkAsReadInput input)
        {
            try
            {
                var memberCode = HttpContext.Items["MemberCode"]?.ToString();
                if (string.IsNullOrEmpty(memberCode) || memberCode != input.MemberCode)
                {
                    return BadRequest(WebstoreBaseOutputDto<GetByMemberOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotMatch_Token, "Invalid Token"));
                }
                var result = await _notificationHistoryV2Service.MarkAsRead(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "MarkAsRead Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        
        [HttpGet]
        [Route("get-notification-by-member")]
        public async Task<ActionResult<WebstoreBaseOutputDto<WebstoreGetNotificationByMemberOutput>>> GetNotificationByMember([FromQuery] WebstoreGetNotificationByMemberInput input)
        {
            try
            {
                var memberCode = HttpContext.Items["MemberCode"]?.ToString();
                if (string.IsNullOrEmpty(memberCode) || memberCode != input.MemberCode)
                {
                    return BadRequest(WebstoreBaseOutputDto<GetByMemberOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotMatch_Token, "Access Token Không Hợp Lệ"));
                }
                var result = await _notificationHistoryV2Service.GetByMember(new GetByMemberInput()
                {
                    MemberCode = input.MemberCode, Lang = input.Lang, NotiCate = input.NotiCate
                });
                return Ok(WebstoreBaseOutputDto<WebstoreGetNotificationByMemberOutput>.Success(new WebstoreGetNotificationByMemberOutput()
                {
                    NotificationHistoryList = result.NotificationHistoryList, Count = result.Count
                }));
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetNotificationByMember Error - " + JsonConvert.SerializeObject(ex));
                return BadRequest(WebstoreBaseOutputDto<WebstoreGetNotificationByMemberOutput>.Error(res.Code, res.Message));
            }
        }

        
        [HttpGet("get-transaction-history")]
        public async Task<ActionResult<WebstoreBaseOutputDto<GetAllTokenTransRespone>>> GetTransactionHistory([FromQuery] GetTransactionHistoryInput input)
        {
            _logger.LogInformation($"WebstoreMemberController_SmeGetTransactionHistory_Input {JsonConvert.SerializeObject(input)}");
            try
            {
                var memberCode = HttpContext.Items["MemberCode"]?.ToString();
                if (string.IsNullOrEmpty(memberCode) || memberCode != input.MemberCode)
                {
                    return BadRequest(WebstoreBaseOutputDto<GetByMemberOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotMatch_Token, "Access Token Không Hợp Lệ"));
                }
                List<string> glstTransactionTypeValid = new List<string> { "EarnedPoints", "UsedPoints" };
                if (!string.IsNullOrEmpty(input.TransactionType) && !glstTransactionTypeValid.Contains(input.TransactionType))
                {
                    return BadRequest("TransactionType invalid");
                }

                var cacheKey = $"WebAppTokenTrans_{input.MemberCode}_{input.MemberType}_{input.FromDateFilter?.ToString("yyyyMMddHHmmss")}_{input.ToDateFilter?.ToString("yyyyMMddHHmmss")}_{input.TransactionType}_{input.SkipCount}_{input.MaxResultCount}";

                if (!string.IsNullOrEmpty(input.OrderCode))
                {
                    cacheKey = $"WebAppTokenTrans_{input.MemberCode}_{input.MemberType}_{input.OrderCode}";
                }
                // Try to get from cache
                var cachedResult = await _cache.GetStringAsync(cacheKey);
                GetAllTokenTransRespone ret;

                if (cachedResult != null)
                {
                    ret = JsonConvert.DeserializeObject<GetAllTokenTransRespone>(cachedResult);
                }
                else
                {
                    string searchFromDate = null;
                    string searchToDate = null;
                    if (input.FromDateFilter.HasValue)
                    {
                        searchFromDate = input.FromDateFilter.Value.ToString("yyyy-MM-ddTHH:mm:ssZ");
                    }
                    if (input.ToDateFilter.HasValue)
                    {
                        searchToDate = input.ToDateFilter.Value.ToString("yyyy-MM-ddTHH:mm:ssZ");
                    }

                    var actionTypeFilter = "";
                    if (input.TransactionType == "UsedPoints")
                    {
                        actionTypeFilter = "PayByToken;Redeem;CashedOut;CashOutFee";
                    }
                    if (input.TransactionType == "EarnedPoints")
                    {
                        actionTypeFilter = "Action;BatchManualGrant;Order;SingleManualGrant;Topup";
                    }

                    if ("SME".Equals(input.MemberType))
                    {
                        ret = await _rewardService.GetALLTokenTransBySmeCif(new GetALLTokenTransBySmeCifInput
                        {
                            SmeCif = input.MemberCode,
                            FromDateFilter = searchFromDate,
                            ToDateFilter = searchToDate,
                            skipCount = input.SkipCount ?? 0,
                            maxResultCount = input.MaxResultCount ?? 10,
                            ActionTypeFilter = actionTypeFilter,
                            OrderCodeFilter = input.OrderCode
                        });
                    }
                    else
                    {
                        throw new Exception("MemberTypeNotSupported");
                    }
                    if (ret != null && ret.items != null && ret.items.Count > 0)
                    {
                        foreach (var itemRet in ret.items)
                        {
                            var title = "Thay đổi số dư";
                            var image = "";
                            var partnerWallet = itemRet.UserAddress == itemRet.FromWalletAddress
                                ? itemRet.ToWalletAddress
                                : itemRet.FromWalletAddress;
                            
                            var partnerObj = await GetMerchantInfoByWallet(partnerWallet);
                            if (partnerObj != null)
                            {
                                itemRet.MerchantName = partnerObj.MerchantName;
                                itemRet.MerchantLogo = partnerObj.MerchantIcon;
                                if (itemRet.ActionType == "Redeem" || itemRet.ActionType == "CashedOut")
                                {
                                    title = "Đổi quà thành công";
                                }

                                if (itemRet.ActionCode == "CashOutFee")
                                {
                                    title = "Phí đổi quà";
                                }
                                if ("Birthday".Equals(itemRet.Reason) && "Birthday".Equals(itemRet.ActionCode))
                                {
                                    title = partnerObj.MerchantName + " chúc mừng sinh nhật bạn";
                                    image =
                                        "https://linkidstorage.s3-ap-southeast-1.amazonaws.com/upload-gift/d31ba3605a42b18fb34f0d9fad2a0382.png";
                                }
                                if ("Exchange".Equals(itemRet.ActionType))
                                {
                                    title = $"Đổi điểm với đối tác {partnerObj.MerchantName}";
                                    image = partnerObj.MerchantIcon;
                                }
                                if ("CreditBalance".Equals(itemRet.ActionType))
                                {
                                    title = "Thu hồi điểm do nợ";
                                }
                                if ("Expired".Equals(itemRet.ActionType))
                                {
                                    title = "Thu hồi điểm do hết hạn sử dụng";
                                }
                                if ("RevertOrder".Equals(itemRet.ActionType))
                                {
                                    title = $"Thu hồi điểm do điều chỉnh giao dịch với {partnerObj.MerchantName}"; 
                                    image = partnerObj.MerchantIcon;
                                    // Đã có OriginalTokenTransId ở bản ghi đó 
                                }
                                if ("RevertRedeem".Equals(itemRet.ActionType))
                                {
                                    title = $"Hoàn điểm do điều chỉnh giao dịch với {partnerObj.MerchantName}"; 
                                }

                                if ("RevertCashOut".Equals(itemRet.ActionType))
                                {
                                    title = $"Hoàn điểm do điều chỉnh giao dịch với {partnerObj.MerchantName}"; 
                                }

                                if ("RevertCashOutFee".Equals(itemRet.ActionType))
                                {
                                    title = $"Hoàn điểm do điều chỉnh giao dịch với {partnerObj.MerchantName}"; 
                                }

                                if ("Revert".Equals(itemRet.ActionType) ||
                                    "AdjustPlus".Equals(itemRet.ActionType))
                                {
                                    title = $"Hoàn điểm do điều chỉnh giao dịch với {partnerObj.MerchantName}";
                                    image = partnerObj.MerchantIcon;
                                }
                                if ("Adjust".Equals(itemRet.ActionType))
                                {
                                    if (itemRet.UserAddress == itemRet.FromWalletAddress)
                                    {
                                        // Tru tien
                                        title = $"Thu hồi điểm do điều chỉnh giao dịch với {partnerObj.MerchantName}";
                                    }
                                    if (itemRet.UserAddress == itemRet.ToWalletAddress)
                                    {
                                        // Tru tien
                                        title = $"Hoàn điểm do điều chỉnh giao dịch với {partnerObj.MerchantName}";
                                    }
                                    image = partnerObj.MerchantIcon;
                                }
                                if ("AdjustMinus".Equals(itemRet.ActionType))
                                {
                                    title = $"Thu hồi điểm do điều chỉnh giao dịch với {partnerObj.MerchantName}";
                                    image = partnerObj.MerchantIcon;
                                }
                                if ("BatchManualGrant".Equals(itemRet.ActionType) || "SingleManualGrant".Equals(itemRet.ActionType))
                                {
                                    title = "Tích điểm từ chương trình " + itemRet.Reason;
                                }
                                if ("Order".Equals(itemRet.ActionType) && "Invoice".Equals(itemRet.ActionCode))
                                {
                                    title = "Tích điểm từ đối tác " + partnerObj.MerchantName;
                                    image = partnerObj.MerchantIcon;
                                }
                                if ("ReturnFull".Equals(itemRet.ActionType))
                                {
                                    title = "Thu hồi điểm do điều chỉnh giao dịch";
                                }
                                if ((new[] { "Adjust", "AdjustMinus", "AdjustPlus"}).ToList().Contains(itemRet.ActionType))
                                {
                                    title = "Giao dịch điều chỉnh điểm";
                                }
                            }

                            itemRet.MessageTitle = title;
                            itemRet.MessagePhotoLink = image;
                        }
                    }

                    // Cache the result for 5 minutes
                    await _cache.SetStringAsync(
                        cacheKey,
                        JsonConvert.SerializeObject(ret),
                        new DistributedCacheEntryOptions
                        {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1)
                        });
                }

                return Ok(WebstoreBaseOutputDto<GetAllTokenTransRespone>.Success(ret));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebstoreMemberController_SmeGetTransactionHistory_Error " + JsonConvert.SerializeObject(ex));
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                return BadRequest(WebstoreBaseOutputDto<GetAllTokenTransRespone>.Error(res.Code, res.Message));
            }
        }
        
        [HttpGet]
        [Route("get-tx-detail")]
        public async Task<ActionResult<TokenTransDetailForWebStoreOutput>> GetTransDetail([FromQuery] TokenTransDetailInput input)
        {
            try
            {
                var memberCode = HttpContext.Items["MemberCode"]?.ToString();
                if (string.IsNullOrEmpty(memberCode) || memberCode != input.MemberCode)
                {
                    return BadRequest(WebstoreBaseOutputDto<GetByMemberOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotMatch_Token, "Access Token Không Hợp Lệ"));
                }
                RewardMemberGetTokenTransDetailOutput tokenTransObj = null;
                if ("TOKEN".Equals(input.Type))
                {
                    try
                    {
                        tokenTransObj = await _rewardService.GetTokenTransById(new RewardMemberGetTokenTransDetailInput()
                        {
                            TokenTransId = input.TokenTransId, MemberCode = input.MemberCode
                        });
                    }
                    catch (Exception e)
                    {
                        var rewardResponse = await _exceptionReponseService.GetExceptionRewardReponse(e);
                        if (rewardResponse != null && rewardResponse.Code == "SearchDataDoesNotExist")
                        {
                            tokenTransObj = await _rewardService.GetExpiredTokenTransById(
                                new RewardMemberGetTokenTransDetailInput()
                                {
                                    TokenTransId = input.TokenTransId, MemberCode = input.MemberCode
                                });
                        }
                    }
                } else if ("MONEYCARD".Equals(input.Type))
                {
                    throw new NotImplementedException("MoneyCardNotYetImplememented");
                }

                if (tokenTransObj == null)
                {
                    // There is no such transaction
                    _logger.LogError(" >> TokenTransId#" + input.TokenTransId + " does not exist");
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Code = "SearchDataDoesNotExist", Message = "Mã giao dịch không tìm thấy"
                    });
                }
                var res = new TokenTransDetailForWebStoreOutput()
                {
                    Message = "", Result = new TokenTransDetailInnerContentForWebStore()
                    {
                        Amount = tokenTransObj.TokenAmount, MemberCode = input.MemberCode
                        , ActionCode = tokenTransObj.ActionCode, TokenTransId = tokenTransObj.TokenTransID
                        , OrderCode = tokenTransObj.OrderCode, ActionType = tokenTransObj.ActionType
                        , WalletAddress = tokenTransObj.UserAddress, ExpiredTime = tokenTransObj.ExpiryDate.Value
                        , CreationTime = tokenTransObj.Time.Value,
                        FromWalletAddress = tokenTransObj.FromWalletAddress,
                        ToWalletAddress = tokenTransObj.ToWalletAddress,
                        PartnerPointAmount = tokenTransObj.PartnerPointAmount,
                        RelatedTokenTransId = tokenTransObj.OriginalTokenTransId,
                        PartnerBindingTxId = tokenTransObj.PartnerBindingTxId
                    }, IsSuccess = true,
                };

                if ("TOKEN".Equals(input.Type))
                {
                    var resultFromNewV2 = await _notificationHistoryV2Service.TokenTransDetail(input);
                    if (resultFromNewV2.IsSuccess)
                    {
                        res.Result.Title = resultFromNewV2.Result.Title;
                        res.Result.Content = resultFromNewV2.Result.Content;
                        res.Result.ContentPhoto = resultFromNewV2.Result.ContentPhoto;
                        res.Result.DescriptionPhoto = resultFromNewV2.Result.DescriptionPhoto;
                        res.Result.PartnerName = resultFromNewV2.Result.PartnerName;
                        res.Result.PartnerIcon = resultFromNewV2.Result.PartnerIcon;
                        res.Result.UsageAddress = resultFromNewV2.Result.UsageAddress;
                    }
                }
                
                // Kiểm tra nếu là giao dịch redeem thì sẽ cần gọi xuống loyalty để lấy more info 
                if (new[] { "Redeem", "CashedOut", "CashOutFee" }.Contains(tokenTransObj.ActionType))
                {
                    var giftRedeemSearch = _giftTransactionsService.GetAllWithEGift(new LoyaltyGetAllWithEGiftInput()
                    {
                        OwnerCodeFilter = input.MemberCode,
                        SkipCount = 0, MaxResultCount =  100, GiftTransactionCode = tokenTransObj.OrderCode
                    }).Result;
                    if (giftRedeemSearch != null)
                    {
                        _logger.LogInformation(" >> giftRedeemSearch = " + JsonConvert.SerializeObject(giftRedeemSearch));
                        if (giftRedeemSearch.Success)
                        {
                            var description = "";
                            var brandName = "";
                            var giftName = "";
                            var cateName = "";
                            if (giftRedeemSearch.Result != null && giftRedeemSearch.Result.Items.Count > 0)
                            {
                                var giftRT = giftRedeemSearch.Result.Items[0];
                                var giftCode = giftRT.GiftTransaction.GiftCode;
                                description = giftRT.GiftTransaction.Description;
                                var egift = giftRT.EGift;

                                if (!"CashOutFee".Equals(tokenTransObj.ActionType))
                                {
                                    res.Result.GiftId = giftRT.GiftTransaction.GiftId?.ToString() ?? "";
                                    res.Result.GiftName = giftRT.GiftTransaction.GiftName;
                                    res.Result.GiftImage = giftRT.ImageLinks?.Where(x => x.Code == giftCode).FirstOrDefault()?.Link;
                                    res.Result.GiftPaidCoin = giftRedeemSearch.Result.Items.Sum(giftRedeem => giftRedeem.GiftTransaction.Coin) ?? 0;
                                    res.Result.Vendor = giftRT.VendorInfo;
                                    res.Result.BrandImage = giftRT.BrandInfo?.BrandImage;
                                    res.Result.RedeemQuantity = giftRedeemSearch.Result.Items.Sum(giftRedeem => giftRedeem.GiftTransaction.Quantity);
                                    brandName = giftRT.BrandInfo?.BrandName;
                                    cateName = giftRT.GiftTransaction.GiftCategoryName;
                                    _logger.LogInformation(" >> Category Name " + cateName);
                                    res.Result.BrandName = brandName;
                                    if (egift != null)
                                    {
                                        res.Result.EGiftExpiredDate = egift.ExpiredDate;
                                    }
                                
                                    // Title change to "DOI QUA xxxx"
                                    res.Result.Title = "Đổi quà thành công";
                                }
                                if ("CashOutFee".Equals(tokenTransObj.ActionType))
                                {
                                    res.Result.Title = "Phí đổi quà";
                                }
                            }
                            //Kiểm tra xem là imedia/asim ko
                            try
                            {
                                var parsedForImedia =
                                    JsonConvert.DeserializeObject<ImediaDescriptionDtoForParsing>(description);
                                if (parsedForImedia.operation == "1000" || parsedForImedia.operation == "1200")
                                {
                                    var isData = false;
                                    if (cateName != null)
                                    {
                                        if (cateName.ToLower().Contains("data"))
                                        {
                                            isData = true;
                                        }
                                    }
                                    
                                    // res.Result.ServiceName = parsedForImedia.operation == "1200" ? $"Topup {topupDataOrPhone}" : $"Thẻ nạp {topupDataOrPhone}";
                                    res.Result.PackageName = "";
                                    if(!isData && (parsedForImedia.operation == "1200"))
                                    {
                                        res.Result.PackageName = (parsedForImedia.accountType == "0") ? "Trả trước" : "Trả sau";
                                    }
                                    
                                    res.Result.CardValue = res.Result.GiftName;
                                    res.Result.ToPhoneNumber = parsedForImedia.operation == "1200" ? parsedForImedia.ownerphone : "";
                                    if (parsedForImedia.operation == "1200" && !string.IsNullOrEmpty(brandName))
                                    {
                                        res.Result.Title = $"Nạp {(isData ? "data" : "tiền điện thoại")} {brandName}";
                                        res.Result.ServiceName = $"Nạp {(isData ? "data" : "tiền điện thoại")}";
                                    }
                                    if (parsedForImedia.operation == "1000" && !string.IsNullOrEmpty(brandName))
                                    {
                                        res.Result.Title = $"Đổi thẻ {(isData ? "data" : "điện thoại")} {brandName}";
                                        res.Result.ServiceName = $"Đổi thẻ {(isData ? "data" : "điện thoại")}";
                                    }
                                }
                            }
                            catch (Exception e)
                            {
                                _logger.LogError(" >> ERROR PARSING DESCRIPTION >> " + description);
                            }
                        }
                    }
                    else
                    {
                        var createGiftRedeemObj =
                            await _giftTransactionsService.GetSingleCreateGiftRedeemTransaction(
                                new GetSingleCreateGiftRedeemTransactionInput()
                                {
                                    MemberCode = input.MemberCode, TransactionCode = tokenTransObj.OrderCode
                                });
                        if (createGiftRedeemObj != null)
                        {
                            var description = "";
                            var brandName = "";
                            var cateName = "";
                            description = createGiftRedeemObj.Description;

                            if (!"CashOutFee".Equals(tokenTransObj.ActionType))
                            {
                                res.Result.GiftId = createGiftRedeemObj.GiftId.ToString();
                                res.Result.GiftName = createGiftRedeemObj.GiftName;
                                res.Result.GiftPaidCoin = (float)createGiftRedeemObj.Amount;
                                res.Result.BrandImage = createGiftRedeemObj.BrandImage;
                                res.Result.RedeemQuantity = 0;
                                brandName = createGiftRedeemObj.BrandName;
                                cateName = createGiftRedeemObj.GiftCategoryName;
                                _logger.LogInformation(" >> Category Name 2 " + cateName);
                                res.Result.BrandName = brandName;
                                res.Result.Title = "Đổi quà thành công";
                            }
                            if ("CashOutFee".Equals(tokenTransObj.ActionType))
                            {
                                res.Result.Title = "Phí đổi quà";
                            }
                            //Kiểm tra xem là imedia/asim ko
                            try
                            {
                                var parsedForImedia =
                                    JsonConvert.DeserializeObject<ImediaDescriptionDtoForParsing>(description);
                                if (parsedForImedia.operation == "1000" || parsedForImedia.operation == "1200")
                                {
                                    var isData = false;
                                    if (cateName != null)
                                    {
                                        if (cateName.ToLower().Contains("data"))
                                        {
                                            isData = true;
                                        }
                                    }
                                    
                                    // res.Result.ServiceName = parsedForImedia.operation == "1200" ? $"Topup {topupDataOrPhone}" : $"Thẻ nạp {topupDataOrPhone}";
                                    res.Result.PackageName = "";
                                    if(!isData && (parsedForImedia.operation == "1200"))
                                    {
                                        res.Result.PackageName = (parsedForImedia.accountType == "0") ? "Trả trước" : "Trả sau";
                                    }
                                    res.Result.CardValue = res.Result.GiftName;
                                    res.Result.ToPhoneNumber = parsedForImedia.operation == "1200" ? parsedForImedia.ownerphone : "";
                                    if (parsedForImedia.operation == "1200" && !string.IsNullOrEmpty(brandName))
                                    {
                                        res.Result.Title = $"Nạp {(isData ? "data" : "tiền điện thoại")} {brandName}";
                                        res.Result.ServiceName = $"Nạp {(isData ? "data" : "tiền điện thoại")}";
                                    }
                                    if (parsedForImedia.operation == "1000" && !string.IsNullOrEmpty(brandName))
                                    {
                                        res.Result.Title = $"Đổi thẻ {(isData ? "data" : "điện thoại")} {brandName}";
                                        res.Result.ServiceName = $"Đổi thẻ {(isData ? "data" : "điện thoại")}";
                                    }
                                }
                            }
                            catch (Exception e)
                            {
                                _logger.LogError(" >> ERROR PARSING DESCRIPTION 2 >> " + description);
                            }
                        }
                    }
                }

                if ("Birthday".Equals(tokenTransObj.Reason) && "Birthday".Equals(tokenTransObj.ActionCode))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                    res.Result.Title = res.Result.PartnerName + " chúc mừng sinh nhật bạn";
                    res.Result.ContentPhoto =
                        "https://linkidstorage.s3-ap-southeast-1.amazonaws.com/upload-gift/d31ba3605a42b18fb34f0d9fad2a0382.png";
                }

                if ("PayByToken".Equals(tokenTransObj.ActionType))
                {
                    var merchantCached = await SetPartnerByMerchantWallet(res, tokenTransObj.ToWalletAddress);
                    res.Result.Title = "Tiêu điểm thành công tại " + res.Result.PartnerName;
                    // Nếu có ActionCode = 'RedeemExchange' thì xử lý thêm
                    if ("RedeemExchange".Equals(tokenTransObj.ActionCode))
                    {
                        var pointOrMile = "điểm";
                        if (merchantCached.PartnerPointExchangeType == "MILE")
                        {
                            pointOrMile = "dặm bay";
                            // Also parse metadata in ActionCodeDetail
                            try
                            {
                                var parsed =
                                    JsonConvert.DeserializeObject<Dictionary<string, object>>(
                                        tokenTransObj.ActionCodeDetail);
                                if (parsed != null)
                                {
                                    var firstName = parsed["FirstName"]?.ToString() ?? "";
                                    var lastName = parsed["LastName"]?.ToString() ?? "";
                                    var airlineMemberCode = parsed["AirlineMemberCode"]?.ToString() ?? "";
                                    res.Result.PartnerMemberFirstName = firstName;
                                    res.Result.PartnerMemberLastName = lastName;
                                    res.Result.AirlineMemberCode = airlineMemberCode;
                                    res.Result.PromotionCode = parsed["PromotionCode"]?.ToString() ?? "";
                                    res.Result.PromotionDate = parsed["PromotionDate"]?.ToString() ?? "";
                                    res.Result.PromotionValue = parsed["PromotionCode"]?.ToString() ?? "";
                                }
                            }
                            catch (Exception e)
                            {
                                _logger.LogError(" >> tokenTransObj#" + tokenTransObj.TokenTransID + " does not have valid ActionCodeDetail to parse.!"  + tokenTransObj.ActionCodeDetail);
                            }
                        }
                        res.Result.Title = $"Đổi điểm LynkiD sang {pointOrMile} của đối tác {res.Result.PartnerName}";
                    }
                }
                if ("Exchange".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                    res.Result.Title = $"Đổi điểm với đối tác {res.Result.PartnerName}";
                }
                if ("RevertExchange".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.ToWalletAddress);
                    res.Result.Title = $"Thu hồi điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                    var listTxOfSameOrderCode = await GetListRelatedByOrderCode(input.MemberCode, tokenTransObj.OrderCode);
                    if (listTxOfSameOrderCode.totalCount > 0)
                    {
                        var origin = listTxOfSameOrderCode.items.FirstOrDefault(x => x.ActionType == "Exchange");
                        if (origin != null)
                        {
                            res.Result.RelatedTokenTransId = origin.TokenTransID;
                        }
                    }
                }
                if ("CreditBalance".Equals(tokenTransObj.ActionType))
                {
                    res.Result.Title = "Thu hồi điểm do nợ";
                }
                if ("Expired".Equals(tokenTransObj.ActionType))
                {
                    res.Result.Title = "Thu hồi điểm do hết hạn sử dụng";
                }
                if ("RevertOrder".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.ToWalletAddress);
                    res.Result.Title = $"Thu hồi điểm do điều chỉnh giao dịch với {res.Result.PartnerName}"; 
                    // Đã có OriginalTokenTransId ở bản ghi đó 
                }
                if ("RevertRedeem".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                    res.Result.Title = $"Hoàn điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                }
                if ("RevertCashOut".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                    var listTxOfSameOrderCode = await GetListRelatedByOrderCode(input.MemberCode, tokenTransObj.OrderCode);
                    if (listTxOfSameOrderCode.totalCount > 0)
                    {
                        var origin = listTxOfSameOrderCode.items.FirstOrDefault(x => x.ActionType == "CashedOut");
                        if (origin != null)
                        {
                            res.Result.RelatedTokenTransId = origin.TokenTransID;
                        }
                    }
                    res.Result.Title = $"Hoàn điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                }
                if ("RevertCashOutFee".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                    var listTxOfSameOrderCode = await GetListRelatedByOrderCode(input.MemberCode, tokenTransObj.OrderCode);
                    if (listTxOfSameOrderCode.totalCount > 0)
                    {
                        var origin = listTxOfSameOrderCode.items.FirstOrDefault(x => x.ActionType == "CashOutFee");
                        if (origin != null)
                        {
                            res.Result.RelatedTokenTransId = origin.TokenTransID;
                        }
                    }
                    res.Result.Title = $"Hoàn điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                }
                if ("Revert".Equals(tokenTransObj.ActionType) || "AdjustPlus".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                    res.Result.Title = $"Hoàn điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                    // Nếu là revert và ko có originaltokentransid thì có thể tìm kiếm dữ liệu của nó như này:
                    if (string.IsNullOrEmpty(tokenTransObj.OriginalTokenTransId))
                    {
                        var listTxOfSameOrderCode =
                            await GetListRelatedByOrderCode(input.MemberCode, tokenTransObj.OrderCode);
                        if (listTxOfSameOrderCode.totalCount > 0)
                        {
                            var origin = listTxOfSameOrderCode.items.FirstOrDefault(x => x.ActionType == "Redeem");
                            if (origin != null)
                            {
                                res.Result.RelatedTokenTransId = origin.TokenTransID;
                            }
                        }
                    }
                }

                if ("Adjust".Equals(tokenTransObj.ActionType))
                {
                    if (tokenTransObj.UserAddress == tokenTransObj.FromWalletAddress)
                    {
                        // Tru tien
                        await SetPartnerByMerchantWallet(res, tokenTransObj.ToWalletAddress);
                        res.Result.Title = $"Thu hồi điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                    }
                    if (tokenTransObj.UserAddress == tokenTransObj.ToWalletAddress)
                    {
                        // Tru tien
                        await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                        res.Result.Title = $"Hoàn điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                    }
                }
                if ("AdjustMinus".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.ToWalletAddress);
                    res.Result.Title = $"Thu hồi điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                }
                if ("BatchManualGrant".Equals(tokenTransObj.ActionType) || "SingleManualGrant".Equals(tokenTransObj.ActionType))
                {
                    res.Result.Title = "Tích điểm từ chương trình " + tokenTransObj.Reason;
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                }
                if ("Order".Equals(tokenTransObj.ActionType) && "Invoice".Equals(tokenTransObj.ActionCode))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                }
                if ("ReturnFull".Equals(tokenTransObj.ActionType))
                {
                    res.Result.Title = "Thu hồi điểm do điều chỉnh giao dịch";
                }

                if ("Topup".Equals(tokenTransObj.ActionType))
                {
                    var giftRedeemSearch = _giftTransactionsService.GetAllWithEGift(new LoyaltyGetAllWithEGiftInput()
                    {
                        OwnerCodeFilter = input.MemberCode,
                        SkipCount = 0, MaxResultCount =  100, GiftTransactionCode = tokenTransObj.OrderCode
                    }).Result;
                    if (giftRedeemSearch != null)
                    {
                        try
                        {
                            var batchNote = giftRedeemSearch.Result?.Items[0]?.GiftTransaction?.BatchNote ?? "";
                            await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                            if (string.IsNullOrEmpty(batchNote))
                            {
                                batchNote = res.Result.PartnerName;
                            }
                            res.Result.Title = "Nhận điểm từ " + batchNote;

                        }
                        catch (Exception e)
                        {
                        }
                    }
                }
                // Finally, hide PartnerName if ActionType is Adjust, AdjustMinus, AdjustPlus, SingleManualGrant, BatchManualGrant, RevertCashout, RevertCashoutFee, RevertRedeem
                if (new[] { "Adjust", "AdjustMinus", "AdjustPlus", "Revert", "ReturnFull",
                        "SingleManualGrant", "BatchManualGrant", "RevertCashOut", "RevertCashOutFee", "RevertRedeem" }.Contains(tokenTransObj.ActionType))
                {
                    res.Result.PartnerName = "";
                }
                // nếu giao dịch là cộng tiền do giới thiệu bạn bè thì cần gọi lên loyalty check để lấy thêm thông tin
                if (new[] { "Referrer"}.Contains(tokenTransObj.ActionCode))
                {
                    var transactionInforReferral = _giftTransactionsService.GetTransactionInforReferral(new GetTransactionInforReferralInput()
                    {
                        TransactionCode = res.Result.OrderCode
                    }).Result;
                    res.Result.InvitedMember = transactionInforReferral.Result.InvitedMember;
                    res.Result.CampaignName = transactionInforReferral.Result.CampaignName;
                }
                _logger.LogInformation(" Final Output: " + JsonConvert.SerializeObject(res));
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetTransDetail Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        
        private async Task<GetAllTokenTransRespone> GetListRelatedByOrderCode(string memberCode, string orderCode)
        {
            // use cache 
            var key = "GetListRelatedByOrderCode_" + memberCode + "_" + orderCode;
            var cachedString = await _cache.GetStringAsync(key);
            try
            {
                var cachedObj = JsonConvert.DeserializeObject<GetAllTokenTransRespone>(cachedString);
                return cachedObj;
            }
            catch (Exception e)
            {
                await _cache.RemoveAsync(key);
            }

            var listTxOfSameOrderCode = await _rewardService.GetALLTokenTransBySmeCif(new GetALLTokenTransBySmeCifInput()
            {
                OrderCodeFilter = orderCode,
                SmeCif = memberCode,
                skipCount = 0,
                maxResultCount = 10,
            });
            await _cache.SetStringAsync(key, JsonConvert.SerializeObject(listTxOfSameOrderCode),
                new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(59)));
            return listTxOfSameOrderCode;
        }
        private async Task<ShortMerchantDto> SetPartnerByMerchantWallet(TokenTransDetailForWebStoreOutput res, string merchantWallet)
        {
            // Get ví của merchant nhận, query trong cache để lấy name x icon
            var merchantCached = await GetMerchantInfoByWallet(merchantWallet);
            if (merchantCached != null)
            {
                res.Result.PartnerIcon = merchantCached.MerchantIcon;
                res.Result.PartnerName = merchantCached.MerchantName;
                res.Result.PartnerPointExchangeType = merchantCached.PartnerPointExchangeType;
            }

            return merchantCached;
        }

        private async Task<ShortMerchantDto> GetMerchantInfoByWallet(string walletAddress)
        {
            var cacheKey = "GetMerchantInfoByWallet_" + walletAddress;
            var cachedString = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(cachedString))
            {
                try
                {
                    var ret = JsonConvert.DeserializeObject<ShortMerchantDto>(cachedString);
                    return ret;
                }
                catch (Exception e)
                {
                    await _cache.RemoveAsync(cacheKey);
                }
            }
            // Get from operator
            try
            {
                var merchantShortInfo = await _rewardMerchantService.GetFullInfoMerchantById(new GetFullInfoMerchantByIdInput() { WalletAddress = walletAddress, MerchantId = null});
                if (merchantShortInfo == null)
                {
                    throw new Exception();
                }

                var ret = new ShortMerchantDto()
                {
                    MerchantName = merchantShortInfo.MerchantName, MerchantId = merchantShortInfo.Id, WalletAddress = walletAddress,
                    MerchantIcon = merchantShortInfo.Logo, PartnerPointExchangeType = merchantShortInfo.PartnerPointExchangeType
                };
                await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(ret),
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(30)));

                return ret;
            }
            catch (Exception e)
            { 
                _logger.LogError(" THERE IS NO MERCHANT WITH WALLET " + walletAddress);
            }
            // IT IS NULL INTENTINALLY
            return null;
        }

        /// <summary>
        /// API cho phép User chủ động đổi password. Authorization header required.
        /// </summary>
        [HttpPost]
        [Route("change-password")]
        public async Task<ActionResult<WebstoreBaseOutputDto<ChangePasswordOutput>>>
            ChangePassword([FromBody] ChangePasswordInput input)
        {
            if (input == null)
            {
                return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_INVALIDINPUT, "Dữ liệu đầu vào không hợp lệ"));
            }
            _logger.LogInformation(" >> Member Change Password >> " + input.MemberCode);
            var memberCode = HttpContext.Items["MemberCode"]?.ToString();
            var memberType = HttpContext.Items["MemberType"]?.ToString();
            if (string.IsNullOrEmpty(memberCode) || memberCode != input.MemberCode)
            {
                return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotMatch_Token, "Access Token không hợp lệ"));
            }
            // Validate old passowrd
            if (string.IsNullOrEmpty(input.NewPassword))
            {
                return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_NEWPASSWORD_INVALID, "Mật khẩu mới không được bỏ trống"));
            }
            if (input.OldPassword == input.NewPassword)
            {
                return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_NEW_AND_OLD_SAME, "Bạn điền mật khẩu cũ và mật khẩu mới y chang nhau"));
            }

            if (!string.Empty.Equals(CommonHelper.WebstoreValidatePasswordFormat(input.NewPassword)))
            {
                return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_NEWPASSWORD_INVALID_FORMAT, "Mật khẩu mới không đúng định dạng"));
            }
            if ("SME".Equals(memberType))
            {
                try
                {
                    var smeObj = await _rewardService.WebstoreGetFullSmeInfo(new WebstoreGetFullSmeInfoRequest()
                    {
                        SmeCode = input.MemberCode
                    });
                    if (smeObj?.Item == null)
                    {
                        _logger.LogError(" >> ChangePassword >> SmeCode not found @@ ");
                        return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_MemberCode_NotFound, "Không tìm thấy mã member"));
                    }
                    var validationResult = await _rewardService.CheckSmePassword(new CheckSmePasswordRewardInput()
                    {
                        SmeLicenseCode = smeObj.Item.LicenseNumber,
                        RepPhone = smeObj.Item.RepPhone,
                        Password = input.OldPassword
                    });
                    if (validationResult.Result != 200)
                    {
                        return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_INCORRECT_OLD_PASSWORD, "Mật khẩu hiện tại không đúng"));
                    }
                    var res = await _rewardService.WebstoreSetSmePassword(new WebstoreSetSmePasswordRequest()
                    {
                        SmeCode = input.MemberCode,
                        Password = input.NewPassword
                    });
                    if (res == null || res.Item == null)
                    {
                        return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR, "Có lỗi không xác định. Vui lòng thử lại sau."));
                    }

                    if (res.Item.Success == false)
                    {
                        return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.INTERNALSERVERERROR, res.Item.Message));
                    }

                    return Ok(WebstoreBaseOutputDto<ChangePasswordOutput>.Success(new ChangePasswordOutput() { }));
                }
                catch(Exception e)
                {
                    if (e.GetType() == typeof(RewardException))
                    {
                        var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(e);
                        if ("InvalidCredentials".Equals(res.Code))
                        {
                            return BadRequest(WebstoreBaseOutputDto<ChangePasswordOutput>.Error(WebstoreConstants.LoginErrorCodes.COMMON_INCORRECT_OLD_PASSWORD, "Mật khẩu hiện tại không đúng"));
                        }
                    }
                    throw e;
                }
            }
            throw new Exception("Only SME supported Right now");
        }
    }
}