﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class GetListQuestByActivityInputDto
    {
        [Required]
        public string MemberCode { get; set; }

        public string QuestStateFilter { get; set; }

        public QuestStatus? Type { get; set; }

        public virtual string Sorting { get; set; }

        public virtual int SkipCount { get; set; }

        public int MaxResultCount { get; set; }
    }

    public enum QuestStatus
    {
        Effective,
        Expired
    }
}
