using AKC.MobileAPI.DTO.Loyalty.AuditLog;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.ThirdParty.Base
{
    public class BaseThirdPartyVPBankLoayltyExchangeService
    {
        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        protected readonly string baseURL;
        protected readonly int tenantId;
        protected readonly string defaultUserName;
        protected readonly string defaultPassowrd;
        private readonly IDistributedCache _cache;
        protected readonly ILogger _logger;
        private readonly ILoyaltyAuditLogService _loyaltyAuditLogService;

        public BaseThirdPartyVPBankLoayltyExchangeService(
            IConfiguration configuration,
            IDistributedCache cache,
            ILogger<BaseThirdPartyDummyService> logger,
            ILoyaltyAuditLogService loyaltyAuditLogService)
        {
            _configuration = configuration;
            baseURL = _configuration.GetSection("VPBankLoyaltyExchange:BaseURL").Value;
            tenantId = Convert.ToInt32(_configuration.GetSection("VPBankLoyaltyExchange:TenantId").Value);
            defaultUserName = _configuration.GetSection("VPBankLoyaltyExchange:Username").Value;
            defaultPassowrd = _configuration.GetSection("VPBankLoyaltyExchange:Password").Value;
            _cache = cache;
            _logger = logger;
            _loyaltyAuditLogService = loyaltyAuditLogService;
        }

        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetLoyaltyAsync<T>(string apiURL, object query = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };

            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            var token = GetAccessToken();
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            _logger.LogInformation("Start call vpbank loyalty exchange request data: " + JsonConvert.SerializeObject(requestURL));
            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();
            _logger.LogInformation("End call vpbank loyalty exchange response data: " + JsonConvert.SerializeObject(rawData));
            //Recall API when Unauthorized
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                string accessToken = GetAccessToken(true);
                req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var reqClone = CloneHttpRequest(req);

                response = await _client.SendAsync(reqClone);
                rawData = await response.Content.ReadAsStringAsync();
            }
            //End Recall API when Unauthorized

            if (response.IsSuccessStatusCode == false)
            {
                var ex = new LoyaltyException();
                ex.Data.Add("ErrorData", rawData);
                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    ex.Data.Add("StatusCode", 400);
                }
                else
                {
                    ex.Data.Add("StatusCode", 500);
                }
                throw ex;
            }

            response.EnsureSuccessStatusCode();


            // Convert response to result object which is a instance of 'T'.
            var result = JsonConvert.DeserializeObject<T>(rawData);
            return result;
        }

        /// <summary>
        /// Convert a object to query string format.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string GetQueryString(object obj)
        {
            var properties = from p in obj.GetType().GetProperties()
                             where p.GetValue(obj, null) != null
                             select p.Name + "=" + HttpUtility.UrlEncode(p.GetValue(obj, null).ToString());

            return string.Join("&", properties.ToArray());
        }

        public async Task<T> PostLoyaltyAsync<T>(string apiURL, string serviceName, string memberId, object body = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            _logger.LogInformation("Start call vpbank loyalty exchange request data: " + serviceName + " " + JsonConvert.SerializeObject(body));
            var start = DateTime.UtcNow;
            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();
            var ipRequest = string.Empty;
            // Add get dest IP from partner
            try
            {
                Uri myUri = new Uri(baseURL);
                var ipHost = Dns.GetHostAddresses(myUri.Host);
                if (ipHost != null && ipHost.Length > 0)
                {
                    ipRequest = ipHost[0].MapToIPv4().ToString();
                }
            }
            catch { }
            // Add get dest IP from partner
            var responseData = JsonConvert.SerializeObject(rawData);
            var requestData = JsonConvert.SerializeObject(body);
            var end = DateTime.UtcNow;
            var errorCode = response.StatusCode != HttpStatusCode.OK ? response.StatusCode.ToString() : "";
            OperationLog(memberId, serviceName, errorCode, requestData, responseData, ipRequest, response.IsSuccessStatusCode, start, end);

            _logger.LogInformation("End call vpbank loyalty exchange response data: " + serviceName + " " + JsonConvert.SerializeObject(rawData));
            //Recall API when Unauthorized
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                string accessToken = GetAccessToken(true);
                req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var reqClone = CloneHttpRequest(req);

                response = await _client.SendAsync(reqClone);
                rawData = await response.Content.ReadAsStringAsync();
            }
            //End Recall API when Unauthorized

            if (response.IsSuccessStatusCode == false)
            {
                var ex = new LoyaltyException();
                ex.Data.Add("ErrorData", rawData);
                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    ex.Data.Add("StatusCode", 400);
                }
                else
                {
                    ex.Data.Add("StatusCode", 500);
                }
                throw ex;
            }

            response.EnsureSuccessStatusCode();

            // Get respone result.
            var result = JsonConvert.DeserializeObject<T>(rawData);

            return result;
        }
        public async Task<T> PutLoyaltyAsync<T>(string apiURL, object body = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Put
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            _logger.LogInformation("Start call vpbank loyalty exchange request data: " + JsonConvert.SerializeObject(body));
            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();
            _logger.LogInformation("End call vpbank loyalty exchange response data: " + JsonConvert.SerializeObject(rawData));
            //Recall API when Unauthorized
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                string accessToken = GetAccessToken(true);
                req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var reqClone = CloneHttpRequest(req);

                response = await _client.SendAsync(reqClone);
                rawData = await response.Content.ReadAsStringAsync();
            }
            //End Recall API when Unauthorized

            if (response.IsSuccessStatusCode == false)
            {
                var ex = new LoyaltyException();
                ex.Data.Add("ErrorData", rawData);
                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    ex.Data.Add("StatusCode", 400);
                }
                else
                {
                    ex.Data.Add("StatusCode", 500);
                }
                throw ex;
            }

            response.EnsureSuccessStatusCode();

            // Get respone result.
            var result = JsonConvert.DeserializeObject<T>(rawData);

            return result;
        }


        /// <summary>
        /// Get a new accessToken form Loyalty.
        /// </summary>
        /// <returns></returns>
        private string GetAccessToken(bool mustResetCache = false)
        {
            var token = _cache.GetString(CommonConstants.ACCESSS_TOKEN_VPBANK_EXCHANGE_CACHE_KEY);

            // Request body
            if (string.IsNullOrEmpty(token) || mustResetCache)
            {
                return LoyaltyHelper.RenewAccessTokenVPBankExchangeCacheValue(_cache, TimeSpan.FromHours(2), true);
            }

            return token;
        }

        //Clone a HttpRequest
        private HttpRequestMessage CloneHttpRequest(HttpRequestMessage req)
        {
            HttpRequestMessage clone = new HttpRequestMessage(req.Method, req.RequestUri);

            clone.Content = req.Content;
            clone.Version = req.Version;

            foreach (KeyValuePair<string, object> prop in req.Properties)
            {
                clone.Properties.Add(prop);
            }

            foreach (KeyValuePair<string, IEnumerable<string>> header in req.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            return clone;
        }

        private void OperationLog(string memberId, string serviceName, string errorCode, string requestData, string responseData, string ip, bool status, DateTime start, DateTime end)
        {
            try
            {
                var listOperationLogDto = new CreateOperationLogDto();
                var key = "VPBankLoyaltyExchange_" + DateTime.UtcNow.Ticks.ToString() + new Random().Next(4);
                var operationLog = new OperationLogDto
                {
                    TenantId = null,
                    MemberId = memberId,
                    Code = key,
                    ReferenceKey = key,
                    PartnerCode = "VPBankLoyaltyExchange",
                    DestIP = ip,
                    StartDate = start,
                    RequestMsg = requestData,
                    ServiceName = serviceName,
                    EndDate = end,
                    Status = status,
                    ErrorCode = errorCode,
                    ResponseMsg = responseData,
                };
                _logger.LogInformation("Dest IP" + operationLog.DestIP);
                listOperationLogDto.OperationLogs.Add(operationLog);

                _loyaltyAuditLogService.CreateOperationLog(listOperationLogDto).Wait();
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Operation exception: " + ex);
            }
        }
    }
}
