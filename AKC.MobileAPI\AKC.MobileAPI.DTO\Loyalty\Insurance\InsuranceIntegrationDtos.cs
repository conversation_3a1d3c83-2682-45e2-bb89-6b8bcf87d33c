﻿using System;
using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.Loyalty.Insurance
{
    // INSURANCE PROGRAM DTOs
    public class GetListProgramsInput
    {
        public string Vendor { get; set; }
        public int Skip { get; set; }
        public int MaxItem { get; set; }
    }
    
    public class GetListProgramsOutput
    {
        public GetListProgramsChildrenOutput Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class GetListProgramsChildrenOutput
    {
        public int TotalCount { get; set; }
        public List<GetListProgramsChildrenContent> Items { get; set; }
    }

    public class GetListProgramsChildrenContent
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public string ProfilePhoto { get; set; }
        public string ListOfPartners { get; set; }
        public int NumberOfProducts { get; set; }
        public List<GetListProductsChildrenContent> ListProducts { get; set; }
    }
    
    // INSURANCE PRODUCT DTOs
    
    public class GetListProductsInput
    {
        public string ProgramCode { get; set; }
        public string Vendor { get; set; }
        public int Skip { get; set; }
        public int MaxItem { get; set; }
    }
    
    public class GetListProductsOutput
    {
        public GetListProductsChildrenOutput Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class GetListProductsChildrenOutput
    {
        public int TotalCount { get; set; }
        public List<GetListProductsChildrenContent> Items { get; set; }
    }

    public class GetListProductsChildrenContent
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public string ProfilePhoto { get; set; }
        public string PartnerCode { get; set; }
        public InsuranceVendorDto Partner { get; set; }
        public string ProgramCode { get; set; }
        public string Description { get; set; }
        public int Ordinal { get; set; }
        public int Likes { get; set; }
        public decimal MinPrice { get; set; }
        public decimal MinRequiredCoin { get; set; }
        public virtual string InsuranceBenefit { get; set; }
        public virtual string InsuranceClaimGuideline { get; set; }
    }
    
    // INSURANCE PACKAGE DTOs
    
    public class GetListPackagesInput
    {
        public string ProductCode { get; set; }
        public string ProgramCode { get; set; }
        public string Vendor { get; set; }
        public int Skip { get; set; }
        public int MaxItem { get; set; }
    }
    
    public class GetListPackagesOutput
    {
        public GetListPackagesChildrenOutput Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class GetListPackagesChildrenOutput
    {
        public int TotalCount { get; set; }
        public List<GetListPackagesChildrenContent> Items { get; set; }
    }

    public class GetListPackagesChildrenContent
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public string ProfilePhoto { get; set; }
        public string PartnerCode { get; set; }
        public string ProgramCode { get; set; }
        public string ProductCode { get; set; }
        public string GiftCode { get; set; }
        public string Description { get; set; }
        public decimal InsuranceFee { get; set; }
        public decimal GiftFullPrice { get; set; }
        public decimal GiftRequiredCoin { get; set; }
        public int EffectiveTime { get; set; } // So thang hay so nam hieu luc
        public string EffectiveTimeUnit { get; set; }
        public bool CanPayPartially { get; set; }
        public bool AutoRenew { get; set; }
        public int Ordinal { get; set; }
    }

    public enum EffectiveTimeUnit
    {
        YEAR =  1, MONTH = 2, DAY = 3
    }
    
    public class InsuranceVendorDto
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string ImageLink { get; set; }
        public string HotLine { get; set; }
    }

    public class InsuranceDetailRes
    {
        public InsuranceDetailDto Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }
    public class InsuranceDetailDto
    {
        public long? Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public string ProfilePhoto { get; set; }
        public string PartnerCode { get; set; }
        public string ProgramCode { get; set; }
        public string Description { get; set; }
        public int Ordinal { get; set; }
        public decimal MinPrice { get; set; }
        public decimal MinRequiredCoin { get; set; }
        public string InsuranceBenefit { get; set; }
        public string InsuranceClaimGuideline { get; set; }
    }

    public class InsuranceDetailInput
    {
        public string ProductCode { get; set; }
    }
}