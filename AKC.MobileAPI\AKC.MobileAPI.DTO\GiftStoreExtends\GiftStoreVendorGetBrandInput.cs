﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.GiftStoreExtends
{
    public class GiftStoreVendorGetBrandInput
    {
        [Required]
        public int MerchantId { get; set; }
        public string Sorting { get; set; }
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
    }

    public class GiftStoreVendorGetBrandDto
    {
        public string Sorting { get; set; }
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
    }
}
