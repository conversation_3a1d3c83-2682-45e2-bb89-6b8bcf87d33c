﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.DTO.User
{
    public class CreateUserDTO
    {
        [Required]
        [EmailAddress(ErrorMessage = "Wrong email format")]
        public string Email { get; set; }

        [Phone]
        public string Phone { get; set; }

        [Required]
        [MinLength(6, ErrorMessage = "Passwords must be at least 6 characters")]
        public string Password { get; set; }

        public virtual string FirstName { get; set; }

        public virtual string LastName { get; set; }

        public string IdCard { get; set; }

        public string Gender { get; set; }
    }
}
