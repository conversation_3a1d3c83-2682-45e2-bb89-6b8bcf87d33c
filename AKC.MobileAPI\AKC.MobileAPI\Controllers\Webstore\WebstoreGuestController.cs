using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.Article;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.DTO.Webstore;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Webstore
{
    [ApiController]
    [Route("api/webstore/public")]
    [ApiConventionType(typeof(DefaultApiConventions))]
    public class WebstoreGuestController : ControllerBase
    {
        private readonly ILoyaltyGiftService _giftService;
        private readonly ILogger<WebstoreGuestController> _logger;
        private readonly IDistributedCache _cache;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltyLocationService _loyaltyLocationService;
        private readonly ILoyaltyArticleService _loyaltyArticleService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IConfiguration _configuration;
        private int _maxQuantityPerRedeem = 0;

        public WebstoreGuestController(ILoyaltyGiftService g,
            IDistributedCache c,
            IExceptionReponseService ie,
            ILoyaltyArticleService la,
            IConfiguration ic,
            ILoyaltyLocationService locs,
            IRewardMemberService rw,
            ILogger<WebstoreGuestController> logger)
        {
            _loyaltyArticleService = la;
            _giftService = g;
            _logger = logger;
            _exceptionReponseService = ie;
            _cache = c;
            _rewardMemberService = rw;
            _loyaltyLocationService = locs;
            _configuration = ic;
            var check = int.TryParse(_configuration.GetSection("WebStoreMaxQuantityDefault").Value, out _maxQuantityPerRedeem);
            if (!check)
            {
                _maxQuantityPerRedeem = 9;
            }
        }

        [HttpGet("get-categories")]
        public async Task<ActionResult<WebstoreBaseOutputDto<CategoriesResultData>>> GetCategories(
            [FromQuery] string CodeFilter = null,
            [FromQuery] string NameFilter = null,
            [FromQuery] string StatusFilter = null,
            [FromQuery] int SkipCount = 0,
            [FromQuery] int MinLevelFilter = 0,
            [FromQuery] int MaxLevelFilter = 1,
            [FromQuery] [Range(1, 50)] int MaxResultCount = 50,
            [FromQuery] string ConnectSource = "Webapp")
        {
            try
            {
                if (MaxResultCount > 50) MaxResultCount = 50;

                var query = new
                {
                    CodeFilter,
                    NameFilter,
                    StatusFilter,
                    SkipCount,
                    MaxResultCount, PartnerCode = "LynkiDWebApp",
                    ConnectSource 
                };
                _logger.LogInformation(">> GetCategories >> Input: " + JsonConvert.SerializeObject(query));
                var cacheKey = $"WebstoreGetCate_{CodeFilter}_{MinLevelFilter}_{MaxLevelFilter}_{NameFilter}_{StatusFilter}_{SkipCount}_{MaxResultCount}_{ConnectSource}";
                var cachedString = await _cache.GetStringAsync(cacheKey);
                if (!string.IsNullOrWhiteSpace(cachedString))
                {
                    try
                    {
                        _logger.LogInformation(">> GetCategories >> Found in cache! Returning...!");
                        var cachedObj = JsonConvert.DeserializeObject<CategoriesResponse>(cachedString);
                        if (cachedObj != null)
                        {
                            return Ok(WebstoreBaseOutputDto<CategoriesResultData>.Success(cachedObj.Result));
                        }

                        throw new Exception("CachedObjectIsNull");
                    }
                    catch (Exception e)
                    {
                        // neu loi parse object thi clear cache
                        _logger.LogError(" >> GetCategories >> fail to parse cached object >> " + e.Message);
                        await _cache.RemoveAsync(cacheKey);
                    }
                }

                var response =
                    await _giftService.GetAllWithoutMemberCodeForHomePage(query);
                if (!response.Success)
                {
                    _logger.LogError(" >> GetCategories >> Error query giftstore: " +
                                     JsonConvert.SerializeObject(response));
                    return BadRequest(WebstoreBaseOutputDto<CategoriesResultData>.Error("100",
                        string.IsNullOrEmpty(response.Error) ? response.Error : "Error Happened"));
                }
                foreach (var categoryItem in response.Result.Items)
                {
                    categoryItem.GiftCategory.Link = categoryItem.GiftCategory.ImageLink?.FullLink;
                    categoryItem.GiftCategory.ImageLink = null;
                }

                var serialized = JsonConvert.SerializeObject(response);
                await _cache.SetStringAsync(cacheKey, serialized,
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(30)));

                return Ok(WebstoreBaseOutputDto<CategoriesResultData>.Success(response.Result));
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return BadRequest(WebstoreBaseOutputDto<CategoriesResultData>.Error(res.Code, res.Message));;
                }
                _logger.LogError(ex, "Error getting categories");
                return BadRequest(WebstoreBaseOutputDto<CategoriesResultData>.Error("500", "Internal server error"));
            }
        }
        
        [HttpGet("get-gift-groups")]
        public async Task<ActionResult<WebstoreBaseOutputDto<ListResultGiftInfors>>> GetGiftGroups(
            [FromQuery] int? GiftGroupId,
            [FromQuery] int? GroupTypeFilter = null,
            [FromQuery] int SkipCount = 0,
            [FromQuery] int MaxGiftInsideCount = 10,
            [FromQuery] int SkipGiftCount = 0,
            [FromQuery] [Range(1, 50)] int MaxResultCount = 5)
        {
            try
            {
                var result = await _giftService.GetForHomePageNoMemberCode(new GetForHomePageNoMemberCodeInput()
                {
                    GroupTypeFilter = GroupTypeFilter, SkipCount = SkipCount, MaxResultCount = MaxResultCount, MaxItem = MaxGiftInsideCount, GiftGroupId = GiftGroupId,
                    SkipGiftCount = SkipGiftCount, Channel = "LynkiDWebApp"
                });
                return Ok(WebstoreBaseOutputDto<ListResultGiftInfors>.Success(result.Result));
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return BadRequest(WebstoreBaseOutputDto<WebstoreGetNotificationByMemberOutput>.Error(res.Code, res.Message));;
                }
                _logger.LogError(ex, "GetGiftGroups Error - " + JsonConvert.SerializeObject(ex));
                return BadRequest(WebstoreBaseOutputDto<WebstoreGetNotificationByMemberOutput>.Error("500", "Internal server error"));
            }
        }

        [HttpGet("get-gift-list")]
        public async Task<ActionResult<WebstoreBaseOutputDto<ResultDataGift>>> GetGiftLists(
            [FromQuery] string CodeFilter = null,
            [FromQuery] string NameFilter = null,
            [FromQuery] string StatusFilter = null,
            [FromQuery] string CategoryCodeFilter = null,
            [FromQuery] bool? IsEGiftFilter = null,
            [FromQuery] string RegionCodeFilter = null,
            [FromQuery] int? FromCoin = null,
            [FromQuery] int? ToCoin = null,
            [FromQuery] string Sorting = null,
            [FromQuery] int SkipCount = 0,
            [FromQuery] [Range(1, 50)] int MaxResultCount = 50)
        {
            try
            {
                if (MaxResultCount > 50) MaxResultCount = 50;
                // Sorting sẽ allow MODE1 and MODE2 hoặc NULL
                // MODE1 = PRICEASC
                // Mode2 = PRICEDESC
                string realSorting = null;
                if (string.IsNullOrEmpty(Sorting) == false)
                {
                    if (Sorting == "PRICEASC")
                    {
                        realSorting = " RequiredCoin ASC ";
                    }
                    if (Sorting == "PRICEDESC")
                    {
                        realSorting = " RequiredCoin DESC ";
                    }
                    
                }
                var query = new
                {
                    CodeFilter,
                    NameFilter,
                    IsEGiftFilter,
                    RegionCodeFilter,
                    StatusFilter,
                    FromCointFilter = FromCoin,
                    ToCoinFilter = ToCoin,
                    FullGiftCategoryCodeFilter = CategoryCodeFilter,
                    SkipCount,
                    MaxResultCount,
                    Sorting = realSorting,
                    PartnerCode = "LynkiDWebApp"
                };
                _logger.LogInformation(">> GetGiftLists >> Input: " + JsonConvert.SerializeObject(query));
                var cacheKey = $"WebstoreGetGiftList_{CategoryCodeFilter}_{Sorting}_{FromCoin}_{ToCoin}_{RegionCodeFilter}_{IsEGiftFilter}_{CodeFilter}_{NameFilter}_{StatusFilter}_{SkipCount}_{MaxResultCount}";
                var cachedString = await _cache.GetStringAsync(cacheKey);
                if (!string.IsNullOrWhiteSpace(cachedString))
                {
                    try
                    {
                        _logger.LogInformation(">> GetGiftLists >> Found in cache! Returning...!");
                        var cachedObj = JsonConvert.DeserializeObject<GiftListResponse>(cachedString);
                        if (cachedObj != null)
                        {
                            return Ok(WebstoreBaseOutputDto<ResultDataGift>.Success(cachedObj.Result));
                        }

                        throw new Exception("CachedObjectIsNull");
                    }
                    catch (Exception e)
                    {
                        // neu loi parse object thi clear cache
                        _logger.LogError(">> GetGiftLists >> fail to parse cached object  >> " + e.Message);
                        await _cache.RemoveAsync(cacheKey);
                    }
                }

                var response =
                    await _giftService.GetAllGiftWithoutMemberCode(query);
                if (!response.Success)
                {
                    _logger.LogError(" >> GetGiftLists >> Error query giftstore: " +
                                     JsonConvert.SerializeObject(response));
                    return BadRequest(WebstoreBaseOutputDto<ResultDataGift>.Error("100",
                        string.IsNullOrEmpty(response.Error) ? response.Error : "Error Happened"));
                }

                var serialized = JsonConvert.SerializeObject(response);
                await _cache.SetStringAsync(cacheKey, serialized,
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(30)));
                return Ok(WebstoreBaseOutputDto<ResultDataGift>.Success(response.Result));
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return BadRequest(WebstoreBaseOutputDto<ResultDataGift>.Error(res.Code, res.Message));
                }

                _logger.LogError(ex, "Error getting gift lists");
                return BadRequest(WebstoreBaseOutputDto<ResultDataGift>.Error("500", "Internal server error"));
            }
        }

        [HttpGet("gift-details")]
        public async Task<ActionResult<WebstoreBaseOutputDto<ListResultGetByIdAndRelatedGift>>> GetGiftDetails([FromQuery] int? GiftId = null)
        {
            try
            {
                if (!GiftId.HasValue)
                {
                    return BadRequest(WebstoreBaseOutputDto<GiftDetailData>.Error("200", "Gift Id is required"));
                }

                var hasToken = HttpContext.Request.Headers.TryGetValue("Authorization", out var authHeader);
                var memberCode = "";
                if (hasToken)
                {
                    // Check if authHeader valid
                    var extractedToken = authHeader.ToString().Replace("Bearer ", "");
                    var tokenCacheKey = "TOKEN_" + extractedToken;
                    var cachedResult = await _cache.GetStringAsync(tokenCacheKey);
                    if (cachedResult != null)
                    {
                        if (cachedResult == "YES")
                        {
                            memberCode = await _cache.GetStringAsync("MemberCode_" + extractedToken);
                        }
                    }

                    if (string.IsNullOrEmpty(memberCode))
                    {
                        var checkTokenInReward = await _rewardMemberService.WebstoreValidateToken(extractedToken);
                        if (checkTokenInReward.IsValid)
                        {
                            memberCode = checkTokenInReward.MemberCode;
                            await _cache.SetStringAsync("TOKEN_" + extractedToken, "YES", new DistributedCacheEntryOptions
                            {
                                AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1)
                            });
                            await _cache.SetStringAsync("MemberCode_" + extractedToken, memberCode, new DistributedCacheEntryOptions
                            {
                                AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1)
                            });
                        }
                    }
                    
                }

                _logger.LogInformation(">> GetGiftDetails >> Input: " + JsonConvert.SerializeObject(new { GiftId }));
                var cacheKey = $"WEBSTORE_GET_GIFT_DETAIL_{memberCode}_{GiftId}";
                var cachedString = await _cache.GetStringAsync(cacheKey);
                if (!string.IsNullOrWhiteSpace(cachedString))
                {
                    try
                    {
                        _logger.LogInformation(">> GetGiftDetails >> Found in cache! Returning...!");
                        var cachedObj = JsonConvert.DeserializeObject<GetByIdAndRelatedGiftOutput>(cachedString);
                        if (cachedObj != null)
                        {
                            return Ok(WebstoreBaseOutputDto<ListResultGetByIdAndRelatedGift>.Success(cachedObj.Result));
                        }

                        throw new Exception("CachedObjectIsNull");
                    }
                    catch (Exception e)
                    {
                        // neu loi parse object thi clear cache
                        _logger.LogError(">> GetGiftDetails >> fail to parse cached object  >> " + e.Message);
                        await _cache.RemoveAsync(cacheKey);
                    }
                }

                var query = new QueryGiftData
                {
                    GiftId = GiftId, 
                    IntroduceMode = "SEPARATE", 
                    MemberCode = memberCode,
                    PartnerCode = "LynkiDWebApp"
                };
                var response =
                    await _giftService.GetGiftDetailWithoutMemberCode(query);
                if (!response.Success)
                {
                    var message = CommonHelper.WebstoreGetLoyaltyMessageByCode(response.Result.ErrorCode);
                    
                    return BadRequest(WebstoreBaseOutputDto<ListResultGetByIdAndRelatedGift>.Error(response.Result.ErrorCode ?? "100",
                        message));
                }

                if (response.Result.GiftInfor.MaxQuantityPerRedemptionOfUser.HasValue == false ||
                    response.Result.GiftInfor.MaxQuantityPerRedemptionOfUser == 0)
                {
                    response.Result.GiftInfor.MaxQuantityPerRedemptionOfUser = _maxQuantityPerRedeem;
                }

                var serialized = JsonConvert.SerializeObject(response);
                await _cache.SetStringAsync(cacheKey, serialized,
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(1)));
                return Ok(WebstoreBaseOutputDto<ListResultGetByIdAndRelatedGift>.Success(response.Result));
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    return BadRequest(WebstoreBaseOutputDto<GiftDetailData>.Error(res.Code, res.Message));;
                }
                _logger.LogError(ex, "GetGiftDetails: Error getting gift details");
                return BadRequest(WebstoreBaseOutputDto<GiftDetailData>.Error("500", "Internal server error"));
            }
        }
        
        [HttpGet]
        [Route("Location/GetAll")]
        public async Task<ActionResult<WebstoreBaseOutputDto<ListResultGetLocationAll>>> GetAll([FromQuery] GetAllLocationInput input)
        {
            try
            {
                var result = await _loyaltyLocationService.GetAll(input);
                return Ok(WebstoreBaseOutputDto<ListResultGetLocationAll>.Success(result.Result));
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Location GetAll Error - " + JsonConvert.SerializeObject(ex));
                return BadRequest(WebstoreBaseOutputDto<ListResultGetLocationAll>.Error(res.Code, res.Message));
            }
        }
        [HttpGet]
        [Route("Location/GetByIds")]
        public async Task<ActionResult<WebstoreBaseOutputDto<ViewLocationByIdsOutput>>> ViewLocationByIds([FromQuery]ViewLocationByIds input)
        {
            try
            {
                var result = await _loyaltyLocationService.ViewLocationByIds(input);
                return Ok(WebstoreBaseOutputDto<ViewLocationByIdsOutput>.Success(result));
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Location/GetByIds Error - " + JsonConvert.SerializeObject(ex));
                return BadRequest(WebstoreBaseOutputDto<ListResultGetLocationAll>.Error(res.Code, res.Message));
            }
        }
        
        [Route("GetBanners")]
        [HttpGet]
        public async Task<ActionResult<WebstoreBaseOutputDto<GetBannersOutput>>> GetBanners(
            [FromQuery] int SkipCount = 0,
            [FromQuery] int MaxResultCount = 10
            )
        {
            try
            {
                _logger.LogInformation(" >> Get Banners >> " + SkipCount + " - " + MaxResultCount);
                if (MaxResultCount > 50)
                {
                    MaxResultCount = 50;
                }

                var cacheKey = "WEBSTORE_GETBANNERS_" + SkipCount + "_" + MaxResultCount;
                var stringCache = await _cache.GetStringAsync(cacheKey);
                if (!string.IsNullOrEmpty(stringCache))
                {
                    try
                    {
                        var ret = JsonConvert.DeserializeObject<WebstoreBaseOutputDto<GetBannersOutput>>(stringCache);
                        return ret;
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(" >> Get Banner >> Cannot Parse JSON from cache");
                        await _cache.RemoveAsync(cacheKey);
                    }
                }
                var result = await _loyaltyArticleService.GetBanners(SkipCount, MaxResultCount);
                var ret2 = WebstoreBaseOutputDto<GetBannersOutput>.Success(new GetBannersOutput()
                {
                    ListBanners = result.Result.Items.Select(x => new GetBannersOutputInner()
                    {
                        Id = (int)x.Id, Code = x.Code, ImageLink = x.LinkAvatar, Description = x.Description
                    }).ToList()
                });
                ;
                await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(ret2),
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(60)));
                return Ok(ret2);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetBanners Error - " + JsonConvert.SerializeObject(ex));
                return BadRequest(WebstoreBaseOutputDto<GetBannersOutput>.Error(res.Code, res.Message));
            }
        }
        
        [Route("get-article-by-id")]
        [HttpGet]
        public async Task<ActionResult<WebstoreBaseOutputDto<CreateOrEditArticleDto>>> GetArticleById(
            [FromQuery] int Id = 0
            )
        {
            try
            {
                _logger.LogInformation(" >> GetArticleById >> " + Id);
                if (Id <= 0)
                {
                    return BadRequest(WebstoreBaseOutputDto<CreateOrEditArticleDto>.Error(WebstoreConstants.LoginErrorCodes.COMMON_INVALIDINPUT, "Invalid Input"));
                }

                var cacheKey = "WEBSTORE_GetArticleById_" + Id;
                var stringCache = await _cache.GetStringAsync(cacheKey);
                if (!string.IsNullOrEmpty(stringCache))
                {
                    try
                    {
                        var ret = JsonConvert.DeserializeObject<WebstoreBaseOutputDto<CreateOrEditArticleDto>>(stringCache);
                        return ret;
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(" >> Get Article By Id >> Cannot Parse JSON from cache");
                        await _cache.RemoveAsync(cacheKey);
                    }
                }
                var result = await _loyaltyArticleService.GetArticleByIdAndRelatedNews(new GetArticleByIdAndRelatedNewsInput(){Id = Id});
                var ret2 = WebstoreBaseOutputDto<CreateOrEditArticleDto>.Success(result.Result.Article);
                await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(ret2),
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(60)));
                return Ok(ret2);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetArticleById Error - " + JsonConvert.SerializeObject(ex));
                return BadRequest(WebstoreBaseOutputDto<CreateOrEditArticleDto>.Error(res.Code, res.Message));
            }
        }
        
        [Route("AgreeTAndC")]
        [HttpPost]
        public async Task<ActionResult<WebstoreBaseOutputDto<AgreeTAndCOutput>>> AgreeTAndC(
            [FromBody] AgreeTAndCInput input
            )
        {
            try
            {
                _logger.LogInformation(" >> GetArticleById >> " + JsonConvert.SerializeObject(input));
                if (input == null || string.IsNullOrEmpty(input.PhoneNumber))
                {
                    return BadRequest(WebstoreBaseOutputDto<CreateOrEditArticleDto>.Error(WebstoreConstants.LoginErrorCodes.COMMON_INVALIDINPUT, "Invalid Input"));
                }

                input.MemberCode = null;

                var result = await _rewardMemberService.AgreeTAndC(new
                {
                    PhoneNumber = input.PhoneNumber, Note = input.Note
                });
                return Ok(WebstoreBaseOutputDto<AgreeTAndCOutput>.Success(new AgreeTAndCOutput()));
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetArticleById Error - " + JsonConvert.SerializeObject(ex));
                return BadRequest(WebstoreBaseOutputDto<CreateOrEditArticleDto>.Error(res.Code, res.Message));
            }
        }
    }
}