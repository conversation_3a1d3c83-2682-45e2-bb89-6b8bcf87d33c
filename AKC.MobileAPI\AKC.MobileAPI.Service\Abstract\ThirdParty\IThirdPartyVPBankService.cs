﻿using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.PartnerPointCaching;
using AKC.MobileAPI.DTO.ThirdParty.VPBank;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.ThirdParty
{
    public interface IThirdPartyVPBankService
    {
        Task<LoyaltyThirdPartyPointViewOutput> PointView(LoyaltyThirdPartyPointViewInput input, HttpContext context);
        Task<LoyaltyThirdPartyVerifyNationalIdOutput> VerifyNationalId(LoyaltyThirdPartyVerifyNationalIdInput input, HttpContext context, string authorization);
        Task<LoyaltyThirdPartyVerifyOTPOutput> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input, HttpContext context, string authorization);
        Task<LoyaltyThirdPartyPointExchangeOutput> PointExchange(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null);
        Task<LoyaltyThirdPartyRevertPointOutput> RevertPoint(LoyaltyThirdPartyRevertPointInput input, HttpContext context);
        Task<LoyaltyThirdPartyRequestAccessTokenOutput> RequestAccessToken(LoyaltyThirdPartyRequestAccessTokenInput input, HttpContext context);
        Task<RewardPartnerPoingCachingItems> UpdatePartnerCaching(LoyaltyThirdPartyVPBankUpdatePartnerCachingInput input, HttpContext context);
        Task<LoyaltyThirdPartyPointExchangeOutput> PointExchangeIntegration(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null);
        Task<LoyaltyThirdPartyRemoveConnectedMerchantOutput> RemoveConnectedMerchant(RewardMemberUpdateOutputDto member, LoyaltyThirdPartyRemoveConnectedMerchant input);
        Task<LoyaltyThirdPartyConfirmConnectOutput> ConfirmConnect(LoyaltyThirdPartyConfirmConnectInput input, string authorization);
        Task<LoyaltyThirdPartyUpdatePhoneNumberOutput> UpdatePhoneNumber(UpdatePhoneNumberInput input);
        Task<LoyaltyThirdPartySendOtpConfirmConnectOutput> SendOtpConfirmConnect(LoyaltyThirdPartySendOtpConfirmConnectInput input, string authorization);
    }
}
