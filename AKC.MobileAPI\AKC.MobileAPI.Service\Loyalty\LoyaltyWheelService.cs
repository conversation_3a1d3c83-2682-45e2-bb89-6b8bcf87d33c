﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.Wheel;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyWheelService : BaseLoyaltyService, ILoyaltyWheelService
    {
        private readonly IDistributedCache _cache;
        private readonly ILogger<LoyaltyWheelService> _logger;
        public LoyaltyWheelService(
            IConfiguration configuration,
            IDistributedCache cache,
            ILogger<LoyaltyWheelService> l
            ) : base(configuration, cache)
        {
            _cache = cache;
            _logger = l;
        }

        public async Task<LoyaltyResponeOutput<CreateOrUpdateWheelByMemberCodeOutput>> CreateWheel(CreateWheelInput input)
        {
            if (input.glstWheelOption != null && input.glstWheelOption.Count > 0)
            {
                foreach (var wheelOptions in input.glstWheelOption)
                {
                    wheelOptions.Status = "A";
                }
            }
            var ret = await PostLoyaltyAsync<LoyaltyResponeOutput<CreateOrUpdateWheelByMemberCodeOutput>>(LoyaltyApiUrl.Wheels_CreateWheelByMemberCode, input);
            var CacheKeyWHEELLIST = $"WHEEL_LIST_OF__{input.MemberCode}";
            await _cache.RemoveAsync(CacheKeyWHEELLIST);
            return ret;
        }

        public async Task<LoyaltyResponeOutput<GetListWheelOutput>> GetListWheel(GetListWheelInput input)
        {
            var CacheKey = $"WHEEL_LIST_OF__{input.MemberCode}";
            var cachedData = await _cache.GetStringAsync(CacheKey);
            if(string.IsNullOrEmpty(cachedData))
            {
                var result = await GetLoyaltyAsync<LoyaltyResponeOutput<GetListWheelOutput>>(LoyaltyApiUrl.Wheels_GETALL, input);
                await _cache.SetStringAsync(CacheKey, JsonConvert.SerializeObject(result),
                  new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(30)));
                return result;
            }
            else
            {
                var convertedObj = JsonConvert.DeserializeObject< LoyaltyResponeOutput<GetListWheelOutput>>(cachedData);
                return convertedObj;
            }
        }

        public async Task<LoyaltyResponeOutput<GetListWheelOutput>> GetSystemWheels(GetListWheelInput input)
        {
            var CacheKey = $"SYSTEMWHEEL_LIST";
            var cachedData = await _cache.GetStringAsync(CacheKey);
            if (string.IsNullOrEmpty(cachedData))
            {
                var result = await GetLoyaltyAsync<LoyaltyResponeOutput<GetListWheelOutput>>(LoyaltyApiUrl.Wheels_GETALL_SYSTEMWHEEL, input);
                await _cache.SetStringAsync(CacheKey, JsonConvert.SerializeObject(result),
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(30)));
                return result;
            }
            else
            {
                var convertedObj = JsonConvert.DeserializeObject<LoyaltyResponeOutput<GetListWheelOutput>>(cachedData);
                return convertedObj;
            }
        }

        public async Task<LoyaltyResponeOutput<GetOneWheelOutput>> GetOneWheelByMemberCode(GetOneWheelInput input)
        {
            var CacheKey = $"WHEEL_OPTIONS_OF__{input.MemberCode}_{input.WheelId}";
            var cachedData = await _cache.GetStringAsync(CacheKey);
            if (string.IsNullOrEmpty(cachedData))
            {
                var result = await GetLoyaltyAsync<LoyaltyResponeOutput<GetOneWheelOutput>>(LoyaltyApiUrl.Wheels_GetOneWheelByMemberCode, input);
                await _cache.SetStringAsync(CacheKey, JsonConvert.SerializeObject(result),
                  new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(30)));
                return result;
            }
            else
            {
                var convertedObj = JsonConvert.DeserializeObject<LoyaltyResponeOutput<GetOneWheelOutput>>(cachedData);
                return convertedObj;
            }
        }

        public async Task<LoyaltyResponeOutput<CreateOrUpdateWheelByMemberCodeOutput>> UpdateWheel(UpdateWheelInput input)
        {
            var CacheKeyWHEELLIST = $"WHEEL_LIST_OF__{input.MemberCode}";
            var CacheKeyWHEELOPTIONS = $"WHEEL_OPTIONS_OF__{input.MemberCode}_{input.WheelId}";
            await _cache.RemoveAsync(CacheKeyWHEELLIST);
            await _cache.RemoveAsync(CacheKeyWHEELOPTIONS);
            if (input.glstWheelOption != null && input.glstWheelOption.Count > 0)
            {
                foreach (var wheelOptions in input.glstWheelOption)
                {
                    wheelOptions.Status = "A";
                }
            }
            return await PostLoyaltyAsync<LoyaltyResponeOutput<CreateOrUpdateWheelByMemberCodeOutput>>(LoyaltyApiUrl.Wheels_UpdateWheelByMemberCode, input);
        }

        public async Task<LoyaltyResponeOutput<SpinWheelOutput>> Spin(SpinWheelInput input)
        {
            _logger.LogInformation(" >> Spin Wheel >> " + JsonConvert.SerializeObject(input));
            var it = await PostLoyaltyAsync<LoyaltyResponeOutput<SpinWheelOutput>>(
                LoyaltyApiUrl.Wheels_DoSpin,
                input
            );
            return it;
        }
        public async Task<LoyaltyResponeOutput<DeleteWheelOutput>> DeleteWheel(DeleteWheelInput input)
        {
            _logger.LogInformation(" >> Delete Wheel >> " + JsonConvert.SerializeObject(input));
            var CacheKey = $"WHEEL_OPTIONS_OF__{input.MemberCode}_{input.WheelId}";
            await _cache.RemoveAsync(CacheKey);
            
            CacheKey = $"WHEEL_LIST_OF__{input.MemberCode}";
            await _cache.RemoveAsync(CacheKey);
            var it = await DeleteLoyaltyAsync<LoyaltyResponeOutput<DeleteWheelOutput>>(
                LoyaltyApiUrl.Wheels_DeleteMemberWheel,
                input
            );
            return it;
        }

        public async Task<LoyaltyResponeOutput<GetSpinHistoryOutput>> GetSpinHistory(GetSpinHistoryInput input)
        {
            _logger.LogInformation(" >> Get Spin History >> " + JsonConvert.SerializeObject(input));
            var it = await GetLoyaltyAsync<LoyaltyResponeOutput<GetSpinHistoryOutput>>(
                LoyaltyApiUrl.Wheels_GetSpinHistory,
                input
            );
            return it;
        }
    }
}
