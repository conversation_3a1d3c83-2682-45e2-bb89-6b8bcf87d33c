using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory.V2;
using Microsoft.AspNetCore.Http;

namespace AKC.MobileAPI.Service.Abstract.NotiV2
{
    public interface INotificationHistoryV2Service
    {
        Task<GetByMemberOutput> GetByMember(GetByMemberInput input);
        Task<MarkAsReadOutput> MarkAsRead(MarkAsReadInput input);
        Task<TokenTransDetailOutput> TokenTransDetail(TokenTransDetailInput input);
    }
}