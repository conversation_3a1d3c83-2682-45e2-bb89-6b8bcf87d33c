﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Loyalty.EGiftInfors
{
    public class UpdateGiftStatusInput
    {
        [Required]
        public string GiftRedeemTransactionCode { get; set; }
        [Required]
        public string MemberCode { get; set; }

        public string TopupChannel { get; set; }
        public DateTime? TopupTime { get; set; }
    }

    public class UpdateEgiftCodeInputDto
    {
        [Required]
        public string GiftRedeemTransactionCode { get; set; }
    }

    public class GetVendorInfoFromTransCodeInput
    {
        public string ListTransactionCode { get; set; }
    }
    public class GetVendorInfoFromTransCodeOutput
    {
        public List<GetVendorInfoFromTransCodeOutputRS> Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class GetVendorInfoFromTransCodeOutputRS
    {
        public string TransactionCode { get; set; }
        public string VendorName { get; set; }
        public string VendorAvatar { get; set; }
        public bool IsTopup { get; set; }
    }
}
