﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Article
{
    public class GetAllArticleByMemberCodeInput
    {
        [Required]
        public string MemberCode { get; set; }
        public string Filter { get; set; }
        [Required]
        public int? CategoryTypeFilter { get; set; }
        public string TagsFilter { get; set; }
        public int MaxItem { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
        public string Sorting { get; set; }
    }
}
