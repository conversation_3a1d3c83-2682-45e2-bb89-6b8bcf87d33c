﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Claim_Deposit
{
    public class GeneratePaymentLinkRequest
    {
        [Required]
        public decimal TotalAmount { get; set; }
        [Required]
        public decimal MoneyAmount { get; set; }
        [Required]
        public string MemberCode { get; set; }
        [Required]
        public int MerchantId { get; set; }
        public string PaymentByIpAddress { get; set; }
        public string PaymentGateway { get; set; }
        public string CardType { get; set; }
    }

    public class GeneratePaymentLinkDto
    {
        public string BackUrl { get; set; }
        public string ReturnUrl { get; set; }
        public string OrderCode { get; set; }
        public decimal TotalAmount { get; set; }
        public string OrderTitleInfo { get; set; }
        public string PaymentGateway { get; set; }
        public string CardType { get; set; }
        public string PaymentByIpAddress { get; set; }
    }

    public class GeneratePaymentLinkCache
    {
        public decimal TotalAmount { get; set; }
        public decimal MoneyAmount { get; set; }
        public string MemberCode { get; set; }
        public int MerchantId { get; set; }
        public string PaymentGateway { get; set; }
        public string CardType { get; set; }
        public string OrderCode { get; set; }
    }
}
