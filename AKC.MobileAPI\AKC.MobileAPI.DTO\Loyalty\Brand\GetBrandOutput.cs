﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Brand
{
    public class GetBrandOutput
    {
        public GetListBrandOutput Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }
    public class GetListBrandOutput
    {
        public int TotalCount { get; set; }
        public List<GetListBrandChildren> Items { get; set; }
    }

    public class GetListBrandChildren
    {
        public BrandInfo brandInfo { get; set; }
    }

    public class BrandInfo
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public string BrandLinkLogo { get; set; }
        public string BrandDescription { get; set; }
        public string BrandAddress { get; set; }
        public bool IsFavourite { get; set; }
    }

    public class GetBrandByCategoryOutput
    {
        public GetListBrandByCategoryOutput Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }
    public class GetListBrandByCategoryOutput
    {
        public int TotalCount { get; set; }
        public List<Item> Items { get; set; }
    }

    public class Item
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public string BrandLinkLogo { get; set; }
        public string BrandDescription { get; set; }
        public string BrandAddress { get; set; }
        public long? NumberOfGifts { get; set; }
        public bool IsFavourite { get; set; }
        public int? DisplayOrder { get; set; } = 100;
    }
}
