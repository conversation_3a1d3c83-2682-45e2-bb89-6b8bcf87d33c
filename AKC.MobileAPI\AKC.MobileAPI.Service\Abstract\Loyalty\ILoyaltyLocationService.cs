﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyLocationService
    {
        Task<GetAllLocationManagementDto> GetAll(GetAllLocationInput input);
        Task<GetAllLocationManagementDto> GetAllNoPaging(GetAllLocationNoPagingInput input);
        Task<ViewLocationByIdsOutput> ViewLocationByIds(ViewLocationByIds input);
    }
}
