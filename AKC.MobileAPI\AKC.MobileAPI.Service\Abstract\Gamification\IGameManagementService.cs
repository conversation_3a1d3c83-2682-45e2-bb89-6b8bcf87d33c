﻿using AKC.MobileAPI.DTO.Gamification;
using AKC.MobileAPI.DTO.Gamification.Exchange;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Gamification
{
    public interface IGameManagementService
    {
        Task<ViewGameListOutput> ViewGameList(ViewGameListInput input);
        Task<GetGamificationLinkOutput> GetGamificationLink(GetGamificationLinkInput input);
        Task<GetAuthenticationKeyOutput> GetAuthenticationKey(GetAuthenticationKeyInput input);
    }
}
