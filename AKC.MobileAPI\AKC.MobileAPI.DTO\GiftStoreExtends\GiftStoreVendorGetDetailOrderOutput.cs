﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.GiftStoreExtends
{
    public class GiftStoreVendorGetDetailOrderOutput
    {
        public string TransactionCode { get; set; }
        public DateTime? TransactionTime { get; set; }
        public string Status { get; set; }
        public long? TotalPrice { get; set; }
        public int? GiftId { get; set; }
        public string GiftCode { get; set; }
        public string GiftType { get; set; }
        public string GiftName { get; set; }
        public List<GiftStoreVendorGetDetailOrderEgiftDetails> EgiftDetails { get; set; }
    }

    public class GiftStoreVendorGetDetailOrderEgiftDetails
    {
        public string CodeDisplay { get; set; }
        public float? Price { get; set; }
        public string EgiftCode { get; set; }
        public string QrCode { get; set; }
        public DateTime? EGiftExpiredDate { get; set; }
        public string EGiftStatus { get; set; }
    }
}
