using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers
{
    [Route("api/mini-app")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class MiniAppController: ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly ILoyaltyGiftService _loyaltyGiftService;
        public MiniAppController(
            ILogger<MiniAppController> logger,
            IExceptionReponseService exceptionReponseService,
            ILoyaltyGiftService ls,
            ICommonHelperService ch
        )
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _loyaltyGiftService = ls;
            _commonHelperService = ch;
        }

        [Route("get-all")]
        [HttpGet]
        public async Task<ActionResult<GetMiniAppOutput>> GetMiniApps([FromQuery] GetMiniAppInput input)
        {
            try
            {
                if (input.MaxResultCount > 100)
                {
                    input.MaxResultCount = 100;
                }

                if (input.MaxResultCount <= 0)
                {
                    input.MaxResultCount = 10;
                }

                if (input.SkipCount < 0)
                {
                    input.SkipCount = 0;
                }
                _logger.LogInformation(" >> GetMiniApps >> " + JsonConvert.SerializeObject(input));
                var result = await _loyaltyGiftService.GetMiniApps(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetMiniApps Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}