﻿using AKC.MobileAPI.DTO.Reward.PartnerPointCaching;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardPartnerPointCachingService : RewardBaseService, IRewardPartnerPointCachingService
    {
        public RewardPartnerPointCachingService(
            IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<RewardPartnerPoingCachingOutput> RequestUpdateIntegration(RewardPartnerPoingCachingInput input)
        {
            return await PostRewardAsync<RewardPartnerPoingCachingOutput>(RewardApiUrl.PARTNER_POINT_CACHING_REQUEST_UPDATE, input);
        }
    }
}