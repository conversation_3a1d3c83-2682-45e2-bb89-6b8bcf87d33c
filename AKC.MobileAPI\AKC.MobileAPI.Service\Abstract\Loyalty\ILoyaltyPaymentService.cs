﻿using AKC.MobileAPI.DTO.Loyalty.Challenge;
using AKC.MobileAPI.DTO.Loyalty.Payment;
using AKC.MobileAPI.DTO.Reward.Claim_Deposit;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyPaymentService
    {
        Dictionary<string, string> GetDataFromURLCallBack(IQueryCollection input);
        Task<GeneratePaymentLinkResponse> GeneratePaymentLink(string hostPath, GeneratePaymentLinkRequest input, string authorization);
        Task<GetDataFromURLReturnUrlOutputDto> GetDataFromReturnUrl(string url, IQueryCollection input);
    }
}
