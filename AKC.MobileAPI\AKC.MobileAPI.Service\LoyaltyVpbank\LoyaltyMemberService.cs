﻿using AKC.MobileAPI.DTO.LoyaltyVpbank.Member;
using AKC.MobileAPI.Service.Abstract.LoyaltyVpbank;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.LoyaltyVpbank
{
    public class LoyaltyMemberService : BaseLoyaltyVpbankService, ILoyaltyMemberService
    {
        public LoyaltyMemberService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }
        public async Task<GetMemberVpbankInforOutput> GetMemberVpbankInfor(GetMemberVpbankInforInput input)
        {
            return await GetLoyaltyAsync<GetMemberVpbankInforOutput>(LoyaltyVpbankApiUrl.GET_MEMBER_INFOR, input);
        }
        public async Task<VPBankCustomerInfoOutput> GetVPBankCustomerInfo(VPBankCustomerInfoInput input)
        {
            return await GetLoyaltyAsync<VPBankCustomerInfoOutput>(LoyaltyVpbankApiUrl.GET_VPBANK_CUSTOMER_INFO, input);
        }

        public async Task<object> RemoveConnection(int linkidMemberId)
        {
            return await PostLoyaltyAsync<DeleteUserMappingResponse>(LoyaltyVpbankApiUrl.REMOVE_CONNECTION_DUETO_ACC_DELETED, new
            {
                LinkID_MemberID = linkidMemberId
            });
        }
    }
}
