using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty.Gift
{
    [Route("api/SearchBar")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    //[AllowAnonymous]
    public class LoyaltySearchBarController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltySearchBarService _loyaltySearchBarService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IDistributedCache _cache;
        public LoyaltySearchBarController
        (
              ILogger<LoyaltyGiftInforController> logger,
              ILoyaltySearchBarService loyaltySearchBarService,
              IExceptionReponseService exceptionReponseService,
              ICommonHelperService commonHelperService,
              IDistributedCache cache
        )
        {
            _logger = logger;
            _loyaltySearchBarService = loyaltySearchBarService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
            _cache = cache;
        }

        [HttpGet]
        [Route("GetAll")]
        public async Task<ActionResult<LoyaltyGetAllForSearchBarOutPut>> GetAll([FromQuery] LoyaltyGetAllForSearchBarInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }

                var result = await _loyaltySearchBarService.GetAll(input);
                
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {

                var res = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result;
                _logger.LogError(ex, "GetAll Search Bar Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }
    }
}
