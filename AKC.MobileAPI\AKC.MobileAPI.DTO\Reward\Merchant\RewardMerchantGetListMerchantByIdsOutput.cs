﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Merchant
{
    public class RewardMerchantGetListMerchantByIdsOutput
    {
        public int result { get; set; }
        public int totalCount { get; set; }
        public string message { get; set; }
        public List<RewardMerchantGetListMerchantByIdsItem> items { get; set; }
    }

    public class RewardMerchantGetListMerchantByIdsItem
    {
        public int MerchantId { get; set; }
        public string WalletAddress { get; set; }
        public string MerchantName { get; set; }
        public string Logo { get; set; }
        public string MerchantType { get; set; }
        public decimal MerchantExchangeRate { get; set; }
        public decimal BaseUnit { get; set; }
        public decimal CurrencyExchangeRate { get; set; }
    }
}
