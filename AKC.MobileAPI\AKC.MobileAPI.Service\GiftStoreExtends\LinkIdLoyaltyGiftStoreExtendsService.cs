﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.GiftStoreExtends;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.Merchant;
using AKC.MobileAPI.DTO.ThirdParty.VPBankLoyaltyExchange;
using AKC.MobileAPI.Service.Abstract.GiftStoreExtends;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.GiftStoreExtends
{
    public class LinkIdLoyaltyGiftStoreExtendsService : BaseLoyaltyGiftStoreService, ILinkIdLoyaltyGiftStoreExtendsService
    {
        private readonly ILogger _logger;
        private IRewardMemberService _rewardMemberService;
        private IRewardMerchantService _rewardMerchantService;
        private IRewardGiftRedeemTransactionService _rewardGiftRedeemTransactionService;
        public LinkIdLoyaltyGiftStoreExtendsService(
            IConfiguration configuration,
            IRewardMemberService rewardMemberService,
            IRewardGiftRedeemTransactionService rewardGiftRedeemTransactionService,
            ILogger<LinkIdLoyaltyGiftStoreExtendsService> logger,
            IRewardMerchantService rewardMerchantService,
            IDistributedCache cache) : base(configuration, cache, logger)
        {
            _rewardMemberService = rewardMemberService;
            _rewardGiftRedeemTransactionService = rewardGiftRedeemTransactionService;
            _logger = logger;
            _rewardMerchantService = rewardMerchantService;
        }

        public async Task<GiftStoreExtendsCreateRedeemTransactionOutput> CreateRedeemTransaction(GiftStoreExtendsCreateRedeemTransactionInput input)
        {
            var tenantConfig = GetConfigByMerchantId(input.MerchantId);
            var tenantId = tenantConfig.TenantId;
            var merchantId = tenantConfig.DefaultRedeemMerchantId;
            var resultMember = await _rewardMemberService.GetReferenceDataConnected(new RewardMemberGetReferenceDataConnectedInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = merchantId,
            });
            //var merchantIdRedeem = await GetMerchantIdFromGiftCode(new GiftStoreExtendsGetMerchantIdFromGiftCodeInput()
            //{
            //    GiftCode = input.GiftCode
            //}, tenantId);
            //if (merchantIdRedeem != null)
            //{
            //    merchantId = merchantIdRedeem.Value;
            //}
            var orderCode = GenOrderCode(input.GiftCode, input.MemberCode);
            var cts = new CancellationTokenSource();
            var memberCode = resultMember.Item.MemberCode;
            // Get token from total amount
            var totalTokenRequest = Math.Floor(input.TotalAmount * (resultMember.Item.MerchantExchangeRate / resultMember.Item.BaseUnit));
            if (resultMember.Item.TokenBalance < totalTokenRequest)
            {
                //throw new ArgumentException("Token Balance is not enough to make this transaction");
                return new GiftStoreExtendsCreateRedeemTransactionOutput()
                {
                    ErrorCode = "BalanceNotEnough",
                    SuccessedOrder = false,
                    Items = null,
                    Messages = "Token Balance is not enough to make this transaction",
                    TransactionCode = null,
                };
            }
            var requestRedeem = new RewardCreateGiftRedeemTransactionRequest()
            {
                OrderCode = orderCode,
                MerchantId = merchantId,
                NationalId = input.MemberCode,
                TotalRequestedAmount = totalTokenRequest,
            };
            try
            {
                var resultRedeemReward = await _rewardGiftRedeemTransactionService.CreateRedeem(requestRedeem);
                var request = new GiftStoreExtendsCreateRedeemTransactionDto()
                {
                    GiftCode = input.GiftCode,
                    Quantity = input.Quantity,
                    TransactionCode = orderCode,
                    CustomerFullName = input.CustomerFullName,
                    CustomerGender = input.CustomerGender,
                    CustomerMemberCode = memberCode,
                    CustomerNote = input.CustomerNote,
                    CustomerPhoneNumber = input.CustomerPhoneNumber,
                    FullLocationShip = input.FullLocationShip,
                    Price = input.TotalAmount,
                    ShipAddress = input.ShipAddress,
                    CustomerEmail = input.CustomerEmail,
                    WardId = "0",
                    CityId = "0",
                    DistrictId = "0",
                };
                var result = await PostLoyaltyAsync<GiftStoreExtendsCreateRedeemTransactionOutputDto>(tenantId, GiftStoreExtendsApiUrl.GIFTTRANSACTION_CREATEREDEEMTRANSACTION, request);
                if (!result.Result.SuccessedOrder)
                {
                    var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
                    {
                        MerchantId = merchantId,
                        NationalId = input.MemberCode,
                        OrderCode = orderCode,
                        TokenAmount = input.TotalAmount,
                    };

                    await retryRevertToken(requestRevert);
                }
                var resultItem = result.Result;
                return new GiftStoreExtendsCreateRedeemTransactionOutput()
                {
                    ErrorCode = resultItem.ErrorCode,
                    Items = resultItem.Items,
                    Messages = resultItem.Messages,
                    SuccessedOrder = resultItem.SuccessedOrder,
                    TransactionCode = resultItem.TransactionCode,
                    TotalRedeemToken = resultRedeemReward.Items.TokenAmount,
                };
            }
            catch (WebException ex)
            {
                var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
                {
                    MerchantId = merchantId,
                    NationalId = input.MemberCode,
                    OrderCode = orderCode,
                    TokenAmount = input.TotalAmount,
                };
                await retryRevertToken(requestRevert);
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
                {
                    MerchantId = merchantId,
                    NationalId = input.MemberCode,
                    OrderCode = orderCode,
                    TokenAmount = input.TotalAmount,
                };
                await retryRevertToken(requestRevert);
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with: ", ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
                {
                    MerchantId = merchantId,
                    NationalId = input.MemberCode,
                    OrderCode = orderCode,
                    TokenAmount = input.TotalAmount,
                };
                await retryRevertToken(requestRevert);
                throw ex;
            }

        }

        public async Task<int?> GetMerchantIdFromGiftCode(GiftStoreExtendsGetMerchantIdFromGiftCodeInput input, int tenantId)
        {
            try
            {
                var output = await GetLoyaltyAsync<GiftStoreExtendsGetMerchantIdFromGiftCodeOutput>(tenantId, GiftStoreExtendsApiUrl.GET_MERCHANT_ID_FROM_GIFT_CODE, input);
                return output.Result.MerchantId;
            }
            catch
            {
                return null;
            }
        }

        public async Task<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetBrandOutput>> GetBrands(GiftStoreVendorGetBrandInput input)
        {
            var tenantConfig = GetConfigByMerchantId(input.MerchantId);
            var request = new GiftStoreVendorGetBrandDto()
            {
                MaxResultCount = input.MaxResultCount ?? 10,
                SkipCount = input.SkipCount ?? 0,
                Sorting = input.Sorting,
            };
            return await GetLoyaltyAsync<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetBrandOutput>>(tenantConfig.TenantId, GiftStoreExtendsApiUrl.GET_BRANDS, request);
        }

        public async Task<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetCategoryOutput>> GetCategories(GiftStoreVendorGetCategoryInput input)
        {
            var tenantConfig = GetConfigByMerchantId(input.MerchantId);
            var request = new GiftStoreVendorGetCategoryDto()
            {
                MaxResultCount = input.MaxResultCount ?? 10,
                SkipCount = input.SkipCount ?? 0,
                Sorting = input.Sorting,
            };
            return await GetLoyaltyAsync<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetCategoryOutput>>(tenantConfig.TenantId, GiftStoreExtendsApiUrl.GET_CATEGORY, request);
        }

        public async Task<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetGiftListOutput>> GetGiftList(GiftStoreVendorGetGiftListInput input)
        {
            var tenantConfig = GetConfigByMerchantId(input.MerchantId);
            var request = new GiftStoreVendorGetGiftListDto()
            {
                MaxResultCount = input.MaxResultCount ?? 10,
                SkipCount = input.SkipCount ?? 0,
                Sorting = input.Sorting,
                BrandId = input.BrandId,
                CategoryId = input.CategoryId,
            };
            return await GetLoyaltyAsync<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetGiftListOutput>>(tenantConfig.TenantId, GiftStoreExtendsApiUrl.GET_GIFT_LIST, request);
        }

        public async Task<GiftStoreExtendsLoyaltyResponse<GiftStoreVendorGetGiftDetailOutput>> GetGiftDetail(GiftStoreVendorGetGiftDetailInput input)
        {
            var tenantConfig = GetConfigByMerchantId(input.MerchantId);
            var request = new GiftStoreVendorGetGiftDetailDto()
            {
                Id = input.Id,
            };
            return await GetLoyaltyAsync<GiftStoreExtendsLoyaltyResponse<GiftStoreVendorGetGiftDetailOutput>>(tenantConfig.TenantId, GiftStoreExtendsApiUrl.GET_GIFT_DETAIL, request);
        }

        public async Task<GiftStoreExtendsLoyaltyResponse<GiftStoreVendorGetDetailOrderOutput>> GetDetailOrder(GiftStoreVendorGetDetailOrderInput input)
        {
            var tenantConfig = GetConfigByMerchantId(input.MerchantId);
            var request = new GiftStoreVendorGetDetailOrderDto()
            {
                TransactionCode = input.TransactionCode,
            };
            return await GetLoyaltyAsync<GiftStoreExtendsLoyaltyResponse<GiftStoreVendorGetDetailOrderOutput>>(tenantConfig.TenantId, GiftStoreExtendsApiUrl.GET_ORDER_DETAIL, request);
        }

        public async Task<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetDetailOrderOutput>> GetListOrder(GiftStoreVendorGetListOrderInput input)
        {
            var tenantConfig = GetConfigByMerchantId(input.MerchantId);
            var request = new GiftStoreVendorGetListOrderDto()
            {
                MemberCode = input.MemberCode,
                MaxResultCount = input.MaxResultCount ?? 10,
                SkipCount = input.SkipCount ?? 0,
                Sorting = input.Sorting,
            };
            return await GetLoyaltyAsync<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetDetailOrderOutput>>(tenantConfig.TenantId, GiftStoreExtendsApiUrl.GET_LIST_ORDER, request);
        }

        public async Task<GiftStoreVendorGetMerchantListOutput> GetMerchantList(GiftStoreVendorGetMerchantListInput input)
        {
            var listMerchantConfig = LoyaltyHelper.GetListMerchantStoreKeyConfig();
            var merchantConfigIds = listMerchantConfig.Select(x => x.DefaultRedeemMerchantId.ToString()).ToList();
            var merchantReward = await _rewardMerchantService.GetListMerchantByIds(new RewardMerchantGetListMerchantByIdsInput()
            {
                MerchantIds = merchantConfigIds,
                MaxResultCount = input.MaxResultCount ?? 10,
                SkipCount = input.SkipCount ?? 0,
                Sorting = input.Sorting,
            });
            var items = merchantReward.items.Select(x => new GiftStoreVendorGetMerchantListItem()
            {
                BaseUnit = x.BaseUnit,
                CurrencyExchangeRate = x.CurrencyExchangeRate,
                Logo = x.Logo,
                MerchantExchangeRate = x.MerchantExchangeRate,
                MerchantId = x.MerchantId,
                MerchantName = x.MerchantName,
                MerchantType = x.MerchantType,
                WalletAddress = x.WalletAddress,
            }).ToList();
            var output = new GiftStoreVendorGetMerchantListOutput()
            {
                Result = 200,
                Items = items,
                Message = "Success",
            };
            return output;
        }

        private async Task<bool> revertToken(RewardRevertGiftRedeemTransactionRequest request)
        {
            try
            {
                await _rewardGiftRedeemTransactionService.RevertRedeem(request);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task retryRevertToken(RewardRevertGiftRedeemTransactionRequest request)
        {
            var retryNum = 4;
            while (retryNum != 0)
            {
                var result = await revertToken(request);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        public async Task<LoyaltyCreateRedeemTransactionOutput> CreateRedeemMerchantTransaction(int tenantIdRequest, LoyaltyCreateRedeemTransactionDto input)
        {
            return await PostLoyaltyAsync<LoyaltyCreateRedeemTransactionOutput>(tenantIdRequest, GiftStoreExtendsApiUrl.GIFTTRANSACTION_CREATE_REDEEM_MERCHANT, input);
        }
    }
}
