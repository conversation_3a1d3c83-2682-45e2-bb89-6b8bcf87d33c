﻿using Cronos;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.Service.CronServices
{
    public class CronHelper
    {
        public static TimeSpan GetDelayToNextRefreshToken(CronExpression cronExpression, TimeZoneInfo timeZoneInfo)
        {
            var next = cronExpression.GetNextOccurrence(DateTimeOffset.Now, timeZoneInfo);
            var delay = next.Value - DateTimeOffset.Now;

            return delay;
        }
    }
}
