﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Gamification
{
    public class ViewGameItemsInput
    {
        public int? MemberID { get; set; }
        //public int? ItemID { get; set; }
        //public string Name { get; set; }
        //public string Status { get; set; }
        //public int? GameID { get; set; }
        //public int? GameMasterID { get; set; }
        //public DateTime? EffectiveDateFrom { get; set; }
        //public DateTime? EffectiveDateTo { get; set; }
        //public int? TypeID { get; set; }
        //public int? InitValue { get; set; }
        //public int? Offset { get; set; }
        //public int? Limit { get; set; }
    }
}
