﻿using System;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.AirlineDto
{
    public class CreateExchangeMilesAirlineRequest
    {
        [Required]
        public string MemberCode { get; set; }
        [Required]
        public int MerchantId { get; set; }
        [Required]
        public decimal TotalAmount { get; set; }
        [Required]
        public int Miles { get; set; }
        [Required]
        public string AirlineMemberCode { get; set; }
        [Required]
        public string MemberFirstName { get; set; }
        [Required]
        public string MemberLastName { get; set; }
        
        public string PromotionCode { get; set; }
        public DateTime? PromotionDate { get; set; }
        public int PromotionMiles { get; set; }
    }

}
