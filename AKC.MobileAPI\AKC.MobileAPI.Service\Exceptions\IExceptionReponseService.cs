﻿using AKC.MobileAPI.DTO.Base;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Exceptions
{
    public interface IExceptionReponseService
    {
        Task<RewardErrorResponse> GetExceptionRewardReponse(Exception ex);
        Task<RewardInvalidResponse> GetExceptionWithDataRewardReponse(Exception ex);
        Task<LoyaltyErrorResponse> GetExceptionLoyaltyReponse(Exception ex);

        Task<GamificationErrorResponse> GetExceptionGamificationReponse(Exception ex);
    }
}
