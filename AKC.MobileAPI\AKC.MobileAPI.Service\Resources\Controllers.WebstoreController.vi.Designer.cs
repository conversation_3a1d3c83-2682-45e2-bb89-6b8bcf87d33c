﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace AKC.MobileAPI.Service.Resources {
    using System;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Controllers_WebstoreController_vi {
        
        private static System.Resources.ResourceManager resourceMan;
        
        private static System.Globalization.CultureInfo resourceCulture;
        
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Controllers_WebstoreController_vi() {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static System.Resources.ResourceManager ResourceManager {
            get {
                if (object.Equals(null, resourceMan)) {
                    System.Resources.ResourceManager temp = new System.Resources.ResourceManager("AKC.MobileAPI.Service.Resources.Controllers_WebstoreController_vi", typeof(Controllers_WebstoreController_vi).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        internal static string InvalidCredentials {
            get {
                return ResourceManager.GetString("InvalidCredentials", resourceCulture);
            }
        }
        
        internal static string LoginError {
            get {
                return ResourceManager.GetString("LoginError", resourceCulture);
            }
        }
        
        internal static string RegistrationRequired {
            get {
                return ResourceManager.GetString("RegistrationRequired", resourceCulture);
            }
        }
        
        internal static string RepPhoneMismatch {
            get {
                return ResourceManager.GetString("RepPhoneMismatch", resourceCulture);
            }
        }
        
        internal static string ThirdPartyError {
            get {
                return ResourceManager.GetString("ThirdPartyError", resourceCulture);
            }
        }
        
        internal static string InternalServerError {
            get {
                return ResourceManager.GetString("InternalServerError", resourceCulture);
            }
        }
        
        internal static string InvalidRefreshToken {
            get {
                return ResourceManager.GetString("InvalidRefreshToken", resourceCulture);
            }
        }
        
        internal static string InvalidOrExpiredRefreshToken {
            get {
                return ResourceManager.GetString("InvalidOrExpiredRefreshToken", resourceCulture);
            }
        }
        
        internal static string MemberNotExistOrInactive {
            get {
                return ResourceManager.GetString("MemberNotExistOrInactive", resourceCulture);
            }
        }
    }
}
