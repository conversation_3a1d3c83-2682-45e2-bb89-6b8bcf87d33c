﻿using AKC.MobileAPI.DTO.Loyalty.CampaignGift;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyCampaignGiftService
    {
        Task<CheckCampaignGiftScanQrResponse> CheckCampaignGiftScanQr(CheckCampaignGiftScanQrInput input, HttpContext httpContext);
        Task<ScanQRCodeResponse> ScanCampaignQRCode(ScanQRCodeInput input, HttpContext httpContext);
    }
}
