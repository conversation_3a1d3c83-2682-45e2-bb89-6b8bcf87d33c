﻿using System;
using System.Collections.Generic;
using System.Text;
using AKC.MobileAPI.DTO.Loyalty.Insurance;

namespace AKC.MobileAPI.DTO.AirlineDto
{
    public class GetListMileExchangeTransactionRequest
    {
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
        public string AirlineCode { get; set; }
        public string Code { get; set; } // mã của gd milesechange 
        public string TransactionCode { get; set; } // mã của bản ghi Transaction tương ứngs
        public string AirlineTransactionCode { get; set; }
        public string AirlineMemberCode { get; set; }
        public DateTime? FromTime { get; set; }
        public DateTime? ToTime { get; set; } // Tìm theo trường TransactionDate
        public string Sorting { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
        public int MerchantId { get; set; }
    }

    public class GetListThirdpartyVendorInput
    {
        public string StatusFilter { get; set; }
        public string TypeFilter { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
    }
    public class GetThirdPartyGiftVendorForView
    {
        public GetThirdPartyGiftVendorForViewInner ThirdPartyGiftVendor { get; set; }
    }

    public class GetThirdPartyGiftVendorForViewInner
    {
        public string VendorName { get; set; }
        public int? MerchantId { get; set; }
        public string Status { get; set; }
        public DateTime CreationTime { get; set; }
        public string Type { get; set; }
    }
    public class GetListThirdpartyVendorOutput
    {
        public int TotalCount { get; set; }
        public List<GetThirdPartyGiftVendorForView> Items { get; set; }
    }
}
