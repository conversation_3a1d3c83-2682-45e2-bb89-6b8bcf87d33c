﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.MobileAPI;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.MemberOtp;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Loyalty.Article;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory.V2;
using AKC.MobileAPI.DTO.Reward;
using AKC.MobileAPI.DTO.Reward.Merchant;
using AKC.MobileAPI.DTO.ThirdParty.Appota;
using AKC.MobileAPI.DTO.Webstore;

namespace AKC.MobileAPI.Service.Abstract.Reward
{
    public interface IRewardMemberService
    {
        Task<RewardMemberViewOutput> View(RewardMemberRequestInput request, string authorization);
        Task<UpdateMemberDeviceIdOutput> UpdateDeviceId(UpdateDeviceIdInput input);
        
        Task<FindOtherUserByPhoneNumberOutput> GetFriendName(FindOtherUserByPhoneNumberInput request);
        Task<RewardMemberUpdateOutputDto> Update(RewardMemberUpdateInput input);
        Task<RewardMemberUpdateOutputDto> DisconnectMerchant(RemoveLienKetInput input);
        Task<RewardMemberViewPointOutput> ViewPoint(RewardMemberRequestInput request, string authorization);
        Task<RewardMemberVerifyOrCreateOutput> Create(RewardMemberCreateInput input, string authorization);
        Task<RewardRegisterWithEmailAndPasswordOutput> RegisterWithEmailAndPassword(RewardRegisterWithEmailAndPasswordInput input);
        Task<RewardMemberVerifyReferralCodeOutput> VerifyReferralCode(RewardMemberVerifyReferralCodeInput input);
        Task<RewardMemberTempPointTransGetByIdOutput> GetTempPointTransByMemberId(RewardMemberTempPointTransGetByIdInput input);
        Task<RewardMemberTokenTransGetByIdOutput> GetTokenTransByMemberId(RewardMemberTokenTransGetByIdInput input);
        Task<RewardAddPointUsingOrdinaryOutput> AddPointUsingOrdinary(RewardAddPointUsingOrdinaryInput input);
        Task<RewardGetMemberBalanceByNationalIdOutput> GetBalanceMember(string memberCode);
        Task<RewardMemberVerifyIsIdCardVerifiedOutput> VerifyIsIdCardVerified(RewardMemberVerifyIsIdCardVerifiedInput input);
        Task<RewardMemberHasPinCodeResponse> HasPinCode(RewardMemberHasPinCodeRequest input);
        Task<ResponseNoDataOutput> CheckDeletedMemberHistory(RewardMemberCheckDeletedHistoryCodeRequest input);
        Task<RewardMemberCreateOrUpdatePinCodeResponse> CreateOrUpdatePinCode(MobileAPICreateOrUpdatePinCodeInput input, string authorization);
        Task<RewardMemberVerifyPinCodeResponse> VerifyPinCode(RewardMemberVerifyPinCodeRequest input);
		Task<VerifyProviderIdByPhoneNumberResponse> VerifyProviderIdByPhoneNumber(VerifyProviderIdByPhoneNumberRequest input);
		Task<GiveFeedbackOutput> GiveFeedback(GiveFeedbackInput input);
        Task<UpdateProviderOutPut> UpdateProvider(VerifyProviderIdByPhoneNumberRequest input);
        Task<RewardMemberVerifyOrCreateOutput> VerifyOrCreate(RewardMemberVerifyOrCreateInput input, string authorization);
        Task<LoyaltyResponse<string>> UpdateNotificationSetting(UpdateNotificationSettingInput input);
        Task<RewardMemberGetRefreshTokenOutput> GetRefreshToken(RewardMemberGetRefreshTokenInput input);
        Task<RewardMemberSaveRefreshTokenOutput> SaveRefreshToken(RewardMemberSaveRefreshTokenInput input);

        Task<RewardMemberSaveRefreshTokenOutput> SaveRefreshTokenMobileAPIAppota(
            CreateConnectForAppotaXSkyJoyInput input);
        Task<RewardMemberCreateRegisterLogOutput> CreateRegisterLog(RewardMemberCreateRegisterLogInput input);
        Task<UpdatePhoneNumberOutputDto> UpdatePhoneNumber(MobileUpdatePhoneNumberInput input, string authorization);
        Task<RewardMemberAccountHavePhoneNumberOutput> AccountHavePhoneNumber(RewardMemberAccountHavePhoneNumberInput input);
        Task<RewardMemberRevokeTokenResponse> RevokeToken(string authorization);
        Task<RewardMemberGetInfoOutput> GetInfo(RewardMemberGetInfoInput input);
        Task<RewardMemberFirstActionMemberOutput> FirstActionMember(RewardMemberFirstActionMemberInput input);
        Task<RewardMemberGetMemberLoginByFirebaseIdOutput> GetMemberLoginByFirebaseId(string authorization);
        Task<RewardMemberGetUsagePriorityOutput> GetUsagePriority(RewardMemberGetUsagePriorityInput input);
        Task<RewardMemberUpdateUsagePriorityOutput> UpdateUsagePriority(RewardMemberUpdateUsagePriorityInput input);
        Task<RewardMemberUpdatePointUsageTypeOutput> UpdatePointUsageType(RewardMemberUpdatePointUsageTypeInput input);
        Task<RewardMemberGetCashoutAndTopupInfoOutput> GetCashoutAndTopupInfo(RewardMemberGetCashoutAndTopupInfoInput input);
        Task<CheckGeneratePaymentLinkOutput> CheckGeneratePaymentLinkByMember(CheckGeneratePaymentLinkInput input);
        Task<RewardMemberGetReferenceDataConnectedOutput> GetReferenceDataConnected(RewardMemberGetReferenceDataConnectedInput input);
        Task<RewardMemberConfirmConnectOutput> ConfirmConnect(RewardMemberConfirmConnectInput input);
        Task<RewardMemberGetInfoConfirmConnectMerchantOutput> GetInfoConfirmConnectMerchant(RewardMemberGetInfoConfirmConnectMerchantInput input);
        Task<VerifyAuthenWithMemberCodeOutput> VerifyAuthenWithMemberCode(VerifyAuthenWithMemberCodeInput input);
        Task<RewardMemberGetTokenTransDetailOutput> GetTokenTransById(RewardMemberGetTokenTransDetailInput input);
        Task<RewardMemberGetTokenTransDetailOutput> GetExpiredTokenTransById(RewardMemberGetTokenTransDetailInput input);
        Task<RewardMemberSendOtpOutput> SendOtp(RewardMemberSendOtpInput input);
        Task<RewardMemberVerifyOtpOutput> VerityOtp(RewardMemberVerifyOtpInput input);
        Task<RewardGetUsingFirebaseOtpOutput> GetUsingFirebaseOtp();
        Task<ResponseDataOutput<RewardMemberCheckSumOutput>> SendOtpChangePhone(SendOtpChangePhoneInput input, string sessionId, string authorization);
        Task<UpdatePhoneNumberOutputDto> VerifyOtpChangePhone(VerifyOtpChangePhoneInput input, string sessionId, string otpCode, string authorization);
        Task<ResponseDataOutput<RewardMemberCheckSumOutput>> SendOtpChangePinCode(SendOtpChangePinCodeInput input, string sessionId, string authorization);
        Task<RewardMemberCreateOrUpdatePinCodeResponse> VerifyOtpChangePinCode(VerifyOtpChangePinCodeInput input, string sessionId, string otpCode, string authorization, string deviceId);
        Task<RewardGetUsingFirebaseOtpDto> GetCommonUsingFirebaseOtp();
        Task<ResponseNoDataOutput> SendOtpForgetPinCode(SendOtpForgetPinCodeInput input, string sessionId);
        Task<RewardMemberForgetPinCodeResponse> VerifyOtpForgetPinCode(VerifyOtpForgetPinCodeInput input, string sessionId, string otpCode);
        Task<ResponseDataOutput<RewardMemberCheckSumOutput>> SendOtpAddPhone(SendOtpAddPhoneInput input, string sessionId, string authorization);
        Task<ResponseNoDataOutput> VerifyOtpAddPhone(VerifyOtpAddPhoneInput input, string sessionId, string otpCode, string authorization);
        Task<RewardMemberUpdatePinCodeForgetOutput> UpdatePinCodeForget(RewardMemberUpdatePinCodeForgetInput input, string pinCode, string deviceId);
        Task<CheckCifExistWithMerchantOutput> CheckCifExistWithMerchant(string cif, int merchantid);
        Task<GetMemberByIdCardOrPhoneNumberOutput> getMemberByIdCardOrPhoneNumber(GetMemberByIdCardOrPhoneNumberInput input);
        Task<DeleteAccountOutput> DeleteAccount(DeleteAccountInput input, RewardMemberGetInfoOutput memberInfo);
        Task<CreateOrUpdateMemberFromPurchaseAgentOutput> CreateMemberFromPurchaseAgent(CreateOrUpdateMemberFromPurchaseAgentInput input);
        Task<GetCreditBalanceByMemberCodeOutput> GetCreditBalanceByMemberCode(GetCreditBalanceByMemberCodeInput input);
        
        // Ham remove connection called from LINKID
        Task Disconnect(RemoveConnectionInput input);

        Task<RewardMemberGetCifByMemberCodeOutput> GetCifByNationalId(RewardMemberGetCifByMemberCodeInput input);
        // Task<RewardSendAckAfterConnectedOutput> RewardSendAckAfterConnected(RewardSendAckAfterConnectedInput input);
        Task<GetMerchantInfoOutput> GetMerchantInfo(GetMerchantInfoInput input);
        Task<RewardSendAckAfterConnectedOutput> RewardSendAckAfterConnected(RewardSendAckAfterConnectedInput input);
        Task<RewardSendAckAfterConnectedOutput> RewardSendAckAfterDisconnected(RewardSendAckAfterConnectedInput input);
        Task<int> GetMemberIdByMemberCode(string memberCode, string auth);
        Task<ExchangeLynkiDToPartnerPointOutput> ExchangeLynkiDToPartnerPoint(ExchangeLynkiDToPartnerPointInput input);
        Task<RevertExchangeLynkiDToPartnerPointOutput> RevertExchangeLynkiDToPartnerPoint(RevertExchangeLynkiDToPartnerPointInput input);
        Task<CheckExchangeLynkiDToPartnerPointOutput> CheckExchangeLynkiDToPartnerPoint(CheckExchangeLynkiDToPartnerPointInput input);
        Task<UpdatePartnerBindingTransactionIdOutput> UpdatePartnerBindingTransactionId(UpdatePartnerBindingTransactionIdInput input);
        // API phuc vu moneycard
        Task<GetMoneyCardOfMemberOutput> GetMoneyCardOfMember(GetMoneyCardOfMemberInput input);
        Task<GetAvailableBalanceOfMoneyCardOutput> GetAvailableBalanceOfMoneyCard(GetAvailableBalanceOfMoneyCardInput input);
        
        Task<GetCardTransactionOfMemberOutput> GetCardTransactionOfMember(GetCardTransactionOfMemberInput input);
        
        Task<RewardMemberGetPaymentMethodOutput> GetPaymentMethod(RewardMemberGetPaymentMethodInput input);

        // Các API phục vụ webstore.
        Task<WebstoreCallRewardLoginResponse> WebstoreLogin(WebstoreCallRewardLoginRequest input);
        Task<WebstoreGetSmeInfoResponse> WebstoreGetSmeInfo(WebstoreGetSmeInfoRequest input);
        Task<WebstoreGetFullSmeInfoResponse> WebstoreGetFullSmeInfo(WebstoreGetFullSmeInfoRequest input);
        Task<WebstoreSetSmePasswordResponse> WebstoreSetSmePassword(WebstoreSetSmePasswordRequest input);
        Task<WebstoreGetSmeBalanceResponse> WebstoreGetSmeBalance(WebstoreGetSmeBalanceRequest input);
        Task<WebstoreValidateAccessTokenItem> WebstoreValidateToken(string token);
        Task<WebstoreRefreshTokenResponse> WebstoreRefreshToken(WebstoreRefreshTokenRequest webstoreRefreshTokenRequest);

        Task<GetAllTokenTransRespone> GetALLTokenTrans (GetAllTokenTransInput input);

        Task<GetAllTokenTransRespone> GetALLTokenTransBySmeCif(GetALLTokenTransBySmeCifInput input);

        Task<CheckSmePasswordRewardOutput> CheckSmePassword(CheckSmePasswordRewardInput input);
        Task<RewardMemberRedeemOutput> WebstoreCallRedwardRedeem(WebstoreCallRedwardRedeemInput input);
        Task<RewardMemberSaveRefreshTokenOutput> SaveRefreshTokenV2(RewardMemberSaveRefreshTokenInputV2 input);
        Task<AgreeTAndCOutput> AgreeTAndC(object s);
        Task<CreateSmeMemberOutput> CreateSmeMember(object o);
        Task<GetPartnerMemberInfoByConnectionOutput> GetPartnerMemberInfoByConnection(GetCifByMemberRequest o);
        Task<AddConnectionResponse> AddConnection(AddConnectionRequest request);
        Task<RemoveConnectionResponse> RemoveConnection(RemoveConnectionRequest request);
    }
}
