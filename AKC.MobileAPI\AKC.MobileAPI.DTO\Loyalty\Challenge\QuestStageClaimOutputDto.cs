﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class QuestStageClaimOutputDto
    {
        public ListQuestStageClaimOutputDto Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class ListQuestStageClaimOutputDto
    {
        public int ClaimQuestStageId { get; set; }
        public decimal QuestStageRewardedCoin { get; set; }
        public decimal QuestStageRewardedPoint { get; set; }
        public decimal QuestStageLevelRewardedCoin { get; set; }
        public decimal QuestStageLevelRewardedPoint { get; set; }
        public List<ClaimQuestForQuestOutputDto> LevelsBaseline { get; set; }

    }
    public class ClaimQuestForQuestOutputDto
    {
        public string QuestCode { get; set; }
        public string QuestName { get; set; }
        public decimal QuestRewardedCoin { get; set; }

        public decimal QuestRewardedPoint { get; set; }

        public decimal MissionRewardedCoin { get; set; }

        public decimal MissionRewardedPoint { get; set; }

        public List<string> ErrorMessage { get; set; }
    }
}
