﻿using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.Merchant;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FirebaseAdmin.Auth;
using AKC.MobileAPI.DTO.MobileAPI;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.EGiftInfors;
using Microsoft.Extensions.Caching.Distributed;
using AKC.MobileAPI.Service.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.Service.Abstract.Loyalty.EGiftInfors;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty.Gift;
using RestSharp.Extensions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using AKC.MobileAPI.DTO.Reward.MemberOtp;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.Service.Helpers;
using System.Text.RegularExpressions;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory.V2;
using AKC.MobileAPI.DTO.ThirdParty.Appota;
using AKC.MobileAPI.Service.Abstract.LoyaltyVpbank;
using Microsoft.Extensions.Caching.Memory;
using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Reward;
using AKC.MobileAPI.DTO.Webstore;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardMemberService : RewardBaseService, IRewardMemberService
    {
        private ILoyaltySecondaryCustomersService _customersService;
        private IUploadImageSevice _uploadImageSevice;
        private IExceptionReponseService _exceptionReponseService;
        private ILoyaltyEGiftInforsService _loyaltyEGiftInforsService;
        private ILoyaltyMemberService _vpbloyUtilService;
        private readonly ILogger<RewardMemberService> _logger;

        private readonly IDistributedCache _cache;
        private readonly IMemoryCache _memoryCache;
        private readonly int linkidTenantIdInCommonLoy;
        public RewardMemberService(IConfiguration configuration,
            ILoyaltySecondaryCustomersService customersService,
            IUploadImageSevice uploadImageSevice,
            IDistributedCache cache,
            ILoyaltyMemberService util,
            IExceptionReponseService exceptionReponseService,
            ILoyaltyEGiftInforsService x,
            IMemoryCache mem,
            ILogger<RewardMemberService> logger) : base(configuration)
        {
            _customersService = customersService;
            _uploadImageSevice = uploadImageSevice;
            _cache = cache;
            _exceptionReponseService = exceptionReponseService;
            _logger = logger;
            _loyaltyEGiftInforsService = x;
            _vpbloyUtilService = util;
            _memoryCache = mem;
            linkidTenantIdInCommonLoy = Convert.ToInt32(_configuration.GetSection("LINKIDTENANTIDONCOMMONLOYALTY").Value);
        }

        public async Task<RewardMemberViewOutput> View(RewardMemberRequestInput input, string authorization)
        {
            var nationalId = await getNational(authorization);
            if (!string.IsNullOrEmpty(input.MemberCode))
            {
                nationalId = input.MemberCode;
            }
            var request = new RewardMemberViewInput()
            {
                NationalId = nationalId,
            };
            return await GetRewardAsync<RewardMemberViewOutput>(RewardApiUrl.MEMBER_VIEW, request);
        }

        /// <summary>
        /// Sending a PhoneNumber to reward system to get Member's Name and Avatar Photo
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<FindOtherUserByPhoneNumberOutput> GetFriendName(FindOtherUserByPhoneNumberInput input)
        {
            return await PostRewardAsync<FindOtherUserByPhoneNumberOutput>(RewardApiUrl.MEMBER_GET_NAME_BY_PHONE, input);
        }
        /**
         * Update DeviceId of a user when change device
         */
        public async Task<UpdateMemberDeviceIdOutput> UpdateDeviceId(UpdateDeviceIdInput input)
        {
            return await PostRewardAsync<UpdateMemberDeviceIdOutput>(RewardApiUrl.MEMBER_UPDATE_DEVICEID, input);
        }

        public async Task<RewardMemberUpdateOutputDto> DisconnectMerchant(RemoveLienKetInput input)
        {
            return await PutRewardAsync<RewardMemberUpdateOutputDto>(RewardApiUrl.DisconnectMerchant, input);
        }

        public async Task<RewardMemberUpdateOutputDto> Update(RewardMemberUpdateInput input)
        {
            var avatarLink = await GetAvatarLink(input.AvatarFromBase64String);
            var updateMemberDto = new RewardMemberUpdateDto()
            {
                // KHOONG CHO PHEP UPDATE SO DIEN THOAI, MEMBER MUST USE THE FLOW: CHANGE PHONENUMBER 
                NationalId = input.NationalId,
                IdCard = input.IdCard,
                PartnerPhoneNumber = input.PartnerPhoneNumber,
                Type = "Member",
                Phone = input.Phone,
                Gender = !(new List<string>{ "M", "F", "O" }).Contains(input.Gender) ? "O" : input.Gender,
                Status = "A",
                RankTypeCode = "Customer",
                IsDeleted = false,
                Address = input.Address,
                Dob = input.Dob,
                Name = input.Name,
                Email = input.Email,
                PointUsingOrdinary = input.PointUsingOrdinary,
                HashAddress = "",
                D365Id = input.NationalId,
                MbcId = input.NationalId,
                MbcCardId = input.NationalId,
                RegionCode = "",
                FullRegionCode = "",
                MemberTypeCode = "",
                FullMemberTypeCode = "",
                ChannelType = "",
                FullChannelTypeCode = "",
                StandardMemberCode = input.NationalId,
                ReferralCode = GenReferralCode(input.Phone, input.NationalId),
                Avatar = avatarLink,
                IsIdCardVerified = input.IsIdCardVerified,
                NationId = input.NationId != 0 ? input.NationId : 0,
                CityId = input.CityId != 0 ? input.CityId : 0,
                DistrictId = input.DistrictId != 0 ? input.DistrictId : 0,
                WardId = input.WardId != 0 ? input.WardId : 0,
                StreetDetail = input.StreetDetail,
            };
            return await PutRewardAsync<RewardMemberUpdateOutputDto>(RewardApiUrl.MEMBER_UPDATE, updateMemberDto);
        }

        public async Task<RewardMemberViewPointOutput> ViewPoint(RewardMemberRequestInput input, string authorization)
        {
            var nationalId = await getNational(authorization);
            if (!string.IsNullOrEmpty(input.MemberCode))
            {
                nationalId = input.MemberCode;
            }
            var request = new RewardMemberViewPointInput()
            {
                NationalId = nationalId
            };
            var cacheKey = "VIEWPOINT_" + nationalId;
            var cachedData = _memoryCache.Get<RewardMemberViewPointOutput>(cacheKey);
            if (cachedData != null)
            {
                _logger.LogInformation(">> ViewPoint >> " +  nationalId +  " >> CACHE HIT");
                return cachedData;
            }

            var viewPointResult = await GetRewardAsync<RewardMemberViewPointOutput>(RewardApiUrl.MEMBER_VIEWPOINT, request);

            // TODO: Merge 3
            var customerProfile = await _customersService.GetMemberInfoByCode(nationalId);
            //var viewResult = await View(authorization);

            if (viewPointResult.Items != null)
            {
                //viewPointResult.Items.MemberName = viewPointResult;

                if (customerProfile != null
                    && customerProfile.Result != null)
                {
                    viewPointResult.Items.MemberLoyaltyInfo = customerProfile.Result;
                }
            }
            
            _memoryCache.Set(cacheKey, viewPointResult, TimeSpan.FromSeconds(30));;
            _logger.LogInformation(">> ViewPoint >> " +  nationalId +  " >> CACHE MISS; GET FROM DB");

            return viewPointResult;
        }

        public async Task<RewardMemberVerifyOrCreateOutput> Create(RewardMemberCreateInput input, string authorization)
        {
            var nationalId = await getNational(authorization);
            var avatarLink = await GetAvatarLink(input.AvatarFromBase64String);
            var createMemberDto = new RewardMemberVerifyCreateMemberAndUpdateProviderInput()
            {
                NationalId = nationalId,
                IdCard = input.IdCard,
                PartnerPhoneNumber = input.PartnerPhoneNumber,
                Type = "Member",
                Phone = input.Phone,
                Gender = !(new List<string> { "M", "F", "O" }).Contains(input.Gender) ? "O" : input.Gender,
                Status = "A",
                RankTypeCode = "Customer",
                IsDeleted = false,
                Address = input.Address,
                Dob = input.Dob,
                Name = input.Name,
                Email = input.Email,
                PointUsingOrdinary = input.PointUsingOrdinary,
                HashAddress = "",
                RegionCode = "",
                FullRegionCode = "",
                MemberTypeCode = "",
                FullMemberTypeCode = "",
                ChannelType = "",
                FullChannelTypeCode = "",
                StandardMemberCode = nationalId,
                ReferralCode = GenReferralCode(input.Phone, nationalId),
                Avatar = avatarLink,
                ReferenceId = input.ReferenceId,
                RegisterType = input.RegisterType,
                NationId = 0,
                CityId = 0,
                DistrictId = 0,
                WardId = 0,
                StreetDetail = "",
                ProviderId = nationalId,
                ProviderName = "Firebase",
                FirebaseCreatedTime = DateTime.UtcNow
            };
            var result = await PostRewardAsync<RewardMemberVerifyCreateMemberAndUpdateProviderOutput>(RewardApiUrl.MEMBER_VERIFY_AND_UPDATE_PROVIDER, createMemberDto);
            return new RewardMemberVerifyOrCreateOutput()
            {
                Messages = "Success",
                Result = new RewardMemberVerifyOrCreateOutputItem()
                {
                    MemberCode = result.MemberCode,
                    PhoneNumber = input.Phone,
                },
                Status = 200,
            };
        }

        public async Task<RewardRegisterWithEmailAndPasswordOutput> RegisterWithEmailAndPassword(RewardRegisterWithEmailAndPasswordInput input)
        {
            var userCheck = new object();
            try
            {
                if (await CheckMemberFromFirebase(input.Email))
                {
                    return new RewardRegisterWithEmailAndPasswordOutput()
                    {
                        Status = true,
                        Messages = "That email is exists",
                    };
                }
                var auth = FirebaseAdmin.Auth.FirebaseAuth.DefaultInstance;
                await auth.CreateUserAsync(new FirebaseAdmin.Auth.UserRecordArgs()
                {
                    Email = input.Email,
                    Password = input.Password,
                    EmailVerified = false,
                });
                return new RewardRegisterWithEmailAndPasswordOutput()
                {
                    Status = true,
                    Messages = "Register member success"
                };

            }
            catch (Exception e)
            {
                return new RewardRegisterWithEmailAndPasswordOutput()
                {
                    Status = false,
                    Messages = e.Message
                };
            }

        }

        public async Task<RewardGetMemberBalanceByNationalIdOutput> GetBalanceMember(string memberCode)
        {
            var request = new RewardGetMemberBalanceByNationalIdInput()
            {
                NationalId = memberCode,
            };
            return await GetRewardAsync<RewardGetMemberBalanceByNationalIdOutput>(RewardApiUrl.MEMBER_GET_TOKEN_BALANCE, request);
        }

        private async Task<bool> CheckMemberFromFirebase(string email)
        {
            try
            {
                var auth = FirebaseAdmin.Auth.FirebaseAuth.DefaultInstance;
                var userCheck = await auth.GetUserByEmailAsync(email);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<RewardMemberVerifyReferralCodeOutput> VerifyReferralCode(RewardMemberVerifyReferralCodeInput input)
        {
            var referralDto = new RewardMemberVerifyReferralCodeDto()
            {
                MerchantId = Convert.ToInt32(_configuration.GetSection("Reward:MerchantId").Value),
                DistributionChannelList = input.DistributionChannelList,
                NationalId = input.NationalId,
                ReferenceAmount = input.ReferenceAmount,
                ReferralCode = input.ReferralCode,
            };
            return await PostRewardAsync<RewardMemberVerifyReferralCodeOutput>(RewardApiUrl.MEMBER_VERIFY_REFERRAL_CODE, referralDto);
        }

        public async Task<RewardMemberTempPointTransGetByIdOutput> GetTempPointTransByMemberId(RewardMemberTempPointTransGetByIdInput input)
        {
            var request = new RewardMemberTempPointTransGetByIdDto()
            {
                sorting = input.Sorting,
                maxResultCount = input.MaxResultCount,
                skipCount = input.SkipCount,
                MemberId = input.MemberId,
                NationalId = input.NationalId,
                MerchantIdFilter = input.MerchantIdFilter,
                StatusFilter = input.StatusFilter,
                FromDateFilter = input.FromDateFilter,
                ToDateFilter = input.ToDateFilter,
                OrderCodeFilter = input.OrderCodeFilter,
                ActionCodeFilter = input.ActionCodeFilter,
                ActionTypeFilter = input.ActionTypeFilter,
            };
            return await GetRewardAsync<RewardMemberTempPointTransGetByIdOutput>(RewardApiUrl.MEMBER_GET_TEMP_POINT_BY_ID, request, MerchantNameConfig.VPID);
        }

        public async Task<RewardGetAllMerchantExchangeOutput> GetAllMerchantExchange(RewardGetAllMerchantExchangeInput input)
        {
            var request = new RewardGetAllMerchantExchangeDto()
            {
                StatusFilter = "A",
                TypeFilter = "Exchange",
                skipCount = input.skipCount,
                maxResultCount = input.maxResultCount,
                sorting = input.sorting,
            };
            return await GetRewardAsync<RewardGetAllMerchantExchangeOutput>(RewardApiUrl.MEMBER_VIEW, request);
        }

        public async Task<RewardMemberTokenTransGetByIdOutput> GetTokenTransByMemberId(RewardMemberTokenTransGetByIdInput input)
        {
            var request = new RewardMemberTokenTransGetByIdDto()
            {
                sorting = input.Sorting,
                maxResultCount = input.MaxResultCount,
                skipCount = input.SkipCount,
                MemberId = input.MemberId,
                NationalId = input.NationalId,
                MerchantIdFilter = input.MerchantIdFilter,
                FromDateFilter = input.FromDateFilter,
                ToDateFilter = input.ToDateFilter,
                OrderCodeFilter = input.OrderCodeFilter,
                ActionCodeFilter = input.ActionCodeFilter,
                ActionTypeFilter = input.ActionTypeFilter,
            };
            var ret = await GetRewardAsync<RewardMemberTokenTransGetByIdOutput>(RewardApiUrl.MEMBER_GET_TOKEN_TRANS_BY_MEMBER_ID, request, null);
            // Get loyalty backend to get vendorname/vendor avatar in the case of nap diem
            if (ret.Result == 200)
            {
                var listTxNapdiem = new List<string>();
                ret.Items.ForEach(x =>
                {
                    if ("Topup".Equals(x.ActionType))
                    {
                        listTxNapdiem.Add(x.OrderCode);
                    }

                    if (!string.IsNullOrEmpty(x.ActionCodeDetail) && x.ActionType == "Expired")
                    {
                        x.ActionCodeDetail = "";
                    }
                });
                if (listTxNapdiem.Count > 0)
                {
                    var res = await _loyaltyEGiftInforsService.GetVendorInfoFromTransOfTopup(new GetVendorInfoFromTransCodeInput()
                    {
                        ListTransactionCode = string.Join(",", listTxNapdiem)
                    });
                    ret.Items.ForEach(r =>
                    {
                        res.Result.ForEach(x =>
                        {
                            if (r.OrderCode == x.TransactionCode && x.IsTopup)
                            {
                                r.TopUpVendorAvatar = x.VendorAvatar;
                                r.TopUpVendorName = x.VendorName;
                            }
                        });
                    });
                }
            }
            return ret;
        }
        public async Task<RewardMemberGetTokenTransDetailOutput> GetExpiredTokenTransById(RewardMemberGetTokenTransDetailInput request)
        {
            return await PostRewardAsync<RewardMemberGetTokenTransDetailOutput>(RewardApiUrl.MEMBER_GET_EXPIRED_TOKEN_TRANS_BY_ID, request, null);
        }
        public async Task<RewardMemberGetTokenTransDetailOutput> GetTokenTransById(RewardMemberGetTokenTransDetailInput request)
        {
            return await PostRewardAsync<RewardMemberGetTokenTransDetailOutput>(RewardApiUrl.MEMBER_GET_TOKEN_TRANS_BY_ID, request, null);
        }

        public async Task<RewardAddPointUsingOrdinaryOutput> AddPointUsingOrdinary(RewardAddPointUsingOrdinaryInput input)
        {
            return await PostRewardAsync<RewardAddPointUsingOrdinaryOutput>(RewardApiUrl.MEMBER_ADD_POINT_USING_ORDINARY, input);
        }

        public async Task<RewardMemberVerifyIsIdCardVerifiedOutput> VerifyIsIdCardVerified(RewardMemberVerifyIsIdCardVerifiedInput input)
        {
            return await GetRewardAsync<RewardMemberVerifyIsIdCardVerifiedOutput>(RewardApiUrl.MEMBER_VERIFY_IDCARD, input);
        }

        private string GenReferralCode(string phoneNumber, string nationalId)
        {
            if (!string.IsNullOrEmpty(phoneNumber))
            {
                return phoneNumber.Replace("+84", "0");
            }
            else
            {
                return null;
                //return nationalId;
            }
        }

        private async Task<string> GetAvatarLink(string FromBase64String)
        {
            if (string.IsNullOrWhiteSpace(FromBase64String))
            {
                return null;
            }
            var link = await _uploadImageSevice.UploadImage(FromBase64String);
            return link;
        }

        public async Task<RewardMemberVerifyPinCodeResponse> VerifyPinCode(RewardMemberVerifyPinCodeRequest input)
        {
            var auth = FirebaseAuth.DefaultInstance;
            UserRecord userRecord = null;
            try
            {
                userRecord = await auth.GetUserByPhoneNumberAsync(input.PhoneNumber);
            }
            catch (FirebaseAuthException ex)
            {
                _logger.LogError("Get firebase error from uuid", ex);
                if (ex.AuthErrorCode == AuthErrorCode.UserNotFound)
                {
                    getExceptionCustome("MemberNotExits", "Member does not exist");
                }
                getExceptionCustome("SystemError", "System error");
            }
            var memberCode = userRecord.Uid;
            var nameToken = "Member_" + memberCode;

            var token = await _cache.GetStringAsync(nameToken);
            try
            {
                if (string.IsNullOrEmpty(token))
                {
                    var cacheAllowRenewTokenFlagCacheOptions = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(60));
                    await _cache.SetStringAsync(nameToken, "1", cacheAllowRenewTokenFlagCacheOptions);
                }
                else
                {
                    return new RewardMemberVerifyPinCodeResponse()
                    {
                        Success = false
                    };
                }

                var res = await PostRewardAsync<RewardMemberVerifyPinCodeDto>(RewardApiUrl.MEMBER_VERIFY_PINCODE, input);

                if (input.IsRevoke)
                {
                    await auth.RevokeRefreshTokensAsync(userRecord.Uid);
                }

                var customToken = await auth.CreateCustomTokenAsync(userRecord.Uid);

                var cacheAllowRenewTokenFlagCacheOptions2 = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(3));
                await _cache.SetStringAsync(nameToken, "1", cacheAllowRenewTokenFlagCacheOptions2);

                return new RewardMemberVerifyPinCodeResponse()
                {
                    MemberCode = res.MemberCode,
                    PhoneNumber = res.PhoneNumber,
                    SessionId = res.SessionId,
                    Success = true,
                    CustomToken = customToken,
                };
            }
            catch (Exception ex)
            {
                await _cache.RemoveAsync(nameToken);
                throw ex;
            }
        }

        public async Task<RewardMemberHasPinCodeResponse> HasPinCode(RewardMemberHasPinCodeRequest input)
        {
            return await GetRewardAsync<RewardMemberHasPinCodeResponse>(RewardApiUrl.MEMBER_HAS_PINCODE, input);
        }

        public async Task<ResponseNoDataOutput> CheckDeletedMemberHistory(RewardMemberCheckDeletedHistoryCodeRequest input)
        {
            return await GetRewardAsync<ResponseNoDataOutput>(RewardApiUrl.CHECK_MEMBER_DELETED_HISTORY, input);
        }

        public async Task<RewardGetUsingFirebaseOtpOutput> GetUsingFirebaseOtp()
        {
            return await GetRewardAsync<RewardGetUsingFirebaseOtpOutput>(RewardApiUrl.COMMON_GET_USING_FIREBASE, null);
        }
        
        /**
         * API tạo ra member cho Tenant tương ứng của merchant trong input
         */
        public async Task<CreateOrUpdateMemberFromPurchaseAgentOutput> CreateMemberFromPurchaseAgent(CreateOrUpdateMemberFromPurchaseAgentInput input)
        {
            // Gọi API để tạo Member ở Loyalty với tenant tương ứng với merchant hiện tại
            return await PostRewardAsync <CreateOrUpdateMemberFromPurchaseAgentOutput> (RewardApiUrl.CreateLoyaltyMemberOfCustomMerchant, input);
        }

        public async Task<RewardMemberCreateOrUpdatePinCodeResponse> CreateOrUpdatePinCode(MobileAPICreateOrUpdatePinCodeInput input, string authorization)
        {
            var memberUidFirebase = await getNational(authorization);
            var result = await PostRewardAsync<RewardMemberCreateOrUpdatePinCodeResponse>(RewardApiUrl.MEMBER_CREATE_OR_UPDATE_PINCODE,
            new RewardMemberCreateOrUpdatePinCodeRequest()
            {
                MemberCode = input.MemberCode,
                PinCode = input.PinCode,
            });
            
            if (result.Result == 200 && input.Type == 3)
            {
                var auth = FirebaseAuth.DefaultInstance;
                //var userRecord = await auth.GetUserAsync(input.MemberCode);
                await auth.RevokeRefreshTokensAsync(memberUidFirebase);

                var customToken = await auth.CreateCustomTokenAsync(memberUidFirebase);

                result.CustomToken = customToken;
            }

            //ghi nhận hành động firstlogin
            var resultLogin = await _customersService.CreateMemberActionFirst(new CreateMemberActionFirstInput { MemberCode = input.MemberCode, ActionCode = "FirstLogin" });
            return result;
        }

        //Loyalty
        public async Task<LoyaltyResponse<string>> UpdateNotificationSetting(UpdateNotificationSettingInput input)
        {
            return await _customersService.UpdateNotificationSetting(input);
        }

        public async Task<VerifyProviderIdByPhoneNumberResponse> VerifyProviderIdByPhoneNumber(VerifyProviderIdByPhoneNumberRequest input)
        {
            return await PostRewardAsync<VerifyProviderIdByPhoneNumberResponse>(RewardApiUrl.VERIFY_PROVIDER_ID_BY_PHONE_NUMBER, input);
        }
        
        public async Task<GiveFeedbackOutput> GiveFeedback(GiveFeedbackInput input)
        {
            return await PostRewardAsync<GiveFeedbackOutput>(RewardApiUrl.GiveFeedback, input);
        }

        public async Task<UpdateProviderOutPut> UpdateProvider(VerifyProviderIdByPhoneNumberRequest input)
        {
            return await PostRewardAsync<UpdateProviderOutPut>(RewardApiUrl.UPDATE_PROVIDER, input);
        }

        public async Task<RewardMemberVerifyOrCreateOutput> VerifyOrCreate(RewardMemberVerifyOrCreateInput input, string authorization)
        {
            var nationalId = await getNational(authorization);
            var avatarLink = await GetAvatarLink(input.AvatarFromBase64String);
            var createMemberDto = new RewardMemberVerifyCreateMemberAndUpdateProviderInput()
            {
                NationalId = nationalId,
                IdCard = input.IdCard,
                PartnerPhoneNumber = input.PartnerPhoneNumber,
                Type = "Member",
                Phone = input.Phone,
                Gender = input.Gender,
                Status = "A",
                RankTypeCode = "Customer",
                IsDeleted = false,
                Address = input.Address,
                Dob = input.Dob,
                Name = input.Name,
                Email = input.Email,
                PointUsingOrdinary = input.PointUsingOrdinary,
                HashAddress = "",
                RegionCode = "",
                FullRegionCode = "",
                MemberTypeCode = "",
                FullMemberTypeCode = "",
                ChannelType = "",
                FullChannelTypeCode = "",
                StandardMemberCode = nationalId,
                ReferralCode = GenReferralCode(input.Phone, nationalId),
                Avatar = avatarLink,
                ProviderId = nationalId,
                ProviderName = "Firebase",
            };
            var result = await PostRewardAsync<RewardMemberVerifyCreateMemberAndUpdateProviderOutput>(RewardApiUrl.MEMBER_VERIFY_AND_UPDATE_PROVIDER, createMemberDto);
            return new RewardMemberVerifyOrCreateOutput()
            {
                Messages = "Success",
                Result = new RewardMemberVerifyOrCreateOutputItem()
                {
                    MemberCode = result.MemberCode,
                    PhoneNumber = input.Phone,
                },
                Status = 200,
            };
        }
		
        public async Task<RewardMemberGetRefreshTokenOutput> GetRefreshToken(RewardMemberGetRefreshTokenInput input)
        {
            return await PostRewardAsync<RewardMemberGetRefreshTokenOutput>(RewardApiUrl.MEMBER_GET_REFRESH_TOKEN, input);
        }

        public async Task<RewardMemberSaveRefreshTokenOutput> SaveRefreshToken(RewardMemberSaveRefreshTokenInput input)
        {
            return await PostRewardAsync<RewardMemberSaveRefreshTokenOutput>(RewardApiUrl.MEMBER_SAVE_REFRESH_TOKEN, input);
        }
        public async Task<RewardMemberSaveRefreshTokenOutput> SaveRefreshTokenMobileAPIAppota(CreateConnectForAppotaXSkyJoyInput input)
        {
            return await PostRewardAsync<RewardMemberSaveRefreshTokenOutput>(RewardApiUrl.MEMBER_SAVE_REFRESH_TOKEN_MOBIAPIAPPOTA, input);
        }
        
        public async Task<RewardMemberCreateRegisterLogOutput> CreateRegisterLog(RewardMemberCreateRegisterLogInput input)
        {
            var request = new RewardMemberCreateRegisterLogDto()
            {
                Code = LoyaltyHelper.GenTransactionCode("CreateMemberLogs"),
                DeviceId = input.DeviceId ?? "",
                ErrorMessage = input.ErrorMessage,
                ExtraData = input.ExtraData,
                Localtion = input.Localtion,
                PhoneNumber = input.PhoneNumber,
                Status = input.Status,
                Time = input.Time,
            };
            return await PostRewardAsync<RewardMemberCreateRegisterLogOutput>(RewardApiUrl.MEMBER_CREATE_REGISTER_LOG, request);
        }

        public async Task<UpdatePhoneNumberOutputDto> UpdatePhoneNumber(MobileUpdatePhoneNumberInput input, string authorization)
        {
            var auth = FirebaseAuth.DefaultInstance;
            var memberUidFirebase = await getNational(authorization);
            var oldPhone = input.OldPhoneNumber;
            UserRecord user = null;


            //Update phone in firebase

            try
            {
                user = await auth.GetUserAsync(memberUidFirebase);
                //oldPhone = user.PhoneNumber;
            }
            catch
            {
                return new UpdatePhoneNumberOutputDto()
                {
                    Status = UpdatePhoneNumberStatus.MemberNotExists,
                    Message = "Member does not exist"
                };
            }

            //try
            //{
            //    await auth.GetUserByPhoneNumberAsync(input.PhoneNumber);
            //    return new UpdatePhoneNumberOutput()
            //    {
            //        Status = UpdatePhoneNumberStatus.DuplicatePhoneNumber,
            //        Message = "Phone number already exist"
            //    };
            //}
            //catch { }

            //try
            //{
            //    await auth.UpdateUserAsync(new UserRecordArgs()
            //    {
            //        PhoneNumber = input.PhoneNumber,
            //        Uid = user.Uid
            //    });
            //}
            //catch
            //{
            //    return new UpdatePhoneNumberOutput()
            //    {
            //        Status = UpdatePhoneNumberStatus.SystemError,
            //        Message = "System error"
            //    };
            //}

            var inputBE = new UpdatePhoneNumberInput()
            {
                MemberCode = input.MemberCode,
                PhoneNumber = input.PhoneNumber
            };
            var rewardUpdatePhone = new RewardMemberUpdatePhoneDto();
            try
            {
                rewardUpdatePhone = await PutRewardAsync<RewardMemberUpdatePhoneDto>(RewardApiUrl.MEMBER_UPDATE_PHONE_NUMBER, inputBE);
                _logger.LogDebug("Reward update phone result: " + JsonConvert.SerializeObject(rewardUpdatePhone));
            }
            catch (Exception ex)
            {
                _logger.LogError("Update phone number reward error: " + ex.Message);
                var flag = false;
                for (int i = 0; i < 2 && !flag && !string.IsNullOrEmpty(oldPhone); i++)
                {
                    try
                    {
                        await auth.UpdateUserAsync(new UserRecordArgs()
                        {
                            PhoneNumber = oldPhone,
                            Uid = memberUidFirebase
                        });
                        flag = true;
                    }
                    catch { }
                }

                var resultEx = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError("Update phone number reward exception error: " + JsonConvert.SerializeObject(resultEx));
                var result = new UpdatePhoneNumberOutputDto();
                switch (resultEx.Code)
                {
                    case "MemberNotExits":
                        result.Status = UpdatePhoneNumberStatus.MemberNotExists;
                        result.Message = "Member does not exist";
                        break;
                    case "DuplicatePhoneNumber":
                        result.Status = UpdatePhoneNumberStatus.DuplicatePhoneNumber;
                        result.Message = "Phone number already exist";
                        break;
                    default:
                        result.Status = UpdatePhoneNumberStatus.SystemError;
                        result.Message = "System error";
                        break;
                }
                return result;
            }


            try
            {
                await _customersService.UpdatePhoneNumber(inputBE);
            }
            catch (Exception ex)
            {
                _logger.LogError("Update phone number loyalty error: " + ex.Message);
                var flag = false;
                for (int i = 0; i < 2 && !flag && !string.IsNullOrEmpty(oldPhone); i++)
                {
                    try
                    {
                        await auth.UpdateUserAsync(new UserRecordArgs()
                        {
                            PhoneNumber = oldPhone,
                            Uid = memberUidFirebase
                        });
                        flag = true;
                    }
                    catch { }
                }

                flag = false;
                inputBE.PhoneNumber = oldPhone;

                for (int i = 0; i < 2 && !flag; i++)
                {
                    try
                    {
                        await PutRewardAsync<RewardMemberUpdatePhoneDto>(RewardApiUrl.MEMBER_UPDATE_PHONE_NUMBER, inputBE);
                        flag = true;
                    }
                    catch { }
                }

                var resultEx = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError("Update phone number loyalty exception error: " + JsonConvert.SerializeObject(resultEx));
                var result = new UpdatePhoneNumberOutputDto();
                switch (resultEx.Code)
                {
                    case "101":
                        result.Status = UpdatePhoneNumberStatus.MemberNotExists;
                        result.Message = "Member does not exist";
                        break;
                    case "202":
                        result.Status = UpdatePhoneNumberStatus.DuplicatePhoneNumber;
                        result.Message = "Phone number already exist";
                        break;
                    default:
                        result.Status = UpdatePhoneNumberStatus.SystemError;
                        result.Message = "System error";
                        break;
                }
                return result;
            }
            var ListMerchantNeedUpdatePhone = rewardUpdatePhone != null ? rewardUpdatePhone.Item.ListMerchantNeedUpdatePhone : null;
            return new UpdatePhoneNumberOutputDto()
            {
                Status = UpdatePhoneNumberStatus.Success,
                Message = "Success",
                HasUpdatePartnerPhone = true,
                ListMerchantNeedUpdatePhone = ListMerchantNeedUpdatePhone,
                MemberCode = rewardUpdatePhone?.Item?.MemberCode,
                MemberId = rewardUpdatePhone?.Item?.MemberId
            };
            //}
            //catch (Exception ex)
            //{
            //    var errorResponse = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
            //    output.Status = StatusReturnConst.Error;
            //    output.Message = errorResponse.Message;
            //}
            //}
            //else
            //{
            //    output.Message = "Incorrect OTP Code!";
            //}
            //return output;
        }

        public async Task<RewardMemberAccountHavePhoneNumberOutput> AccountHavePhoneNumber(RewardMemberAccountHavePhoneNumberInput input)
        {
            return await PostRewardAsync<RewardMemberAccountHavePhoneNumberOutput>(RewardApiUrl.MEMBER_ACCOUNT_HAVE_PHONE_NUMBER, input);
        }

        public async Task<RewardMemberRevokeTokenResponse> RevokeToken(string authorization)
        {
            var nationalId = await getNational(authorization);
            var auth = FirebaseAuth.DefaultInstance;
            var nameToken = "Member_" + nationalId;

            var token = await _cache.GetStringAsync(nameToken);
            try
            {
                if (string.IsNullOrEmpty(token))
                {
                    var cacheAllowRenewTokenFlagCacheOptions = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(60));
                    await _cache.SetStringAsync(nameToken, "1", cacheAllowRenewTokenFlagCacheOptions);
                }
                else
                {
                    return new RewardMemberRevokeTokenResponse()
                    {
                        Result = (int)RevokeTokenErrorCode.CanNotVerifyPinCodeNow,
                        Message = "Can Not Verify PinCode Now"
                    };
                }

                var userRecord = await auth.GetUserAsync(nationalId);
                var phoneNumber = userRecord.PhoneNumber;

                var res = new RewardMemberHasPinCodeResponse();

                try
                {
                    res = await GetRewardAsync<RewardMemberHasPinCodeResponse>(RewardApiUrl.MEMBER_HAS_PINCODE, new RewardMemberHasPinCodeRequest()
                    {
                        PhoneNumber = phoneNumber
                    });
                }
                catch (Exception ex) { }

                if (res != null && res.IsLocked)
                {
                    await _cache.RemoveAsync(nameToken);
                    return new RewardMemberRevokeTokenResponse()
                    {
                        Result = (int)RevokeTokenErrorCode.AccountIsLocked,
                        Message = "Account Is Locked"
                    };
                }

                //result.HasPinCode = resultReward.HasPinCode;
                //result.IsLocked = resultReward.IsLocked;

                await auth.RevokeRefreshTokensAsync(userRecord.Uid);

                var customToken = await auth.CreateCustomTokenAsync(userRecord.Uid);

                var cacheAllowRenewTokenFlagCacheOptions2 = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(3));
                await _cache.SetStringAsync(nameToken, "1", cacheAllowRenewTokenFlagCacheOptions2);

                return new RewardMemberRevokeTokenResponse()
                {
                    Result = (int)RevokeTokenErrorCode.Success,
                    CustomToken = customToken,
                    Message = "Success"
                };
            }
            catch (Exception ex)
            {
                await _cache.RemoveAsync(nameToken);
                return new RewardMemberRevokeTokenResponse()
                {
                    Result = (int)RevokeTokenErrorCode.IntenalServerError,
                    CustomToken = null,
                    Message = ex.Message
                };
            }
        }

        public async Task<RewardMemberGetInfoOutput> GetInfo(RewardMemberGetInfoInput input)
        {
            var request = new RewardMemberGetInfoInput()
            {
                NationalId = input.NationalId,
                MemberId = input.MemberId,
                PhoneNumber = input.PhoneNumber,
                IdCard = input.IdCard,
            };
            return await GetRewardAsync<RewardMemberGetInfoOutput>(RewardApiUrl.MEMBER_GET_INFO, request);
        }

        public async Task<GetMemberByIdCardOrPhoneNumberOutput> getMemberByIdCardOrPhoneNumber(GetMemberByIdCardOrPhoneNumberInput input)
        {
            var request = new GetMemberByIdCardOrPhoneNumberInput()
            {
                NationalId = input.NationalId,
                Id = input.Id,
                PhoneNumber = input.PhoneNumber,
                IdCard = input.IdCard,
            };
            return await GetRewardAsync<GetMemberByIdCardOrPhoneNumberOutput>(RewardApiUrl.MEMBER_GET_INFO_BY_ID_PHONE_IDCARD, request);
        }
            // Hafm goi tu Mobile API để disconnect từ App LinkID
        public async Task Disconnect(RemoveConnectionInput input)
        {
            try
            {
                await PostToPartnerIntegrationApiAsync<DeleteAccountOutput>("integration/remove-connection-by-linkid-membercode-and-merchant-id", 
                    input);
            }
            catch (Exception e)
            {
                _logger.LogError(" >> ERROR remove-connection-by-linkid-membercode-and-merchant-id >> " + input.LinkIdMemberCode);
                _logger.LogError(e.Message);
            }
        }
        /**
         * Xoas member cua Linkid
         * - Xoa o operator db (Reward Network)
         * - Xoa o LinkiD Loyalty
         * - Xoa connection o Loyalty VPBank
         * - Xoa connection o partner-integration api
         */
        public async Task<DeleteAccountOutput> DeleteAccount(DeleteAccountInput input, RewardMemberGetInfoOutput memberInfo)
        {
            if (memberInfo == null)
            {
                throw new Exception("Member Code không hợp lệ");
            }
            // Buoc 1: Xoa ben OPERATOR
            await PostRewardAsync<DeleteAccountOutput>(RewardApiUrl.DELETE_MEMBER, new
            {
                memberInfo.PhoneNumber,
                input.PinCode,
                input.DeviceId
            });
            // Buoc 2: goi xuong LinkID Loyalty
            try
            {
                await _customersService.DeleteAccount(new DeleteAccountInputForLoyLinkId
                {
                    MemberCode = input.MemberCode, OtpSessionId = input.RequestId
                });
            }
            catch (Exception e)
            {
                _logger.LogError(" >> ERROR KHI XOA O LINKID LOYALTY >> " + input.MemberCode);
                _logger.LogError(e.Message + " - " + e.StackTrace);
            }
            // Buoc 2.5: goi xuong LinkID tenant ở common loyalty
            try
            {
                await _customersService.DeleteMemberLoyaltyInCommonLoyalty(new CommonLoyDeleteAccountInput
                {
                    MemberCode = input.MemberCode,
                }, linkidTenantIdInCommonLoy);
            }
            catch (Exception e)
            {
                _logger.LogError(" >> ERROR KHI XOA O LINKID TENANT O COMMON LOYALTY >> " + input.MemberCode);
                _logger.LogError(e.Message + " - " + e.StackTrace);
            }
            // Buoc 3: goi sang VPBank OnPrem de xoa 
            try
            {
                await _vpbloyUtilService.RemoveConnection(memberInfo.Id);
            }
            catch (Exception e)
            {
                _logger.LogError(" >> ERROR KHI XOA O ONPREMISE >> " + input.MemberCode);
                _logger.LogError(e.Message + " - " + e.StackTrace);
            }
            
            // Buoc 4: goi sang Partner Integration API de xoa
            try
            {
                await PostToPartnerIntegrationApiAsync<DeleteAccountOutput>("integration/linkid-account-deleted", 
                    new {LinkIdMemberCode = input.MemberCode });
            }
            catch (Exception e)
            {
                _logger.LogError(" >> ERROR KHI XOA O PostToPartnerIntegrationApiAsync >> " + input.MemberCode);
                _logger.LogError(e.Message + " - " + e.StackTrace);
            }
            // Bước cuối: Xoá ở firebaseauth
            try
            {
                _logger.LogInformation(" >> About to delete user from Firebase Admin SDK " + input.MemberCode);
                var auth = FirebaseAuth.DefaultInstance;
                var userRecord = await auth.GetUserByPhoneNumberAsync(memberInfo.PhoneNumber);
                await auth.DeleteUserAsync(userRecord.Uid);
            }
            catch (Exception e)
            {
                _logger.LogError(" >> Error delete firebase: " + e.StackTrace);
                _logger.LogError(" >> Error when delete from FirebaseAuth >> " + input.MemberCode);
            }
            return new DeleteAccountOutput()
            {
                Error = "00", Message = "Success"
            };
        }

        public async Task<RewardMemberFirstActionMemberOutput> FirstActionMember(RewardMemberFirstActionMemberInput input)
        {
            try
            {
                return await PostRewardAsync<RewardMemberFirstActionMemberOutput>(RewardApiUrl.MEMBER_FIRST_ACTION_MEMBER, input);
            } catch {
                return null;
            }
        }

        public async Task<RewardMemberGetMemberLoginByFirebaseIdOutput> GetMemberLoginByFirebaseId(string authorization)
        {
            var nationalId = await getNational(authorization);
            var request = new RewardMemberGetMemberLoginByFirebaseIdInput()
            {
                FirebaseId = nationalId,
            };
            return await GetRewardAsync<RewardMemberGetMemberLoginByFirebaseIdOutput>(RewardApiUrl.MEMBER_GET_LOGIN_BY_FIREBASE_ID, request);
        }

        public async Task<RewardMemberGetUsagePriorityOutput> GetUsagePriority(RewardMemberGetUsagePriorityInput input)
        {
            return await GetRewardAsync<RewardMemberGetUsagePriorityOutput>(RewardApiUrl.MEMBER_GET_USAGE_PRIORITY, input);
        }

        public async Task<RewardMemberGetCashoutAndTopupInfoOutput> GetCashoutAndTopupInfo(RewardMemberGetCashoutAndTopupInfoInput input)
        {
            return await GetRewardAsync<RewardMemberGetCashoutAndTopupInfoOutput>(RewardApiUrl.MEMBER_GET_CASHOUT_AND_TOPUP_INFO, input);
        }

        public async Task<RewardMemberUpdateUsagePriorityOutput> UpdateUsagePriority(RewardMemberUpdateUsagePriorityInput input)
        {
            return await PostRewardAsync<RewardMemberUpdateUsagePriorityOutput>(RewardApiUrl.MEMBER_UPDATE_USAGE_PRIORITY, input);
        }

        public async Task<RewardMemberUpdatePointUsageTypeOutput> UpdatePointUsageType(RewardMemberUpdatePointUsageTypeInput input)
        {
            return await PostRewardAsync<RewardMemberUpdatePointUsageTypeOutput>(RewardApiUrl.MEMBER_UPDATE_POINT_USAGE_TYPE, input);
        }

        public async Task<CheckGeneratePaymentLinkOutput> CheckGeneratePaymentLinkByMember(CheckGeneratePaymentLinkInput input)
        {
            return await PostRewardAsync<CheckGeneratePaymentLinkOutput>(RewardApiUrl.PAYMENT_CHECK_GENERATE_PAYMENT_LINK, input);
        }

        public async Task<RewardMemberGetReferenceDataConnectedOutput> GetReferenceDataConnected(RewardMemberGetReferenceDataConnectedInput input)
        {
            return await GetRewardAsync<RewardMemberGetReferenceDataConnectedOutput>(RewardApiUrl.MEMBER_GET_REFERENCE_DATA_CONNECTED, input);
        }

        public async Task<RewardMemberConfirmConnectOutput> ConfirmConnect(RewardMemberConfirmConnectInput input)
        {
            return await PostRewardAsync<RewardMemberConfirmConnectOutput>(RewardApiUrl.MEMBER_CONFIRM_CONNECT_MERCHANT, input);
        }

        public async Task<RewardMemberGetInfoConfirmConnectMerchantOutput> GetInfoConfirmConnectMerchant(RewardMemberGetInfoConfirmConnectMerchantInput input)
        {
            return await PostRewardAsync<RewardMemberGetInfoConfirmConnectMerchantOutput>(RewardApiUrl.MEMBER_INFO_CONFIRM_CONNECT_MERCHANT, input);
        }

        public async Task<VerifyAuthenWithMemberCodeOutput> VerifyAuthenWithMemberCode(VerifyAuthenWithMemberCodeInput input)
        {
            return await PostRewardAsync<VerifyAuthenWithMemberCodeOutput>(RewardApiUrl.MEMBER_VERIFY_AUTHEN_MEMBER_CODE, input);
        }

        public async Task<RewardMemberSendOtpOutput> SendOtp(RewardMemberSendOtpInput input)
        {
            return await PostRewardAsync<RewardMemberSendOtpOutput>(RewardApiUrl.MEMBER_SEND_OTP, input);
        }

        public async Task<RewardMemberVerifyOtpOutput> VerityOtp(RewardMemberVerifyOtpInput input)
        {
            return await PostRewardAsync<RewardMemberVerifyOtpOutput>(RewardApiUrl.MEMBER_CONFIRM_OTP, input);
        }

        public async Task<UpdatePhoneNumberOutputDto> UpdatePhoneNumberTelco(MobileUpdatePhoneNumberInput input, string authorization)
        {
            var auth = FirebaseAuth.DefaultInstance;
            var memberUidFirebase = await getNational(authorization);
            var oldPhone = input.OldPhoneNumber;
            UserRecord user = null;
            try
            {
                user = await auth.GetUserAsync(memberUidFirebase);
            }
            catch (FirebaseAuthException ex)
            {
                _logger.LogError("Get firebase error from uuid", ex);
                if (ex.AuthErrorCode == AuthErrorCode.UserNotFound)
                {
                    getExceptionCustome("MemberNotExits", "Member does not exist");
                }
                getExceptionCustome("SystemError", "System error");
            }

            try
            {
                await auth.UpdateUserAsync(new UserRecordArgs()
                {
                    PhoneNumber = input.PhoneNumber,
                    Uid = user.Uid
                });
            }
            catch (FirebaseAuthException ex)
            {
                _logger.LogError("Update phone number firebase error from uuid", ex);
                if (ex.AuthErrorCode == AuthErrorCode.PhoneNumberAlreadyExists)
                {
                    getExceptionCustome("DuplicatePhoneNumber", "Duplicate phone number request");
                }
                getExceptionCustome("SystemError", "System error");
            }

            var inputBE = new UpdatePhoneNumberInput()
            {
                MemberCode = input.MemberCode,
                PhoneNumber = input.PhoneNumber
            };
            var rewardUpdatePhone = new RewardMemberUpdatePhoneDto();
            try
            {
                rewardUpdatePhone = await PutRewardAsync<RewardMemberUpdatePhoneDto>(RewardApiUrl.MEMBER_UPDATE_PHONE_NUMBER, inputBE);
                _logger.LogDebug("Reward update phone result: " + JsonConvert.SerializeObject(rewardUpdatePhone));
            }
            catch (Exception ex)
            {
                _logger.LogError("Update phone number reward error: " + ex.Message);
                var flag = false;
                for (int i = 0; i < 2 && !flag && !string.IsNullOrEmpty(oldPhone); i++)
                {
                    try
                    {
                        await auth.UpdateUserAsync(new UserRecordArgs()
                        {
                            PhoneNumber = oldPhone,
                            Uid = memberUidFirebase
                        });
                        flag = true;
                    }
                    catch { }
                }

                var resultEx = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError("Update phone number reward exception error: " + JsonConvert.SerializeObject(resultEx));
                var result = new UpdatePhoneNumberOutputDto();
                switch (resultEx.Code)
                {
                    case "MemberNotExits":
                        result.Status = UpdatePhoneNumberStatus.MemberNotExists;
                        result.Message = "Member does not exist";
                        break;
                    case "DuplicatePhoneNumber":
                        result.Status = UpdatePhoneNumberStatus.DuplicatePhoneNumber;
                        result.Message = "Phone number already exist";
                        break;
                    default:
                        result.Status = UpdatePhoneNumberStatus.SystemError;
                        result.Message = "System error";
                        break;
                }
                return result;
            }


            try
            {
                await _customersService.UpdatePhoneNumber(inputBE);
            }
            catch (Exception ex)
            {
                _logger.LogError("Update phone number loyalty error: " + ex.Message);
                var flag = false;
                for (int i = 0; i < 2 && !flag && !string.IsNullOrEmpty(oldPhone); i++)
                {
                    try
                    {
                        await auth.UpdateUserAsync(new UserRecordArgs()
                        {
                            PhoneNumber = oldPhone,
                            Uid = memberUidFirebase
                        });
                        flag = true;
                    }
                    catch { }
                }

                flag = false;
                inputBE.PhoneNumber = oldPhone;

                for (int i = 0; i < 2 && !flag; i++)
                {
                    try
                    {
                        await PutRewardAsync<RewardMemberUpdatePhoneDto>(RewardApiUrl.MEMBER_UPDATE_PHONE_NUMBER, inputBE);
                        flag = true;
                    }
                    catch { }
                }

                var resultEx = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError("Update phone number loyalty exception error: " + JsonConvert.SerializeObject(resultEx));
                var result = new UpdatePhoneNumberOutputDto();
                switch (resultEx.Code)
                {
                    case "101":
                        result.Status = UpdatePhoneNumberStatus.MemberNotExists;
                        result.Message = "Member does not exist";
                        break;
                    case "202":
                        result.Status = UpdatePhoneNumberStatus.DuplicatePhoneNumber;
                        result.Message = "Phone number already exist";
                        break;
                    default:
                        result.Status = UpdatePhoneNumberStatus.SystemError;
                        result.Message = "System error";
                        break;
                }
                return result;
            }
            var ListMerchantNeedUpdatePhone = rewardUpdatePhone != null ? rewardUpdatePhone.Item.ListMerchantNeedUpdatePhone : null;
            return new UpdatePhoneNumberOutputDto()
            {
                Status = UpdatePhoneNumberStatus.Success,
                Message = "Success",
                HasUpdatePartnerPhone = true,
                ListMerchantNeedUpdatePhone = ListMerchantNeedUpdatePhone,
                MemberCode = rewardUpdatePhone?.Item?.MemberCode,
                MemberId = rewardUpdatePhone?.Item?.MemberId
            };
        }

        public async Task<UpdatePhoneNumberOutputDto> AddPhoneNumberTelco(MobileUpdatePhoneNumberInput input, string authorization)
        {
            var auth = FirebaseAuth.DefaultInstance;
            var memberUidFirebase = await getNational(authorization);
            UserRecord user = null;
            var hasSystemError = false;
            try
            {
                user = await auth.GetUserAsync(memberUidFirebase);
                if (!string.IsNullOrWhiteSpace(user.PhoneNumber))
                {
                    getExceptionCustome("MemberHavePhoneNumber", "Member have phone number, cannot this action");
                }
            }
            catch (FirebaseAuthException ex)
            {
                _logger.LogError("Get firebase error from uuid", ex);
                if (ex.AuthErrorCode == AuthErrorCode.UserNotFound)
                {
                    getExceptionCustome("MemberNotExits", "Member does not exist");
                }
                getExceptionCustome("SystemError", "System error");
            }

            try
            {
                await auth.UpdateUserAsync(new UserRecordArgs()
                {
                    PhoneNumber = input.PhoneNumber,
                    Uid = user.Uid
                });
            }
            catch (FirebaseAuthException ex)
            {
                _logger.LogError("Update phone number firebase error from uuid", ex);
                if (ex.AuthErrorCode == AuthErrorCode.PhoneNumberAlreadyExists)
                {
                    getExceptionCustome("DuplicatePhoneNumber", "Duplicate phone number request");
                }
                getExceptionCustome("SystemError", "System error");
            }

            var inputBE = new UpdatePhoneNumberInput()
            {
                MemberCode = input.MemberCode,
                PhoneNumber = input.PhoneNumber
            };
            var rewardUpdatePhone = new RewardMemberUpdatePhoneDto();
            try
            {
                rewardUpdatePhone = await PutRewardAsync<RewardMemberUpdatePhoneDto>(RewardApiUrl.MEMBER_UPDATE_PHONE_NUMBER, inputBE);
                _logger.LogDebug("Reward update phone result: " + JsonConvert.SerializeObject(rewardUpdatePhone));
            }
            catch (Exception ex)
            {
                _logger.LogError("Update phone number reward error: " + ex.Message);
                var flag = false;
                for (int i = 0; i < 2 && !flag; i++)
                {
                    try
                    {
                        await auth.UpdateUserAsync(new UserRecordArgs()
                        {
                            PhoneNumber = null,
                            Uid = memberUidFirebase
                        });
                        flag = true;
                    }
                    catch { }
                }

                var resultEx = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError("Update phone number reward exception error: " + JsonConvert.SerializeObject(resultEx));
                hasSystemError = true;
            }


            try
            {
                await _customersService.UpdatePhoneNumber(inputBE);
            }
            catch (Exception ex)
            {
                _logger.LogError("Update phone number loyalty error: " + ex.Message);
                var flag = false;
                for (int i = 0; i < 2 && !flag; i++)
                {
                    try
                    {
                        await auth.UpdateUserAsync(new UserRecordArgs()
                        {
                            PhoneNumber = null,
                            Uid = memberUidFirebase
                        });
                        flag = true;
                    }
                    catch { }
                }

                flag = false;
                inputBE.PhoneNumber = "";

                for (int i = 0; i < 2 && !flag; i++)
                {
                    try
                    {
                        await PutRewardAsync<RewardMemberUpdatePhoneDto>(RewardApiUrl.MEMBER_UPDATE_PHONE_NUMBER, inputBE);
                        flag = true;
                    }
                    catch { }
                }
                var resultEx = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError("Update phone number loyalty exception error: " + JsonConvert.SerializeObject(resultEx));
                hasSystemError = true;
            }
            if (hasSystemError)
            {
                getExceptionCustome("SystemError", "System error");
            }
            var ListMerchantNeedUpdatePhone = rewardUpdatePhone != null ? rewardUpdatePhone.Item.ListMerchantNeedUpdatePhone : null;
            return new UpdatePhoneNumberOutputDto()
            {
                Status = UpdatePhoneNumberStatus.Success,
                Message = "Success",
                HasUpdatePartnerPhone = true,
                ListMerchantNeedUpdatePhone = ListMerchantNeedUpdatePhone,
                MemberCode = rewardUpdatePhone?.Item?.MemberCode,
                MemberId = rewardUpdatePhone?.Item?.MemberId
            };
        }

        public async Task<ResponseDataOutput<RewardMemberCheckSumOutput>> SendOtpChangePhone(SendOtpChangePhoneInput input, string sessionId, string authorization)
        {
            checkPhone(input.PhoneNumber);
            checkPhoneOld(input.OldPhoneNumber);
            var auth = FirebaseAuth.DefaultInstance;
            //Kiểm tra old phone nhập vào có map hay là không
           var memberUidFirebase = await getNational(authorization);
            try
            {
                var userOld = await auth.GetUserAsync(memberUidFirebase);
                if (string.IsNullOrWhiteSpace(userOld.PhoneNumber))
                {
                    getExceptionCustome("MemberNotHavePhoneNumber", "Member have not phone number, please update phone number");
                }
                if (userOld.PhoneNumber != input.OldPhoneNumber)
                {
                    getExceptionCustome("OldPhoneNumberNotMatch", "Old phone number not match");
                }
            }
            catch (FirebaseAuthException ex)
            {
                _logger.LogError("Get firebase error from uuid", ex);
                if (ex.AuthErrorCode == AuthErrorCode.UserNotFound)
                {
                    getExceptionCustome("MemberNotExits", "Member does not exist");
                }
                getExceptionCustome("SystemError", "System error");
            }
            //Kiểm trả xem số điện thoại mới có tồn tại hay chưa
            try
            {
                var userRecord = await auth.GetUserByPhoneNumberAsync(input.PhoneNumber);
                if (userRecord != null)
                {
                    getExceptionCustome("DuplicatePhoneNumber", "Duplicate phone number request");
                }
            }
            catch (FirebaseAuthException ex)
            {
                if (ex.AuthErrorCode != AuthErrorCode.UserNotFound)
                {
                    _logger.LogError("Send otp change phone firebase error from uuid", ex);
                    getExceptionCustome("SystemError", "System error");
                }
            }
            var requestUpdatePhone = new MobileUpdatePhoneNumberInput()
            {
                MemberCode = input.MemberCode,
                OldPhoneNumber = input.OldPhoneNumber,
                PhoneNumber = input.PhoneNumber,
            };
            await PostRewardAsync<RewardReponseNoDataOutput>(RewardApiUrl.MEMBER_UPDATE_PHONE_VALIDATION, requestUpdatePhone);
            await SendOtp(new RewardMemberSendOtpInput()
            {
                PhoneNumber = input.PhoneNumber,
                SmsType = "ChangePhoneNumber",
                SessionId = sessionId,
            });
            return new ResponseDataOutput<RewardMemberCheckSumOutput>()
            {
                Message = "Success",
                Result = 200,
                Item = new RewardMemberCheckSumOutput()
                {
                    Checksum = SignatureHelper.DoSignature(input,
                        _configuration.GetSection("HmacSHA512Secret").Value),
                }
            };
        }

        public async Task<UpdatePhoneNumberOutputDto> VerifyOtpChangePhone(VerifyOtpChangePhoneInput input, string sessionId, string otpCode, string authorization)
        {
            checkPhone(input.PhoneNumber);
            checkPhoneOld(input.OldPhoneNumber);
            await VerityOtp(new RewardMemberVerifyOtpInput()
            {
                PhoneNumber = input.PhoneNumber,
                SmsType = "ChangePhoneNumber",
                SessionId = sessionId,
                OtpCode = otpCode,
            });
            return await UpdatePhoneNumberTelco(new MobileUpdatePhoneNumberInput()
            {
                MemberCode = input.MemberCode,
                OldPhoneNumber = input.OldPhoneNumber,
                PhoneNumber = input.PhoneNumber,
            }, authorization);
        }

        public async Task<ResponseDataOutput<RewardMemberCheckSumOutput>> SendOtpChangePinCode(SendOtpChangePinCodeInput input, string sessionId, string authorization)
        {
            var requestUpdatePinCode = new RewardMemberCreateOrUpdatePinCodeRequest()
            {
                MemberCode = input.MemberCode,
                PinCode = input.PinCode,
            };
            await PostRewardAsync<RewardReponseNoDataOutput>(RewardApiUrl.MEMBER_UPDATE_PIN_CODE_VALIDATION, requestUpdatePinCode);
            var requestOtp = new RewardMemberOtpSendOtpInput()
            {
                MemberCode = input.MemberCode,
                SmsType = "ChangePinCode",
                SessionId = sessionId,
            };
            await PostRewardAsync<RewardMemberOtpSendOtpOutput>(RewardApiUrl.MEMBER_SEND_OTP_ACTION, requestOtp);

            return new ResponseDataOutput<RewardMemberCheckSumOutput>()
            {
                Message = "Success",
                Result = 200,
                Item = new RewardMemberCheckSumOutput()
                {
                    Checksum = SignatureHelper.DoSignature(input,
                        _configuration.GetSection("HmacSHA512Secret").Value),
                }
            };
        }

        public async Task<RewardMemberCreateOrUpdatePinCodeResponse> VerifyOtpChangePinCode(VerifyOtpChangePinCodeInput input, string sessionId, string otpCode, string authorization, string deviceId)
        {
            var requestOtp = new RewardMemberOtpVerifyOtpInput()
            {
                MemberCode = input.MemberCode,
                SmsType = "ChangePinCode",
                SessionId = sessionId,
                OtpCode = otpCode,
            };
            await PostRewardAsync<RewardMemberOtpVerifyOtpOutput>(RewardApiUrl.MEMBER_VERIFY_OTP_ACTION, requestOtp);
            return await CreateOrUpdatePinCode(new MobileAPICreateOrUpdatePinCodeInput()
            {
                MemberCode = input.MemberCode,
                PinCode = input.PinCode,
                Type = 3,
            }, authorization);
        }

        public async Task<ResponseNoDataOutput> SendOtpForgetPinCode(SendOtpForgetPinCodeInput input, string sessionId)
        {
            //var requestUpdatePinCode = new RewardMemberCreateOrUpdatePinCodeRequest()
            //{
            //    MemberCode = input.MemberCode,
            //    PinCode = input.PinCode,
            //};
            //await PostRewardAsync<RewardReponseNoDataOutput>(RewardApiUrl.MEMBER_UPDATE_PIN_CODE_VALIDATION, requestUpdatePinCode);
            var requestOtp = new RewardMemberOtpSendOtpInput()
            {
                MemberCode = input.MemberCode,
                SmsType = "ForgetPinCode",
                SessionId = sessionId,
            };
            await PostRewardAsync<RewardMemberOtpSendOtpOutput>(RewardApiUrl.MEMBER_SEND_OTP_ACTION, requestOtp);

            return new ResponseNoDataOutput()
            {
                Message = "Success",
                Result = 200,
                MessageDetail = null,
            };
        }

        public async Task<RewardMemberForgetPinCodeResponse> VerifyOtpForgetPinCode(VerifyOtpForgetPinCodeInput input, string sessionId, string otpCode)
        {
            var requestOtp = new RewardMemberOtpVerifyOtpInput()
            {
                MemberCode = input.MemberCode,
                SmsType = "ForgetPinCode",
                SessionId = sessionId,
                OtpCode = otpCode,
            };
            var resultOtp = await PostRewardAsync<RewardMemberOtpVerifyOtpOutput>(RewardApiUrl.MEMBER_VERIFY_OTP_ACTION, requestOtp);
            var checksumInput = new RewardMemberUpdatePinCodeForgetChecksum()
            {
                SessionId = sessionId,
                FirebaseId = resultOtp.FirebaseId,
                MemberCode = input.MemberCode,
            };
            return new RewardMemberForgetPinCodeResponse()
            {
                Item = new RewardMemberForgetPinCodeResponseItem()
                {
                    SessionId = sessionId,
                    FirebaseId = resultOtp.FirebaseId,
                    MemberCode = input.MemberCode,
                    Checksum = SignatureHelper.DoSignature(checksumInput,
                        _configuration.GetSection("HmacSHA512Secret").Value),
                },
                Message = "Success",
                Result = 200,
            };
        }

        public async Task<RewardMemberUpdatePinCodeForgetOutput> UpdatePinCodeForget(RewardMemberUpdatePinCodeForgetInput input, string pinCode, string deviceId)
        {
            await PostRewardAsync<RewardMemberCreateOrUpdatePinCodeResponse>(RewardApiUrl.MEMBER_CREATE_OR_UPDATE_PINCODE,
            new RewardMemberCreateOrUpdatePinCodeRequest()
            {
                MemberCode = input.MemberCode,
                PinCode = pinCode,
                DeviceId = deviceId ?? ""
            });

            var auth = FirebaseAuth.DefaultInstance;
            await auth.RevokeRefreshTokensAsync(input.FirebaseId);
            var customToken = await auth.CreateCustomTokenAsync(input.FirebaseId);
            return new RewardMemberUpdatePinCodeForgetOutput()
            {
                Item = new RewardMemberUpdatePinCodeForgetItem()
                {
                    CustomToken = customToken,
                    MemberCode = input.MemberCode,
                },
                Message = "Success",
                Result = 200,
            };
        }

        public async Task<ResponseDataOutput<RewardMemberCheckSumOutput>> SendOtpAddPhone(SendOtpAddPhoneInput input, string sessionId, string authorization)
        {
            checkPhone(input.PhoneNumber);
            var auth = FirebaseAuth.DefaultInstance;
            // Kiểm tra old phone nhập vào có map hay là không
            var memberUidFirebase = await getNational(authorization);
            try
            {
                var userOld = await auth.GetUserAsync(memberUidFirebase);
                if (!string.IsNullOrWhiteSpace(userOld.PhoneNumber))
                {
                    getExceptionCustome("MemberHavePhoneNumber", "Member have phone number, cannot this action");
                }
            }
            catch (FirebaseAuthException ex)
            {
                _logger.LogError("Get firebase error from uuid", ex);
                if (ex.AuthErrorCode == AuthErrorCode.UserNotFound)
                {
                    getExceptionCustome("MemberNotExits", "Member does not exist");
                }
                getExceptionCustome("SystemError", "System error");
            }
            // Kiểm trả xem số điện thoại mới có tồn tại hay chưa
            try
            {
                var userRecord = await auth.GetUserByPhoneNumberAsync(input.PhoneNumber);
                if (userRecord != null)
                {
                    getExceptionCustome("DuplicatePhoneNumber", "Duplicate phone number request");
                }
            }
            catch (FirebaseAuthException ex)
            {
                if (ex.AuthErrorCode != AuthErrorCode.UserNotFound)
                {
                    _logger.LogError("Send otp change phone firebase error from uuid", ex);
                    getExceptionCustome("SystemError", "System error");
                }
            }
            var requestUpdatePhone = new MobileUpdatePhoneNumberInput()
            {
                MemberCode = input.MemberCode,
                PhoneNumber = input.PhoneNumber,
            };
            await PostRewardAsync<RewardReponseNoDataOutput>(RewardApiUrl.MEMBER_UPDATE_PHONE_VALIDATION, requestUpdatePhone);
            await SendOtp(new RewardMemberSendOtpInput()
            {
                PhoneNumber = input.PhoneNumber,
                SmsType = "AddPhoneNumber",
                SessionId = sessionId,
            });
            return new ResponseDataOutput<RewardMemberCheckSumOutput>()
            {
                Message = "Success",
                Result = 200,
                Item = new RewardMemberCheckSumOutput()
                {
                    Checksum = SignatureHelper.DoSignature(input,
                        _configuration.GetSection("HmacSHA512Secret").Value),
                }
            };
        }

        public async Task<ResponseNoDataOutput> VerifyOtpAddPhone(VerifyOtpAddPhoneInput input, string sessionId, string otpCode, string authorization)
        {
            checkPhone(input.PhoneNumber);
            await VerityOtp(new RewardMemberVerifyOtpInput()
            {
                PhoneNumber = input.PhoneNumber,
                SmsType = "AddPhoneNumber",
                SessionId = sessionId,
                OtpCode = otpCode,
            });
            await AddPhoneNumberTelco(new MobileUpdatePhoneNumberInput()
            {
                MemberCode = input.MemberCode,
                OldPhoneNumber = null,
                PhoneNumber = input.PhoneNumber,
            }, authorization);
            return new ResponseNoDataOutput()
            {
                Result = 200,
                Message = "Success",
                MessageDetail = null,
            };
        }


        public async Task<RewardGetUsingFirebaseOtpDto> GetCommonUsingFirebaseOtp()
        {
            var usingFirebaseOtp = false;
            try
            {
                var result = await GetUsingFirebaseOtp();
                usingFirebaseOtp = result.UsingFirebaseOtp;
            } catch
            {
                usingFirebaseOtp = false;
            }

            return new RewardGetUsingFirebaseOtpDto()
            {
                Item = new RewardGetUsingFirebaseOtpOutput()
                {
                    UsingFirebaseOtp = usingFirebaseOtp,
                },
                Message = "Success",
                Result = 200,
            };
        }

        public async Task<CheckCifExistWithMerchantOutput> CheckCifExistWithMerchant(string cif, int merchantid)
        {
            var input = new CheckCifExistWithMerchantInput() {Cif = cif , MerchantId = merchantid};
            return await PostRewardAsync<CheckCifExistWithMerchantOutput>(RewardApiUrl.MEMBER_CheckCifExistWithMerchant, input);
        }

        public async Task<GetCreditBalanceByMemberCodeOutput> GetCreditBalanceByMemberCode(
            GetCreditBalanceByMemberCodeInput input)
        {
            return await GetRewardAsync<GetCreditBalanceByMemberCodeOutput>(RewardApiUrl.GetCreditBalanceByMemberCode, input);
        }

        public async Task<RewardMemberGetCifByMemberCodeOutput> GetCifByNationalId(RewardMemberGetCifByMemberCodeInput input)
        {
            return await PostRewardAsync<RewardMemberGetCifByMemberCodeOutput>(RewardApiUrl.GET_CIF_BY_NATIONAL_ID, input);
        }
        
        public async Task<RewardSendAckAfterConnectedOutput> RewardSendAckAfterConnected(
            RewardSendAckAfterConnectedInput input)
        {
            return await PostRewardAsync<RewardSendAckAfterConnectedOutput>(RewardApiUrl.RewardSendAckAfterConnected, input);
        }
        public async Task<RewardSendAckAfterConnectedOutput> RewardSendAckAfterDisconnected(
                    RewardSendAckAfterConnectedInput input)
        {
            return await PostRewardAsync<RewardSendAckAfterConnectedOutput>(RewardApiUrl.RewardSendAckAfterDisconnected, input);
        }

        private void checkPhone(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                getExceptionCustome("CannotEmptyPhoneNumber", "Cannot empty phone");
            }
            if (!phoneNumber.StartsWith("+84"))
            {
                getExceptionCustome("InvalidFormatPhone", "Invalid format phone");
            }
            var temp = phoneNumber.Replace("+", "");
            var regOnlyNumberChar = new Regex(@"^[0-9]+$");
            if (!regOnlyNumberChar.IsMatch(temp))
            {
                getExceptionCustome("InvalidFormatPhone", "Invalid format phone");
            }
            if (temp.Length <= 10 || temp.Length >= 12)
            {
                getExceptionCustome("InvalidFormatPhone", "Invalid format phone");
            }
        }

        private void checkPhoneOld(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                getExceptionCustome("CannotEmptyOldPhoneNumber", "Cannot empty old phone");
            }
            if (!phoneNumber.StartsWith("+84"))
            {
                getExceptionCustome("InvalidFormatOldPhone", "Invalid format old phone");
            }
            var temp = phoneNumber.Replace("+", "");
            var regOnlyNumberChar = new Regex(@"^[0-9]+$");
            if (!regOnlyNumberChar.IsMatch(temp))
            {
                getExceptionCustome("InvalidFormatOldPhone", "Invalid format old phone");
            }
            if (temp.Length <= 10 || temp.Length >= 12)
            {
                getExceptionCustome("InvalidFormatOldPhone", "Invalid format old phone");
            }
        }

        private void getExceptionCustome(string errorCode, string message)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = message,
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }
        public async Task<GetMerchantInfoOutput> GetMerchantInfo(GetMerchantInfoInput input)
        {
            return await GetRewardAsync<GetMerchantInfoOutput>(RewardApiUrl.MERCHANT_GETINFO, input);
        }
        public async Task<int> GetMemberIdByMemberCode(string memberCode, string token)
        {
            var cacheKey = "GTX_" + memberCode;
            var cachedValue = await _cache.GetStringAsync(cacheKey);
            if (cachedValue != null)
            {
                try
                {
                    var id = 0;
                    var check = int.TryParse(cachedValue, out id);
                    if (check && id > 0)
                    {
                        return id;
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError("Error parse data of " + cacheKey);
                }
            }

            try
            {
                var getInfo = await ViewPoint(new RewardMemberRequestInput()
                {
                    MemberCode = memberCode,
                }, token);
                var memberid = getInfo.Items?.Id ?? 0;
                if (memberid > 0)
                {
                    await _cache.SetStringAsync(cacheKey, memberid + "",
                        new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromHours(1)));
                    return memberid;
                }
            }
            catch (Exception e)
            {
                throw new Exception("CannotGetMemberData");
            }

            throw new Exception("DataInvalid");
        }
        public async Task<ExchangeLynkiDToPartnerPointOutput> ExchangeLynkiDToPartnerPoint(
            ExchangeLynkiDToPartnerPointInput input)
        {
            return await PostRewardAsync<ExchangeLynkiDToPartnerPointOutput>(RewardApiUrl.MEMBER_EXCHANGE_LINKIDTOKEN_TO_PARTNER_POINT, new
            {
                NationalId = input.MemberCode, MemberId = input.MemberId, OrderCode = input.TransactionCode, 
                SourceExchange = input.SourceExchange, MerchantId = input.MerchantId
                , TokenAmount = input.TokenAmount, Cif = input.Cif, ExtraData = input.ExtraData
            }, MerchantNameConfig.VPID);
        }

        public async Task<RevertExchangeLynkiDToPartnerPointOutput> RevertExchangeLynkiDToPartnerPoint(
            RevertExchangeLynkiDToPartnerPointInput input)
        {
            return await PostRewardAsync<RevertExchangeLynkiDToPartnerPointOutput>(RewardApiUrl.MEMBER_EXCHANGE_LINKIDTOKEN_TO_PARTNER_POINT_Revert, new
            {
                NationalId = input.MemberCode, MemberId = input.MemberId, OrderCode = input.TransactionCode, 
                MerchantId = input.MerchantId, Message = input.Message
            }, MerchantNameConfig.VPID);
        }
        public async Task<CheckExchangeLynkiDToPartnerPointOutput> CheckExchangeLynkiDToPartnerPoint(CheckExchangeLynkiDToPartnerPointInput input)
        {
            throw new NotImplementedException();
        }

        public async Task<UpdatePartnerBindingTransactionIdOutput> UpdatePartnerBindingTransactionId(
            UpdatePartnerBindingTransactionIdInput input)
        {
            return await PutRewardAsync<UpdatePartnerBindingTransactionIdOutput>(RewardApiUrl.UPDATE_EXCHANGE_PARTNERBINDINGTXID, input, null);
        }
        // API for MoneyCard
        public async Task<GetMoneyCardOfMemberOutput> GetMoneyCardOfMember(GetMoneyCardOfMemberInput input)
        {
            return await GetRewardAsync<GetMoneyCardOfMemberOutput>(RewardApiUrl.GetMoneyCardByMember, input, null);
        }
        
        public async Task<GetAvailableBalanceOfMoneyCardOutput> GetAvailableBalanceOfMoneyCard(
            GetAvailableBalanceOfMoneyCardInput input)
        {
            return await GetRewardAsync<GetAvailableBalanceOfMoneyCardOutput>(RewardApiUrl.GetCardTransactionByMember, input, null);
        }

        public async Task<GetCardTransactionOfMemberOutput> GetCardTransactionOfMember(GetCardTransactionOfMemberInput input)
        {
            var request = new GetCardTransactionOfMemberDto()
            {
                maxResultCount = input.MaxResultCount,
                skipCount = input.SkipCount,
                MemberId = input.MemberId,
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                OrderCode = input.OrderCode,
                CardCode = input.CardCode,
                ActionType = input.ActionType,
                CardTransactionId = input.CardTransactionId
            };
            return await GetRewardAsync<GetCardTransactionOfMemberOutput>(RewardApiUrl.GetCardTransactionByMember, request, null);
        }
        
        public async Task<RewardMemberGetPaymentMethodOutput> GetPaymentMethod(RewardMemberGetPaymentMethodInput input)
        {
            return await PostRewardAsync<RewardMemberGetPaymentMethodOutput>(RewardApiUrl.GetPaymentMethod, input, null);
        }

        public async Task<WebstoreCallRewardLoginResponse> WebstoreLogin(WebstoreCallRewardLoginRequest input)
        {
            return await PostRewardAsync<WebstoreCallRewardLoginResponse>(RewardApiUrl.WebstoreSmeLogin, input);
        }

        public async Task<WebstoreGetSmeInfoResponse> WebstoreGetSmeInfo(WebstoreGetSmeInfoRequest input)
        {
            return await GetRewardAsync<WebstoreGetSmeInfoResponse>(RewardApiUrl.WebstoreGetSmeInfo, input);
        }

        public async Task<WebstoreGetFullSmeInfoResponse> WebstoreGetFullSmeInfo(WebstoreGetFullSmeInfoRequest input)
        {
            return await GetRewardAsync<WebstoreGetFullSmeInfoResponse>(RewardApiUrl.WebstoreGetSmeFullInfo, input);
        }

        public async Task<WebstoreSetSmePasswordResponse> WebstoreSetSmePassword(WebstoreSetSmePasswordRequest input)
        {
            return await PostRewardAsync<WebstoreSetSmePasswordResponse>(RewardApiUrl.WebstoreSetSmePasword, input);
        }

        public async Task<WebstoreGetSmeBalanceResponse> WebstoreGetSmeBalance(WebstoreGetSmeBalanceRequest input)
        {
            return await GetRewardAsync<WebstoreGetSmeBalanceResponse>(RewardApiUrl.WebstoreGetSmeBalance, input);
        }

        public async Task<WebstoreValidateAccessTokenItem> WebstoreValidateToken(string token)
        {
            var ret = await PostRewardAsync<WebstoreValidateAccessTokenResponse>(RewardApiUrl.WebstoreValidateAccessToken, new {AccessToken = token});
            return ret.Item;
        }

        public async Task<WebstoreRefreshTokenResponse> WebstoreRefreshToken(WebstoreRefreshTokenRequest input)
        {
            return await PostRewardAsync<WebstoreRefreshTokenResponse>(RewardApiUrl.WebstoreRefreshToken, input);
        }

        public async Task<GetAllTokenTransRespone> GetALLTokenTrans(GetAllTokenTransInput input)
        {
            return await GetRewardAsync<GetAllTokenTransRespone>(RewardApiUrl.GetALLTokenTrans, input, MerchantNameConfig.VPID);
        }
        public async Task<GetAllTokenTransRespone> GetALLTokenTransBySmeCif(GetALLTokenTransBySmeCifInput input)
        {
            return await GetRewardAsync<GetAllTokenTransRespone>(RewardApiUrl.GetALLTokenTransBySmeCif, input);
        }
        public async Task<CheckSmePasswordRewardOutput> CheckSmePassword(CheckSmePasswordRewardInput input)
        {
            return await PostRewardAsync<CheckSmePasswordRewardOutput>(RewardApiUrl.WebstoreCheckPassword, input);
        }

        public async Task<RewardMemberRedeemOutput> WebstoreCallRedwardRedeem(WebstoreCallRedwardRedeemInput input)
        {
            return await PostRewardAsync<RewardMemberRedeemOutput>(RewardApiUrl.SME_REDEEM_GIFT_TRANSACTION_CREATE, input, MerchantNameConfig.VPID);
        }
        public async Task<RewardMemberSaveRefreshTokenOutput> SaveRefreshTokenV2(RewardMemberSaveRefreshTokenInputV2 input)
        {
            return await PostRewardAsync<RewardMemberSaveRefreshTokenOutput>(RewardApiUrl.MEMBER_SAVE_REFRESH_TOKEN_V2, input);
        }
        public async Task<AgreeTAndCOutput> AgreeTAndC(object s)
        {
            return await PostRewardAsync<AgreeTAndCOutput>(RewardApiUrl.WebstoreIntegration_SaveTANDC, s);
        }

        public async Task<CreateSmeMemberOutput> CreateSmeMember(object o)
        {
            return await PostRewardAsync<CreateSmeMemberOutput>(RewardApiUrl.INTEGRATION_CREATE_SME, o);
        }

        public async Task<GetPartnerMemberInfoByConnectionOutput> GetPartnerMemberInfoByConnection(GetCifByMemberRequest o)
        {
            return await PostRewardAsync<GetPartnerMemberInfoByConnectionOutput>(RewardApiUrl.GET_PARTNER_MEMBER_BY_CONNECTINFO, o);
        }

        public async Task<AddConnectionResponse> AddConnection(AddConnectionRequest request)
        {
            return await PostToPartnerIntegrationApiAsync<AddConnectionResponse>("integration/add-connection-from-mobile", request);
        }

        public async Task<RemoveConnectionResponse> RemoveConnection(RemoveConnectionRequest request)
        {
            return await PostToPartnerIntegrationApiAsync<RemoveConnectionResponse>("integration/remove-connection-from-mobile", request);
        }

        private enum ErrorCode
        {
            MemberNotExists = 101,
            PhoneNumberAlreadyExists = 201
        }

        private enum RevokeTokenErrorCode
        {
            Success = 1,
            IntenalServerError = 2,
            AccountIsLocked = 3,
            CanNotVerifyPinCodeNow = 4,
            MemberNotExists = 5
        }
    }
}
