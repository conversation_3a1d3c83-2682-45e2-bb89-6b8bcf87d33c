﻿using AKC.MobileAPI.DTO.Gamification;
using AKC.MobileAPI.DTO.Gamification.Exchange;
using AKC.MobileAPI.DTO.Gamification.Game;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Gamification
{
    public interface IItemManagementService
    {
        Task<GetAllGetExchangeRuleListOutput> GetExchangeRuleType(GetAllGetExchangeRuleListInput input);
        Task<GetDetailsExchangeRuleOutput> GetDetailsExchangeRule(GetDetailsExchangeRuleInput input);
    }
}
