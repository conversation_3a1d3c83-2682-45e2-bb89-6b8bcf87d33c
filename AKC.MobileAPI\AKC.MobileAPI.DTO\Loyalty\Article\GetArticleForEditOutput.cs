﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Article
{
    public class GetArticleForEditOutput
    {
        public DataResult Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }

    public class DataResult
    {
        public CreateOrEditArticleDto Article { get; set; }

        public string CreatedByUser { get; set; }

        public string AvatarLink { get; set; }
        public int? AvatarLinkId { get; set; }

        public List<RelatedNewsDto> RelatedNews { get; set; }
    }

    public class CreateOrEditArticleDto
    {
        public long Id { get; set; }
        public string Code { get; set; }

        [StringLength(ArticleConsts.MaxNameLength, MinimumLength = ArticleConsts.MinNameLength)]
        public string Name { get; set; }

        [StringLength(ArticleConsts.MaxDescriptionLength, MinimumLength = ArticleConsts.MinDescriptionLength)]
        public string Description { get; set; }

        public string Content { get; set; }

        [StringLength(ArticleConsts.MaxAvatarLength, MinimumLength = ArticleConsts.MinAvatarLength)]
        public string Avatar { get; set; }

        public string LinkAvatar { get; set; }

        [StringLength(ArticleConsts.MaxStatusLength, MinimumLength = ArticleConsts.MinStatusLength)]
        public string Status { get; set; }

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        public string Tags { get; set; }

        public string UpdatedByUser { get; set; }
        public int? TenantId { get; set; }

        public int? RequiredReadingTime { get; set; }

        public int? RewardedCoin { get; set; }

        public string ActionCode { get; set; }

        public int? Ordinal { get; set; }

        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string Link { get; set; }
        public string ExtraType { get; set; }

        public  string Scope { get; set; }
        public  string ContentAlert { get; set; }
        public  string ContentButton { get; set; }
    }

    public class RelatedNewsDto
    {
        public long Id { get; set; }

        public string Code { get; set; }

        [StringLength(ArticleConsts.MaxNameLength, MinimumLength = ArticleConsts.MinNameLength)]
        public string Name { get; set; }

        [StringLength(ArticleConsts.MaxDescriptionLength, MinimumLength = ArticleConsts.MinDescriptionLength)]
        public string Description { get; set; }

        public string Content { get; set; }

        [StringLength(ArticleConsts.MaxAvatarLength, MinimumLength = ArticleConsts.MinAvatarLength)]
        public string Avatar { get; set; }

        public string LinkAvatar { get; set; }

        [StringLength(ArticleConsts.MaxStatusLength, MinimumLength = ArticleConsts.MinStatusLength)]
        public string Status { get; set; }

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        public string Tags { get; set; }

        public int? RequiredReadingTime { get; set; }

        public int? RewardedCoin { get; set; }

        public string ActionCode { get; set; }

        public int? Ordinal { get; set; }

        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string Link { get; set; }
    }

    public class ArticleConsts
    {
        public const int MinCodeLength = 0;
        public const int MaxCodeLength = 80;

        public const int MinNameLength = 0;
        public const int MaxNameLength = 200;

        public const int MinDescriptionLength = 0;
        public const int MaxDescriptionLength = 1000;

        public const int MinAvatarLength = 0;
        public const int MaxAvatarLength = 1000;

        public const int MinStatusLength = 0;
        public const int MaxStatusLength = 2;
    }
}