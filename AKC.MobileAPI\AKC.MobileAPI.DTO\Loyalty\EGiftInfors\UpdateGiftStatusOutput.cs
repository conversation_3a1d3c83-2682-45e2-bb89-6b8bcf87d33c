﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.EGiftInfors
{
    public class UpdateGiftStatusOutput
    {
        public string Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class UpdateEgiftCodeOutput
    {
        public UpdateEgiftCodeResultDto Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class UpdateEgiftCodeResultDto
    {
        public string GiftRedeemTransactionCode { get; set; }
        public string GiftCode { get; set; }
        public string EgiftGiftCode { get; set; }
        public DateTime? ExpiredDate { get; set; }
    }
}
