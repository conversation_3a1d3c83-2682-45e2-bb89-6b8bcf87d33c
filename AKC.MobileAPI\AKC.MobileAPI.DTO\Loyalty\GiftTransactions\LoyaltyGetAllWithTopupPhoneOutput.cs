﻿using System;
using System.Collections.Generic;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftTransactions
{
    public class LoyaltyGetAllWithTopupPhoneOutput
    {
        public Result Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }

    public class Result
    {
        public int totalCount { get; set; }
        public List<GiftTransactionTopupPhoneWithDate> items { get; set; }
    }

    public class GiftTransactionTopupPhoneWithDate
    {
        public string CreatedDate { get; set; }
        public List<GiftTransactionTopupPhone> listTopupPhoneTransaction { get; set; }
    }

    public class LoyaltyGetAllWithTopupPhone_v1Output
    {
        public LoyaltyGetAllWithTopupPhone_v1Result Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }
    public class LoyaltyGetAllWithTopupPhone_v1Result
    {
        public int totalCount { get; set; }
        public List<GiftTransactionTopupPhone> items { get; set; }
    }
    public class GiftTransactionTopupPhone
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string EGiftCode { get; set; }
        public string EGiftStatus { get; set; }
        public DateTime? EGiftExpiredDate { get; set; }
        public string SerialNo { get; set; }
        public string GiftName { get; set; }
        public string ThirdPartyBrandId { get; set; }
        public string CreationTime { get; set; }
        public int VendorId { get; set; }
        public decimal RequiredCoin { get; set; }
        public string TransactionCode { get; set; }
        public string ThirdPartyCategoryName { get; set; }
        public string TopupCard { get; set; }
        public string CategoryName { get; set; }
        public string whyHaveIt { get; set; }
        public string Status { get; set; }
        public string RecipientPhone { get; set; }
        public bool IsTopup { get; set; }
        public DateTime? CreationDateTime { get; set; }
        public decimal FullPrice { get; set; }
        public BrandGiftDataInfo BrandInfo { get; set; }
    }
}
