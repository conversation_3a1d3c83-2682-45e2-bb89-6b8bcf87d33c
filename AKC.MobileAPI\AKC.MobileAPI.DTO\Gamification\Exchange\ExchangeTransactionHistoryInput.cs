﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Gamification.Exchange
{
    public class ExchangeTransactionHistoryInput
    {
        public int? ID { get; set; }
        public int? TenantID { get; set; }
        public string MemberCode { get; set; }
        public string MemberID { get; set; }
        public int? ExchangeRuleID { get; set; }
        public int? Offset { get; set; }
        public int? Limit { get; set; }
    }
}
