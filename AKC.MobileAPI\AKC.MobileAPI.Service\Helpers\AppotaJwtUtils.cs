﻿using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.Helpers
{
    public static class AppotaJwtUtils
    {
        public static string Get_X_APPOTAPAY_AUTH(string apikey, string secret, string code, ILogger logger)
        {
            logger.LogInformation(" Secret Key when gen Get_X_APPOTAPAY_AUTH: " + secret);
            logger.LogInformation(" apikey when gen Get_X_APPOTAPAY_AUTH: " + apikey);
            logger.LogInformation(" partner code when gen Get_X_APPOTAPAY_AUTH: " + code);
            var headers = new Dictionary<string, string>();
            headers.Add("typ", "JWT");
            headers.Add("alg", "HS256");
            headers.Add("cty", "appotapay-api;v=1");
            var threeMinLater = DateTime.SpecifyKind(DateTime.UtcNow.AddDays(2), DateTimeKind.Utc);
            DateTimeOffset dateTimeOffset = threeMinLater;
            var time = dateTimeOffset.ToUnixTimeSeconds();
            var jti = apikey + "-" + time;
            var payload = new Dictionary<string, dynamic>();
            payload.Add("iss", code);
            payload.Add("jti", jti);
            payload.Add("api_key", apikey);
            payload.Add("exp", time);
            var base64UrlOfHeader = EncodeBase64Url(Encoding.ASCII.GetBytes(JsonConvert.SerializeObject(headers)));
            var base64UrlOfPayload = EncodeBase64Url(Encoding.ASCII.GetBytes(JsonConvert.SerializeObject(payload)));
            logger.LogInformation(" >> base64UrlOfHeader: " + base64UrlOfHeader);
            logger.LogInformation(" >> base64UrlOfPayload: " + base64UrlOfPayload);
            var headerAndPayloadInBase64 =
                base64UrlOfHeader + "." + base64UrlOfPayload;
            var signature = HashHMAC(Encoding.ASCII.GetBytes(secret),
                Encoding.ASCII.GetBytes(headerAndPayloadInBase64));
            return headerAndPayloadInBase64 + "." + EncodeBase64Url(signature);
        }

        private static string EncodeBase64Url(byte[] input)
        {
            var output = Convert.ToBase64String(input);

            output = output.Split('=')[0]; // Remove any trailing '='s
            output = output.Replace('+', '-'); // 62nd char of encoding
            output = output.Replace('/', '_'); // 63rd char of encoding

            return output;
        }

        private static byte[] HashHMAC(byte[] key, byte[] message)
        {
            var hash = new HMACSHA256(key);
            return hash.ComputeHash(message);
        }
    }
}