﻿using AKC.MobileAPI.DTO.Loyalty.EarningRule;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyEarningRuleService : BaseLoyaltyService, ILoyaltyEarningRuleService
    {
        public LoyaltyEarningRuleService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {

        }

        public async Task<EarningRuleOutput> GetEarningRules(EarningRuleInput input)
        {
            return await PostLoyaltyAsync<EarningRuleOutput>(LoyaltyApiUrl.EARNING_RULE_GETLIST, input);
        }
    }
}
