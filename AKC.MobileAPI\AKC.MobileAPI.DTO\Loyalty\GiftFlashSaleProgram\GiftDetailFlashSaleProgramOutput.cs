﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftFlashSaleProgram
{
    public class GiftDetailFlashSaleProgramOutput
    {
        public GiftDetailFlashSaleProgramResult result { get; set; }
        public object targetUrl { get; set; }
        public bool success { get; set; }
        public object error { get; set; }
        public bool unAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class GiftDetailFlashSaleProgramResult
    {
        public int Id { get; set; }
        public int TenantId { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Introduce { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public DateTime EffectiveTo { get; set; }
        public decimal RequiredCoin { get; set; }
        public string Status { get; set; }
        public decimal TotalQuantity { get; set; }
        public decimal UsedQuantity { get; set; }
        public decimal RemainingQuantity { get; set; }
        public bool IsEGift { get; set; }
        public int? TargetAudienceId { get; set; }
        public bool Is3rdPartyGift { get; set; }
        public bool Selected { get; set; }
        public string ThirdPartyGiftCode { get; set; }
        public string ThirdPartyCategoryCode { get; set; }
        public bool IsAvaiable => !Is3rdPartyGift || (Is3rdPartyGift && Selected);
        public decimal VendorPrice { get; set; }

        public bool IsAutoGeneratedEGiftCode { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public int? ExpiryDuration { get; set; }
        public string FullLink { get; set; }
        public string ExpireDuration { get; set; }

        // Tỉ lệ giảm
        public float? ReductionRate { get; set; }
        // Giá bán gốc
        public decimal? OriginalPrice { get; set; }
        // Giá khuyến mại
        public decimal? SalePrice { get; set; }
        // Danh sách link ảnh
        public string ImageLink { get; set; }
        public decimal MaxAmountRedeemed { get; set; }
        public decimal Amount { get; set; }
        public bool WarningOutOfStock { get; set; }
        public int? UsedQuantityFlashSale { get; set; }
        public int ReductionRateDisplay
        {
            get
            {
                if (ReductionRate.HasValue)
                {
                    if (ReductionRate > 0 && ReductionRate < 1)
                    {
                        return 1;
                    }
                    else if (ReductionRate > 99 && ReductionRate < 100)
                    {
                        return 99;
                    }
                    else
                    {
                        return (int)Math.Round((double)ReductionRate);
                    }
                }
                return 0;
            }
        }
        public int? RemainingQuantityFlashSale { get; set; }
        public string FullGiftCategoryCode { get; set; }
        public string GiftStatus { get; set; }
        public int? TotalWish { get; set; }
        public bool IsSoldOut { get; set; }
    }
}
