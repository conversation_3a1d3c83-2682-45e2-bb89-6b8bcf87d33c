﻿using AKC.MobileAPI.DTO.ThirdParty.VPBank;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.Service.Helpers.ThirdParty.VPBank
{
    public static class ThirdPartyVPBankErrorMessageHelper
    {
        public static Dictionary<string, ThirdPartyVPBankErrorMessageDto> GetListError()
        {
            var list = new Dictionary<string, ThirdPartyVPBankErrorMessageDto>();

            list.Add("0", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0000",
                Message = "Success with no error",
            });
            list.Add("1", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0001",
                Message = "Member Code not exist",
            });
            list.Add("2", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0002",
                Message = "OTP SessionID exist",
            });
            list.Add("3", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0003",
                Message = "Phone invalid",
            });
            list.Add("4", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0004",
                Message = "Call request T24 Fail",
            });
            list.Add("5", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0005",
                Message = "Request OTP Error",
            });
            list.Add("6", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0006",
                Message = "Resend OTP more than 3 times",
            });
            list.Add("7", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0007",
                Message = "OTP not valid",
            });
            list.Add("8", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0008",
                Message = "OTP SessionID not exist",
            });
            list.Add("9", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0009",
                Message = "ValidateOTP Error",
            });
            list.Add("10", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0010",
                Message = "Access Token not exist",
            });
            list.Add("11", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0011",
                Message = "Access Token expired",
            });
            list.Add("12", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0012",
                Message = "Error from Loyalty",
            });
            list.Add("13", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0013",
                Message = "Insufficient balance",
            });
            list.Add("14", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0014",
                Message = "VPIDTransactionID not exist",
            });
            list.Add("15", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0015",
                Message = "Loyalty TransactionID not exist",
            });
            list.Add("16", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0016",
                Message = "Transaction has been revert",
            });
            list.Add("17", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0017",
                Message = "ResendOTP invalid",
            });
            list.Add("18", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0018",
                Message = "Refresh Token expired",
            });
            list.Add("19", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0019",
                Message = "Member Code Invalid",
            });
            list.Add("20", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0020",
                Message = "Refresh Token invalid",
            });
            list.Add("21", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0021",
                Message = "Member Code Invalid",
            });
            list.Add("22", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0022",
                Message = "Exchanged Amount invalid",
            });
            list.Add("23", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0023",
                Message = "VPID TransactionID invalid",
            });
            list.Add("24", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0024",
                Message = "Loyalty TransactionID invalid",
            });
            list.Add("25", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0025",
                Message = "OTP SessionID invalid",
            });

            list.Add("26", new ThirdPartyVPBankErrorMessageDto()
            {
                Code = "VPB0026",
                Message = "Phone not correct with customer info",
            }); 
            return list;
        }

        public static ThirdPartyVPBankErrorMessageDto GetError(string key)
        {
            try
            {
                var allError = GetListError();
                var error = allError[key];
                return error != null ? error : new ThirdPartyVPBankErrorMessageDto()
                {
                    Code = "VPB9999",
                    Message = "System error",
                };
            } catch
            {
                return new ThirdPartyVPBankErrorMessageDto()
                {
                    Code = "VPB9999",
                    Message = "System error",
                };
            }
        }
    }
}
