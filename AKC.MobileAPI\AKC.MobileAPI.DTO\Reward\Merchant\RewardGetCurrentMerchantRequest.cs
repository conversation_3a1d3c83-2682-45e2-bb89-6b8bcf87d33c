﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Merchant
{
    public class RewardGetCurrentMerchantRequest
    {
        public int MerchantId { get; set; }
        public string WalletAddress { get; set; }
    }

    public class RevertExchangeLynkiDToPartnerPointInput
    {
        public  string MemberCode { get; set; }
        public  string TransactionCode { get; set; }
        public  string Message { get; set; }
        public  int MemberId { get; set; }
        public  int MerchantId { get; set; }
    }

    public class RevertExchangeLynkiDToPartnerPointOutput
    {
        public int result { get; set; }
        public string message { get; set; }
    }
    public class ExchangeLynkiDToPartnerPointInput
    {
        public int MemberId { get; set; }
        public string MemberCode { get; set; }
        public string TransactionCode { get; set; }
        public string SourceExchange { get; set; }
        public int MerchantId { get; set; }
        public double TokenAmount { get; set; }
        public string Cif { get; set; } // Optional
        public string ExtraData { get; set; } // Optional
        public string PartnerBindingTxID { get; set; }
    }
    public class ExchangeLynkiDToPartnerPointOutput
    {
        public string message { get; set; }
        public int result { get; set; }
    }

    public class CheckExchangeLynkiDToPartnerPointInput
    {
        public string NationalId { get; set; }
        public int MemberId { get; set; }
        public int MerchantId { get; set; }
        public int TokenAmount { get; set; }
    }

    public class CheckExchangeLynkiDToPartnerPointOutput
    {
        public string message { get; set; }
        public int result { get; set; }
        public CheckExchangeLynkiDToPartnerPointOutputItem item { get; set; }
    }

    public class CheckExchangeLynkiDToPartnerPointOutputItem
    {
        public int ExchangeAmount { get; set; }
        public int TokenAmount { get; set; }
        public string MemberCif { get; set; }
    }

    public class UpdatePartnerBindingTransactionIdInput
    {
        public string TransactionCode { get; set; }
        public string PartnerBindingTxID { get; set; }
    }

    public class UpdatePartnerBindingTransactionIdOutput
    {
        public string message { get; set; }
        public string messageDetail { get; set; }
        public int result { get; set; }
    }
}
