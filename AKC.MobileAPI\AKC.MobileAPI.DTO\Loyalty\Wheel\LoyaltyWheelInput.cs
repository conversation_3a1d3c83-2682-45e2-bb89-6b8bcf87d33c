﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Loyalty.Wheel
{
    public class GetListWheelInput
    {
        [Required]
        public string MemberCode { get; set; }
    }

    public class GetSpinHistoryInput
    {
        [Required]
        public string MemberCode { get; set; }
        [Required]
        public string WheelCode { get; set; }
    }

    public class GetOneWheelInput
    {
        [Required]
        public string MemberCode { get; set; }
        [Required]
        public int? WheelId { get; set; }
    }

    public class CreateWheelInput
    {
        [Required]
        public string MemberCode { get; set; }
        public string WheelName { get; set; }
        public string Logo { get; set; }
        public List<WheelOptions> glstWheelOption { get; set; }
    }

    public class WheelOptions
    {
        public string Label { get; set; }
        public string Status { get; set; }
        public string ReferrenceCode { get; set; }
        public string ReferrenceType { get; set; }
        public string Color { get; set; }
        public string ImageLink { get; set; }
    }

    public class UpdateWheelInput
    {
        [Required]
        public string MemberCode { get; set; }
        public string WheelName { get; set; }
        [Required]
        public int WheelId { get; set; }
        public List<WheelOptions> glstWheelOption { get; set; }
    }

    public class LoyaltyResponeOutput<T>
    {
        public T Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class SpinWheelInput
    {
        public string MemberCode { get; set; }
        public string WheelCode { get; set; }
    }

    public class DeleteWheelInput
    {
        public string MemberCode { get; set; }
        public string WheelCode { get; set; }
        public int? WheelId { get; set; }
    }

    public class DeleteWheelOutput
    {
        
    }

    public class SpinWheelOutput
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public string WheelCode { get; set; }
        public string SelectedOptionCode { get; set; }
        public int SelectedOptionId { get; set; }
        public string SelectedOptionContent { get; set; }
    }

}
