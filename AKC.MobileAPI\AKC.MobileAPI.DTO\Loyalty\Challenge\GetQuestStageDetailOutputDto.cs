﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class GetQuestStageDetailOutputDto
    {
        public GetQuestStageDetailOutPutDto Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class GetQuestStageDetailOutPutDto
    {
        public DateTime? CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string LinkAvatar { get; set; }
        public string Status { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int? ExpiryDuration { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public DateTime? JoinedDate { get; set; }
        public string State { get; set; }
        public bool IsQuestStageClaimed { get; set; }
        public string Process { get; set; }
        public decimal PercentCompleted { get; set; }
        public List<GetLevelAndListQuestInfoDto> Levels { get; set; }
        public int? ItemCount { get; set; }
        public int? QuestStageBaselineId { get; set; }
        public decimal Point { get; set; }
        public decimal Coin { get; set; }
    }

    public class GetLevelAndListQuestInfoDto
    {
        public int Id { get; set; }

        public int? TenantId { get; set; }
        public string Code { get; set; }

        public string QuestStageCode { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Status { get; set; }

        public int? LevelOrder { get; set; }

        public string ListOfChallenge { get; set; }

        public string ImageLink { get; set; }

        public string State { get; set; }

        public bool IsLevelClaimed { get; set; }

        public int QuestStageLevelBaselineId { get; set; }

        public List<GetQuestAndListMissionsDto> Quest { get; set; }

    }
    public class GetQuestAndListMissionsDto
    {
        public string Code { get; set; }

        public string Name { get; set; }
        public virtual string Description { get; set; }

        public virtual string Avatar { get; set; }

        public virtual string LinkAvatar { get; set; }

        public int? Ordinal { get; set; }

        public string Status { get; set; }

        public DateTime FromDate { get; set; }

        public DateTime ToDate { get; set; }

        public int? MissionCount { get; set; }

        public decimal Point { get; set; }
        public decimal Coin { get; set; }

        public int? State { get; set; }
        public decimal PointRewarded { get; set; }
        public decimal CoinRewarded { get; set; }
        public string FrequencyType { get; set; }
        public decimal Actual { get; set; }
        public decimal Target { get; set; }
        public int QuestBaselineId { get; set; }
        public List<MissionDto> Missions { get; set; }
    }
}
