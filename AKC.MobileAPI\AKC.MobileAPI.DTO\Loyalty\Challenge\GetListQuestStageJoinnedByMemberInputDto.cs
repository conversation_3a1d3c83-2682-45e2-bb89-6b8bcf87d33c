﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class GetListQuestStageJoinedByMemberInputDto
    {
        [Required]
        public string MemberCode { get; set; }

        public string StateFilter { get; set; }

        public QuestStatus? Type { get; set; }

        public virtual string Sorting { get; set; }

        public virtual int SkipCount { get; set; }

        public int MaxResultCount { get; set; }
    }

    public enum QuestStageStatus
    {
        Effective,
        Expired
    }
}
