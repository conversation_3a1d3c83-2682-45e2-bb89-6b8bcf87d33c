﻿using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Webstore;
using AKC.MobileAPI.DTO.Reward;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardGiftRedeemTransactionService: RewardBaseService, IRewardGiftRedeemTransactionService
    {
        public RewardGiftRedeemTransactionService(
            IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<RewardCreateGiftRedeemTransactionResponse> CreateRedeem(RewardCreateGiftRedeemTransactionRequest request)
        {
            return await PostRewardAsync<RewardCreateGiftRedeemTransactionResponse>(RewardApiUrl.REDEEM_GIFT_TRANSACTION_CREATE, request, MerchantNameConfig.VPID);
        }

        public async Task<RewardRevertGiftRedeemTransactionResponse> RevertRedeem(RewardRevertGiftRedeemTransactionRequest request)
        {
            return await PostRewardAsync<RewardRevertGiftRedeemTransactionResponse>(RewardApiUrl.REDEEM_GIFT_TRANSACTION_REVERT, request, MerchantNameConfig.VPID);
        }

        public async Task<RedeemUsingMoneyCardOutput> RedeemUsingMoneyCard(RedeemUsingMoneyCardRequest request)
        {
            return await PostRewardAsync<RedeemUsingMoneyCardOutput>(RewardApiUrl.RedeemUsingMoneyCard, request, null);
        }

        public async Task<RevertRedeemUsingMoneyCardOutput> RevertRedeemUsingMoneyCard(RevertRedeemUsingMoneyCardRequest request)
        {
            return await PostRewardAsync<RevertRedeemUsingMoneyCardOutput>(RewardApiUrl.GetCardTransaction_REVERT_MULTICARD_SAMEORDERs, request, null);
        }

        public async Task<RewardNapEvoucherOutputSuccess> CreateTransForTopupVoucher(RewardNapEvoucherInput request)
        {
            return await PostRewardAsync<RewardNapEvoucherOutputSuccess>(RewardApiUrl.MEMBER_TOPUP_EVOUCHER_CREATE_TRANS, request);
        }

        public async Task<RewardRevertGiftRedeemTransactionResponse> RevertRedeen_V2(RewardMemberRevertRedeemInput request, string url = null)
        {
            var calledUrl = RewardApiUrl.REDEEM_GIFT_TRANSACTION_REVERT; // Default là URL redeem cũ đang dùng.
            if (!string.IsNullOrWhiteSpace(url))
            {
                calledUrl = url;
            }
            return await PostRewardAsync<RewardRevertGiftRedeemTransactionResponse>(calledUrl, request, MerchantNameConfig.VPID);
        }
    }
}
