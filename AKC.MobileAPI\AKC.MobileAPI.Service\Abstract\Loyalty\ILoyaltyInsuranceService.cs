﻿using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.Insurance;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyInsuranceService
    {
        Task<GetListProgramsOutput> FindProgram(GetListProgramsInput input);
        Task<GetListProductsOutput> FindProduct(GetListProductsInput input);
        Task<GetListPackagesOutput> FindPackage(GetListPackagesInput input);
        Task<GetListInsuranceContractOutput> FindContract(GetListContractInput input);
        Task<InsuranceSingleContractDto> PurchasePackage(PurchasePackageInput input);
        Task<InsuranceDetailRes> FindProductByCode(InsuranceDetailInput input);
    }
}