using System;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class CreateMemberOfCustomTenantInput
    {
        public string AccountType { get; set; }
        public string Address { get; set; }
        public string Avatar { get; set; }
        public DateTime? Birthday { get; set; }
        public DateTime? JoiningDate { get; set; }
        public DateTime WithdrawnDate { get; set; }
        public string Cards { get; set; }
        public string ChannelType { get; set; }
        public int CityId { get; set; }
        public string Code { get; set; }
        public string ContractCode { get; set; }
        public int DistrictId { get; set; }
        public int WardId { get; set; }
        public int NationId { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string FullChannelTypeCode { get; set; }
        public string FullMemberTypeCode { get; set; }
        public string FullRegionCode { get; set; }
        public string Gender { get; set; }
        public string IdCard { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsIdCardVerified { get; set; }
        public string LastName { get; set; }
        public string MemberTypeCode { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public string Phone { get; set; }
        public string RankType { get; set; }
        public string RankTypeCode { get; set; }
        public string ReferenceId { get; set; }
        public string ReferenceInfo { get; set; }
        public string ReferralCode { get; set; }
        public string RegionCode { get; set; }
        public string RegisterType { get; set; }
        public string StandardMemberCode { get; set; }
        public string Status { get; set; }
        public string StreetDetail { get; set; }
        public int TenantId { get; set; }
        public string Type { get; set; }
    }

    public class CreateMemberOfCustomTenantOutput
    {
        
    }
}