﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using RabbitMQ.Client;

namespace AKC.RabbitMQ
{
    public class RabbitModelPooledObjectPolicy: IPooledObjectPolicy<IModel>
    {
        private readonly RabbitOptions _options;
        private IConnection _connection;
        private readonly ILogger<RabbitModelPooledObjectPolicy> _logger;

        public RabbitModelPooledObjectPolicy(RabbitOptions optionsAccs, ILogger<RabbitModelPooledObjectPolicy> logger)
        {
            _options = optionsAccs;
            _connection = GetConnection();
            _logger = logger;
        }

        private IConnection GetConnection()
        {
            try
            {
                var factory = new ConnectionFactory()
                {
                    HostName = _options.HostName,
                    UserName = _options.UserName,
                    Password = _options.Password,
                    Port = _options.Port,
                    VirtualHost = _options.VHost,
                };

                return factory.CreateConnection();
            }
            catch (System.Exception ex)
            {
                _logger.LogError("Could not create RabbitMQ connection", ex);
                return null;
            }
        }

        public IModel Create()
        {
            try
            {
                // Retry create connection if not existed.
                if (_connection == null)
                {
                    _connection = GetConnection();
                }

                return _connection.CreateModel();
            }
            catch (System.Exception ex)
            {
                _logger.LogError("Could not create RabbitMQ connection Model", ex);
                return null;
            }
        }

        public bool Return(IModel obj)
        {
            if (obj == null)
            {
                return false;
            }

            if (obj.IsOpen)
            {
                return true;
            }
            else
            {
                obj?.Dispose();
                return false;
            }
        }
    }
}
