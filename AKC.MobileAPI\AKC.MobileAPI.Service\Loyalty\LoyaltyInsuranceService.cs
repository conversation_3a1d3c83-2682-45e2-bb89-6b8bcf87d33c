﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.Insurance;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyInsuranceService : BaseLoyaltyService, ILoyaltyInsuranceService
    {
        private readonly IDistributedCache _cache;
        public LoyaltyInsuranceService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
            _cache = cache;
        }

        public async Task<GetListProgramsOutput> FindProgram(GetListProgramsInput input)
        {
            return await PostLoyaltyAsync<GetListProgramsOutput>(LoyaltyApiUrl.INSURANCE_GETPROGRAM, input);
        }

        public async Task<GetListProductsOutput> FindProduct(GetListProductsInput input)
        {
            if (input.MaxItem > 50)
            {
                input.MaxItem = 50;
            }

            if (string.IsNullOrWhiteSpace(input.Vendor) && string.IsNullOrWhiteSpace(input.ProgramCode))
            {
                var cachedData = await _cache.GetStringAsync("INSURANCE_GET_LIST_PRODUCTS_LOYALTY");
                if (!string.IsNullOrWhiteSpace(cachedData))
                {
                    try
                    {
                        var convertedObj = JsonConvert.DeserializeObject<List<GetListProductsOutput>>(cachedData);
                        if (convertedObj != null && convertedObj.Count > 0)
                        {
                            convertedObj[0].TargetUrl = "#from-cache";
                            return convertedObj[0];
                        }
                    }
                    catch (Exception e)
                    {
                        // Cannot deserialize the object in cache, then delete the cache
                        await _cache.RemoveAsync("INSURANCE_GET_LIST_PRODUCTS_LOYALTY");
                    }
                }
            
                var realOne =  await PostLoyaltyAsync<GetListProductsOutput>(LoyaltyApiUrl.INSURANCE_GETPRODUCT, input);
                // Luu vao cache roi return (cached trong 2hrs)
                var cached = new List<GetListProductsOutput>();
                cached.Add(realOne);
                await _cache.SetStringAsync("INSURANCE_GET_LIST_PRODUCTS_LOYALTY", JsonConvert.SerializeObject(cached),
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromSeconds(1800)));
                return realOne;
            }
            return await PostLoyaltyAsync<GetListProductsOutput>(LoyaltyApiUrl.INSURANCE_GETPRODUCT, input);
        }

        public async Task<GetListPackagesOutput> FindPackage(GetListPackagesInput input)
        {
            return await PostLoyaltyAsync<GetListPackagesOutput>(LoyaltyApiUrl.INSURANCE_GETPACKAGE, input);
        }

        public async Task<GetListInsuranceContractOutput> FindContract(GetListContractInput input)
        {
            return await PostLoyaltyAsync<GetListInsuranceContractOutput>(LoyaltyApiUrl.INSURANCE_GETCONTRACT, input);
        }

        public async Task<InsuranceSingleContractDto> PurchasePackage(PurchasePackageInput input)
        {
            return await PostLoyaltyAsync<InsuranceSingleContractDto>(LoyaltyApiUrl.INSURANCE_PURCHASE_PACKAGE, input);
        }

        public async Task<InsuranceDetailRes> FindProductByCode(InsuranceDetailInput input)
        {
            return await PostLoyaltyAsync<InsuranceDetailRes>(LoyaltyApiUrl.INSURANCE_DETAIL, input);
        }
    }
}