﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.Challenge;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/QuestStage")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyChallengeQuestStageController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyChallengeService _challengeService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        public LoyaltyChallengeQuestStageController(
            ILogger<LoyaltyChallengeController> logger,
            ILoyaltyChallengeService challengeService,
            IExceptionReponseService exceptionReponseService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _challengeService = challengeService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
        }

        #region Loyalty challenge quest stage

        [HttpGet]
        [Route("GetAllQuestStage")]
        public async Task<ActionResult<GetAllQuestStageOutputDto>> GetAllQuestStage([FromQuery] GetAllQuestStageInputDto input)
        {
            try
            {
                var result = await _challengeService.GetAllQuestStage(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetAllQuestStage Error- " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }


        [HttpPost]
        [Route("JoinQuestStage")]
        public async Task<ActionResult<JoinQuestStageOutputDto>> JoinQuestStage(JoinQuestStageInputDto input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _challengeService.JoinQuestStage(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "JoinQuestStage Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        //[HttpPost]
        //[Route("ClaimQuestStage")]
        //public async Task<ActionResult<ClaimQuestStageOutputDto>> ClaimQuestStage(ClaimQuestStageInputDto input)
        //{
        //    try
        //    {
        //        var result = await _challengeService.ClaimQuestStage(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "ClaimQuestStage Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        //[HttpGet]
        //[Route("GetListQuestStageByMemberCode")]
        //public async Task<ActionResult<GetListQuestStageByMemberCodeOutputDto>> GetListQuestStageByMemberCode([FromQuery]GetListQuestStageByMemberCodeInputDto input)
        //{
        //    try
        //    {
        //        var result = await _challengeService.GetListQuestStageByMemberCode(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "GetListQuestStageByMemberCode Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        [HttpGet]
        [Route("GetQuestStageDetail")]
        public async Task<ActionResult<GetQuestStageDetailOutputDto>> GetQuestStageDetail([FromQuery] GetQuestStageDetailInputDto input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _challengeService.GetQuestStageDetail(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetQuestStageDetail Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        //[HttpGet]
        //[Route("GetListQuestStageJoinedByMember")]
        //public async Task<ActionResult<GetListQuestStageJoinedByMemberOutPutDto>> GetListQuestStageJoinnedByMember([FromQuery]GetListQuestStageJoinedByMemberInputDto input)
        //{
        //    try
        //    {
        //        var result = await _challengeService.GetListQuestStageJoinedByMember(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "GetListQuestStageJoinedByMember Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}


        [HttpPost]
        [Route("ClaimQuestStageOrLevel")]
        public async Task<ActionResult<QuestStageClaimOutputDto>> ClaimQuestStageOrLevel(QuestStageClaimInputDto input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _challengeService.ClaimQuestStageOrLevel(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "ClaimQuestStageOrLevel Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }


        [HttpGet]
        [Route("GetListQuestAndQuestStageByMemberCode")]
        public async Task<ActionResult<GetListQuestStageByMemberCodeOutputDto>> GetListQuestAndQuestStageByMemberCode([FromQuery]GetListQuestStageByMemberCodeInputDto input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _challengeService.GetListQuestAndQuestStageByMemberCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetListQuestStageByMemberCode Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        #endregion

    }
}
