﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Languages;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyLanguageService: BaseLoyaltyService, ILoyaltyLanguageService
    {
        public LoyaltyLanguageService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<LoyaltyResponse<List<LoyaltyMultiLanguageTextListOutput>>> GetListLanguageTexts(LoyaltyMultiLanguageTextListInput input)
        {
            var query = "?SourceName=RedeemTransaction";
            foreach (var name in input.Names)
            {
                query = query + "&Names=" + name;
            }
            return await GetLoyaltyAsync<LoyaltyResponse<List<LoyaltyMultiLanguageTextListOutput>>> (LoyaltyApiUrl.GET_LANGUAGES_TEXT + query);
        }

        public async Task<object> GetListMessagesByKey(string keyName)
        {
            try
            {
                var key = new List<string>();
                key.Add(CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_REFIX + keyName);
                var languages = await this.GetListLanguageTexts(new LoyaltyMultiLanguageTextListInput()
                {
                    Names = key,
                });
                return GenLanguageByKey(languages.Result);
            } catch
            {
                var key = new List<string>();
                key.Add(CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                var languages = await this.GetListLanguageTexts(new LoyaltyMultiLanguageTextListInput()
                {
                    Names = key,
                });
                return GenLanguageByKey(languages.Result);
            }
        }

        private object GenLanguageByKey(List<LoyaltyMultiLanguageTextListOutput> languages)
        {
            var list = new Dictionary<string, string>();
            foreach (var language in languages)
            {
                foreach (var target in language.TargetValue)
                {
                    list.Add(target.LanguageKey, target.LanguageText);
                }
            }
            return list;
        }
    }
}
