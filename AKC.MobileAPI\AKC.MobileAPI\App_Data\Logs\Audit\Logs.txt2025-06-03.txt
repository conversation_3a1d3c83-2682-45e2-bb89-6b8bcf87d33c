INFO  2025-06-03 14:32:24,974 [45   ]            - Http Request Information: {"ControllerName":"LoyaltyCampaignGift","ActionName":"CheckCampaignGiftScanQr","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/campaign-gift/check-campaign-gift","QueryString":"{}","Body":"{\"type\":\"ScanCampaignQRCode\",\"data\":\"4pONRJN6A7lVQ1uyS71JvEzEltWJkHy282VmMolDOYSUi/CyWtfSPlh1oM5X7MbTIjP03Xee9szym87pw0Fd1efhdAfGYuiaemzsdcNG8Rs=\",\"memberCode\":\"l3DZtsR2s7VNu2Dmbnb0dTSPQR32\"}","TrueClientIp":"","XForwardedFor":""}
ERROR 2025-06-03 14:32:25,182 [45   ] Middleware - An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Unable to resolve service for type 'Microsoft.Extensions.Logging.ILogger' while attempting to activate 'AKC.MobileAPI.Controllers.Loyalty.LoyaltyCampaignGiftController'.
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass4_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass5_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location where exception was thrown ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|19_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at AKC.MobileAPI.Helper.WebstoreCustomAuthenticationMiddleware.InvokeAsync(HttpContext context) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Helper\WebstoreCustomAuthenticationMiddleware.cs:line 36
   at AKC.MobileAPI.Helper.CSTicketAnnonymousRateLimitingMiddleware.InvokeAsync(HttpContext context) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Helper\CSTicketAnnonymousRateLimitingMiddleware.cs:line 64
   at AKC.MobileAPI.AuditLog.RequestLoginMiddleware.Invoke(HttpContext context) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\AuditLog\RequestLoginMiddleware.cs:line 46
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at AspNetCoreRateLimit.RateLimitMiddleware`1.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AKC.MobileAPI.Helper.SwaggerBasicAuthMiddleware.InvokeAsync(HttpContext context) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Helper\SwaggerBasicAuthMiddleware.cs:line 48
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
INFO  2025-06-03 14:36:07,850 [36   ]            - Http Request Information: {"ControllerName":"LoyaltyCampaignGift","ActionName":"CheckCampaignGiftScanQr","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/campaign-gift/check-campaign-gift","QueryString":"{}","Body":"{\"type\":\"ScanCampaignQRCode\",\"data\":\"4pONRJN6A7lVQ1uyS71JvEzEltWJkHy282VmMolDOYSUi/CyWtfSPlh1oM5X7MbTIjP03Xee9szym87pw0Fd1efhdAfGYuiaemzsdcNG8Rs=\",\"memberCode\":\"l3DZtsR2s7VNu2Dmbnb0dTSPQR32\"}","TrueClientIp":"","XForwardedFor":""}
