﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Wheel
{
    public class GetListWheelOutput
    {
        public int totalCount { get; set; }
        public List<WheelsDto> items { get; set; }
    }

    public class GetSpinHistoryOutput
    {
        public int TotalCount { get; set; }
        public List<SpinHistoryDto> items { get; set; }
    }
    public class WheelsDto
    {
        public int? Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public string Status { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public string MemberCode { get; set; }
        public bool TrackSpinHistory { get; set; }
        public int WheelSize { get; set; }
        public string Logo { get; set; }
    }

    public class SpinHistoryDto
    {
        public int WheelId { get; set; }
        public string MemberCode { get; set; }
        public int OptionId { get; set; }
        public string OptionLabel { get; set; }

        public DateTime? CreationTime { get; set; }
    }

    public class GetOneWheelOutput
    {
        public int WheelId { get; set; }
        public string WheelName { get; set; }
        public List<WheelOptionDto> glstWheelOption { get;set;}
        public string GiftGroupCode { get; set; }
    }

    public class WheelOptionDto
    {
        public string Code { get; set; }
        public string Label { get; set; }
        public string Status { get; set; }
        public string ReferrenceCode { get; set; }
        public string ReferrenceType { get; set; }
        public string Color { get; set; }
        public string ImageLink { get; set; }
    }

    public class CreateOrUpdateWheelByMemberCodeOutput
    {
        public bool isSuccess { get; set; }
        public string Message { get; set; }
    }
}
