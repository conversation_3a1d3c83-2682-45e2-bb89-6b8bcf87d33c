namespace AKC.MobileAPI.Service.Constants
{
    public class AirlineIntegrationErrorCodes
    {
        public static string Success = "00";
        public static string AirlineMemberCodeNotFound = "AIR001";
        public static string AirlineMemberCodeAndNameMismatch = "AIR002";
        public static string AirlineMemberHasInvalidStatus = "AIR003";
        public static string PartnerTemporarilyNotAcceptMilesExchange = "AIR004";
        public static string BalanceNotEnough = "AIR005";
        public static string InitConnectionError = "AIR006";
        public static string MilesAndAmountNotValid = "AIR010";
        public static string AirlineNotConfigured = "AIR011";
        public static string InputMilesOutRange = "AIR012";
        public static string AirlineAndNotConfigMerchant = "AIR013";
        public static string MerchantNotExist = "AIR014";
        public static string MerchantMisconfigured = "AIR015";
        public static string MilesAndTotalAmountNotMatch = "AIR016";
        public static string MerchantExchangeConfigLimitPerTime = "AIR017";
        public static string MerchantExchangeConfigMinPerTime = "AIR018";
        public static string MerchantExchangeConfigLimitPerMonth = "AIR019";
        public static string GeneralError = "AIR100";
        public static string Timeout = "AIR101";
        public static string Error202Blockchain = "AIR202";
    }
}