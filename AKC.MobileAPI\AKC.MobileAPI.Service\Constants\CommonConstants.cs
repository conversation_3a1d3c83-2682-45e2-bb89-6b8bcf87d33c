﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.Service.Constants
{
    public class CommonConstants
    {
        public const string ACCESSS_TOKEN_CACHE_KEY = "ACCESSS_TOKEN_CACHE_KEY";
        public const string ALLOW_RENEW_LOYALTY_TOKEN = "ALLOW_RENEW_LOYALTY_TOKEN";
        public const string ACCESS_TOKEN_BY_MERCHANT = "ACCESS_TOKEN_BY_MERCHANT_";
        // To do for dummy
        public const string ACCESSS_TOKEN_DUMMY_LOYALTY_CACHE_KEY = "ACCESSS_TOKEN_DUMMY_LOYALTY_CACHE_KEY";
        public const string ALLOW_RENEW_DUMMY_LOYALTY_TOKEN = "ALLOW_RENEW_DUMMY_LOYALTY_TOKEN";
        public const string GiftRedemptionRejectRouteKey = "gift.redemption.reject";
        public const string LANGUAGE_SOURCE_NAME_REDEEM = "RedeemTransaction";
        public const string LANGUAGE_SOURCE_NAME_REDEEM_REFIX = "RedeemMsg_";
        public const string LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR = "SystemError";
        public const string LANGUAGE_SOURCE_NAME_REDEEM_BALANCE_NOT_ENOUGH = "BalanceNotEnough";
        // To do for vpbankexchange
        public const string ACCESSS_TOKEN_VPBANK_EXCHANGE_CACHE_KEY = "ACCESSS_TOKEN_VPBANK_EXCHANGE_CACHE_KEY";
        public const string ACCESSS_TOKEN_VPBANK_SECURITIES_CACHE_KEY = "ACCESSS_TOKEN_VPBANK_SECURITIES_CACHE_KEY";
        public const string ALLOW_RENEW_VPBANK_EXCHANGE_LOYALTY_TOKEN = "ALLOW_RENEW_VPBANK_EXCHANGE_LOYALTY_TOKEN";
        public const string ALLOW_RENEW_VPBANK_SECURITIES_LOYALTY_TOKEN = "ALLOW_RENEW_VPBANK_SECURITIES_LOYALTY_TOKEN";

        public const string MERCHANT_CONNECTION_STATUS_CONFIRMED = "Confirmed";
        public const string MERCHANT_CONNECTION_STATUS_REMOVED = "Removed";
        public const string ACCESSS_TOKEN_VPBANK_LOYALTY_CACHE_KEY = "ACCESSS_TOKEN_VPBANK_LOYALTY_CACHE_KEY";
        
        public const string COMMONLOYACCESSS_TOKEN_CACHE_KEY = "COMMONLOYACCESSS_TOKEN_CACHE_KEY";
        public const string COMMONLOYALLOW_RENEW_LOYALTY_TOKEN = "COMMONLOYALLOW_RENEW_LOYALTY_TOKEN";
        public const string COMMONLOYACCESS_TOKEN_BY_MERCHANT = "COMMONLOYACCESS_TOKEN_BY_MERCHANT_";

        public const string DOWNSTREAM_ACCESSS_TOKEN_CACHE_KEY = "DOWNSTREAM_ACCESSS_TOKEN_CACHE_KEY";
    }

    public class InsuranceProviders
    {
        public const string OPES = "OPES";
    }

    public class SmartOtpConstant
    {
        public const string OtpTypeBasic = "basic";
        public const string FlowValidateSmartOtp = "ValidateSmartOtp";
        public const int PeriodSyncTimeSmartOtp = 15;
        public const string FlowReedem = "FlowReedemOtp";
    }

    public enum ValidateSmartOtpErrorCode
    {
        InforPartnerIncorrect = 103, // Thông tin Partner không chính xác 
        TokenInValid = 129, // Token không hợp lệ
        InactiveAccount = 190108, // Tài khoản chưa hoạt động
        OtpInValid = 124, // OTP không hợp lệ
        SystemError = 500, // Internal Server Error
        TimeOut = 504 // Time out
    }

    public class GiftCategoryTypeCode
    {
        public const string Normal = "NORMAL";
        public const string Insurance = "INSURANCE";
        public const string TopUp = "TOPUP";
        public const string CashOut = "CASHOUT";
        public const string TopUpPhone = "TOPUPPHONE";
        public const string TopUpData = "TOPUPDATA";
    }
}
