﻿using AKC.MobileAPI.DTO.Loyalty.EGiftInfors;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.Service.Abstract.Loyalty.EGiftInfors;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty.Gift
{
    public class LoyaltyEGiftInforsService: BaseLoyaltyService, ILoyaltyEGiftInforsService
    {
        public LoyaltyEGiftInforsService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<UpdateGiftStatusOutput> UpdateGiftStatus(UpdateGiftStatusInput input)
        {
            return await PutLoyaltyAsync<UpdateGiftStatusOutput>(LoyaltyApiUrl.EGIFT_INFORS_UPDATE_GIFT_STATUS, input);
        }

        //public async Task<UpdateEgiftCodeOutput> UpdateEgiftCode(UpdateEgiftCodeInputDto input)
        //{
        //    return await PutLoyaltyAsync<UpdateEgiftCodeOutput>(LoyaltyApiUrl.UPDATE_EGIFT_CODE, input);
        //}
        
        public async Task<GetVendorInfoFromTransCodeOutput> GetVendorInfoFromTransOfTopup(GetVendorInfoFromTransCodeInput input)
        {
            return await PostLoyaltyAsync<GetVendorInfoFromTransCodeOutput>(LoyaltyApiUrl.GET_LIST_EGIFT_TOPUP, input);
        }
    }
}
