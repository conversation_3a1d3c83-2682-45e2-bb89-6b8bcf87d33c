﻿using System;
using System.Collections.Generic;


namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class GetQuestDetailOutputDto
    {
        public ListResultQuestDetail Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
        
    }

    public class ListResultQuestDetail
    {
        public int TotalCount { get; set; }

        public List<ListItemsQuestDetail> Items { get; set; }
    }

    public class ListItemsQuestDetail
    {
        public DateTime? LastModificationTime { get; set; }
        public string Code { get; set; }

        public string Name { get; set; }
        public virtual string Description { get; set; }

        public virtual string LinkAvatar { get; set; }


        public string Status { get; set; }

        public DateTime FromDate { get; set; }

        public DateTime ToDate { get; set; }

        public string Tag { get; set; }

        public string State { get; set; }
        public string Process { get; set; }
        public List<MissionDto> Missions { get; set; }
        public int? MissionCount { get; set; }
        public DateTime? Date { get; set; }
        public decimal Point { get; set; }
        public decimal Coin { get; set; }
        public decimal Actual { get; set; }
        public decimal Target { get; set; }
        public int? QuestBaselineId { get; set; }
        public int? Id { get; set; }
        public string Venue { get; set; }
        public string Address { get; set; }
        public string LocationId { get; set; }
    }
    
}
