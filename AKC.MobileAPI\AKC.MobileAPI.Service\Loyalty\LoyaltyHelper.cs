﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.GiftStoreExtends;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyHelper
    {
        #region Private Methods
        /// <summary>
        /// Perform a http request to login into Loyalty system .
        /// </summary>
        /// <param name="tenantId">Loyalty TenantId</param>
        /// <param name="baseURL">Loyalty base URL</param>
        /// <param name="userName">admin username loyalty system</param>
        /// <param name="password">admin password</param>
        /// <returns><see cref="LoyaltyLoginResponse"/></returns>
        public static LoyaltyLoginResponse LoginLoyalty(int? tenantId, string baseURL, string userName, string password)
        {
            LoyaltyLoginDTO loyaltyLoginDTO = new LoyaltyLoginDTO()
            {
                UserNameOrEmailAddress = userName,
                Password = password
            };

            var body = JsonConvert.SerializeObject(loyaltyLoginDTO, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            });

            // Setup request.
            var req = new HttpRequestMessage { Method = HttpMethod.Post };
            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            req.RequestUri = new Uri($"{baseURL}/TokenAuth/Authenticate");
            req.Content = new StringContent(body, Encoding.UTF8, "application/json");

            // Get Response.
            HttpClient _client = new HttpClient();
            var response = _client.SendAsync(req).Result;
            var rawData = response.Content.ReadAsStringAsync().Result;

            // Make sure success status code.
            response.EnsureSuccessStatusCode();

            // Get respone result.
            return JsonConvert.DeserializeObject<LoyaltyLoginResponse>(rawData);
        }

        /// <summary>
        /// Perform set a new accesstoken into redis cache.
        /// </summary>
        /// <param name="cache"></param>
        /// <param name="allowRenewByJobCacheKey"></param>
        /// <param name="accessTokenCacheKey"></param>
        /// <param name="GetAccessTokenFunc"></param>
        /// <param name="delay"></param>
        /// <returns>Loyalty accesstoken</returns>
        private static string RenewCacheAndGetToken(IDistributedCache cache,
            string allowRenewByJobCacheKey,
            string accessTokenCacheKey,
            LoyaltyLoginResponse tokenInfo,
            TimeSpan delay)
        {
            // Cache flag allow refresh token (for cluster case).
            var cacheAllowRenewTokenFlagCacheOptions = new DistributedCacheEntryOptions()
                .SetAbsoluteExpiration(TimeSpan.FromSeconds(delay.TotalSeconds - 1));

            // Not allow refresh token until allowRenewByJobFlag expired
            cache.SetString(allowRenewByJobCacheKey, allowRenewByJobCacheKey, cacheAllowRenewTokenFlagCacheOptions);

            // Renew accesstoken and set new value into cache.
            var tokenExpiredSeconds = tokenInfo.Result.ExpireInSeconds > 0
                   ? tokenInfo.Result.ExpireInSeconds
                   : delay.TotalSeconds * 2;

            var cacheAccessTokenOptions = new DistributedCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(tokenExpiredSeconds));

            cache.SetString(accessTokenCacheKey, tokenInfo?.Result?.AccessToken, cacheAccessTokenOptions);

            // Set accesstoken after renew.
            return tokenInfo.Result.AccessToken;
        }


        /// <summary>
        /// Get new access token of loyalty.
        /// </summary>
        /// <returns></returns>
        private static LoyaltyLoginResponse GetNewAccessToken()
        {
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var baseURL = configuration.GetSection("Loyalty:RemoteURL").Value;
            var tenantId = Convert.ToInt32(configuration.GetSection("Loyalty:TenantId").Value);
            var defaultUserName = configuration.GetSection("Loyalty:Username").Value;
            var defaultPassoword = configuration.GetSection("Loyalty:Password").Value;

            return LoginLoyalty(tenantId, baseURL, defaultUserName, defaultPassoword);
        }

        /// <summary>
        /// Get new accesstoken dummy tenant.
        /// </summary>
        /// <returns></returns>
        private static LoyaltyLoginResponse GetNewAccessTokenDummy()
        {
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var baseURL = configuration.GetSection("ThirdPartyMerchant:LoyaltyDummy:BaseURL").Value;
            var tenantId = Convert.ToInt32(configuration.GetSection("ThirdPartyMerchant:LoyaltyDummy:TenantId").Value);
            var defaultUserName = configuration.GetSection("ThirdPartyMerchant:LoyaltyDummy:Username").Value;
            var defaultPassoword = configuration.GetSection("ThirdPartyMerchant:LoyaltyDummy:Password").Value;

            return LoginLoyalty(tenantId, baseURL, defaultUserName, defaultPassoword);
        }

        /// <summary>
        /// Get new accesstoken dummy tenant.
        /// </summary>
        /// <returns></returns>
        private static LoyaltyLoginResponse GetNewAccessTokenVPBankLoyaltyExchange()
        {
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var baseURL = configuration.GetSection("VPBankLoyaltyExchange:BaseURL").Value;
            var tenantId = Convert.ToInt32(configuration.GetSection("VPBankLoyaltyExchange:TenantId").Value);
            var defaultUserName = configuration.GetSection("VPBankLoyaltyExchange:Username").Value;
            var defaultPassoword = configuration.GetSection("VPBankLoyaltyExchange:Password").Value;

            return LoginLoyalty(tenantId, baseURL, defaultUserName, defaultPassoword);
        }

        public static LoyaltyLoginResponse GetNewAccessTokenVPBankSecurities()
        {
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var baseURL = configuration.GetSection("ThirdPartyMerchant:VPBankSecurities:BaseURL").Value;
            var tenantId = Convert.ToInt32(configuration.GetSection("ThirdPartyMerchant:VPBankSecurities:TenantId").Value);
            var defaultUserName = configuration.GetSection("ThirdPartyMerchant:VPBankSecurities:Username").Value;
            var defaultPassoword = configuration.GetSection("ThirdPartyMerchant:VPBankSecurities:Password").Value;

            return LoginLoyalty(tenantId, baseURL, defaultUserName, defaultPassoword);
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// Get a new accesstoken in loyalty system
        /// and set this value into cache.
        /// </summary>
        /// <param name="cache"></param>
        /// <param name="delay"></param>
        /// <param name="isForceRenew">
        /// If true: Get new accesstoken form loyalty server and set this value into redis cache.
        /// If false: Return accesstoken in cache if existed, otherwirse get new accestoken from loyalty system and set this value into cache.
        /// </param>
        /// <returns>Loyalty accesstoken</returns>
        public static string RenewAccessTokenCacheValue(IDistributedCache cache, TimeSpan delay, bool isForceRenew = false)
        {
            return isForceRenew || string.IsNullOrEmpty(cache.GetString(CommonConstants.ALLOW_RENEW_LOYALTY_TOKEN))
                ? RenewCacheAndGetToken(cache,
                    CommonConstants.ALLOW_RENEW_LOYALTY_TOKEN,
                    CommonConstants.ACCESSS_TOKEN_CACHE_KEY,
                    GetNewAccessToken(), delay)
                : cache.GetString(CommonConstants.ACCESSS_TOKEN_CACHE_KEY);
        }

        /// <summary>
        /// Get a new accesstoken dummy tenant in loyalty system
        /// and set this value into cache.
        /// </summary>
        /// <param name="cache"></param>
        /// <param name="delay"></param>
        /// <param name="isForceRenew">
        /// If true: Get new accesstoken form loyalty server and set this value into redis cache.
        /// If false: Return accesstoken in cache if existed, otherwise get new accestoken from loyalty system and set this value into cache.
        /// </param>
        /// <returns>Loyalty accesstoken</returns>
        public static string RenewAccessTokenDummyCacheValue(IDistributedCache cache, TimeSpan delay, bool isForceRenew = false)
        {
            return isForceRenew || string.IsNullOrEmpty(cache.GetString(CommonConstants.ALLOW_RENEW_DUMMY_LOYALTY_TOKEN))
                ? RenewCacheAndGetToken(cache,
                    CommonConstants.ALLOW_RENEW_DUMMY_LOYALTY_TOKEN,
                    CommonConstants.ACCESSS_TOKEN_DUMMY_LOYALTY_CACHE_KEY,
                    GetNewAccessTokenDummy(), delay)
                : cache.GetString(CommonConstants.ACCESSS_TOKEN_DUMMY_LOYALTY_CACHE_KEY);
        }

        // For loyalty gift store vendor
        public static void RenewAccessTokenLoyaltyGiftStoreCacheValue(IDistributedCache cache, TimeSpan delay, ILogger logger, bool isForceRenew = false)
        {
            var data = GetNewAccessTokenLoyaltyGiftStoreList(logger);
            foreach (var item in data)
            {
                RenewAccessTokenGiftStoreCacheValue(item, cache, delay, isForceRenew);
            }
        }

        private static List<LoyaltyGiftStoreVendorToken> GetNewAccessTokenLoyaltyGiftStoreList(ILogger logger)
        {
            var output = new List<LoyaltyGiftStoreVendorToken>();
            var configs = GetListMerchantStoreKeyConfig();
            foreach(var config in configs)
            {
                try
                {
                    var item = GetNewAccessTokenLoyaltyGiftStore(config);
                    output.Add(item);
                } catch (Exception ex) {
                    logger.LogError("Job: RenewAccessToken gift store vendor error for TenantId: " + ex.Message);
                }
            }
            return output;
        }

        public static LoyaltyGiftStoreVendorToken GetNewAccessTokenLoyaltyGiftStore(LoyaltyGiftStoreVendor config)
        {
            var token = LoginLoyalty(config.TenantId, config.RemoteUrl, config.UserName, config.Password);
            return new LoyaltyGiftStoreVendorToken()
            {
                KeyCacheData = "CacheGiftStore_" + config.KeyTenant,
                LoyaltyTokenLoginResponse = token
            };
        }

        public static string RenewAccessTokenGiftStoreCacheValue(LoyaltyGiftStoreVendorToken config, IDistributedCache cache, TimeSpan delay, bool isForceRenew = false)
        {
            return isForceRenew || string.IsNullOrEmpty(cache.GetString(config.KeyCacheData))
                ? RenewCacheAndGetToken(cache, "Allow_" + config.KeyCacheData, config.KeyCacheData, config.LoyaltyTokenLoginResponse, delay)
                : cache.GetString(config.KeyCacheData);
        }
        // End or loyalty gift store vendor

        public static string RenewAccessTokenVPBankExchangeCacheValue(IDistributedCache cache, TimeSpan delay, bool isForceRenew = false)
        {
            return isForceRenew || string.IsNullOrEmpty(cache.GetString(CommonConstants.ALLOW_RENEW_VPBANK_EXCHANGE_LOYALTY_TOKEN))
                ? RenewCacheAndGetToken(cache,
                    CommonConstants.ALLOW_RENEW_VPBANK_EXCHANGE_LOYALTY_TOKEN,
                    CommonConstants.ACCESSS_TOKEN_VPBANK_EXCHANGE_CACHE_KEY,
                    GetNewAccessTokenVPBankLoyaltyExchange(), delay)
                : cache.GetString(CommonConstants.ACCESSS_TOKEN_VPBANK_EXCHANGE_CACHE_KEY);
        }
        public static string RenewAccessTokenVPBankSecuritiesCacheValue(IDistributedCache cache, TimeSpan delay, bool isForceRenew = false)
        {
            return isForceRenew || string.IsNullOrEmpty(cache.GetString(CommonConstants.ACCESSS_TOKEN_VPBANK_SECURITIES_CACHE_KEY))
                ? RenewCacheAndGetToken(cache,
                    CommonConstants.ALLOW_RENEW_VPBANK_SECURITIES_LOYALTY_TOKEN,
                    CommonConstants.ACCESSS_TOKEN_VPBANK_SECURITIES_CACHE_KEY,
                    GetNewAccessTokenVPBankSecurities(), delay)
                : cache.GetString(CommonConstants.ACCESSS_TOKEN_VPBANK_SECURITIES_CACHE_KEY);
        }

        /// <summary>
        /// Generate transaction code.
        /// </summary>
        /// <param name="prefix"></param>
        /// <returns></returns>
        public static string GenTransactionCode(string prefix)
        {
            //var temp = prefix + DateTime.Now.ToString("yyyyMMddHHmmss") + DateTime.Now.Ticks.ToString();
            //MD5 mh = MD5.Create();
            //byte[] inputBytes = Encoding.ASCII.GetBytes(temp);
            //byte[] hash = mh.ComputeHash(inputBytes);
            //StringBuilder sb = new StringBuilder();

            //for (int i = 0; i < hash.Length; i++)
            //{
            //    sb.Append(hash[i].ToString("X2"));
            //}
            var prefixStart = DateTime.Now.ToString("HH");
            var prefixEnd = DateTime.Now.ToString("mmss");
            Guid gui = Guid.NewGuid();
            var textGui = gui.ToString().ToUpper().Replace("-", "");
            return prefixStart + textGui + prefixEnd;
        }

        public static string GenTransactionCodeV2(string prefix)
        {
            //var temp = prefix + DateTime.Now.ToString("yyyyMMddHHmmss") + DateTime.Now.Ticks.ToString();
            //MD5 mh = MD5.Create();
            //byte[] inputBytes = Encoding.ASCII.GetBytes(temp);
            //byte[] hash = mh.ComputeHash(inputBytes);
            //StringBuilder sb = new StringBuilder();

            //for (int i = 0; i < hash.Length; i++)
            //{
            //    sb.Append(hash[i].ToString("X2"));
            //}
            //var prefixStart = DateTime.Now.ToString("HHmmss");
            //var prefixEnd = DateTime.Now.Millisecond.ToString("X2");
            var prefixStart = DateTime.Now.ToString("HH");
            var prefixEnd = DateTime.Now.ToString("mmss");
            Guid gui = Guid.NewGuid();
            var textGui = gui.ToString().ToUpper().Replace("-", "");
            return prefixStart + textGui + prefixEnd;
        }
        public static string GenerateRandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            Random random = new Random();
            char[] result = new char[length];
        
            for (int i = 0; i < length; i++)
            {
                result[i] = chars[random.Next(chars.Length)];
            }
        
            return new string(result);
        }
        public static string GenAirlineTxCode(string prefix)
        {
            var prefixStart = DateTime.Now.ToString("HH");
            var prefixEnd = DateTime.Now.ToString("mmss");
            Guid gui = Guid.NewGuid();
            var textGui = gui.ToString().ToUpper().Replace("-", "").Substring(0, 20); // Cut to 24 characters to match the example length
            return prefix + "_" + prefixStart + textGui + prefixEnd;
        }
        /// <summary>
        /// Generate member code.
        /// </summary>
        /// <param name="prefix"></param>
        /// <returns></returns>
        public static string GenMemberCode(string phoneNumber)
        {
            var memberCode = "MSO" + Regex.Replace(phoneNumber, @"[^0-9]+", "") + GenTimeStamp();
            return memberCode;
        }
        private static string GenTimeStamp()
        {
            return DateTime.Now.ToString("yyyyMMddHHmmss");
        }

        public static List<LoyaltyGiftStoreVendor> GetListMerchantStoreKeyConfig()
        {
            var _configuration = AccessConfigurationService.Instance.GetConfiguration();
            var configs = new List<LoyaltyGiftStoreVendor>();
            var listGiftStoreTenant = _configuration.GetSection("LoyaltyGiftStoreExtends").GetChildren();
            foreach (var item in listGiftStoreTenant)
            {
                var config = new LoyaltyGiftStoreVendor();
                var path = item.Path + ":";
                config.KeyTenant = item.Key;
                config.TenantId = Convert.ToInt32(_configuration.GetSection(path + "TenantId").Value);
                config.DefaultRedeemMerchantId = Convert.ToInt32(_configuration.GetSection(path + "DefaultRedeemMerchantId").Value);
                config.RemoteUrl = _configuration.GetSection(path + "RemoteUrl").Value;
                config.UserName = _configuration.GetSection(path + "Username").Value;
                config.Password = _configuration.GetSection(path + "Password").Value;
                configs.Add(config);
            }
            return configs;
        }

        public static LoyaltyGiftStoreVendor GetConfigByMerchantId(int merchantId)
        {
            try
            {
                var config = GetListMerchantStoreKeyConfig().Find(x => x.DefaultRedeemMerchantId == merchantId);
                if (config == null)
                {
                    var ex = new RewardException();
                    var error = new RewardDataExceptionResponse()
                    {
                        result = new RewardDataExceptionResultItem()
                        {
                            code = "MerchantNotFound",
                            message = "Cannot find merchant store from setting"
                        },
                        status = 500
                    };
                    ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
                    ex.Data.Add("StatusCode", 400);
                    throw ex;
                }
                return config;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }
        #endregion
    }
}
