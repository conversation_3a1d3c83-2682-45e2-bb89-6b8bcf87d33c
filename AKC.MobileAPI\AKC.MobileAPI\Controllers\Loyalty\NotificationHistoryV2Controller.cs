﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory.V2;
using AKC.MobileAPI.DTO.Reward;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.Merchant;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.NotiV2;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/notification-history-v2")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class NotificationHistoryV2Controller : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly INotificationHistoryV2Service _notificationHistoryV2Service;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IRewardMemberService _memberService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IRewardMerchantService _rewardMerchantService;
        private readonly ILoyaltyGiftTransactionsService _giftTransactionsService;
        private readonly IDistributedCache _cache;
        
        public NotificationHistoryV2Controller(
            ILogger<NotificationHistoryV2Controller> logger,
            INotificationHistoryV2Service sv,
            IExceptionReponseService exceptionReponseService,
            IRewardMemberService ms,
            ILoyaltyGiftTransactionsService gf,
            IRewardMerchantService mc,
            IDistributedCache cache,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _notificationHistoryV2Service = sv;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
            _memberService = ms;
            _giftTransactionsService = gf;
            _rewardMerchantService = mc;
            _cache = cache;
        }

        [HttpGet]
        [Route("find-by-member")]
        public async Task<ActionResult<GetByMemberOutput>> FindByMember([FromQuery] GetByMemberInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _notificationHistoryV2Service.GetByMember(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "FindByMember Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("mark-as-read")]
        public async Task<ActionResult<MarkAsReadOutput>> MarkAsRead([FromBody] MarkAsReadInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _notificationHistoryV2Service.MarkAsRead(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "MarkAsRead Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        
        [HttpGet]
        [Route("get-tx-detail")]
        public async Task<ActionResult<TokenTransDetailOutput>> GetTransDetail([FromQuery] TokenTransDetailInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }

                RewardMemberGetTokenTransDetailOutput tokenTransObj = null;
                if ("TOKEN".Equals(input.Type))
                {
                    try
                    {
                        tokenTransObj = await _memberService.GetTokenTransById(new RewardMemberGetTokenTransDetailInput()
                        {
                            TokenTransId = input.TokenTransId, MemberCode = input.MemberCode
                        });
                    }
                    catch (Exception e)
                    {
                        var rewardResponse = await _exceptionReponseService.GetExceptionRewardReponse(e);
                        if (rewardResponse != null && rewardResponse.Code == "SearchDataDoesNotExist")
                        {
                            tokenTransObj = await _memberService.GetExpiredTokenTransById(
                                new RewardMemberGetTokenTransDetailInput()
                                {
                                    TokenTransId = input.TokenTransId, MemberCode = input.MemberCode
                                });
                        }
                    }
                } else if ("MONEYCARD".Equals(input.Type))
                {
                    var checkTx = await _memberService.GetCardTransactionOfMember(new GetCardTransactionOfMemberInput()
                    {
                        MemberCode = input.MemberCode, OrderCode = input.OrderCode, CardTransactionId = input.CardTransactionId
                    });
                    if (checkTx == null || checkTx.TotalCount == 0)
                    {
                        return StatusCode(400, new LoyaltyErrorResponse()
                        {
                            Code = "SearchDataDoesNotExist", Message = "Mã giao dịch không tìm thấy"
                        });
                    }

                    tokenTransObj = new RewardMemberGetTokenTransDetailOutput()
                    {
                        ActionType = checkTx.Items[0].ActionType, OrderCode = checkTx.Items[0].OrderCode,
                        FromWalletAddress = checkTx.Items[0].FromWalletAddress,
                        UserAddress = checkTx.Items[0].CardCode,
                        ToWalletAddress = checkTx.Items[0].ToWalletAddress,
                        PartnerBindingTxId = "",
                        TokenAmount = decimal.Parse(checkTx.Items[0].TokenAmount + ""),
                        Time = checkTx.Items[0].CreatedAt,
                        ExpiryDate = checkTx.Items[0].ExpiryDate,
                        OriginalTokenTransId = checkTx.Items[0].OriginalId
                    };
                }

                if (tokenTransObj == null)
                {
                    // There is no such transaction
                    _logger.LogError(" >> TokenTransId#" + input.TokenTransId + " does not exist");
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Code = "SearchDataDoesNotExist", Message = "Mã giao dịch không tìm thấy"
                    });
                }
                var res = new TokenTransDetailOutput()
                {
                    Message = "", Result = new TokenTransDetailInnerContent()
                    {
                        Amount = tokenTransObj.TokenAmount, MemberCode = input.MemberCode
                        , ActionCode = tokenTransObj.ActionCode, TokenTransId = tokenTransObj.TokenTransID
                        , OrderCode = tokenTransObj.OrderCode, ActionType = tokenTransObj.ActionType
                        , WalletAddress = tokenTransObj.UserAddress, ExpiredTime = tokenTransObj.ExpiryDate
                        , CreationTime = tokenTransObj.Time.Value,
                        FromWalletAddress = tokenTransObj.FromWalletAddress,
                        ToWalletAddress = tokenTransObj.ToWalletAddress,
                        PartnerPointAmount = tokenTransObj.PartnerPointAmount,
                        RelatedTokenTransId = tokenTransObj.OriginalTokenTransId,
                        PartnerBindingTxId = tokenTransObj.PartnerBindingTxId
                    }, IsSuccess = true,
                };

                if ("TOKEN".Equals(input.Type))
                {
                    var resultFromNewV2 = await _notificationHistoryV2Service.TokenTransDetail(input);
                    if (resultFromNewV2.IsSuccess)
                    {
                        res.Result.Title = resultFromNewV2.Result.Title;
                        res.Result.Content = resultFromNewV2.Result.Content;
                        res.Result.ContentPhoto = resultFromNewV2.Result.ContentPhoto;
                        res.Result.DescriptionPhoto = resultFromNewV2.Result.DescriptionPhoto;
                        res.Result.PartnerName = resultFromNewV2.Result.PartnerName;
                        res.Result.PartnerIcon = resultFromNewV2.Result.PartnerIcon;
                        res.Result.UsageAddress = resultFromNewV2.Result.UsageAddress;
                    }
                }

                // Kiểm tra nếu là giao dịch redeem thì sẽ cần gọi xuống loyalty để lấy more info 
                if (new[] { "Redeem", "CashedOut", "CashOutFee" }.Contains(tokenTransObj.ActionType))
                {
                    var giftRedeemSearch = _giftTransactionsService.GetAllWithEGift(new LoyaltyGetAllWithEGiftInput()
                    {
                        OwnerCodeFilter = input.MemberCode,
                        SkipCount = 0, MaxResultCount =  100, GiftTransactionCode = tokenTransObj.OrderCode
                    }).Result;
                    if (giftRedeemSearch != null)
                    {
                        if (giftRedeemSearch.Success)
                        {
                            var description = "";
                            var brandName = "";
                            var giftName = "";
                            var cateName = "";
                            if (giftRedeemSearch.Result != null && giftRedeemSearch.Result.Items.Count > 0)
                            {
                                var giftRT = giftRedeemSearch.Result.Items[0];
                                var giftCode = giftRT.GiftTransaction.GiftCode;
                                description = giftRT.GiftTransaction.Description;
                                var egift = giftRT.EGift;

                                if (!"CashOutFee".Equals(tokenTransObj.ActionType))
                                {
                                    res.Result.GiftId = giftRT.GiftTransaction.GiftId?.ToString() ?? "";
                                    res.Result.GiftName = giftRT.GiftTransaction.GiftName;
                                    res.Result.GiftImage = giftRT.ImageLinks?.Where(x => x.Code == giftCode).FirstOrDefault()?.Link;
                                    res.Result.GiftPaidCoin = giftRedeemSearch.Result.Items.Sum(giftRedeem => giftRedeem.GiftTransaction.Coin) ?? 0;
                                    res.Result.Vendor = giftRT.VendorInfo;
                                    res.Result.BrandImage = giftRT.BrandInfo?.BrandImage;
                                    res.Result.RedeemQuantity = giftRedeemSearch.Result.Items.Sum(giftRedeem => giftRedeem.GiftTransaction.Quantity);
                                    brandName = giftRT.BrandInfo?.BrandName;
                                    cateName = giftRT.GiftTransaction.GiftCategoryName;
                                    res.Result.BrandName = brandName;
                                    if (egift != null)
                                    {
                                        res.Result.EGiftExpiredDate = egift.ExpiredDate;
                                    }
                                
                                    // Title change to "DOI QUA xxxx"
                                    res.Result.Title = "Đổi quà thành công";
                                }
                                if ("CashOutFee".Equals(tokenTransObj.ActionType))
                                {
                                    res.Result.Title = "Phí đổi quà";
                                }
                            }
                            //Kiểm tra xem là imedia/asim ko
                            try
                            {
                                var parsedForImedia =
                                    JsonConvert.DeserializeObject<ImediaDescriptionDtoForParsing>(description);
                                if (parsedForImedia.operation == "1000" || parsedForImedia.operation == "1200")
                                {
                                    var isData = false;
                                    if (cateName != null)
                                    {
                                        if (cateName.ToLower().Contains("data"))
                                        {
                                            isData = true;
                                        }
                                    }
                                    
                                    // res.Result.ServiceName = parsedForImedia.operation == "1200" ? $"Topup {topupDataOrPhone}" : $"Thẻ nạp {topupDataOrPhone}";
                                    res.Result.PackageName = "";
                                    if(!isData && (parsedForImedia.operation == "1200"))
                                    {
                                        res.Result.PackageName = (parsedForImedia.accountType == "0") ? "Trả trước" : "Trả sau";
                                    }
                                    
                                    res.Result.CardValue = res.Result.GiftName;
                                    res.Result.ToPhoneNumber = parsedForImedia.operation == "1200" ? parsedForImedia.ownerphone : "";
                                    if (parsedForImedia.operation == "1200" && !string.IsNullOrEmpty(brandName))
                                    {
                                        res.Result.Title = $"Nạp {(isData ? "data" : "tiền điện thoại")} {brandName}";
                                        res.Result.ServiceName = $"Nạp {(isData ? "data" : "tiền điện thoại")}";
                                    }
                                    if (parsedForImedia.operation == "1000" && !string.IsNullOrEmpty(brandName))
                                    {
                                        res.Result.Title = $"Đổi thẻ {(isData ? "data" : "điện thoại")} {brandName}";
                                        res.Result.ServiceName = $"Đổi thẻ {(isData ? "data" : "điện thoại")}";
                                    }
                                }
                            }
                            catch (Exception e)
                            {
                                _logger.LogError(" >> ERROR PARSING DESCRIPTION >> " + description);
                            }
                        }
                    }
                    else
                    {
                        var createGiftRedeemObj =
                            await _giftTransactionsService.GetSingleCreateGiftRedeemTransaction(
                                new GetSingleCreateGiftRedeemTransactionInput()
                                {
                                    MemberCode = input.MemberCode, TransactionCode = tokenTransObj.OrderCode
                                });
                        if (createGiftRedeemObj != null)
                        {
                            var description = "";
                            var brandName = "";
                            var cateName = "";
                            description = createGiftRedeemObj.Description;

                            if (!"CashOutFee".Equals(tokenTransObj.ActionType))
                            {
                                res.Result.GiftId = createGiftRedeemObj.GiftId.ToString();
                                res.Result.GiftName = createGiftRedeemObj.GiftName;
                                res.Result.GiftPaidCoin = (float)createGiftRedeemObj.Amount;
                                res.Result.BrandImage = createGiftRedeemObj.BrandImage;
                                res.Result.RedeemQuantity = 0;
                                brandName = createGiftRedeemObj.BrandName;
                                cateName = createGiftRedeemObj.GiftCategoryName;
                                _logger.LogInformation(" >> Category Name 2 " + cateName);
                                res.Result.BrandName = brandName;
                                res.Result.Title = "Đổi quà thành công";
                            }
                            if ("CashOutFee".Equals(tokenTransObj.ActionType))
                            {
                                res.Result.Title = "Phí đổi quà";
                            }
                            //Kiểm tra xem là imedia/asim ko
                            try
                            {
                                var parsedForImedia =
                                    JsonConvert.DeserializeObject<ImediaDescriptionDtoForParsing>(description);
                                if (parsedForImedia.operation == "1000" || parsedForImedia.operation == "1200")
                                {
                                    var isData = false;
                                    if (cateName != null)
                                    {
                                        if (cateName.ToLower().Contains("data"))
                                        {
                                            isData = true;
                                        }
                                    }
                                    
                                    // res.Result.ServiceName = parsedForImedia.operation == "1200" ? $"Topup {topupDataOrPhone}" : $"Thẻ nạp {topupDataOrPhone}";
                                    res.Result.PackageName = "";
                                    if(!isData && (parsedForImedia.operation == "1200"))
                                    {
                                        res.Result.PackageName = (parsedForImedia.accountType == "0") ? "Trả trước" : "Trả sau";
                                    }
                                    res.Result.CardValue = res.Result.GiftName;
                                    res.Result.ToPhoneNumber = parsedForImedia.operation == "1200" ? parsedForImedia.ownerphone : "";
                                    if (parsedForImedia.operation == "1200" && !string.IsNullOrEmpty(brandName))
                                    {
                                        res.Result.Title = $"Nạp {(isData ? "data" : "tiền điện thoại")} {brandName}";
                                        res.Result.ServiceName = $"Nạp {(isData ? "data" : "tiền điện thoại")}";
                                    }
                                    if (parsedForImedia.operation == "1000" && !string.IsNullOrEmpty(brandName))
                                    {
                                        res.Result.Title = $"Đổi thẻ {(isData ? "data" : "điện thoại")} {brandName}";
                                        res.Result.ServiceName = $"Đổi thẻ {(isData ? "data" : "điện thoại")}";
                                    }
                                }
                            }
                            catch (Exception e)
                            {
                                _logger.LogError(" >> ERROR PARSING DESCRIPTION 2 >> " + description);
                            }
                        }
                    }
                }

                if ("Birthday".Equals(tokenTransObj.Reason) && "Birthday".Equals(tokenTransObj.ActionCode))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                    res.Result.Title = res.Result.PartnerName + " chúc mừng sinh nhật bạn";
                    res.Result.ContentPhoto =
                        "https://linkidstorage.s3-ap-southeast-1.amazonaws.com/upload-gift/d31ba3605a42b18fb34f0d9fad2a0382.png";
                }

                if ("PayByToken".Equals(tokenTransObj.ActionType))
                {
                    var merchantCached = await SetPartnerByMerchantWallet(res, tokenTransObj.ToWalletAddress);
                    res.Result.Title = "Tiêu điểm thành công tại " + res.Result.PartnerName;
                    // Nếu có ActionCode = 'RedeemExchange' thì xử lý thêm
                    if ("RedeemExchange".Equals(tokenTransObj.ActionCode))
                    {
                        var pointOrMile = "điểm";
                        if (merchantCached.PartnerPointExchangeType == "MILE")
                        {
                            pointOrMile = "dặm bay";
                            // Also parse metadata in ActionCodeDetail
                            try
                            {
                                var parsed =
                                    JsonConvert.DeserializeObject<Dictionary<string, object>>(
                                        tokenTransObj.ActionCodeDetail);
                                if (parsed != null)
                                {
                                    var firstName = parsed["FirstName"]?.ToString() ?? "";
                                    var lastName = parsed["LastName"]?.ToString() ?? "";
                                    var airlineMemberCode = parsed["AirlineMemberCode"]?.ToString() ?? "";
                                    res.Result.PartnerMemberFirstName = firstName;
                                    res.Result.PartnerMemberLastName = lastName;
                                    res.Result.AirlineMemberCode = airlineMemberCode;
                                    res.Result.PromotionCode = parsed["PromotionCode"]?.ToString() ?? "";
                                    res.Result.PromotionDate = parsed["PromotionDate"]?.ToString() ?? "";
                                    res.Result.PromotionValue = parsed["PromotionCode"]?.ToString() ?? "";
                                }
                            }
                            catch (Exception e)
                            {
                                _logger.LogError(" >> tokenTransObj#" + tokenTransObj.TokenTransID + " does not have valid ActionCodeDetail to parse.!"  + tokenTransObj.ActionCodeDetail);
                            }
                        }
                        res.Result.Title = $"Đổi {pointOrMile} với đối tác {res.Result.PartnerName}";
                    }
                }
                if ("Exchange".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                    res.Result.Title = $"Đổi điểm với đối tác {res.Result.PartnerName}";
                }
                if ("RevertExchange".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.ToWalletAddress);
                    res.Result.Title = $"Thu hồi điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                    var listTxOfSameOrderCode = await GetListRelatedByOrderCode(input.MemberCode, tokenTransObj.OrderCode);
                    if (listTxOfSameOrderCode.TotalCount > 0)
                    {
                        var origin = listTxOfSameOrderCode.Items.FirstOrDefault(x => x.ActionType == "Exchange");
                        if (origin != null)
                        {
                            res.Result.RelatedTokenTransId = origin.TokenTransID;
                        }
                    }
                }
                if ("CreditBalance".Equals(tokenTransObj.ActionType))
                {
                    res.Result.Title = "Thu hồi điểm do nợ";
                }
                if ("Expired".Equals(tokenTransObj.ActionType))
                {
                    res.Result.Title = "Thu hồi điểm do hết hạn sử dụng";
                }
                if ("RevertOrder".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.ToWalletAddress);
                    res.Result.Title = $"Thu hồi điểm do điều chỉnh giao dịch với {res.Result.PartnerName}"; 
                    // Đã có OriginalTokenTransId ở bản ghi đó 
                }
                if ("RevertRedeem".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                    res.Result.Title = $"Hoàn điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                }
                if ("RevertCashOut".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                    var listTxOfSameOrderCode = await GetListRelatedByOrderCode(input.MemberCode, tokenTransObj.OrderCode);
                    if (listTxOfSameOrderCode.TotalCount > 0)
                    {
                        var origin = listTxOfSameOrderCode.Items.FirstOrDefault(x => x.ActionType == "CashedOut");
                        if (origin != null)
                        {
                            res.Result.RelatedTokenTransId = origin.TokenTransID;
                        }
                    }
                    res.Result.Title = $"Hoàn điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                }
                if ("RevertCashOutFee".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                    var listTxOfSameOrderCode = await GetListRelatedByOrderCode(input.MemberCode, tokenTransObj.OrderCode);
                    if (listTxOfSameOrderCode.TotalCount > 0)
                    {
                        var origin = listTxOfSameOrderCode.Items.FirstOrDefault(x => x.ActionType == "CashOutFee");
                        if (origin != null)
                        {
                            res.Result.RelatedTokenTransId = origin.TokenTransID;
                        }
                    }
                    res.Result.Title = $"Hoàn điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                }
                if ("Revert".Equals(tokenTransObj.ActionType) || "AdjustPlus".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                    res.Result.Title = $"Hoàn điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                    // Nếu là revert và ko có originaltokentransid thì có thể tìm kiếm dữ liệu của nó như này:
                    if (string.IsNullOrEmpty(tokenTransObj.OriginalTokenTransId))
                    {
                        var listTxOfSameOrderCode =
                            await GetListRelatedByOrderCode(input.MemberCode, tokenTransObj.OrderCode);
                        if (listTxOfSameOrderCode.TotalCount > 0)
                        {
                            var origin = listTxOfSameOrderCode.Items.FirstOrDefault(x => x.ActionType == "Redeem");
                            if (origin != null)
                            {
                                res.Result.RelatedTokenTransId = origin.TokenTransID;
                            }
                        }
                    }
                }

                if ("Adjust".Equals(tokenTransObj.ActionType))
                {
                    if (tokenTransObj.UserAddress == tokenTransObj.FromWalletAddress)
                    {
                        // Tru tien
                        await SetPartnerByMerchantWallet(res, tokenTransObj.ToWalletAddress);
                        res.Result.Title = $"Thu hồi điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                    }
                    if (tokenTransObj.UserAddress == tokenTransObj.ToWalletAddress)
                    {
                        // Tru tien
                        await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                        res.Result.Title = $"Hoàn điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                    }
                }
                if ("AdjustMinus".Equals(tokenTransObj.ActionType))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.ToWalletAddress);
                    res.Result.Title = $"Thu hồi điểm do điều chỉnh giao dịch với {res.Result.PartnerName}";
                }
                if ("BatchManualGrant".Equals(tokenTransObj.ActionType) || "SingleManualGrant".Equals(tokenTransObj.ActionType))
                {
                    res.Result.Title = "Tích điểm từ chương trình " + tokenTransObj.Reason;
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                }
                if ("Order".Equals(tokenTransObj.ActionType) && "Invoice".Equals(tokenTransObj.ActionCode))
                {
                    await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                }
                if ("ReturnFull".Equals(tokenTransObj.ActionType))
                {
                    res.Result.Title = "Thu hồi điểm do điều chỉnh giao dịch";
                }

                if ("Topup".Equals(tokenTransObj.ActionType))
                {
                    var giftRedeemSearch = _giftTransactionsService.GetAllWithEGift(new LoyaltyGetAllWithEGiftInput()
                    {
                        OwnerCodeFilter = input.MemberCode,
                        SkipCount = 0, MaxResultCount =  100, GiftTransactionCode = tokenTransObj.OrderCode
                    }).Result;
                    if (giftRedeemSearch != null)
                    {
                        try
                        {
                            var batchNote = giftRedeemSearch.Result?.Items[0]?.GiftTransaction?.BatchNote ?? "";
                            await SetPartnerByMerchantWallet(res, tokenTransObj.FromWalletAddress);
                            if (string.IsNullOrEmpty(batchNote))
                            {
                                batchNote = res.Result.PartnerName;
                            }
                            res.Result.Title = "Nhận điểm từ " + batchNote;

                        }
                        catch (Exception e)
                        {
                        }
                    }
                }
                // Finally, hide PartnerName if ActionType is Adjust, AdjustMinus, AdjustPlus, SingleManualGrant, BatchManualGrant, RevertCashout, RevertCashoutFee, RevertRedeem
                if (new[] { "Adjust", "AdjustMinus", "AdjustPlus", "Revert", "ReturnFull",
                        "SingleManualGrant", "BatchManualGrant", "RevertCashOut", "RevertCashOutFee", "RevertRedeem" }.Contains(tokenTransObj.ActionType))
                {
                    res.Result.PartnerName = "";
                }
                // nếu giao dịch là cộng tiền do giới thiệu bạn bè thì cần gọi lên loyalty check để lấy thêm thông tin
                if (new[] { "Referrer"}.Contains(tokenTransObj.ActionCode))
                {
                    var transactionInforReferral = _giftTransactionsService.GetTransactionInforReferral(new GetTransactionInforReferralInput()
                    {
                        TransactionCode = res.Result.OrderCode
                    }).Result;
                    res.Result.InvitedMember = transactionInforReferral.Result.InvitedMember;
                    res.Result.CampaignName = transactionInforReferral.Result.CampaignName;
                }

                if ("Deactivate" == tokenTransObj.ActionType)
                {
                    res.Result.Title = "Thu hồi thẻ điểm";
                }
                _logger.LogInformation(" Final Output: " + JsonConvert.SerializeObject(res));
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetTransDetail Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        private async Task<RewardMemberTokenTransGetByIdOutput> GetListRelatedByOrderCode(string memberCode, string orderCode)
        {
            // use cache 
            var key = "GetListRelatedByOrderCode_" + memberCode + "_" + orderCode;
            var cachedString = await _cache.GetStringAsync(key);
            try
            {
                var cachedObj = JsonConvert.DeserializeObject<RewardMemberTokenTransGetByIdOutput>(cachedString);
                return cachedObj;
            }
            catch (Exception e)
            {
                await _cache.RemoveAsync(key);
            }

            var listTxOfSameOrderCode = await _memberService.GetTokenTransByMemberId(new RewardMemberTokenTransGetByIdInput()
            {
                NationalId = memberCode, SkipCount = 0, MaxResultCount = 10, OrderCodeFilter = orderCode
            });
            await _cache.SetStringAsync(key, JsonConvert.SerializeObject(listTxOfSameOrderCode),
                                new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(59)));
            return listTxOfSameOrderCode;
        }
        private async Task<ShortMerchantDto> SetPartnerByMerchantWallet(TokenTransDetailOutput res, string merchantWallet)
        {
            // Get ví của merchant nhận, query trong cache để lấy name x icon
            var merchantCached = await GetMerchantInfoByWallet(merchantWallet);
            if (merchantCached != null)
            {
                res.Result.PartnerIcon = merchantCached.MerchantIcon;
                res.Result.PartnerName = merchantCached.MerchantName;
                res.Result.PartnerPointExchangeType = merchantCached.PartnerPointExchangeType;
            }

            return merchantCached;
        }

        private async Task<ShortMerchantDto> GetMerchantInfoByWallet(string walletAddress)
        {
            var cacheKey = "GetMerchantInfoByWallet_" + walletAddress;
            var cachedString = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(cachedString))
            {
                try
                {
                    var ret = JsonConvert.DeserializeObject<ShortMerchantDto>(cachedString);
                    return ret;
                }
                catch (Exception e)
                {
                    await _cache.RemoveAsync(cacheKey);
                }
            }
            // Get from operator
            try
            {
                var merchantShortInfo = await _rewardMerchantService.GetFullInfoMerchantById(new GetFullInfoMerchantByIdInput() { WalletAddress = walletAddress, MerchantId = null});
                if (merchantShortInfo == null)
                {
                    throw new Exception();
                }

                var ret = new ShortMerchantDto()
                {
                    MerchantName = merchantShortInfo.MerchantName, MerchantId = merchantShortInfo.Id, WalletAddress = walletAddress,
                    MerchantIcon = merchantShortInfo.Logo, PartnerPointExchangeType = merchantShortInfo.PartnerPointExchangeType
                };
                await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(ret),
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(30)));

                return ret;
            }
            catch (Exception e)
            { 
                _logger.LogError(" THERE IS NO MERCHANT WITH WALLET " + walletAddress);
            }
            // IT IS NULL INTENTINALLY
            return null;
        }

        private async Task<RewardGetAllMerchantOutput> GetListMerChantFromCache()
        {
            var CacheKey = "GetListMerChantFromCache_ALL";
            var cachedData = await _cache.GetStringAsync(CacheKey);
            if (!string.IsNullOrEmpty(cachedData))
            {
                try
                {
                    var convertedObj = JsonConvert.DeserializeObject<RewardGetAllMerchantOutput>(cachedData);
                    return convertedObj;
                }
                catch (Exception e)
                {
                    await _cache.RemoveAsync(CacheKey);
                }
            }

            var output = await _rewardMerchantService.GetAll(new RewardGetAllMerchantInput(){StatusFilter = "A"});
            await _cache.SetStringAsync(CacheKey, JsonConvert.SerializeObject(output),
                new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(10)));
            return output;
        }
    }
}