using System;

namespace AKC.MobileAPI.Service.Exceptions
{
    public class LoyaltyTimeoutException : Exception
    {
        public string ApiUrl { get; set; }
        
        public LoyaltyTimeoutException() : base()
        {
        }

        public LoyaltyTimeoutException(string message) : base(message)
        {
        }

        public LoyaltyTimeoutException(string message, Exception innerException) : base(message, innerException)
        {
        }

        public LoyaltyTimeoutException(string message, string apiUrl) : base(message)
        {
            ApiUrl = apiUrl;
        }

        public LoyaltyTimeoutException(string message, string apiUrl, Exception innerException) : base(message, innerException)
        {
            ApiUrl = apiUrl;
        }
    }
}
