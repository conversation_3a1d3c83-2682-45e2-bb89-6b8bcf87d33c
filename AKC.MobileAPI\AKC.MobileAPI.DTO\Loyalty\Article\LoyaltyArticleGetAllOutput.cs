﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Article
{
    public class LoyaltyArticleGetAllOutput
    {
        public LoyaltyArticleGetAll Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }
    public class LoyaltyArticleGetAll
    {
        public int TotalCount { get; set; }
        public List<GetArticleForView> Items { get; set; }
    }

    public class GetArticleForView
    {
        public ArticleDto Article { get; set; }
        public DateTime? LastModificationTimeDetail { get; set; }
    }
    
    public class ArticleDto
    { 
        public long Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Content { get; set; }
        public string Avatar { get; set; }
        public string LinkAvatar { get; set; }
        public int? RequiredReadingTime { get; set; }
        public int? RewardedCoin { get; set; }
        public string ActionCode { get; set; }
        public string Status { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Tags { get; set; }
        public string LastUpdateByName { get; set; }
        public long? LastModifierUserId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string Link { get; set; }
    }

    public class GetGiftCategoryAndInfoForView
    {
        public GiftCategoryAndInfoDto GiftCategory { get; set; }
    }
    public class GiftCategoryAndInfoDto
    {
        public string Code { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Status { get; set; }

        public int Level { get; set; }

        public string ParentCode { get; set; }


        public int? ParentId { get; set; }

        public ImageLinkDto ImageLink
        {
            get; set;
        }
        public GetGiftInforByGiftCategoryForView GiftInfor
        {
            get; set;
        }
    }
    public class ImageLinkDto
    {
        public string Code { get; set; }

        public string Type { get; set; }

        public string Link { get; set; }

        public bool isActive { get; set; }

        public int? Ordinal { get; set; }

        public string FullLink { get; set; }

    }

    public class GetGiftInforByGiftCategoryForView
    {
        public GiftInforDto GiftInfor { get; set; }

        public List<ImageLinkDto> ImageLinkGiftInfor { get; set; }

    }
    public class GiftInforDto
    {
        public string Code { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Introduce { get; set; }

        public string FullGiftCategoryCode { get; set; }

        public string Producer { get; set; }

        public string Vendor { get; set; }

        public DateTime EffectiveFrom { get; set; }

        public DateTime EffectiveTo { get; set; }

        public decimal RequiredCoin { get; set; }

        public string Status { get; set; }

        public decimal TotalQuantity { get; set; }

        public decimal UsedQuantity { get; set; }

        public decimal RemainingQuantity { get; set; }

        public bool IsEGift { get; set; }

        public SettingParamDto TargetAudience { get; set; }
        public int? TargetAudienceId { get; set; }

        public DateTime? LastModificationTime { get; set; }
        public DateTime CreationTime { get; set; }
        public string CreatedByUser { get; set; }
        public string UpdatedByUser { get; set; }

        public string Tag { get; set; }
    }

    public class SettingParamDto
    {
        public long Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public TargetAudienceType? Type { get; set; }
        public int Count { get; set; }
        public bool IsFilterProfile { get; set; }
        public ICollection<SettingParamFieldDto> TargetAudienceDetail { get; set; }

        public SettingParamDto(SettingParamDto s)
        {
            Code = s.Code;
            Name = s.Name;
            IsActive = s.IsActive;
            Type = s.Type;
            Count = s.Count;
            IsFilterProfile = s.IsFilterProfile;
            TargetAudienceDetail = s.TargetAudienceDetail;
            Id = s.Id;
        }
        public SettingParamDto() { }
    }
    public enum TargetAudienceType
    {
        Basic = 1,
        Segment = 2,
        MemberList = 3
    }

    public class SettingParamFieldDto
    {
        public string Code { get; set; }
        public int? TargetAudienceId { get; set; }
        public string Type { get; set; }
        public string FullValue { get; set; }
        public string Value { get; set; }
        public string FullValueName { get; set; }
        public string Level { get; set; }
        public int SegmentId { get; set; }
    }
}
