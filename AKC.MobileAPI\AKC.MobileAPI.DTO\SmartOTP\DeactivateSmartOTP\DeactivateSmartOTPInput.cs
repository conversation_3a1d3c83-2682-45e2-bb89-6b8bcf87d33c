﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.SmartOTP.DeactivateSmartOTP
{
    public class DeactivateSmartOTPRequest
    {
        public string MemberCode { get; set; }
        public string Token { get; set; }
        public DateTime ChangeTime { get; set; } = DateTime.UtcNow;
    }

    public class RetireSmartOTPRequest
    {
        public string partner_id { get; set; }
        public string partner_key { get; set; }
        public string token { get; set; }
    }
}
