﻿using AKC.MobileAPI.DTO.Loyalty.AuditLog;
using AKC.MobileAPI.DTO.Loyalty.Payment;
using AKC.MobileAPI.DTO.Reward.Claim_Deposit;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.Payment;
using AKC.MobileAPI.DTO.Reward.PaymentFail;
using AKC.MobileAPI.DTO.Reward.TopUpTransaction;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Reward;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyPaymentService : RewardBaseService, ILoyaltyPaymentService
    {
        private readonly ILogger _logger;
        private readonly IRewardTopUpTransactionService _rewardTopUpTransactionService;
        private readonly IRewardPaymentFailService _rewardPaymentFailService;
        private readonly IDistributedCache _cache;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly ILoyaltyAuditLogService _loyaltyAuditLogService;

        public LoyaltyPaymentService(
            IConfiguration configuration,
            ILogger<LoyaltyPaymentService> logger,
            IRewardTopUpTransactionService rewardTopUpTransactionService,
            IRewardPaymentFailService rewardPaymentFailService,
            IDistributedCache cache,
            IExceptionReponseService exceptionReponseService,
            IRewardMemberService rewardMemberService,
            ILoyaltyAuditLogService loyaltyAuditLogService
            ) : base(configuration)
        {
            _logger = logger;
            _rewardTopUpTransactionService = rewardTopUpTransactionService;
            _rewardPaymentFailService = rewardPaymentFailService;
            _cache = cache;
            _exceptionReponseService = exceptionReponseService;
            _rewardMemberService = rewardMemberService;
            _loyaltyAuditLogService = loyaltyAuditLogService;
        }
        public Dictionary<string, string> GetDataFromURLCallBack(IQueryCollection input)
        {
            var listValue = new Dictionary<string, string>();
            foreach (var t in input.Keys)
            {
                var key = t;
                var value = input[t].ToString();

                listValue.Add(key, value);
            }
            return listValue;
        }

        public async Task<GetDataFromURLReturnUrlOutputDto> GetDataFromReturnUrl(string url, IQueryCollection input)
        {
            var listParams = GetDataFromURLCallBack(input);
            if (listParams.ContainsKey("backQueryLink"))
            {
                var internalTransactionCode = listParams.GetValueOrDefault("vpc_MerchTxnRef");
                if (string.IsNullOrEmpty(internalTransactionCode))
                {
                    return new GetDataFromURLReturnUrlOutputDto()
                    {
                        ErrorCode = "Payment_Status_101", // Không tìm thấy giao dịch
                        IsSuccessPayment = false,
                        Item = null,
                        Result = 400,
                        IsBackQuery = true,
                    };
                }
                var topupCacheBack = "TopupBack" + internalTransactionCode;
                _logger.LogInformation("topup transaction cache data back: " + JsonConvert.SerializeObject(topupCacheBack));
                var cacheDataTopupBack = await _cache.GetStringAsync(topupCacheBack);
                _logger.LogInformation("topup transaction cache data back: " + JsonConvert.SerializeObject(cacheDataTopupBack));
                if (string.IsNullOrEmpty(cacheDataTopupBack))
                {
                    return new GetDataFromURLReturnUrlOutputDto()
                    {
                        ErrorCode = "Payment_Status_101", // Không tìm thấy giao dịch
                        IsSuccessPayment = false,
                        Item = null,
                        Result = 400,
                        IsBackQuery = true,
                    };
                }
                try
                {
                    var output = JsonConvert.DeserializeObject<GetDataFromURLReturnUrlOutputDto>(cacheDataTopupBack);
                    output.IsBackQuery = true;
                    return output;
                } catch (Exception ex)
                {
                    _logger.LogInformation("topup transaction cache data back exception: " + JsonConvert.SerializeObject(ex));
                    return new GetDataFromURLReturnUrlOutputDto()
                    {
                        ErrorCode = "SystemError",
                        IsSuccessPayment = false,
                        Item = null,
                        Result = 400,
                    };
                }
            }
            var topupCache = string.Empty;
            var cacheOption = new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromSeconds(600));
            // Add logs
            var ipPayment = "";
            try
            {
                Uri myUri = new Uri(url);
                var ipHost = Dns.GetHostAddresses(myUri.Host);
                if (ipHost != null && ipHost.Length > 0)
                {
                    ipPayment = ipHost[0].MapToIPv4().ToString();
                }
            }
            catch { }
            try
            {
                var checkReturn = await CheckReturnPaymentLink(new RewardCheckReturnPaymentLinkInput()
                {
                    ReturnUrl = url,
                });
                _logger.LogInformation("topup transaction url: " + JsonConvert.SerializeObject(url));
                _logger.LogInformation("topup transaction check return url result: " + JsonConvert.SerializeObject(checkReturn));
                var topupCacheBack = "TopupBack" + checkReturn.Items.InternalTransactionCode;
                topupCache = "Topup" + checkReturn.Items.InternalTransactionCode;
                _logger.LogInformation("topup transaction cache response: " + JsonConvert.SerializeObject(topupCache));
                var cacheDataTopup = await _cache.GetStringAsync(topupCache);
                _logger.LogInformation("topup transaction cache data: " + JsonConvert.SerializeObject(cacheDataTopup));
                if (string.IsNullOrEmpty(cacheDataTopup))
                {
                    OperationLog("", null, url, checkReturn.Items.TransactionCode, ipPayment, checkReturn.Items.PaymentStatusCode, "PaymentGatewayResponse");
                    return new GetDataFromURLReturnUrlOutputDto()
                    {
                        ErrorCode = "Payment_Status_101", // Không tìm thấy giao dịch
                        IsSuccessPayment = false,
                        Item = null,
                        Result = 400,
                    };
                }
                if (checkReturn.Items.ReturnValid && checkReturn.Items.PaymentSuccess)
                {
                    var dataTopup = JsonConvert.DeserializeObject<GeneratePaymentLinkCache>(cacheDataTopup);
                    var paymentTransactionId = checkReturn.Items.TransactionCode;
                    var requestTopup = new HandlerCreateTopUpTransactionPayment()
                    {
                        CacheDataTopup = cacheDataTopup,
                        CheckReturn = checkReturn,
                        IpPayment = ipPayment,
                        PaymentTransactionStatus = "Success",
                        TopupCache = topupCache,
                        Url = url,
                    };
                    var resultTopup = await HandlerTopupTransaction(requestTopup, checkReturn.Items.PaymentCode);
                    if (resultTopup != null && resultTopup.IsSuccess)
                    {
                        var output = new GetDataFromURLReturnUrlOutputDto()
                        {
                            ErrorCode = null,
                            IsSuccessPayment = true,
                            Item = new GetDataFromURLReturnUrlItem()
                            {
                                MemberCode = dataTopup.MemberCode,
                                MoneyAmount = dataTopup.MoneyAmount,
                                PaymentTransactionId = paymentTransactionId,
                                TokenAmount = dataTopup.TotalAmount,
                                TokenTransactionId = resultTopup.Data.Items.TokenTransactionId,
                            },
                            Result = 200,
                        };
                        await _cache.SetStringAsync(topupCacheBack, JsonConvert.SerializeObject(output), cacheOption);
                        return output;
                    } else
                    {
                        var output = new GetDataFromURLReturnUrlOutputDto()
                        {
                            ErrorCode = resultTopup.ErrorCode, // Giao dịch đã được ghi nhận vào hệ thống lỗi
                            IsSuccessPayment = false,
                            Item = null,
                            Result = 400,
                        };
                        await _cache.SetStringAsync(topupCacheBack, JsonConvert.SerializeObject(output), cacheOption);
                        return output;
                    }
                }
                else
                {
                    var requestTopup = new HandlerCreateTopUpTransactionPayment()
                    {
                        CacheDataTopup = cacheDataTopup,
                        CheckReturn = checkReturn,
                        IpPayment = ipPayment,
                        PaymentTransactionStatus = "Fail",
                        TopupCache = topupCache,
                        Url = url,
                    };
                    var resultTopup = await HandlerTopupTransaction(requestTopup, checkReturn.Items.PaymentCode);
                    if (resultTopup != null && resultTopup.IsSuccess)
                    {
                        removeCache(topupCache);
                        var output = new GetDataFromURLReturnUrlOutputDto()
                        {
                            ErrorCode = "Payment_Status_" + checkReturn.Items.PaymentStatusCode,
                            IsSuccessPayment = false,
                            Item = null,
                            Result = 400,
                        };
                        await _cache.SetStringAsync(topupCacheBack, JsonConvert.SerializeObject(output), cacheOption);
                        return output;
                    } else
                    {
                        var errorCode = "Payment_Status_" + checkReturn.Items.PaymentStatusCode;
                        var output = new GetDataFromURLReturnUrlOutputDto()
                        {
                            ErrorCode = resultTopup.ErrorCode != null ? resultTopup.ErrorCode : errorCode, // Giao dịch đã được ghi nhận vào hệ thống lỗi
                            IsSuccessPayment = false,
                            Item = null,
                            Result = 400,
                        };
                        await _cache.SetStringAsync(topupCacheBack, JsonConvert.SerializeObject(output), cacheOption);
                        return output;
                    }
                }
            } catch (Exception ex)
            {
                _logger.LogInformation("topup transaction with catch" + JsonConvert.SerializeObject(ex));
                removeCache(topupCache);
                var errorCode = "SystemError";
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    errorCode = res.Code;
                }
                OperationLog("", null, url, "", ipPayment, "SystemError", "PaymentGatewayResponse");
                return new GetDataFromURLReturnUrlOutputDto()
                {
                    ErrorCode = errorCode,
                    IsSuccessPayment = false,
                    Item = null,
                    Result = 400,
                };
            }
        }


        public async Task<GeneratePaymentLinkResponse> GeneratePaymentLink(string hostPath, GeneratePaymentLinkRequest input, string authorization)
        {
            await _rewardMemberService.CheckGeneratePaymentLinkByMember(new CheckGeneratePaymentLinkInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                MoneyAmount = input.MoneyAmount,
                TokenAmount = input.TotalAmount,
            });
            // Set cache  = 8min
            var cacheOption = new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromSeconds(600));
            var orderCode = genOrderCode();
            var cacheData = new GeneratePaymentLinkCache()
            {
                CardType = input.CardType,
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                MoneyAmount = input.MoneyAmount,
                OrderCode = orderCode,
                PaymentGateway = input.PaymentGateway,
                TotalAmount = input.TotalAmount,
            };
            var topupCache = "Topup" + orderCode;
            await _cache.SetStringAsync(topupCache, JsonConvert.SerializeObject(cacheData), cacheOption);
            _logger.LogInformation("topup transaction cache request: " + JsonConvert.SerializeObject(topupCache));
            var tokenFormat = input.TotalAmount.ToString("N0");
            var request = new GeneratePaymentLinkDto()
            {
                BackUrl = hostPath + RewardApiUrl.PAYMENT_BACK_URL,
                ReturnUrl = hostPath + RewardApiUrl.PAYMENT_RETURN_URL,
                CardType = input.CardType,
                OrderCode = orderCode,
                OrderTitleInfo = "Nạp " +tokenFormat+ " điểm LinkID",
                PaymentGateway = input.PaymentGateway,
                TotalAmount = input.MoneyAmount,
                PaymentByIpAddress = input.PaymentByIpAddress,
            };
            var result = await GetRewardAsync<GeneratePaymentLinkResponse>(RewardApiUrl.PAYMENT_GENERATE_PAYMENT_LINK, request);
            var ipPayment = "";
            try
            {
                Uri myUri = new Uri(result.Items.Link);
                var ipHost = Dns.GetHostAddresses(myUri.Host);
                if (ipHost != null && ipHost.Length > 0)
                {
                    ipPayment = ipHost[0].MapToIPv4().ToString();
                }
            }
            catch { }
            OperationLog(input.MemberCode, result.Items.Link, null, orderCode, ipPayment, null, "PaymentGatewayRequest");
            return result;
        }

        private async Task<RewardCreateTopUpTransactionPayment> HandlerTopupTransaction(HandlerCreateTopUpTransactionPayment request, string partnerStatusCode)
        {
            var dataTopup = JsonConvert.DeserializeObject<GeneratePaymentLinkCache>(request.CacheDataTopup);
            var memberCode = dataTopup.MemberCode;
            var merchantId = dataTopup.MerchantId;
            var tokenAmount = dataTopup.TotalAmount;
            var paymentTransactionId = request.CheckReturn.Items.TransactionCode;
            var requestForSucess = new RewardCreateTopUpTransactionInput()
            {
                MemberCode = memberCode,
                MerchantId = merchantId,
                PaymentTransactionId = paymentTransactionId,
                PaymentTransactionStatus = request.PaymentTransactionStatus,
                RequestDate = DateTime.UtcNow,
                TokenAmount = decimal.ToDouble(tokenAmount),
            };
            var requestForFail = new RewardPaymentFailCreateInput()
            {
                FeeAmount = 0,
                MemberCode = memberCode,
                MerchantId = merchantId,
                Reason = "Không phản hồi",
                TokenAmount = tokenAmount,
                TransactionId = paymentTransactionId != null ? paymentTransactionId : request.CheckReturn.Items.InternalTransactionCode,
                TransactionTime = DateTime.UtcNow,
                Type = "Topup",
            };
            removeCache(request.TopupCache);
            var resultTopup = await RetryTopupTransaction(requestForSucess, requestForFail, partnerStatusCode);
            OperationLog(dataTopup.MemberCode, null, request.Url, request.CheckReturn.Items.TransactionCode, request.IpPayment, request.CheckReturn.Items.PaymentStatusCode, "PaymentGatewayResponse");
            return resultTopup;
        }

        private async Task<RewardCreateTopUpTransactionPayment> RetryTopupTransaction(RewardCreateTopUpTransactionInput request, RewardPaymentFailCreateInput requestForFail, string partnerStatusCode)
        {
            var retryNum = 3;
            var output = new RewardCreateTopUpTransactionOutput();
            var success = false;
            var errorCode = "SystemError";
            while (retryNum != 0)
            {
                var result = await TopupTransaction(request, partnerStatusCode);
                if (result != null && result.IsSuccess)
                {
                    retryNum = 0;
                    output = result.Data;
                    errorCode = null;
                    success = true;
                }
                else
                {
                    retryNum--;
                    output = null;
                    errorCode = result.ErrorCode;
                }
            }
            if (retryNum <= 0 && !success)
            {
                // retry for payment fail
                var dataFail = await RetryPaymentFail(requestForFail);
                success = dataFail.IsSuccess;
                errorCode = dataFail.ErrorCode;
            }
            return new RewardCreateTopUpTransactionPayment()
            {
                Data = output,
                ErrorCode = errorCode,
                IsSuccess = success,
            };
        }

        private async Task<RewardCreateTopUpTransactionPayment> RetryPaymentFail(RewardPaymentFailCreateInput request)
        {
            var retryNum = 3;
            var success = false;
            var errorCode = "SystemError";
            var output = new RewardCreateTopUpTransactionOutput();
            while (retryNum != 0)
            {
                var result = await PaymentFail(request);
                if (result != null && result.IsSuccess)
                {
                    retryNum = 0;
                    output = null;
                    errorCode = null;
                    success = true;
                }
                else
                {
                    retryNum--;
                    output = null;
                    errorCode = result.ErrorCode;
                }
            }
            return new RewardCreateTopUpTransactionPayment()
            {
                Data = output,
                ErrorCode = errorCode,
                IsSuccess = success,
            };
        }

        private async Task<RewardCreateTopUpTransactionPayment> PaymentFail(RewardPaymentFailCreateInput request)
        {
            try
            {
                _logger.LogInformation("" + JsonConvert.SerializeObject(request));
                await _rewardPaymentFailService.Create(request);
                return new RewardCreateTopUpTransactionPayment()
                {
                    Data = null,
                    ErrorCode = "Payment_Status_102",
                    IsSuccess = true,
                };
            }
            catch (Exception ex)
            {
                var errorCode = "SystemError";
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    errorCode = res.Code;
                }
                return new RewardCreateTopUpTransactionPayment()
                {
                    Data = null,
                    ErrorCode = errorCode,
                    IsSuccess = false,
                };
            }
        }

        private async Task<RewardCreateTopUpTransactionPayment> TopupTransaction(RewardCreateTopUpTransactionInput request, string partnerStatusCode)
        {
            try
            {
                _logger.LogInformation("topup transaction request" + JsonConvert.SerializeObject(request));
                var result = await _rewardTopUpTransactionService.CreateTopUpTransaction(request, partnerStatusCode);
                _logger.LogInformation("topup transaction response" + JsonConvert.SerializeObject(result));
                return new RewardCreateTopUpTransactionPayment()
                {
                    Data = result,
                    ErrorCode = null,
                    IsSuccess = true,
                };
            }
            catch (Exception ex)
            {
                var errorCode = "SystemError";
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    errorCode = res.Code;
                }
                return new RewardCreateTopUpTransactionPayment()
                {
                    Data = null,
                    ErrorCode = errorCode,
                    IsSuccess = false,
                };
            }
        }

        private async Task<RewardCheckReturnPaymentLinkOutput> CheckReturnPaymentLink(RewardCheckReturnPaymentLinkInput input)
        {
            return await PostRewardAsync<RewardCheckReturnPaymentLinkOutput>(RewardApiUrl.PAYMENT_CHECK_PAYMENT_RETURN_LINK, input);
        }

        private string genOrderCode()
        {
            var random = new Random().Next(99999).ToString("00000");
            return LoyaltyHelper.GenTransactionCodeV2(random);
        }

        private void removeCache(string key)
        {
            if (!string.IsNullOrEmpty(key))
            {
                _cache.RemoveAsync(key).Wait();
            }
        }

        public void OperationLog(string memberCode, string requestData, string responseData, string orderCode, string ipPayment, string errorCode, string serviceName)
        {
            try
            {
                var listOperationLogDto = new CreateOperationLogDto();
                var operationLog = new OperationLogDto
                {
                    TenantId = null,
                    MemberId = memberCode,
                    Code = "Payment_" + DateTime.UtcNow.Ticks.ToString() + new Random().Next(4),
                    ReferenceKey = orderCode,
                    PartnerCode = "Napas",
                    DestIP = ipPayment,
                    StartDate = DateTime.UtcNow,
                    RequestMsg = requestData,
                    ServiceName = serviceName,
                    EndDate = DateTime.UtcNow,
                    Status = true,
                    ErrorCode = errorCode,
                    ResponseMsg = responseData,
                };
                _logger.LogInformation("Dest IP" + operationLog.DestIP);
                listOperationLogDto.OperationLogs.Add(operationLog);

                _loyaltyAuditLogService.CreateOperationLog(listOperationLogDto).Wait();
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Operation exception: " + ex);
            }
        }
    }
}
