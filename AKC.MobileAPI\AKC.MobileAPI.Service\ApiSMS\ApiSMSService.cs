﻿using AKC.MobileAPI.DTO.ApiSMS;
using AKC.MobileAPI.DTO.Loyalty.Rewards;
using AKC.MobileAPI.Service.Abstract.ApiSMS;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.ApiSMS
{
    public class ApiSMSService : IApiSMSService
    {
        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        protected readonly string api_url;
        protected readonly string api_key;
        protected readonly string api_secret;
        protected readonly string brand_name;

        public ApiSMSService(IConfiguration configuration)
        {
            _configuration = configuration;
            api_url = _configuration.GetSection("ApiSMS:api_url").Value;
            api_key = _configuration.GetSection("ApiSMS:api_key").Value;
            api_secret = _configuration.GetSection("ApiSMS:api_secret").Value;
        }

        public async Task<ResponseApiSMSModel> SendSMS(List<SMSModel> listSMS)
        {
            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            var body = new RequestApiSMSModel()
            {
                submission = new SubmissionModel()
                {
                    api_key = api_key,
                    api_secret = api_secret,
                    sms = listSMS
                }
            };

            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.RequestUri = new Uri($"{api_url}");

            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode == false)
            {
                throw new Exception(rawData);
            }

            response.EnsureSuccessStatusCode();

            var result = JsonConvert.DeserializeObject<ResponseApiSMSModel>(rawData);

            return result;
        }
    }
}
