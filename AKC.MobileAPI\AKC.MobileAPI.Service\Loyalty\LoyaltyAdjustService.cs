﻿using AKC.MobileAPI.DTO.Loyalty.Adjust;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyAdjustService: BaseLoyaltyService, ILoyaltyAdjustService
    {
        public LoyaltyAdjustService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }
        public async Task<UpdateMemberBalanceOutput> UpdateMemberBalance(UpdateMemberBalanceInput input)
        {
            return await PutLoyaltyAsync<UpdateMemberBalanceOutput>(LoyaltyApiUrl.UPDATE_MEMBER_BALANCE, input);
        }
    }
}
