﻿using System;

namespace AKC.MobileAPI.DTO.Reward.CashoutTransaction
{
    public class RewardGetGlobalSettingOutput
    {
        public int PointValidDuration { get; set; }
        public int PointExpireDuration { get; set; }
        public DateTime ExpiryDate { get; set; }
        public DateTime ValidDate { get; set; }
        public decimal CurrencyExchangeRate { get; set; }
        public int CashoutTimeout { get; set; }
        public int LockTime { get; set; }
        public int MaxWrongTime { get; set; }
        public bool UsingYubikey { get; set; }
        public string DefaultPointUsagePriority { get; set; }
        public decimal FeeAmountCashoutAmountRatio { get; set; }
        public decimal MaximumCashoutFeeAmount { get; set; }
        public decimal MinimumCashoutFeeAmount { get; set; }
        public decimal FreeEntranceTimesOfCashoutPerMonth { get; set; }
        public decimal MerchantCashoutFeeSetting { get; set; }
        public decimal FeeAmountClaimAmountRatio { get; set; }
        public decimal MaximumClaimFeeAmount { get; set; }
        public decimal MinimumClaimFeeAmount { get; set; }
        public decimal FreeEntranceTimesOfClaimPerMonth { get; set; }
        public decimal MerchantClaimFeeSetting { get; set; }
        public bool UsingFirebaseOtp { get; set; }
        public decimal MaxDepositOfMerchant { get; set; }
        public int CashoutExpireTime { get; set; }
    }
}
