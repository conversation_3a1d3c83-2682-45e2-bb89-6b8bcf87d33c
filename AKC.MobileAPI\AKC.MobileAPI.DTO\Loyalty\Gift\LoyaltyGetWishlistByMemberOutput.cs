﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{


    public class LoyaltyGetWishlistByMemberOutput
    {
        public GetWishlistByMemberResultData Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class LoyaltyGetWishlistByMemberOutputForView
    {
        public GiftShortInforDto WishListItem { get; set; }
        public List<ImageLinkDto> ImageLink { get; set; }
        // public List<GiftShortInforForView> RelatedGifts { get; set; }
        public FlashSaleProgramDto FlashSaleProgramInfor { get; set; }
        public GiftDiscountDto GiftDiscountInfor { get; set; }
    }

    public class GetWishlistByMemberResultData
    {
        public int TotalCount { get; set; }
        public decimal? BalanceAbleToCashout { get; set; }

        public List<LoyaltyGetWishlistByMemberOutputForView> Items { get; set; }
    }

    public class LoyaltyGetBrandWishlistByMemberOutput
    {
        public GetBrandWishlistByMemberResultData Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }
    public class GetBrandWishlistByMemberResultData
    {
        public int TotalCount { get; set; }

        public List<BrandDto> Items { get; set; }
    }
    public class BrandDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public long? LastModifierUserId { get; set; }
        public string LastModifierUserName { get; set; }
        public string BrandLinkLogo { get; set; }
        public string BrandDescription { get; set; }
        public string BrandAddress { get; set; }
        public string LinkLogo { get; set; }
        public string Description { get; set; }
        public string Address { get; set; }
        public double CommissPercent { get; set; }
        public long? NumberOfGifts { get; set; }
        public string CoverPhoto { get; set; } 
        public int TotalWish { get; set; }
    }
}
