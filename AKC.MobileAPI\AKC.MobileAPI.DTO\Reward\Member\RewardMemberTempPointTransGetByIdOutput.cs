﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberTempPointTransGetByIdOutput
    {
        public int Result { get; set; }
        public List<RewardMemberTempPointTransItem> Items { get; set; }
        public int TotalCount { get; set; }
        public string Message { get; set; }
    }

    public class RewardMemberTempPointTransItem
    {
        public string Id { get; set; }
        public DateTime? Time { get; set; }
        public DateTime? ValidDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string OrderCode { get; set; }
        public string Status { get; set; }
        public string ActionCode { get; set; }
        public string ActionCodeDetail { get; set; }
        public string NationalId { get; set; }
        public string ActionName { get; set; }
        public string MerchantId { get; set; }
        public string MerchantName { get; set; }
        public int MemberId { get; set; }
        public string ActionType { get; set; }
        public string TempPointAmount { get; set; }
        public string GrantType { get; set; }
    }
}
