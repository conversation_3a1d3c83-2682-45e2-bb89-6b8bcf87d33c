﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.TopUp;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/topup-evoucher")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyEVoucherForTopUpController : ControllerBase
    {
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltyLanguageService _loyaltyLanguageService;
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionResponseService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly ILoyaltyGiftTransactionsService _giftTransactionsService;
        private readonly ILoyaltyGiftService _giftService;
        private readonly IRewardMemberService _rewardMemberService;

        public LoyaltyEVoucherForTopUpController(ILogger<LoyaltyEVoucherForTopUpController> lg,
            ILoyaltyInsuranceService ix,
            IExceptionReponseService exceptionResponseService,
            ILoyaltyLanguageService loyaltyLanguageService,
            ILoyaltyGiftTransactionsService giftSrv,
            ICommonHelperService help,
            IRewardMemberService ms,
            ILoyaltyGiftService gs,
            IExceptionReponseService ex)
        {
            _giftService = gs;
            _rewardMemberService = ms;
            _exceptionReponseService = exceptionResponseService;
            _loyaltyLanguageService = loyaltyLanguageService;
            _giftTransactionsService = giftSrv;
            _logger = lg;
            _exceptionResponseService = ex;
            _commonHelperService = help;
        }

        [HttpPost]
        [Route("check-code")]
        public async Task<ActionResult<CheckVoucherOutput>> CheckEVoucherCode([FromBody] CheckVoucherInput input)
        {
            var authorization = Request.Headers["Authorization"].ToString();
            var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
            if (checkAuthen != null)
            {
                return StatusCode(401, checkAuthen);
            }
            var result = await _giftService.CheckEVoucherCode(input);
            return StatusCode(200, result);
        }

        [HttpPost]
        [Route("check-if-member-blocked")]
        public async Task<ActionResult<CheckMemberOutput>> CheckMember([FromBody] CheckMemberInput input)
        {
            var authorization = Request.Headers["Authorization"].ToString();
            var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
            if (checkAuthen != null)
            {
                return StatusCode(401, checkAuthen);
            }
            var result = await _giftService.CheckMemberCodeBlocked(input);
            return StatusCode(200, result);
        }




        [HttpPost]
        [Route("topup")]
        public async Task<ActionResult<RedeemVoucherOutput>> TopUp([FromBody] CheckVoucherInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _giftService.UseEVoucherCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogInformation("TopUp " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));
                    return StatusCode(400, res);
                }
            }
        }
    }
}