﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.MobileAPI;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Reward.Member;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltySecondaryCustomersService : BaseLoyaltyService,  ILoyaltySecondaryCustomersService
    {
        public LoyaltySecondaryCustomersService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        /// <summary>
        /// Get Profile by code form loyalty.
        /// </summary>
        /// <param name="code">Member ID | (NationalId in Reward)</param>
        /// <returns></returns>
        public async Task<LoyaltyResponse<SecondaryCustomerDto>> GetProfileByCode(string code)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<SecondaryCustomerDto>>(LoyaltyApiUrl.GET_PROFILE_BY_CODE, new { Code = code });
        }
        
        public async Task<LoyaltyResponse<CustomSecondaryCustomerInfoDto>> GetMemberInfoByCode(string code)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<CustomSecondaryCustomerInfoDto>>(LoyaltyApiUrl.GET_MEMBER_INFO_BY_CODE, new { Code = code });
        }

        public async Task<LoyaltyResponse<LoyaltyVerifyReferralCodeOutputDto>> VerifyReferralCode(LoyaltyVerifyReferralCodeInput input)
        {
            var distributionChannelList = new string[] { };
            if (input.DistributionChannelList != null && input.DistributionChannelList.Length > 0) {
                distributionChannelList = input.DistributionChannelList;
                var duplicateKeys = distributionChannelList.GroupBy(x => x)
                  .Where(g => g.Count() > 1)
                  .Select(y => y.Key)
                  .ToList();
                if (duplicateKeys != null && duplicateKeys.Count > 0)
                {
                    throw new ArgumentException("DistributionChannelList cannot be duplicate data");
                }
            }
            var merchantId = Convert.ToInt32(_configuration.GetSection("Reward" + MerchantNameConfig.VPID + ":MerchantId").Value);
            var request = new LoyaltyVerifyReferralCodeInputDto()
            {
                TenantId = this.tenantId,
                MerchantId = merchantId,
                DistributionChannelList = distributionChannelList,
                MemberCode = input.NationalId,
                ReferenceAmount = input.ReferenceAmount,
                ReferralCode = input.ReferralCode,
            };
            return await PostLoyaltyAsync<LoyaltyResponse<LoyaltyVerifyReferralCodeOutputDto>>(LoyaltyApiUrl.VERIFY_REFERRAL_CODE, request);
        }

        public async Task<LoyaltyMemberCheckReferralCodeExistanceOutput> CheckReferralCodeExistance(LoyaltyMemberCheckReferralCodeExistanceInput input)
        {
            var result = await PostLoyaltyAsync<LoyaltyResponse<LoyaltyMemberCheckReferralCodeExistanceItems>>(LoyaltyApiUrl.CHECK_REFERRAL_CODE_EXISTANCE, input);
            return new LoyaltyMemberCheckReferralCodeExistanceOutput()
            {
                Result = 200,
                Messages = "Succcess",
                Items = result.Result,
                MessagesDetail = null,
            };
        }

        public async Task<LoyaltyResponse<string>> UpdateNotificationSetting(UpdateNotificationSettingInput input)
        {
            return await PutLoyaltyAsync<LoyaltyResponse<string>>(LoyaltyApiUrl.UPDATE_NOTIFICATION_SETTING, input);
        }
        
        public async Task<LoyaltyResponse<string>> UpdatePhoneNumber(UpdatePhoneNumberInput input)
        {
            return await PutLoyaltyAsync<LoyaltyResponse<string>>(LoyaltyApiUrl.MEMBER_UPDATE_PHONE_NUMBER, input);
        }

        public async Task<object> DeleteAccount(DeleteAccountInputForLoyLinkId input)
        {
            return await DeleteLoyaltyAsync<object>(LoyaltyApiUrl.DELETE_LOY_ACCOUNTS, input);
        }

        public async Task<CreateMemberOfCustomTenantOutput> CreateMemberOfCustomTenant(
            CreateMemberOfCustomTenantInput input, string partner)
        {
            return await PostLoyaltyCustomMerchantAsync<CreateMemberOfCustomTenantOutput>(LoyaltyApiUrl.CREATE_SECONDARY_CUSTOMERS, input, null, partner);
        }

        public async Task<LoyaltyResponse<SecondaryCustomerDto>> GetProfileByCode(string code, string partner)
        {
            return await GetLoyaltyCustomMerchantAsync<LoyaltyResponse<SecondaryCustomerDto>>(LoyaltyApiUrl.GET_PROFILE_BY_CODE, new { Code = code }, partner);
        }
        public async Task<LoyaltyResponse<CheckRefCodeOutput>> CheckRefCode(CheckRefCodeInput input)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<CheckRefCodeOutput>>(LoyaltyApiUrl.CheckRefCode, input);
        }

        public async Task<LoyaltyResponse<CheckMemberHasInvitedOutput>> CheckMemberHasInvited(CheckMemberHasInvitedInput input)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<CheckMemberHasInvitedOutput>>(LoyaltyApiUrl.CheckMemberHasInvited, input);
        }

        public async Task<LoyaltyResponse<VerifyRefferralCode_v2Output>> VerifyReferralCode_v2(VerifyReferralCode_v2DTO input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<VerifyRefferralCode_v2Output>>(LoyaltyApiUrl.Verify_Refferral_Code_v2, input);
        }

        public async Task<LoyaltyResponse<GetInforCampaignRefOutput>> GetInforCampaignRef()
        {
            return await GetLoyaltyAsync<LoyaltyResponse<GetInforCampaignRefOutput>>(LoyaltyApiUrl.GetInforCampaignRef);
        }

        public async Task<LoyaltyResponse<RegisterCampaignFriendReferralOutput>> RegisterCampaignFriendReferral(RegisterCampaignFriendReferralInput input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<RegisterCampaignFriendReferralOutput>>(LoyaltyApiUrl.RegisterCampaignFriendReferral, input);
        }

        public async Task<LoyaltyResponse<CheckMemberRegisterCampaignOutput>> CheckMemberRegisterCampaign(CheckMemberRegisterCampaignInput input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<CheckMemberRegisterCampaignOutput>>(LoyaltyApiUrl.CheckMemberRegisterCampaign, input);
        }
        
        public async Task<LoyaltyResponse<CommonLoyDeleteAccountOutput>> DeleteMemberLoyaltyInCommonLoyalty(CommonLoyDeleteAccountInput input, int tID)
        {
            return await DeleteCommonLoyaltyLoyaltyAsync<LoyaltyResponse<CommonLoyDeleteAccountOutput>>(LoyaltyApiUrl.DELETE_LOY_ACCOUNTS, tID, input);
        }

        public async Task<LoyaltyResponse<CreateMemberActionFirstOutput>> CreateMemberActionFirst(CreateMemberActionFirstInput input)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<CreateMemberActionFirstOutput>>(LoyaltyApiUrl.CREATE_MEMBER_ACTION_FIRST, input);
        }
    }
}
