﻿using AKC.MobileAPI.DTO.Loyalty.MemberFirstUseToken;
using AKC.MobileAPI.DTO.Reward.PayByTokenTransaction;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardPayByTokenTransactionService : RewardBaseService, IRewardPayByTokenTransactionService
    {
        private readonly IMemberFirstUseTokenService _memberFirstUseTokenService;
        public RewardPayByTokenTransactionService(IConfiguration configuration, IMemberFirstUseTokenService memberFirstUseTokenService) : base(configuration)
        {
            _memberFirstUseTokenService = memberFirstUseTokenService;
        }
        public async Task<RewardCreatePayByTokenTransactionOutput> CreatePayByTokenTransaction(RewardCreatePayByTokenTransactionInput input)
        {
            var request = new RewardCreatePayByTokenTransactionDto()
            {
                NationalId = input.NationalId,
                OrderCode = LoyaltyHelper.GenTransactionCode(input.NationalId + "PayByToken"),
                StoreId = input.StoreId,
                Time = input.Time,
                TotalRequestedAmount = input.TotalRequestedAmount,
                WalletAddress = input.WalletAddress,
            };

            var result = await PostRewardAsync<RewardCreatePayByTokenTransactionOutput>(RewardApiUrl.PAY_BY_TOKEN_TRANSACTION_CREATE, request, MerchantNameConfig.VPID);
            if(result.Result == 200)
            {
                await _memberFirstUseTokenService.CreateMeberFirstUseToken(new MemberFirstUseTokenInput { MemberCode = input.NationalId, RedeemSource = "LinkID", RefCode = result.Items.OrderCode, TransferTime = input.Time, FirstUseType = "Paybytoken" });
            }    
            return result;
        }
    }
}
