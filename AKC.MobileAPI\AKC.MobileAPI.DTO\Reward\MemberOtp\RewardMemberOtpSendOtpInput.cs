﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.MemberOtp
{
    public class RewardMemberOtpSendOtpInput
    {
        public string MemberCode { get; set; }
        public string SessionId { get; set; }
        public string SmsType { get; set; }
    }

    public class RewardMemberOtpSendOtpOutput
    {
        public int result { get; set; }
        public string message { get; set; }
        public RewardMemberOtpSendOtpItem item { get; set; }
    }

    public class RewardMemberOtpSendOtpItem
    {
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
        public string FirebaseId { get; set; }
    }
}
