﻿using AKC.MobileAPI.DTO.Loyalty.GiftFlashSaleProgram;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/GiftFlashSaleProgram")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyGiftFlashSaleProgramController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyGiftFlashSaleProgramService _loyaltyGiftFlashSaleProgramService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IDistributedCache _cache;

        public LoyaltyGiftFlashSaleProgramController(
            ILogger<LoyaltyGiftFlashSaleProgramController> logger,
            ILoyaltyGiftFlashSaleProgramService loyaltyGiftFlashSaleProgramService,
            ICommonHelperService commonHelperService,
            IExceptionReponseService exceptionReponseService,
            IDistributedCache cache
            )
        {
            _logger = logger;
            _loyaltyGiftFlashSaleProgramService = loyaltyGiftFlashSaleProgramService;
            _commonHelperService = commonHelperService;
            _exceptionReponseService = exceptionReponseService;
            _cache = cache;
        }

        [HttpGet]
        [Route("ListGiftByFlashSaleProgram")]
        public async Task<ActionResult<GiftFlashSaleProgramOutput>> ListGiftByFlashSaleProgram([FromQuery] GiftFlashSaleProgramInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                //var CacheKey = "ListGiftByFlashSaleProgram_2022DEC26";
                //var cachedData = await _cache.GetStringAsync(CacheKey);
                //if (!string.IsNullOrEmpty(cachedData))
                //{
                //    var convertedObj = JsonConvert.DeserializeObject<GiftFlashSaleProgramOutput>(cachedData);
                //    _logger.LogInformation(">> ListGiftByFlashSaleProgram_2022DEC26 >> CACHE HIT");
                //    return StatusCode(200, convertedObj);
                //}
                var result = await _loyaltyGiftFlashSaleProgramService.ListGiftByFlashSaleProgram(input);
                //_logger.LogInformation(">> ListGiftByFlashSaleProgram_2022DEC26 >> CACHE MISS; GET FROM DB");
                //await _cache.SetStringAsync(CacheKey, JsonConvert.SerializeObject(result),
                //    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(5)));
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {

                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "ListGiftByFlashSaleProgram Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetGiftDetailFlashSaleProgram")]
        public async Task<ActionResult<GiftDetailFlashSaleProgramOutput>> GetGiftDetailFlashSaleProgram([FromQuery] GiftDetailFlashSaleProgramInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftFlashSaleProgramService.GetGiftDetailFlashSaleProgram(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {

                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetGiftDetailFlashSaleProgram Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllFlashSaleProgram")]
        public async Task<ActionResult<GiftFlashSaleProgramForViewOutput>> GetAllFlashSaleProgram([FromQuery] GetAllFlashSaleProgramInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftFlashSaleProgramService.GetAllFlashSaleProgram(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {

                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetAllFlashSaleProgram Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }
    }
}
