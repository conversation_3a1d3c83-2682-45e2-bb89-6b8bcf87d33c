﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Gamification
{
    public class ViewGameItemsOutput
    {
        public int result { get; set; }
        public List<ViewGameItemsDto> item { get; set; }
        public int totalCount { get; set; }
        public string message { get; set; }
        public string messageDetail { get; set; }
    }
    public class ItemList
    {
        public int? ItemID { get; set; }
        public int? Quantity { get; set; }
    }
    public class GameList
    {
        public int? GameID { get; set; }
        public int? Level { get; set; }
        public List<ItemList> ItemList { get; set; }
        public List<QuestList> QuestList { get; set; }
    }
    public class QuestList
    {
        public int? QuestID { get; set; }
        public List<MissionList> MissionList { get; set; }
    }
    public class MissionList
    {
        public int? MissionID { get; set; }
    }
    public class ViewGameItemsDto
    {
        public int? MemberID { get; set; }
        public int? GameMasterID { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Status { get; set; }
        public string Avatar { get; set; }
        public DateTime? CreateDate { get; set; }
        public int? CreatorUserID { get; set; }
        public DateTime? DeletedDate { get; set; }
        public bool? IsDeleted { get; set; }
        public int? DeletedUserID { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public int? LastModificationUserID { get; set; }
        public List<ItemList> ItemList { get; set; }
        public List<GameList> GameList { get; set; }
    }
}
