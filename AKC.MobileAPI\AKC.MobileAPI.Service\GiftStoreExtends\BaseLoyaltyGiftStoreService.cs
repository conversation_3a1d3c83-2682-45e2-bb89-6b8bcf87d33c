﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.GiftStoreExtends;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.CronServices;
using AKC.MobileAPI.Service.Exceptions;
using Cronos;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class BaseLoyaltyGiftStoreService
    {
        private readonly ILogger _logger;
        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        protected string baseURL;
        protected int tenantId;
        protected string defaultUserName;
        protected string defaultPassowrd;
        private readonly IDistributedCache _cache;

        public BaseLoyaltyGiftStoreService(
            IConfiguration configuration,
            IDistributedCache cache,
            ILogger<BaseLoyaltyGiftStoreService> logger)
        {
            _client.Timeout = TimeSpan.FromSeconds(300);
            _configuration = configuration;
            _cache = cache;
            _logger = logger;
        }

        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetLoyaltyAsync<T>(int tenantIdRequest, string apiURL, object query = null)
        {
            var configTenant = GetConfigByTenantId(tenantIdRequest);
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };

            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            var token = GetAccessToken(configTenant);
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("loyalty gift store response: " + rawData);

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(configTenant, true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();


                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Perform a DELETE obj to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> DeleteLoyaltyAsync<T>(int tenantIdRequest, string apiURL, object query = null)
        {
            var configTenant = GetConfigByTenantId(tenantIdRequest);
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Delete
            };

            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            var token = GetAccessToken(configTenant);
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("loyalty gift store response: " + rawData);

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(configTenant, true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();


                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Convert a object to query string format.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string GetQueryString(object obj)
        {
            var properties = from p in obj.GetType().GetProperties()
                             where p.GetValue(obj, null) != null
                             select p.Name + "=" + HttpUtility.UrlEncode(p.GetValue(obj, null).ToString());

            return string.Join("&", properties.ToArray());
        }

        public async Task<T> PostLoyaltyAsync<T>(int tenantIdRequest, string apiURL, object body = null, HttpContext request = null)
        {
            var configTenant = GetConfigByTenantId(tenantIdRequest);
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            var Client_Request_Address = request != null && request.Request.Headers.ContainsKey("Client-Request-Address") ?
                                            request?.Request.Headers["Client-Request-Address"].ToString() : request?.Connection.RemoteIpAddress.ToString();
            req.Headers.Add("Client-Request-Address", Client_Request_Address);
            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken(configTenant));
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("loyalty gift store response: " + rawData);

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(configTenant, true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<T> PutLoyaltyAsync<T>(int tenantIdRequest, string apiURL, object body = null)
        {
            var configTenant = GetConfigByTenantId(tenantIdRequest);
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Put
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken(configTenant));
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("loyalty gift store response: " + rawData);

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(configTenant, true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Get a new accessToken form Loyalty.
        /// </summary>
        /// <returns></returns>
        private string GetAccessToken(LoyaltyGiftStoreVendor config, bool mustResetCache = false)
        {
            var token = _cache.GetString("CacheGiftStore_" + config.KeyTenant);
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var expression = CronExpression.Parse(configuration.GetSection("Loyalty:CronExpressionRefreshToken").Value);
            var timeZoneInfo = TimeZoneInfo.Local;

            // Request body
            if (string.IsNullOrEmpty(token) || mustResetCache)
            {
                var configToken = LoyaltyHelper.GetNewAccessTokenLoyaltyGiftStore(config);
                return LoyaltyHelper.RenewAccessTokenGiftStoreCacheValue(configToken, _cache, CronHelper.GetDelayToNextRefreshToken(expression, timeZoneInfo), mustResetCache);
            }

            return token;
        }

        public LoyaltyGiftStoreVendor GetConfigByTenantId(int tenantIdRequest)
        {
            try
            {
                var config = LoyaltyHelper.GetListMerchantStoreKeyConfig().Find(x => x.TenantId == tenantIdRequest);
                if (config == null)
                {
                    throw new ArgumentException("Cannot config for gift vendor of tenant = " + tenantIdRequest);
                }
                baseURL = config.RemoteUrl;
                tenantId = config.TenantId;
                defaultUserName = config.UserName;
                defaultPassowrd = config.Password;
                return config;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        public LoyaltyGiftStoreVendor GetConfigByMerchantId(int merchantId)
        {
            try
            {
                var config = LoyaltyHelper.GetListMerchantStoreKeyConfig().Find(x => x.DefaultRedeemMerchantId == merchantId);
                if (config == null)
                {
                    var ex = new RewardException();
                    var error = new RewardDataExceptionResponse()
                    {
                        result = new RewardDataExceptionResultItem()
                        {
                            code = "MerchantNotFound",
                            message = "Cannot find merchant store from setting"
                        },
                        status = 500
                    };
                    ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
                    ex.Data.Add("StatusCode", 400);
                    throw ex;
                }
                baseURL = config.RemoteUrl;
                tenantId = config.TenantId;
                defaultUserName = config.UserName;
                defaultPassowrd = config.Password;
                return config;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        public string GenOrderCode(string orderCode, string memberCode)
        {
            return LoyaltyHelper.GenTransactionCode(orderCode + memberCode + DateTime.Now.Ticks);
        }

        //Clone a HttpRequest
        private HttpRequestMessage CloneHttpRequest(HttpRequestMessage req)
        {
            HttpRequestMessage clone = new HttpRequestMessage(req.Method, req.RequestUri);

            clone.Content = req.Content;
            clone.Version = req.Version;

            foreach (KeyValuePair<string, object> prop in req.Properties)
            {
                clone.Properties.Add(prop);
            }

            foreach (KeyValuePair<string, IEnumerable<string>> header in req.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            return clone;
        }
    }
}
