﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftTransactions
{
    public class LoyaltyGetAllWithEGiftInput
    {
        [Required]
        public string OwnerCodeFilter { get; set; }

        public string StatusFilter { get; set; }
        public string EGiftStatusFilter { get; set; }

        public DateTime? FromDateFilter { get; set; }
        public DateTime? ToDateFilter { get; set; }
        public string GiftTransactionCode { get; set; }

        [Range(0, int.MaxValue)]
        public int MaxResultCount { get; set; }

        [Range(0, int.MaxValue)]
        public int SkipCount { get; set; }

        public string Sorting { get; set; }
        public string TypeOfGift { get; set; }
        public string TypeOfTransaction { get; set; }
        public string Ver { get; set; }
        public bool? IsNearExpire { get; set; }
        public string VendorsNotShow { get; set; } = string.Empty;
        public decimal? MaxCoin { get; set; }
        public bool IsGiftable { get; set; } = false;
        public string GiftTransType { get; set; }
        public int? UseType { get; set; }
    }
    public class GetSingleCreateGiftRedeemTransactionInput
    {
        [Required]
        public string MemberCode { get; set; }
        [Required]
        public string TransactionCode { get; set; }
    }

    public class GetSingleCreateGiftRedeemTransactionOutput
    {
        public string GiftCode { get; set; }
        public string GiftName { get; set; }
        public string FullCategoryCode { get; set; }
        public string GiftCategoryName { get; set; }
        public string Description { get; set; }
        public int GiftId { get; set; }
        public string VendorName { get; set; }
        public string VendorImage { get; set; }
        public string BrandName { get; set; }
        public string BrandImage { get; set; }
        public decimal Amount { get; set; }
    }
}
