﻿using AKC.MobileAPI.DTO.Loyalty.MemberFirstUseToken;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class MemberFirstUseTokenService :  BaseLoyaltyService, IMemberFirstUseTokenService
    {
        private readonly IDistributedCache _cache;
        public MemberFirstUseTokenService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
            _cache = cache;
        }
        public async Task<MemberFirstUseTokenOuput> CreateMeberFirstUseToken(MemberFirstUseTokenInput input)
        {
            return await PostLoyaltyAsync<MemberFirstUseTokenOuput>(LoyaltyApiUrl.CREATE_MemberFirstUseToken, input);
        }
    }
}
