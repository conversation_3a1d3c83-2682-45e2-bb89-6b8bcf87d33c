﻿using AKC.MobileAPI.DTO.Reward.ExchangeTransaction;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Reward
{
    public interface IRewardExchangeTransactionService
    {
        Task<RewardCreateExchangeTransactionOutput> CreateExchangeTransactionIntegration(RewardCreateExchangeTransactionInput input, string rewardType = null);
        Task<RewardExchangeVerifyOTPOutput> ExchangeVerifyOTP(RewardExchangeVerifyOTPInput input);
        Task<CheckPhoneNumberOutput> CheckPhoneNumber(CheckPhoneNumberInput input);
        Task<RewardRevertExchangeTransactionOutput> RevertExchangeTransactionIntegration(RewardRevertExchangeTransactionInput input, string rewardType = null);
    }
}
