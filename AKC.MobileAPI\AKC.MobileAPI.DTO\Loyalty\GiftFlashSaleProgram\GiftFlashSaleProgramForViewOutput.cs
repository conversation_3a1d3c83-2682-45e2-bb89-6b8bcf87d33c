﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftFlashSaleProgram
{
    public class GiftFlashSaleProgramForViewOutput
    {
        public GiftFlashSaleProgramForViewResult result { get; set; }
        public object targetUrl { get; set; }
        public bool success { get; set; }
        public object error { get; set; }
        public bool unAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class GiftFlashSaleProgramForViewResult
    {
        public int totalCount { get; set; }
        public List<GiftFlashSaleProgramForView> items { get; set; }
    }

    public class GiftFlashSaleProgramForView
    {
        public long FlashSaleProgramId { get; set; }
        public string FlashSaleProgramCode { get; set; }
        public string FlashSaleProgramName { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Status { get; set; }
        public List<GiftInFlashSaleProgramForView> GiftInFlashSaleProgramForViews { get; set; }
        public int? QuantityThresholdWarning { get; set; }
        public int? PercentThresholdWarning { get; set; }
        public string Image { get; set; }
    }

    public class GiftInFlashSaleProgramForView
    {
        public long GiftId { get; set; }
        public long GiftDiscountId { get; set; }
        public string GiftCode { get; set; }
        public string GiftName { get; set; }
        public decimal? SalePrice { get; set; }
        public decimal? OriginalPrice { get; set; }
        public float? ReductionRate { get; set; }
        public int ReductionRateDisplay
        {
            get
            {
                if (ReductionRate.HasValue)
                {
                    if (ReductionRate > 0 && ReductionRate < 1)
                    {
                        return 1;
                    }
                    else if (ReductionRate > 99 && ReductionRate < 100)
                    {
                        return 99;
                    }
                    else
                    {
                        return (int)Math.Round((double)ReductionRate);
                    }
                }
                return 0;
            }
        }
        public int? RemainingQuantityFlashSale { get; set; }
        public string ImageLink { get; set; }
        public int? UsedQuantityFlashSale { get; set; }
        public decimal? Amount { get; set; }
        public bool? WarningOutOfStock { get; set; }
        public bool IsSoldOut { get; set; }
    }
}
