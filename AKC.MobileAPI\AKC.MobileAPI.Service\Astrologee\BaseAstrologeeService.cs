using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.Astrologee
{
    public class BaseAstrologeeService
    {
        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        private readonly string apiKey;
        private readonly string baseURL;
        private string APIKEYSTRING = "x-apikey";
        protected readonly IDistributedCache _cache;

        public BaseAstrologeeService(IConfiguration configuration, IDistributedCache cache)
        {
            _configuration = configuration;
            _cache = cache;
            baseURL = _configuration.GetSection("Astrologee:RemoteUrl").Value;
            apiKey = _configuration.GetSection("Astrologee:ApiKey").Value;
        }
        
        public async Task<T> GetAsync<T>(string apiURL, object query = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };

            req.Headers.Add(APIKEYSTRING, apiKey);
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();


                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }
        private string GetQueryString(object obj)
        {
            var properties = from p in obj.GetType().GetProperties()
                where p.GetValue(obj, null) != null
                select p.Name + "=" + HttpUtility.UrlEncode(p.GetValue(obj, null).ToString());

            return string.Join("&", properties.ToArray());
        }
    }
}