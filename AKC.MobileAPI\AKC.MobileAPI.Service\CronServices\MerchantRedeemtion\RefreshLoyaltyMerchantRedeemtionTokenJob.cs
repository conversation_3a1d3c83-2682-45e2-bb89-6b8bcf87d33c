﻿using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.CronServices.MerchantRedeemtion
{


    public class RefreshLoyaltyMerchantRedeemtionTokenJob : CronJobService
    {
        private readonly ILogger<RefreshLoyaltyMerchantRedeemtionTokenJob> _logger;
        private readonly IDistributedCache _cache;

        public RefreshLoyaltyMerchantRedeemtionTokenJob(IScheduleConfig<RefreshLoyaltyMerchantRedeemtionTokenJob> config,
            ILogger<RefreshLoyaltyMerchantRedeemtionTokenJob> logger,
            IDistributedCache cache) : base(config.CronExpression, config.TimeZoneInfo)
        {
            _logger = logger;
            _cache = cache;
        }

        public override Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                var delayToNextRunGiftStoreVendor = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
                LoyaltyHelper.RenewAccessTokenLoyaltyGiftStoreCacheValue(_cache, delayToNextRunGiftStoreVendor, _logger);
            }
            catch (Exception ex)
            {
                _logger.LogError("Start get token fail from gift store vendor: " + ex.Message);
            }
            return base.StartAsync(cancellationToken);
        }

        public override Task DoWork(CancellationToken cancellationToken)
        {
            try
            {
                var delayToNextRunGiftStoreVendor = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
                LoyaltyHelper.RenewAccessTokenLoyaltyGiftStoreCacheValue(_cache, delayToNextRunGiftStoreVendor, _logger);
                _logger.LogInformation("RenewAccessToken Job run successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError("Job: RenewAccessToken gift store vendor error: " + ex.Message);
            }

            return Task.CompletedTask;
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            return base.StopAsync(cancellationToken);
        }
    }
}
