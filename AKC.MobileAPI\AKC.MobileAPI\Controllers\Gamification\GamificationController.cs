﻿using AKC.MobileAPI.DTO.Gamification;
using AKC.MobileAPI.DTO.Gamification.Exchange;
using AKC.MobileAPI.DTO.Loyalty.Adjust;
using AKC.MobileAPI.DTO.Gamification.Game;
using AKC.MobileAPI.Service.Abstract.Gamification;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using System.Security.Cryptography;
using AKC.MobileAPI.Service.Helpers;
using System.Text;
using AKC.MobileAPI.Service.Common;

namespace AKC.MobileAPI.Controllers.Gamification
{
    [Route("api/Gamification")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class GamificationController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IItemManagementService _gamificationItemManagementService;
        private readonly IGameManagementService _gamificationManagementService;
        private readonly IExchangeManagementService _gamificationExchangeManagementService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IMemberManagementService _gamificationMemberManagementService;
        private readonly ILoyaltyAdjustService _loyaltyAdjustService;
        private readonly IConfiguration _configuration;
        private readonly ICommonHelperService _commonHelperService;

        public GamificationController(
            ILogger<GamificationController> logger,
            IExceptionReponseService exceptionReponseService,
            IItemManagementService gamificationItemManagementService,
            IGameManagementService gamificationManagementService,
            IMemberManagementService gamificationMemberManagementService,
            IExchangeManagementService gamificationExchangeManagementService,
            ILoyaltyAdjustService loyaltyAdjustService,
            IConfiguration configuration,
            ICommonHelperService commonHelperService
            )
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _gamificationItemManagementService = gamificationItemManagementService;
            _gamificationManagementService = gamificationManagementService;
            _gamificationMemberManagementService = gamificationMemberManagementService;
            _gamificationExchangeManagementService = gamificationExchangeManagementService;
            _loyaltyAdjustService = loyaltyAdjustService;
            _configuration = configuration;
            _commonHelperService = commonHelperService;
        }

        [HttpGet]
        [Route("GetExchangeRuleType")]
        public async Task<ActionResult<GetAllGetExchangeRuleListOutput>> GetExchangeRuleType([FromQuery] GetAllGetExchangeRuleListInput input)
        {
            try
            {
                var result = await _gamificationItemManagementService.GetExchangeRuleType(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionGamificationReponse(ex);
                _logger.LogError(ex, "GetExchangeRuleType GetAll Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("ViewGameList")]
        public async Task<ActionResult<ViewGameListOutput>> ViewGameList([FromQuery] ViewGameListInput input)
        {
            try
            {
                var result = await _gamificationManagementService.ViewGameList(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "ViewGameList GetAll Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        [HttpGet]
        [Route("ViewGameItems")]
        public async Task<ActionResult<ViewGameItemsOutput>> ViewGameItems([FromQuery] ViewGameItemsInput input)
        {
            try
            {
                var result = await _gamificationMemberManagementService.ViewGameItems(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "ViewGameItems GetAll Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        // [HttpPost]
        // [Route("CreateExchangeTransaction")]
        // public async Task<ActionResult<CreateExchangeTransactionOutput>> CreateExchangeTransaction(CreateExchangeTransactionInput input)
        // {
        //     try
        //     {
        //         var result = await _gamificationExchangeManagementService.CreateExchangeTransaction(input);
        //         //var balanceInfo = null;
        //         if (result.message == "success" && result.item[0].ItemQuantity != null)
        //         {
        //             var loyaltyInput = new UpdateMemberBalanceInput()
        //             {
        //                 MemberCode = input.MemberCode,
        //                 TransactionCode = result.item[0].TransactionCode,
        //                 Coin = result.item[0].ItemQuantity,
        //                 BusinessTime = DateTime.UtcNow
        //
        //             };
        //             try
        //             {
        //                 var balanceInfo = await _loyaltyAdjustService.UpdateMemberBalance(loyaltyInput);
        //                 return StatusCode(200, result);
        //             }
        //             catch (Exception ex)
        //             {
        //                 var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //                 _logger.LogError(ex, "UpdateMemberBalance Error - " + JsonConvert.SerializeObject(ex));
        //
        //                 return StatusCode(400, res);
        //             }
        //
        //         }
        //         return StatusCode(200, result);
        //     }
        //     catch (Exception ex)
        //     {
        //         var res = await _exceptionReponseService.GetExceptionGamificationReponse(ex);
        //         _logger.LogError(ex, "CreateExchangeTransaction Error - " + JsonConvert.SerializeObject(ex));
        //
        //         return StatusCode(400, res);
        //     }
        // }
        [HttpGet]
        [Route("GetDetails")]
        public async Task<ActionResult<GetDetailsExchangeRuleOutput>> GetDetailsExchangeRule([FromQuery] GetDetailsExchangeRuleInput input)
        {
            try
            {
                var result = await _gamificationItemManagementService.GetDetailsExchangeRule(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetDetails GetAll Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("MemberPlayGame")]
        public async Task<ActionResult<MemberPlayGameOutputDto>> MemberPlayGame(MemberPlayGameInputDto input)
        {

            try
            {
                var result = await _gamificationMemberManagementService.MemberPlayGame(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionGamificationReponse(ex);
                _logger.LogError(ex, "MemberPlayGame Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }
        [HttpGet]
        [Route("ExchangeTransactionHistory")]
        public async Task<ActionResult<ExchangeTransactionHistoryOutput>> ExchangeTransactionHistory([FromQuery] ExchangeTransactionHistoryInput input)
        {
            try
            {
                var result = await _gamificationExchangeManagementService.ExchangeTransactionHistory(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionGamificationReponse(ex);
                _logger.LogError(ex, "ExchangeTransactionHistory Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetGamificationLink")]
        public async Task<ActionResult<GetGamificationLinkOutput>> GetGamificationLink([FromQuery] GetGamificationLinkInput input)
        {
            try
            {
                var result = new GetGamificationLinkOutput();
                var member = new MemberPlayGameOutputDto();
                member = await _gamificationMemberManagementService.CreateMember(input);
                if (member.message == "success")
                {
                    result = await _gamificationManagementService.GetGamificationLink(input);
                    return StatusCode(200, result);
                }
                else
                {
                    return StatusCode(404, member);
                }
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionGamificationReponse(ex);
                _logger.LogError(ex, "GetGamificationLink Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAuthenticationKey")]
        public async Task<ActionResult<GetAuthenticationKeyOutput>> GetAuthenticationKey([FromQuery] GetAuthenticationKeyInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }

                var result = await _gamificationManagementService.GetAuthenticationKey(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionGamificationReponse(ex);
                _logger.LogError(ex, "GetAuthenticationKey Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}
