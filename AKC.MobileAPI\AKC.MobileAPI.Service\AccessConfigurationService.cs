﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace AKC.MobileAPI.Service
{
    public class AccessConfigurationService
    {
        #region Properties
        private static AccessConfigurationService instance;
        private IConfiguration configuration;

        private AccessConfigurationService()
        {
            var rootPath = Directory.GetCurrentDirectory();
            configuration = BuildConfiguration(rootPath);
        }
        public static AccessConfigurationService Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new AccessConfigurationService();
                }
                return instance;
            }
        }
        #endregion


        #region Methods
        public IConfiguration GetConfiguration()
        {
            return configuration;
        }
        private IConfigurationRoot BuildConfiguration(string path, string environmentName = null)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(path)
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            if (!string.IsNullOrWhiteSpace(environmentName))
            {
                builder = builder.AddJsonFile($"appsettings.{environmentName}.json", optional: true);
            }

            builder = builder.AddEnvironmentVariables();

            return builder.Build();
        }
        #endregion
    }
}
