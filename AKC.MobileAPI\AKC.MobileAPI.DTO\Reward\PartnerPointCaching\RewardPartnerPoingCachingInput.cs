﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.PartnerPointCaching
{
    public class RewardPartnerPoingCachingInput
    {
        public string NationalId { get; set; }
        public List<RewardPartnerPoingCachingItems> Items { get; set; }
    }

    public class RewardPartnerPoingCachingItems
    {
        public int MerchantId { get; set; }
        public decimal PointBalance { get; set; }
        public string Status { get; set; }
        public bool HaveException { get; set; } = false;
        public string ExceptionCode { get; set; } = null;
    }
}
