﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class LoyaltyUpdateWishlistInput
    {
        [Required]
        public string MemberCode { get; set; }
        [Required]
        public string GiftId { get; set; }
        [Required]
        public string Action { get; set; }

        public string Type { get; set; } = "G"; // G or B, default is G
    }

    public class LikeItemInput
    {
        public string MemberCode { get; set; }
        // EntityType = G (Gift), B (Brand)
        public string EntityType { get; set; }
        public List<int> EntityIds { get; set; }
    }

    public class LikeItemOutputWrapper
    {
        public LikeItemOutput Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }
    public class LikeItemOutput
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string ListLiked { get; set; }
    }

}
