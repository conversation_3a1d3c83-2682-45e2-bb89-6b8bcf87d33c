﻿using AKC.MobileAPI.DTO.Loyalty.Brand;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyBrandService : BaseLoyaltyService, ILoyaltyBrandService
    {
        public LoyaltyBrandService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<GetBrandOutput> GetAll(GetBrandInput input)
        {
            return await GetLoyaltyAsync<GetBrandOutput>(LoyaltyApiUrl.BRAND_GET_ALL, input);
        }
        public async Task<GetBrandByCategoryOutput> GetAllBrandByCategoryCode(GetBrandInput input)
        {
            return await GetLoyaltyAsync<GetBrandByCategoryOutput>(LoyaltyApiUrl.BRAND_GET_ALL_BY_CATEGORY, input);
        }

        public async Task<GetBrandByCategoryOutput> GetProminentBrand(GetProminentBrandInput input)
        {
            return await GetLoyaltyAsync<GetBrandByCategoryOutput>(LoyaltyApiUrl.PROMINENT_BRAND_GET_LIST, input);
        }
    }
}
