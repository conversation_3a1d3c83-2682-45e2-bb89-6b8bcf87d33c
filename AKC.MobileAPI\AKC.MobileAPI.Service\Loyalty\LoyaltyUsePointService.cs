﻿using AKC.MobileAPI.DTO.Loyalty.UsePoint;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyUsePointService: BaseLoyaltyService, ILoyaltyUsePointService
    {
        public LoyaltyUsePointService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }
        public async Task<LoyaltyPreCalculatePointOutput> PreCalculatePoint(LoyaltyPreCalculatePointInput input)
        {
            return await PostLoyaltyAsync<LoyaltyPreCalculatePointOutput>(LoyaltyApiUrl.USEPOINT_PRECALCULATEPOINT, input);
        }
    }
}
