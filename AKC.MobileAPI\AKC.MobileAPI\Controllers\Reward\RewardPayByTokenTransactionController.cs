﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Reward.PayByTokenTransaction;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Reward
{
    [Route("api/PayByTokenTransaction")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class RewardPayByTokenTransactionController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IRewardPayByTokenTransactionService _rewardPayByTokenTransactionService;
        private readonly ICommonHelperService _commonHelperService;
        public RewardPayByTokenTransactionController(
            ILogger<RewardMerchantController> logger,
            IExceptionReponseService exceptionReponseService,
            IRewardPayByTokenTransactionService rewardPayByTokenTransactionService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _rewardPayByTokenTransactionService = rewardPayByTokenTransactionService;
            _commonHelperService = commonHelperService;
        }

        [HttpPost]
        [Route("Create")]
        public async Task<ActionResult<RewardCreatePayByTokenTransactionOutput>> CreatePayByTokenTransaction(RewardCreatePayByTokenTransactionInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.NationalId);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _rewardPayByTokenTransactionService.CreatePayByTokenTransaction(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "PayByTokenTransaction Error" + JsonConvert.SerializeObject(ex));
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                if (res.Code == "PositiveCreditBalance")
                {
                    res.Message = "Mã lỗi: PCB - Bạn không thể thực hiện hành động. Vui lòng liên hệ LynkiD để được hỗ trợ";
                }
                
                return StatusCode(400, res);
            }
        }
    }
}