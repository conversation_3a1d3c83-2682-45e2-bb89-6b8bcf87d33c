﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.ThirdParty.VPBankLoyaltyExchange;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.ThirdParty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.ThirdParty.Base;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.ThirdParty
{
    public class ThirdPartyVPBankLoyaltyExchangeService : BaseThirdPartyVPBankLoayltyExchangeService, IThirdPartyVPBankLoyaltyExchangeService
    {
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltyAuditLogService _loyaltyAuditLogService;
        public ThirdPartyVPBankLoyaltyExchangeService(IConfiguration configuration,
            IDistributedCache cache,
            IExceptionReponseService exceptionReponseService,
            ILoyaltyAuditLogService loyaltyAuditLogService,
            ILogger<ThirdPartyDummyService> logger) : base(configuration, cache, logger, loyaltyAuditLogService)
        {
            _exceptionReponseService = exceptionReponseService;
            _loyaltyAuditLogService = loyaltyAuditLogService;
        }

        public async Task<ThirdPartyVPBankLoyaltyCheckIdCardItemDto> CheckIdCard(ThirdPartyVPBankLoyaltyCheckIdCardInput input, string memberCode)
        {
            try
            {
                var result = await PostLoyaltyAsync<LoyaltyResponse<ThirdPartyVPBankLoyaltyCheckIdCardOutput>>(
                    LoyaltyApiUrl.VPBANK_LOYALTY_EXCHANGE_CHECK_ID_CARD, "CheckIdCard", memberCode, input);
                return new ThirdPartyVPBankLoyaltyCheckIdCardItemDto()
                {
                    IsChangedLoyalty = true
                };

            } catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    // Thành viên có số CIF này đã nằm trong danh sách chặn chuyển đổi
                    if (res.Code == "749")
                    {
                        return new ThirdPartyVPBankLoyaltyCheckIdCardItemDto()
                        {
                            IsChangedLoyalty = false
                        };
                    }
                }
                throw ex;
            }
        }

        public async Task<LoyaltyResponse<ThirdPartyVPBankLoyaltyConfirmConnectOutput>> ConfirmConnect(ThirdPartyVPBankLoyaltyConfirmConnectInput input, string memberCode)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<ThirdPartyVPBankLoyaltyConfirmConnectOutput>>(
                LoyaltyApiUrl.VPBANK_LOYALTY_CONFIRM_CONNECT, "ConfirmConnect", memberCode, input);
        }

        public async Task<LoyaltyResponse<ThirdPartyVPBankLoyaltyRemoveConnetedOutput>> RemoveConneted(ThirdPartyVPBankLoyaltyRemoveConnetedInput input, string memberCode)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<ThirdPartyVPBankLoyaltyRemoveConnetedOutput>>(
                LoyaltyApiUrl.VPBANK_LOYALTY_REMOVE_CONNECT, "RemoveConnect", memberCode, input);
        }

        public async Task<LoyaltyResponse<ThirdPartyVPBankLoyaltySendOtpConfirmConnectOutput>> SendOtpConfirmConnect(ThirdPartyVPBankLoyaltySendOtpConfirmConnectInput input, string memberCode)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<ThirdPartyVPBankLoyaltySendOtpConfirmConnectOutput>>(
                LoyaltyApiUrl.VPBANK_LOYALTY_SEND_OTP_CONFIRM_CONNECT, "SendOtpConfirmConnect", memberCode, input);
        }

        public async Task<LoyaltyResponse<ThirdPartyVPBankLoyaltyVerifyOtpConfirmConnectOutput>> VerifyOtpConfirmConnect(ThirdPartyVPBankLoyaltyVerifyOtpConfirmConnectInput input, string memberCode)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<ThirdPartyVPBankLoyaltyVerifyOtpConfirmConnectOutput>>(
                LoyaltyApiUrl.VPBANK_LOYALTY_VERIFY_OTP_CONFIRM_CONNECT, "VerifyOtpConfirmConnect", memberCode, input);
        }

        public async Task<LoyaltyResponse<ThirdPartyVPBankLoyaltyUpdatePhoneNumberOutput>> UpdatePhoneNumber(UpdatePhoneNumberInput input, string memberCode)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<ThirdPartyVPBankLoyaltyUpdatePhoneNumberOutput>>(
                LoyaltyApiUrl.VPBANK_LOYALTY_UPDATE_PHONE_NUMBER_CONNECT, "UpdatePhoneNumber", memberCode, input);
        }

        public async Task<ThirdPartyVPBankLoyaltyVerifyIdCardDto> VerifyIdCard(ThirdPartyVPBankLoyaltyVerifyIdCardInput input, string memberCode)
        {
            try
            {
                var result = await PostLoyaltyAsync<LoyaltyResponse<ThirdPartyVPBankLoyaltyVerifyIdCardOutput>>(
                    LoyaltyApiUrl.VPBANK_LOYALTY_EXCHANGE_VERIFY_ID_CARD, "VerifyIdCard", memberCode, input);
                return new ThirdPartyVPBankLoyaltyVerifyIdCardDto()
                {
                    IsChangedLoyalty = true,
                    ResponseLoyalty = result
                };

            }
            catch (Exception ex)
            {
                _logger.LogError("ThirdParty VerifyNationalId Error " + ex.Message);
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    // Thành viên có số CIF này đã nằm trong danh sách chặn chuyển đổi -> sẽ connect theo luồng cũ
                    if (res.Code == "749")
                    {
                        return new ThirdPartyVPBankLoyaltyVerifyIdCardDto()
                        {
                            IsChangedLoyalty = false,
                            ResponseLoyalty = null,
                        };
                    }
                    throw ex;
                } else
                {
                    // Call service fail http request
                    return new ThirdPartyVPBankLoyaltyVerifyIdCardDto()
                    {
                        IsChangedLoyalty = false,
                        ResponseLoyalty = null,
                    };
                }
            }
        }

        public async Task<LoyaltyResponse<ThirdPartyVPBankLoyaltyVerifyOtpOutput>> VerifyOtp(ThirdPartyVPBankLoyaltyVerifyOtpInput input, string memberCode)
        {
            return await PostLoyaltyAsync<LoyaltyResponse<ThirdPartyVPBankLoyaltyVerifyOtpOutput>>(
                LoyaltyApiUrl.VPBANK_LOYALTY_EXCHANGE_VERIFY_OTP, "VerifyOtp", memberCode, input);
        }

        public async Task<LoyaltyResponse<ThirdPartyVPBankLoyaltyGetCifCodeByIdCardOutput>> GetCifCodeByIdCard(ThirdPartyVPBankLoyaltyGetCifCodeByIdCardInput input, string memberCode)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<ThirdPartyVPBankLoyaltyGetCifCodeByIdCardOutput>>(
                LoyaltyApiUrl.VPBANK_LOYALTY_GET_CIF_CODE, input);
        }
    }
}
