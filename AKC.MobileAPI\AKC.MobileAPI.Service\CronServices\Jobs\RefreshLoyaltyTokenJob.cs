﻿using AKC.MobileAPI.Service.Abstract;
using AKC.MobileAPI.Service.Loyalty;
using Cronos;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.CronServices
{


    public class RefreshLoyaltyTokenJob : CronJobService
    {
        private readonly ILogger<RefreshLoyaltyTokenJob> _logger;
        private readonly IDistributedCache _cache;

        public RefreshLoyaltyTokenJob(IScheduleConfig<RefreshLoyaltyTokenJob> config,
            ILogger<RefreshLoyaltyTokenJob> logger,
            IDistributedCache cache) : base(config.CronExpression, config.TimeZoneInfo)
        {
            _logger = logger;
            _cache = cache;
        }

        public override Task StartAsync(CancellationToken cancellationToken)
        {
            var delayToNextRun = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
            // var delayToNextRunDummy = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
            var delayToNextRunVPBankExchang = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
            //var delayToNextRunGiftStoreVendor = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            // Get token after run app.
            try
            {
                LoyaltyHelper.RenewAccessTokenCacheValue(_cache, delayToNextRun);
            } catch (Exception ex)
            {
                _logger.LogError("Start get token fail from loyalty: " +  ex.Message);
            }
            try
            {
                // LoyaltyHelper.RenewAccessTokenDummyCacheValue(_cache, delayToNextRunDummy);
            }
            catch (Exception ex)
            {
                _logger.LogError("Start get token fail from dummy exchange: " + ex.Message);
            }
            // Enable vpbank new
            var IsEnabledVPBankNew = configuration.GetSection("IsUsingVPBankLoyalty").Value;
            if (bool.Parse(IsEnabledVPBankNew))
            {
                try
                {
                    LoyaltyHelper.RenewAccessTokenVPBankExchangeCacheValue(_cache, delayToNextRunVPBankExchang);
                }
                catch (Exception ex)
                {
                    _logger.LogError("Start get token fail from vpbank loyalty exchange new: " + ex.Message);
                }
                //try
                //{
                //    LoyaltyHelper.RenewAccessTokenLoyaltyGiftStoreCacheValue(_cache, delayToNextRunGiftStoreVendor);
                //}
                //catch (Exception ex)
                //{
                //    _logger.LogError("Start get token fail from vpbank loyalty gift store", ex.Message);
                //}
            }
            return base.StartAsync(cancellationToken);
        }

        public override Task DoWork(CancellationToken cancellationToken)
        {
            try
            {
                var delayToNextRun = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
                // var delayToNextRunDummy = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
                var delayToNextRunVPBankExchang = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
                //var delayToNextRunGiftStoreVendor = CronHelper.GetDelayToNextRefreshToken(_expression, _timeZoneInfo);
                // Try renew access token.
                LoyaltyHelper.RenewAccessTokenCacheValue(_cache, delayToNextRun);
                // LoyaltyHelper.RenewAccessTokenDummyCacheValue(_cache, delayToNextRunDummy);
                LoyaltyHelper.RenewAccessTokenVPBankExchangeCacheValue(_cache, delayToNextRunVPBankExchang);
                //LoyaltyHelper.RenewAccessTokenLoyaltyGiftStoreCacheValue(_cache, delayToNextRunGiftStoreVendor);
                _logger.LogInformation("RenewAccessToken Job run successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError("Job: RenewAccessToken error" + JsonConvert.SerializeObject(ex));
            }

            return Task.CompletedTask;
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            return base.StopAsync(cancellationToken);
        }
    }
}
