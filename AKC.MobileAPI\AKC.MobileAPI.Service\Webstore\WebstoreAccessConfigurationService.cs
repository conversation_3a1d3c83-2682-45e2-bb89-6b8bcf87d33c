﻿using System.IO;
using Microsoft.Extensions.Configuration;

namespace AKC.MobileAPI.Service.Webstore
{
    public class WebstoreAccessConfigurationService
    {
        #region Properties
        private static WebstoreAccessConfigurationService instance;
        private IConfiguration configuration;

        private WebstoreAccessConfigurationService()
        {
            var rootPath = Directory.GetCurrentDirectory();
            configuration = BuildConfiguration(rootPath);
        }
        public static WebstoreAccessConfigurationService Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new WebstoreAccessConfigurationService();
                }
                return instance;
            }
        }
        #endregion


        #region Methods
        public IConfiguration GetConfiguration()
        {
            return configuration;
        }
        private IConfigurationRoot BuildConfiguration(string path, string environmentName = null)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(path)
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            if (!string.IsNullOrWhiteSpace(environmentName))
            {
                builder = builder.AddJsonFile($"appsettings.{environmentName}.json", optional: true);
            }

            builder = builder.AddEnvironmentVariables();

            return builder.Build();
        }
        #endregion
    }
}
