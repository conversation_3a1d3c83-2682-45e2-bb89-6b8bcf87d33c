﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardBaseService
    {
        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        protected string baseURL;
        protected int MerchantId;
        protected string accessToken;

        public RewardBaseService(IConfiguration configuration)
        {
            _client.Timeout = TimeSpan.FromSeconds(300);
            _configuration = configuration;
        }

        public void updateConfig(string rewardType)
        {
            if (!string.IsNullOrEmpty(rewardType))
            {
                baseURL = _configuration.GetSection("Reward" + rewardType + ":RemoteURL").Value;
                MerchantId = Convert.ToInt32(_configuration.GetSection("Reward" + rewardType + ":MerchantId").Value);
                accessToken = _configuration.GetSection("Reward" + rewardType + ":AccessToken").Value;
            }
            else
            {
                baseURL = _configuration.GetSection("Reward:RemoteURL").Value;
                MerchantId = Convert.ToInt32(_configuration.GetSection("Reward:MerchantId").Value);
                accessToken = _configuration.GetSection("Reward:AccessToken").Value;
            }
        }

        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetRewardAsync<T>(string apiURL, object query = null, string rewardType = null)
        {
            this.updateConfig(rewardType);
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };


            req.Headers.Add("MerchantId", MerchantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            req.RequestUri = new Uri(requestURL);
            //var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode == false)
                {
                    try
                    {
                        var error = JsonConvert.DeserializeObject<RewardDataExceptionResponse>(rawData);
                        if (error != null && error.result != null)
                        {
                            GetErrorValidation(error.result.code, error.result.message);
                        }
                    }
                    catch { }

                    var ex = new RewardException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// Convert a object to query string format.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string GetQueryString(object obj)
        {
            var properties = from p in obj.GetType().GetProperties()
                             where p.GetValue(obj, null) != null
                             select p.Name + "=" + HttpUtility.UrlEncode(p.GetValue(obj, null).ToString());

            return string.Join("&", properties.ToArray());
        }

        public async Task<T> PostToPartnerIntegrationApiAsync<T>(string apiURL, object body = null, object headers = null)
        {

            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var partnerIntegrationApiUrlRoot = _configuration.GetSection("PartnerIntegrationByLinkId:BaseURL").Value;
            var apiKey = _configuration.GetSection("PartnerIntegrationByLinkId:ApiKey").Value;
            var authName = _configuration.GetSection("PartnerIntegrationByLinkId:AuthName").Value;

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body);

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Add("X-Partner-ID", authName);
            req.Headers.Add("X-API-Key", apiKey);
            req.RequestUri = new Uri($"{partnerIntegrationApiUrlRoot}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode == false)
                {
                    try
                    {
                        var error = JsonConvert.DeserializeObject<RewardDataExceptionResponse>(rawData);
                        if (error != null && error.result != null)
                        {
                            GetErrorValidation(error.result.code, error.result.message);
                        }
                    }
                    catch { }

                    var ex = new RewardException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                response.EnsureSuccessStatusCode();

                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<T> PostRewardAsync<T>(string apiURL, object body = null, string rewardType = null)
        {

            this.updateConfig(rewardType);
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body);

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            req.Headers.Add("MerchantId", MerchantId.ToString());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode == false)
                {

                    var ex = new RewardException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                response.EnsureSuccessStatusCode();

                // Get respone result.

                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<T> PutRewardAsync<T>(string apiURL, object body = null, string rewardType = null)
        {
            this.updateConfig(rewardType);
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Put
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body);

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            req.Headers.Add("MerchantId", MerchantId.ToString());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);


                var rawData = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode == false)
                {
                    try
                    {
                        var error = JsonConvert.DeserializeObject<RewardDataExceptionResponse>(rawData);
                        if (error != null && error.result != null)
                        {
                            GetErrorValidation(error.result.code, error.result.message);
                        }
                    }
                    catch { }

                    var ex = new RewardException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                response.EnsureSuccessStatusCode();

                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        public async Task<string> getNational(string tokenBearer)
        {
            var idToken = tokenBearer.Replace("Bearer ", "").Replace("bearer ", "");
            var token = await FirebaseAdmin.Auth.FirebaseAuth.DefaultInstance.VerifyIdTokenAsync(idToken);
            return token.Uid;
        }

        public async Task<T> PostRewardExchangeAsync<T>(string apiURL, object body = null, string rewardType = null)
        {

            this.updateConfig(rewardType);
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body);

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            req.Headers.Add("MerchantId", MerchantId.ToString());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode == false)
                {
                    try
                    {
                        var error = JsonConvert.DeserializeObject<RewardDataExceptionResponse>(rawData);
                        if (error != null && error.result != null)
                        {
                            GetErrorValidation(error.result.code, error.result.message);
                        }
                    }
                    catch { }

                    var ex = new RewardException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }
                response.EnsureSuccessStatusCode();

                // Get respone result.

                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                GetErrorValidation("ExchangeTransactionTimeout", "Timeout request");
                throw ex;
            }
            catch (TimeoutException ex)
            {
                GetErrorValidation("ExchangeTransactionTimeout", "Timeout request");
                throw ex;
            }
            catch (OperationCanceledException ex)
            {
                GetErrorValidation("ExchangeTransactionTimeout", "Timeout request");
                throw ex;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private RewardDataExceptionResponse GetErrorValidation(string errorCode, string errorMessage)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = errorMessage
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }
    }
}
