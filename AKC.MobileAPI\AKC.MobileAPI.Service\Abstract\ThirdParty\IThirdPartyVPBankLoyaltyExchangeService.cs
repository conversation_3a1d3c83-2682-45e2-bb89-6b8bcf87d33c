﻿using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.ThirdParty.VPBankLoyaltyExchange;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.ThirdParty
{
    public interface IThirdPartyVPBankLoyaltyExchangeService
    {
        Task<ThirdPartyVPBankLoyaltyCheckIdCardItemDto> CheckIdCard(ThirdPartyVPBankLoyaltyCheckIdCardInput input, string memberCode);
        Task<ThirdPartyVPBankLoyaltyVerifyIdCardDto> VerifyIdCard(ThirdPartyVPBankLoyaltyVerifyIdCardInput input, string memberCode);
        Task<LoyaltyResponse<ThirdPartyVPBankLoyaltyVerifyOtpOutput>> VerifyOtp(ThirdPartyVPBankLoyaltyVerifyOtpInput input, string memberCode);
        Task<LoyaltyResponse<ThirdPartyVPBankLoyaltyRemoveConnetedOutput>> RemoveConneted(ThirdPartyVPBankLoyaltyRemoveConnetedInput input, string memberCode);
        Task<LoyaltyResponse<ThirdPartyVPBankLoyaltyConfirmConnectOutput>> ConfirmConnect(ThirdPartyVPBankLoyaltyConfirmConnectInput input, string memberCode);
        Task<LoyaltyResponse<ThirdPartyVPBankLoyaltyUpdatePhoneNumberOutput>> UpdatePhoneNumber(UpdatePhoneNumberInput input, string memberCode);
        Task<LoyaltyResponse<ThirdPartyVPBankLoyaltySendOtpConfirmConnectOutput>> SendOtpConfirmConnect(ThirdPartyVPBankLoyaltySendOtpConfirmConnectInput input, string memberCode);
        Task<LoyaltyResponse<ThirdPartyVPBankLoyaltyVerifyOtpConfirmConnectOutput>> VerifyOtpConfirmConnect(ThirdPartyVPBankLoyaltyVerifyOtpConfirmConnectInput input, string memberCode);
        Task<LoyaltyResponse<ThirdPartyVPBankLoyaltyGetCifCodeByIdCardOutput>> GetCifCodeByIdCard(ThirdPartyVPBankLoyaltyGetCifCodeByIdCardInput input, string memberCode);
    }
}
