INFO  2024-07-02 14:18:14,139 [41   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:18:20,823 [41   ] iftService - >> EvoucherTopup: Sending request to <PERSON><PERSON>ty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:18:53,431 [41   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:18:57,638 [41   ] Middleware - An unhandled exception has occurred while executing the request.
System.AggregateException: One or more errors occurred. (No connection could be made because the target machine actively refused it.)
 ---> System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it.
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean allowHttp2, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttpConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)
   --- End of inner exception stack trace ---
   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)
   at System.Threading.Tasks.Task`1.get_Result()
   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.LoginLoyalty(Nullable`1 tenantId, String baseURL, String userName, String password) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyHelper.cs:line 50
   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.GetNewAccessToken() in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyHelper.cs:line 109
   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.RenewAccessTokenCacheValue(IDistributedCache cache, TimeSpan delay, Boolean isForceRenew) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyHelper.cs:line 169
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetAccessToken(Boolean mustResetCache) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 421
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.PostLoyaltyAsync[T](String apiURL, Object body, HttpContext request) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 261
   at AKC.MobileAPI.Service.Loyalty.Gift.LoyaltyGiftService.RevertEVoucherCode(RevertEVoucherInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\Gift\LoyaltyGiftService.cs:line 263
   at AKC.MobileAPI.Service.Loyalty.Gift.LoyaltyGiftService.UseEVoucherCode(CheckVoucherInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\Gift\LoyaltyGiftService.cs:line 245
   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyEVoucherForTopUpController.TopUp(CheckVoucherInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\Loyalty\LoyaltyEVoucherForTopUpController.cs:line 90
   at lambda_method(Closure , Object )
   at Microsoft.Extensions.Internal.ObjectMethodExecutorAwaitable.Awaiter.GetResult()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location where exception was thrown ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|19_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at AKC.MobileAPI.AuditLog.RequestLoginMiddleware.Invoke(HttpContext context) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\AuditLog\RequestLoginMiddleware.cs:line 46
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
INFO  2024-07-02 14:19:04,978 [23   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:19:16,136 [23   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:19:20,250 [23   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:19:24,421 [23   ] Middleware - An unhandled exception has occurred while executing the request.
System.AggregateException: One or more errors occurred. (No connection could be made because the target machine actively refused it.)
 ---> System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it.
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean allowHttp2, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttpConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)
   --- End of inner exception stack trace ---
   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)
   at System.Threading.Tasks.Task`1.get_Result()
   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.LoginLoyalty(Nullable`1 tenantId, String baseURL, String userName, String password) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyHelper.cs:line 50
   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.GetNewAccessToken() in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyHelper.cs:line 109
   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.RenewAccessTokenCacheValue(IDistributedCache cache, TimeSpan delay, Boolean isForceRenew) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyHelper.cs:line 169
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetAccessToken(Boolean mustResetCache) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 421
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.PostLoyaltyAsync[T](String apiURL, Object body, HttpContext request) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 261
   at AKC.MobileAPI.Service.Loyalty.Gift.LoyaltyGiftService.RevertEVoucherCode(RevertEVoucherInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\Gift\LoyaltyGiftService.cs:line 263
   at AKC.MobileAPI.Service.Loyalty.Gift.LoyaltyGiftService.UseEVoucherCode(CheckVoucherInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\Gift\LoyaltyGiftService.cs:line 245
   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyEVoucherForTopUpController.TopUp(CheckVoucherInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\Loyalty\LoyaltyEVoucherForTopUpController.cs:line 90
   at lambda_method(Closure , Object )
   at Microsoft.Extensions.Internal.ObjectMethodExecutorAwaitable.Awaiter.GetResult()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location where exception was thrown ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|19_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at AKC.MobileAPI.AuditLog.RequestLoginMiddleware.Invoke(HttpContext context) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\AuditLog\RequestLoginMiddleware.cs:line 46
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
INFO  2024-07-02 14:20:29,301 [55   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:20:35,746 [55   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:20:53,453 [55   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:20:59,775 [55   ] Middleware - An unhandled exception has occurred while executing the request.
System.AggregateException: One or more errors occurred. (No connection could be made because the target machine actively refused it.)
 ---> System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it.
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean allowHttp2, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttpConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)
   --- End of inner exception stack trace ---
   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)
   at System.Threading.Tasks.Task`1.get_Result()
   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.LoginLoyalty(Nullable`1 tenantId, String baseURL, String userName, String password) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyHelper.cs:line 50
   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.GetNewAccessToken() in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyHelper.cs:line 109
   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.RenewAccessTokenCacheValue(IDistributedCache cache, TimeSpan delay, Boolean isForceRenew) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyHelper.cs:line 169
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetAccessToken(Boolean mustResetCache) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 421
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.PostLoyaltyAsync[T](String apiURL, Object body, HttpContext request) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 261
   at AKC.MobileAPI.Service.Loyalty.Gift.LoyaltyGiftService.RevertEVoucherCode(RevertEVoucherInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\Gift\LoyaltyGiftService.cs:line 263
   at AKC.MobileAPI.Service.Loyalty.Gift.LoyaltyGiftService.UseEVoucherCode(CheckVoucherInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\Gift\LoyaltyGiftService.cs:line 245
   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyEVoucherForTopUpController.TopUp(CheckVoucherInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\Loyalty\LoyaltyEVoucherForTopUpController.cs:line 90
   at lambda_method(Closure , Object )
   at Microsoft.Extensions.Internal.ObjectMethodExecutorAwaitable.Awaiter.GetResult()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location where exception was thrown ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|19_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at AKC.MobileAPI.AuditLog.RequestLoginMiddleware.Invoke(HttpContext context) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\AuditLog\RequestLoginMiddleware.cs:line 46
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
INFO  2024-07-02 14:21:31,125 [63   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:21:36,834 [63   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:22:06,918 [63   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:22:13,543 [63   ] Middleware - An unhandled exception has occurred while executing the request.
System.AggregateException: One or more errors occurred. (No connection could be made because the target machine actively refused it.)
 ---> System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it.
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean allowHttp2, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttpConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)
   --- End of inner exception stack trace ---
   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)
   at System.Threading.Tasks.Task`1.get_Result()
   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.LoginLoyalty(Nullable`1 tenantId, String baseURL, String userName, String password) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyHelper.cs:line 50
   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.GetNewAccessToken() in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyHelper.cs:line 109
   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.RenewAccessTokenCacheValue(IDistributedCache cache, TimeSpan delay, Boolean isForceRenew) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\LoyaltyHelper.cs:line 169
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.GetAccessToken(Boolean mustResetCache) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 421
   at AKC.MobileAPI.Service.Loyalty.BaseLoyaltyService.PostLoyaltyAsync[T](String apiURL, Object body, HttpContext request) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\BaseLoyaltyService.cs:line 261
   at AKC.MobileAPI.Service.Loyalty.Gift.LoyaltyGiftService.RevertEVoucherCode(RevertEVoucherInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\Gift\LoyaltyGiftService.cs:line 263
   at AKC.MobileAPI.Service.Loyalty.Gift.LoyaltyGiftService.UseEVoucherCode(CheckVoucherInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Loyalty\Gift\LoyaltyGiftService.cs:line 245
   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyEVoucherForTopUpController.TopUp(CheckVoucherInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\Loyalty\LoyaltyEVoucherForTopUpController.cs:line 90
   at lambda_method(Closure , Object )
   at Microsoft.Extensions.Internal.ObjectMethodExecutorAwaitable.Awaiter.GetResult()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location where exception was thrown ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|19_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at AKC.MobileAPI.AuditLog.RequestLoginMiddleware.Invoke(HttpContext context) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\AuditLog\RequestLoginMiddleware.cs:line 46
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
INFO  2024-07-02 14:22:38,328 [71   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:22:40,178 [71   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:22:59,427 [71   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
INFO  2024-07-02 14:24:14,570 [29   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:24:18,188 [29   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:24:58,722 [28   ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 14:24:58,750 [28   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
INFO  2024-07-02 14:25:16,212 [26   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:25:19,594 [26   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:30:51,660 [6    ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 14:30:51,695 [6    ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
INFO  2024-07-02 14:32:23,811 [28   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:32:27,473 [28   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:32:43,424 [32   ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 14:32:43,451 [32   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
INFO  2024-07-02 14:33:00,389 [29   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:33:08,454 [29   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:33:13,958 [45   ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 14:33:13,982 [45   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
INFO  2024-07-02 14:33:32,072 [44   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:33:36,235 [44   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:34:32,014 [39   ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 14:34:32,036 [39   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
INFO  2024-07-02 14:35:41,858 [31   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:35:41,899 [31   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:36:03,307 [33   ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 14:36:03,334 [33   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
INFO  2024-07-02 14:36:30,045 [30   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:36:30,060 [30   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:36:37,401 [7    ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 14:36:37,425 [7    ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
INFO  2024-07-02 14:37:46,170 [45   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:37:46,180 [45   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:38:20,016 [53   ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
INFO  2024-07-02 14:40:49,565 [30   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:40:49,608 [30   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:40:51,282 [34   ] iftService - >> Issue calling Loyalty API: Error When Mark EVoucher as USED >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
INFO  2024-07-02 14:41:05,113 [40   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:41:05,118 [40   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:41:05,806 [42   ] iftService - >> Issue calling Loyalty API: Error When Mark EVoucher as USED >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
INFO  2024-07-02 14:43:35,292 [43   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:43:35,302 [43   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:43:45,291 [53   ] iftService - >> Issue calling Loyalty API: Error When Mark EVoucher as USED >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
INFO  2024-07-02 14:44:10,615 [60   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKKQXBIMWJ\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:44:10,621 [60   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
ERROR 2024-07-02 14:44:20,061 [49   ] iftService - >> Issue calling Loyalty API: Error When Mark EVoucher as USED >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKKQXBIMWJ
INFO  2024-07-02 14:45:37,728 [61   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKVRGHX50Z\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:45:37,740 [61   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKVRGHX50Z
ERROR 2024-07-02 14:45:46,295 [39   ] iftService - >> Issue calling Loyalty API: Error When Mark EVoucher as USED >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKVRGHX50Z
INFO  2024-07-02 14:47:00,371 [31   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\"memberCode\":\"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\"eGiftCode\":\"LYNKVRGHX50Z\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:47:00,380 [31   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKVRGHX50Z
ERROR 2024-07-02 14:47:05,875 [23   ] iftService - >> Issue calling Loyalty API: Error When Mark EVoucher as USED >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKVRGHX50Z
INFO  2024-07-02 14:48:26,970 [25   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\r\n    \"eGiftCode\": \"LYNKPMEK98T0\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:48:26,982 [25   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKPMEK98T0
ERROR 2024-07-02 14:49:02,226 [7    ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 14:49:08,095 [7    ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKPMEK98T0
INFO  2024-07-02 14:51:01,803 [28   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\r\n    \"eGiftCode\": \"LYNKPMEK98T0\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:51:01,851 [28   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKPMEK98T0
ERROR 2024-07-02 14:51:14,573 [23   ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 14:51:16,559 [23   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKPMEK98T0
INFO  2024-07-02 14:52:02,358 [24   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\r\n    \"eGiftCode\": \"LYNKPMEK98T0\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:52:02,375 [24   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKPMEK98T0
ERROR 2024-07-02 14:52:15,890 [55   ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 14:52:15,912 [55   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKPMEK98T0
INFO  2024-07-02 14:53:58,854 [27   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\r\n    \"eGiftCode\": \"LYNKPMEK98T0\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:53:58,894 [27   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKPMEK98T0
ERROR 2024-07-02 14:54:11,604 [30   ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 14:54:17,933 [30   ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 14:54:48,993 [30   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKPMEK98T0
INFO  2024-07-02 14:58:53,089 [35   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\r\n    \"eGiftCode\": \"LYNKPMEK98T0\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 14:58:53,130 [35   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKPMEK98T0
ERROR 2024-07-02 14:59:03,266 [29   ] iftService - >> Issue calling Loyalty API: Error When Mark EVoucher as USED >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKPMEK98T0
ERROR 2024-07-02 14:59:24,704 [26   ] tyTokenJob - Job: RenewAccessToken error{"ClassName":"System.AggregateException","Message":"One or more errors occurred.","Data":null,"InnerException":{"StackTrace":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean allowHttp2, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.GetHttpConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":10060},"HelpLink":null,"Source":"System.Net.Http","HResult":-2147467259},"HelpURL":null,"StackTraceString":"   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)\r\n   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)\r\n   at System.Threading.Tasks.Task`1.get_Result()\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.LoginLoyalty(Nullable`1 tenantId, String baseURL, String userName, String password) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyHelper.cs:line 50\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.GetNewAccessTokenVPBankLoyaltyExchange() in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyHelper.cs:line 139\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.RenewAccessTokenVPBankExchangeCacheValue(IDistributedCache cache, TimeSpan delay, Boolean isForceRenew) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyHelper.cs:line 245\r\n   at AKC.MobileAPI.Service.CronServices.RefreshLoyaltyTokenJob.DoWork(CancellationToken cancellationToken) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\CronServices\\Jobs\\RefreshLoyaltyTokenJob.cs:line 89","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-**********,"Source":"System.Private.CoreLib","WatsonBuckets":null,"InnerExceptions":[{"StackTrace":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean allowHttp2, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.GetHttpConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":10060},"HelpLink":null,"Source":"System.Net.Http","HResult":-2147467259}]}
INFO  2024-07-02 15:01:30,070 [29   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\r\n    \"eGiftCode\": \"LYNKPMEK98T0\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 15:01:30,113 [29   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKPMEK98T0
ERROR 2024-07-02 15:01:40,058 [29   ] iftService - >> Issue calling Loyalty API: Error When Mark EVoucher as USED >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKPMEK98T0
INFO  2024-07-02 15:04:52,286 [29   ]            - Http Request Information: {"ControllerName":"LoyaltyEVoucherForTopUp","ActionName":"TopUp","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/topup-evoucher/topup","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"1A8tdbirTAhKrmOOOKLJjwBj3qp2\",\r\n    \"eGiftCode\": \"LYNKL1GDP934\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-02 15:04:52,306 [29   ] iftService - >> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKL1GDP934
ERROR 2024-07-02 15:05:07,501 [25   ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
ERROR 2024-07-02 15:05:07,526 [25   ] iftService -  Error when doing topup for evoucher - >> 1A8tdbirTAhKrmOOOKLJjwBj3qp2 - LYNKL1GDP934
ERROR 2024-07-02 15:05:14,099 [4    ] iftService - Exception occurs when call to RN >> {"Result":400,"Code":"AccountHasBeenBanned","Message":"BadRequest","MessageDetail":"AccountHasBeenBanned","ListMessages":null}
