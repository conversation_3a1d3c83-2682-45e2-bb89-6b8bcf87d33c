﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.Rewards;
using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.Service.Abstract.GiftStoreExtends;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Reward.Member;
using Newtonsoft.Json;
using AKC.MobileAPI.DTO.Reward.CashoutTransaction;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.Service.Helpers;
using AKC.MobileAPI.DTO.User;
using FirebaseAdmin.Auth;
using AKC.MobileAPI.Service.Abstract;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Reward;
using AKC.MobileAPI.DTO.Webstore;

namespace AKC.MobileAPI.Service.Loyalty
{
    public partial class LoyaltyGiftTransactionsService : BaseLoyaltyService, ILoyaltyGiftTransactionsService
    {
        private IRewardMemberService _rewardMemberService;
        private IRewardGiftRedeemTransactionService _rewardGiftRedeemTransactionService;
        private ILinkIdLoyaltyGiftStoreExtendsService _merchantGiftStoreService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILogger _logger;
        private readonly ILoyaltyRewardsService _rewardsService;
        private readonly IRewardCashoutTransactionService _rewardCashoutTransactionService;
        private readonly IUserService _userService;
        private readonly ILoyaltySecondaryCustomersService _loyaltySecondaryCustomersService;

        public LoyaltyGiftTransactionsService(
            IConfiguration configuration,
            IDistributedCache memoryCache,
            IRewardMemberService rewardMemberService,
            IRewardGiftRedeemTransactionService rewardGiftRedeemTransactionService,
            ILinkIdLoyaltyGiftStoreExtendsService merchantGiftStoreService,
            IExceptionReponseService exceptionReponseService,
            ILogger<LoyaltyGiftTransactionsService> logger,
            ILoyaltyRewardsService rewardsService,
            IRewardCashoutTransactionService rewardCashoutTransactionService,
            IUserService userService,
             ILoyaltySecondaryCustomersService loyaltySecondaryCustomersService
        ) : base(configuration, memoryCache)
        {
            _rewardMemberService = rewardMemberService;
            _rewardGiftRedeemTransactionService = rewardGiftRedeemTransactionService;
            _merchantGiftStoreService = merchantGiftStoreService;
            _exceptionReponseService = exceptionReponseService;
            _logger = logger;
            _rewardsService = rewardsService;
            _rewardCashoutTransactionService = rewardCashoutTransactionService;
            _userService = userService;
            _loyaltySecondaryCustomersService = loyaltySecondaryCustomersService;
        }

        public async Task<LoyaltyCreateRedeemTransactionOutput> CreateRedeemTransaction(LoyaltyCreateRedeemTransactionInput input, HttpContext context)
        {
            //Lấy từ cache hoặc gọi xuống loyalty để check category type của quà
            var categoryTypeCode = string.Empty;
            try
            {
                var keyOfCategoryType = "MobileApi_GiftCategoryType_" + input.GiftCode;
                categoryTypeCode = await GetCacheValueByKey(keyOfCategoryType);
                //Không lấy đc từ cache thì get từ loyalty
                if (string.IsNullOrEmpty(categoryTypeCode))
                {
                    var inputGetGiftCategoryType = new LoyaltyGetGiftCategoryTypeInput()
                    {
                        GiftCode = input.GiftCode
                    };
                    _logger.LogInformation("GET_GIFT_CATEGORY_TYPE_CODE start");
                    var getGiftCategoryTypeResult = await GetLoyaltyAsync<LoyaltyGetGiftCategoryTypeOutput>(LoyaltyApiUrl.GET_GIFT_CATEGORY_TYPE_CODE, inputGetGiftCategoryType);
                    _logger.LogInformation("GET_GIFT_CATEGORY_TYPE_CODE end");
                    if (getGiftCategoryTypeResult.Success)
                    {
                        categoryTypeCode = getGiftCategoryTypeResult.Result.GiftCategoryTypeCode;
                        await SetCacheByKeyValueExpiredInMinute(keyOfCategoryType, getGiftCategoryTypeResult.Result.GiftCategoryTypeCode, 30);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("GET_GIFT_CATEGORY_TYPE_CODE error: " + ex.Message);
                var result = new LoyaltyCreateRedeemTransactionOutput()
                {
                    Error = "CantGetGiftCategoryType",
                    Result = new LoyaltyCreateRedeemTransaction()
                    {
                        IsNotEnoughBalance = false,
                        SuccessedRedeem = false,
                        Messages = "Can't get gift category type"
                    }
                };
                return result;
            }
            // LINKID-3864 Đổi quà TopupPhone hoặc TopupData thì không cho nhập Quantity > 1
            if (!string.IsNullOrEmpty(categoryTypeCode) 
                && (categoryTypeCode.ToUpper() == GiftCategoryTypeCode.TopUpData || categoryTypeCode.ToUpper() == GiftCategoryTypeCode.TopUpPhone)
                && input.Quantity > 1)
            {
                var result = new LoyaltyCreateRedeemTransactionOutput()
                {
                    Error = "QuantityMustBeOne",
                    Result = new LoyaltyCreateRedeemTransaction()
                    {
                        IsNotEnoughBalance = false,
                        SuccessedRedeem = false,
                        Messages = "Quantity must be one with TopUpPhone or TopUpData"
                    }
                };

                return result;
            }
            // KHUONGDV2 - Linkid-308 - Check xem có Creditbalance ko, nếu có thì khôgn cho redeem
            var checkCreditBalanceOutput =
                await _rewardMemberService.GetCreditBalanceByMemberCode(new GetCreditBalanceByMemberCodeInput()
                {
                    MemberCode = input.MemberCode
                });
            if (checkCreditBalanceOutput is { Items: { } } && checkCreditBalanceOutput.Items.CreditBalance > 0 && input.TotalAmount > 0)
            {
                _logger.LogError(" >> CreateRedeemTransaction >> Member: " + input.MemberCode + " >> GiftCode: " + input.GiftCode + " >> Cannot Redeem Because User has CreditBalance");
                var result = new LoyaltyCreateRedeemTransactionOutput()
                {
                    Error = "BalanceNotEnough",
                    Result = new LoyaltyCreateRedeemTransaction()
                    {
                        IsNotEnoughBalance = true,
                        SuccessedRedeem = false,
                        Messages = "Token Balance is not enough to make this transaction"
                    }
                };
                return result;
            }
            // END LINKID-308
            _logger.LogInformation("CreateRedeemTransaction start");
            var merchantId = Convert.ToInt32(_configuration.GetSection("Reward" + MerchantNameConfig.VPID + ":MerchantId").Value);
            var checkTenantInternal = CheckTenantIsInternal(input.MerchantId);
            var tenantIdWithMerchant = 0;
            // redeem filter value ( thêm các value filter vào đây để tính phần cashback liên quan )
            var redeemFilterValues = new List<string>();
            redeemFilterValues.Add(input.GiftCode);
            // redeem filter value

            try
            {
                var merchantIdFromGiftCode = await GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
                {
                    GiftCode = input.GiftCode
                });
                _logger.LogInformation($"GetMerchantIdFromGiftCode({input.GiftCode})___Result:{JsonConvert.SerializeObject(merchantIdFromGiftCode)}");
                if (merchantIdFromGiftCode.Success && merchantIdFromGiftCode.Result != null)
                {
                    if (merchantIdFromGiftCode.Result.MerchantId.HasValue)
                    {
                        merchantId = merchantIdFromGiftCode.Result.MerchantId.Value;
                    }

                    if (merchantIdFromGiftCode.Result.BrandId.HasValue)
                    {
                        redeemFilterValues.Add(merchantIdFromGiftCode.Result.BrandId.Value.ToString());
                    }

                    if (merchantIdFromGiftCode.Result.VendorId.HasValue)
                    {
                        redeemFilterValues.Add(merchantIdFromGiftCode.Result.VendorId.Value.ToString());
                    }
                }
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);

                    var result = new LoyaltyCreateRedeemTransactionOutput()
                    {
                        Error = res.Code,
                        Result = new LoyaltyCreateRedeemTransaction()
                        {
                            Exception = res.Code,
                            IsNotEnoughBalance = false,
                            SuccessedRedeem = false,
                            Messages = res.Message
                        }
                    };
                    return result;
                }
                throw ex;
            }

            if (checkTenantInternal)
            {
                // KHUONGDV2: Check xem Mã quà được đổi có phải là mã quà nằm trong danh sách chỉ đc đổi 30d 1 lần ko
                try
                {
                    var list30dString = _configuration.GetSection("LoyaltyGiftCodeAllowBuyOneIn30").Value ?? "";
                    var listSplit = list30dString.Split(";").ToList();
                    if (listSplit.Count > 0 && listSplit.Contains(input.GiftCode))
                    {
                        var keyOf30D = "BE30SP_" + input.MemberCode + "_" + input.GiftCode;
                        var vl = await GetCacheValueByKey(keyOf30D);
                        if (vl == "TRUE")
                        {
                            // Không được phép đổi tiếp.
                            return new LoyaltyCreateRedeemTransactionOutput()
                            {
                                Error = "1022",
                                Result = new LoyaltyCreateRedeemTransaction()
                                {
                                    Exception = "1022",
                                    IsNotEnoughBalance = false,
                                    SuccessedRedeem = false,
                                    Messages = "Cannot Redeem At This Time"
                                }
                            };
                        }
                    }
                }
                catch (Exception)
                {
                    // EMPTY
                }
                // END CHECK BY KHUONGDV2
            }
            else
            {
                var merchantStoreConfig = LoyaltyHelper.GetConfigByMerchantId(input.MerchantId.Value);
                merchantId = merchantStoreConfig.DefaultRedeemMerchantId;
                tenantIdWithMerchant = merchantStoreConfig.TenantId;
            }
            var orderCode = genOrderCode(input.GiftCode, input.MemberCode);
            var cts = new CancellationTokenSource();

            //Nếu là đổi quà cashout => check điểm cash out, nếu ko => check token balance
            if (!string.IsNullOrEmpty(categoryTypeCode) && categoryTypeCode.ToUpper() == GiftCategoryTypeCode.CashOut)
            {
                _logger.LogInformation("GetBalanceAbleToCashOut start");
                var inputGetBalanceAbleToCashOut = new RewardValidateBalanceAbleToCashoutInput()
                {
                    MemberId = input.MemberId.HasValue ? input.MemberId.Value : 0,
                    MemberCode = input.MemberCode,
                    UserAddress = input.UserAddress
                };
                var resultBalance = await _rewardCashoutTransactionService.ValidateBalanceAbleToCashout(inputGetBalanceAbleToCashOut);
                _logger.LogInformation("GetBalanceAbleToCashOut end");
                _logger.LogInformation("GetGlobalSetting Start");
                var resultFee = await _rewardCashoutTransactionService.GetGlobalSetting();
                _logger.LogInformation("GetGlobalSetting End");
                var feeAmount = Math.Round((input.TotalAmount * resultFee.FeeAmountCashoutAmountRatio) / 100);
                feeAmount = feeAmount < resultFee.MinimumCashoutFeeAmount ? resultFee.MinimumCashoutFeeAmount : feeAmount;
                feeAmount = feeAmount > resultFee.MaximumCashoutFeeAmount ? resultFee.MaximumCashoutFeeAmount : feeAmount;
                var totalAmountToCashOut = input.TotalAmount + feeAmount;
                if (resultBalance == null || resultBalance.items == null || totalAmountToCashOut > resultBalance.items.AbleToCashoutBalance)
                {
                    var result = new LoyaltyCreateRedeemTransactionOutput()
                    {
                        Error = "BalanceAbleToCashOutNotEnough",
                        Result = new LoyaltyCreateRedeemTransaction()
                        {
                            IsNotEnoughBalance = true,
                            SuccessedRedeem = false,
                            Messages = "Token Balance Able To Cash out is not enough to make this transaction"
                        }
                    };

                    return result;
                }
            }
            else
            {
                _logger.LogInformation("GetBalanceMember start");
                var resultBalance = await _rewardMemberService.GetBalanceMember(input.MemberCode);
                _logger.LogInformation("GetBalanceMember end");
                if (resultBalance.TokenBalance < input.TotalAmount)
                {
                    //throw new ArgumentException("Token Balance is not enough to make this transaction");
                    var result = new LoyaltyCreateRedeemTransactionOutput()
                    {
                        Error = "BalanceNotEnough",
                        Result = new LoyaltyCreateRedeemTransaction()
                        {
                            IsNotEnoughBalance = true,
                            SuccessedRedeem = false,
                            Messages = "Token Balance is not enough to make this transaction"
                        }
                    };

                    return result;
                }
            }

            var request = new LoyaltyCreateRedeemTransactionDto()
            {
                Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"), //input.Date,
                Description = input.Description,
                GiftCode = input.GiftCode,
                MemberCode = input.MemberCode,
                Quantity = input.Quantity,
                TotalAmount = input.TotalAmount,
                TransactionCode = orderCode,
                MerchantIdRedeem = merchantId,
                FlashSaleProgramCode = input.FlashSaleProgramCode
            };

            // Kiểm tra quà bên loyalty và tạo đơn hàng redeem tạm. nếu tạo thành công thì mới process thanh toán (gọi sang reward)
            try
            {
                _logger.LogInformation("VERIFY_OR_CREATE_REDEEM_ORDER start");
                var checkTransactionPending = await PostLoyaltyAsync<VerifyAndCreateRedeemOrderOutput>(LoyaltyApiUrl.VERIFY_OR_CREATE_REDEEM_ORDER, request, context);
                _logger.LogInformation("VERIFY_OR_CREATE_REDEEM_ORDER end");
                if ((!checkTransactionPending.Success || (checkTransactionPending.Success && !checkTransactionPending.Result.IsSuccess)))
                {
                    var result = new LoyaltyCreateRedeemTransactionOutput()
                    {
                        Error = "1022",
                        Result = new LoyaltyCreateRedeemTransaction()
                        {
                            Exception = "1022",
                            IsNotEnoughBalance = false,
                            SuccessedRedeem = false,
                            Messages = "Cannot Redeem At This Time"
                        }
                    };
                    return result;
                }
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);

                    var result = new LoyaltyCreateRedeemTransactionOutput()
                    {
                        Error = res.Code,
                        Result = new LoyaltyCreateRedeemTransaction()
                        {
                            Exception = res.Code,
                            IsNotEnoughBalance = false,
                            SuccessedRedeem = false,
                            Messages = res.Message
                        }
                    };
                    return result;
                }
                throw ex;
            }

            var successPaymentToken = false;
            var originalTokenTransID = "";
            try
            {
                if (!string.IsNullOrEmpty(categoryTypeCode) && categoryTypeCode.ToUpper() == GiftCategoryTypeCode.CashOut)
                {
                    _logger.LogInformation("CreateRedeemCashOut start");
                    var inputCreateCashoutTransaction = new RewardCreateCashoutTransactionInput()
                    {
                        MemberId = input.MemberId.HasValue ? input.MemberId.Value : 0,
                        MemberCode = input.MemberCode,
                        MerchantId = merchantId,
                        TokenAmount = input.TotalAmount,
                        OrderCode = orderCode

                    };
                    var resultRedeemCashOutReward = await _rewardCashoutTransactionService.CreateCashoutTransaction(inputCreateCashoutTransaction);
                    if (resultRedeemCashOutReward.Result == 202)
                    {
                        successPaymentToken = true;
                        _logger.LogError($"Create redeem cashout reward with status 202 {JsonConvert.SerializeObject(resultRedeemCashOutReward)}");
                        GetErrorValidation("RedeemCashOutRewardTransactionStatus202", "Create redeem reward with status 202");
                    }
                    _logger.LogInformation("CreateRedeemCashOut end");
                    originalTokenTransID = resultRedeemCashOutReward.Items.TokenTransactionId;
                    successPaymentToken = true;
                }
                else
                {
                    _logger.LogInformation("CreateRedeem start");
                    var requestRedeem = new RewardCreateGiftRedeemTransactionRequest()
                    {
                        OrderCode = orderCode,
                        MerchantId = merchantId,
                        NationalId = input.MemberCode,
                        TotalRequestedAmount = input.TotalAmount,
                        RedeemFilterValue = redeemFilterValues.Count > 0 ? string.Join(";", redeemFilterValues) : null,
                    };
                    var resultRedeemReward = await _rewardGiftRedeemTransactionService.CreateRedeem(requestRedeem);
                    if (resultRedeemReward.Result == 202)
                    {
                        successPaymentToken = true;
                        _logger.LogError($"Create redeem reward with status 202 {JsonConvert.SerializeObject(resultRedeemReward)}");
                        GetErrorValidation("RedeemRewardTransactionStatus202", "Create redeem reward with status 202");
                    }
                    _logger.LogInformation("CreateRedeem end");

                    successPaymentToken = true;
                }

                var result = new LoyaltyCreateRedeemTransactionOutput()
                {
                    Result = new LoyaltyCreateRedeemTransaction()
                    {
                        SuccessedRedeem = false,
                    }
                };
                // Nếu là tenant internal (LinkID) thì redeem như cũ, ngược lại thì redeem sang merchant kho quà
                if (checkTenantInternal)
                {
                    _logger.LogInformation("GIFTTRANSACTION_CREATEREDEEMTRANSACTION start");
                    result = await PostLoyaltyAsync<LoyaltyCreateRedeemTransactionOutput>(LoyaltyApiUrl.GIFTTRANSACTION_CREATEREDEEMTRANSACTION, request, context);
                    _logger.LogInformation("GIFTTRANSACTION_CREATEREDEEMTRANSACTION end");
                }
                else
                {
                    _logger.LogInformation("CreateRedeemMerchantTransaction start");
                    result = await _merchantGiftStoreService.CreateRedeemMerchantTransaction(tenantIdWithMerchant, request);
                    _logger.LogInformation("CreateRedeemMerchantTransaction end");
                }
                if (!string.IsNullOrWhiteSpace(result.Result.Exception) || !result.Result.SuccessedRedeem)
                {

                    if (!result.Result.Timeout)
                    {
                        if (!string.IsNullOrEmpty(categoryTypeCode) && categoryTypeCode.ToUpper() == GiftCategoryTypeCode.CashOut)
                        {
                            _logger.LogInformation("retryRevertCashOutToken start");
                            var requestRevert = new RewardRevertCashoutTransactionInput()
                            {
                                MerchantId = merchantId,
                                MemberCode = input.MemberCode,
                                TokenTransactionId = originalTokenTransID
                            };

                            await retryRevertCashOutToken(requestRevert);
                            _logger.LogInformation("retryRevertCashOutToken end");
                        }
                        else
                        {
                            _logger.LogInformation("retryRevertToken start");
                            var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
                            {
                                MerchantId = merchantId,
                                NationalId = input.MemberCode,
                                OrderCode = orderCode,
                                TokenAmount = input.TotalAmount,
                            };

                            await retryRevertToken(requestRevert);
                            _logger.LogInformation("retryRevertToken end");
                        }
                    }
                }

                if (result.Success && result.Result.SuccessedRedeem)
                {
                    try
                    {
                        _logger.LogInformation("GetSection LoyaltyGiftCodeAllowBuyOneIn30 start");
                        // Check xem Mã quà được đổi có phải là mã quà nằm trong danh sách chỉ đc đổi 30d 1 lần ko
                        var list30d = _configuration.GetSection("LoyaltyGiftCodeAllowBuyOneIn30").Value ?? "";
                        var listSplit = list30d.Split(";").ToList();
                        if (list30d.Length > 0 && listSplit.Contains(input.GiftCode))
                        {
                            var keyOf30D = "BE30SP_" + input.MemberCode + "_" + input.GiftCode;
                            await SetCacheByKeyValue(keyOf30D, "TRUE", 30);
                        }
                        _logger.LogInformation("GetSection LoyaltyGiftCodeAllowBuyOneIn30 end");
                    }
                    catch (Exception e)
                    {
                        // EMPTY
                    }
                    // Ghi nhận action cho các hành động nạp viễn thông IMEDIA
                    if (!string.IsNullOrEmpty(result.Result.ActionCode) && !string.IsNullOrEmpty(result.Result.ActionFilter))
                    {
                        try
                        {
                            var inputActionRequest = new LoyaltyInputActionInput()
                            {
                                Actions = new List<ActionRequestDto>()
                                {
                                    new ActionRequestDto()
                                    {
                                        TransactionCode = orderCode,
                                        Type = 1,
                                        OriginalTransactionCode = "",
                                        ActionCode = result.Result.ActionCode,
                                        ActionFilter = new ActionListValueFilterReward()
                                        {
                                            ListCodeFilter = result.Result.ActionFilter,
                                        },
                                        MemberCode = input.MemberCode,
                                        Value = (int)Decimal.Truncate(input.TotalAmount),
                                        ActionTime = DateTime.UtcNow,
                                        MlmApplied = false,
                                        MLMDealerChannel = null,
                                        MLMDistributionChannel = null,
                                        ReferenceCode = null,
                                        Tag = "",
                                        RankCode = ""
                                    }
                                }
                            };
                            // Send to LinkID
                            await _rewardsService.InputAction(inputActionRequest);
                        }
                        catch (Exception e)
                        {
                            _logger.LogError(" >> Error while sending InputAction to LinkId tenant for Topup Transaction");
                            _logger.LogError(e.StackTrace);
                        }
                    }
                }
                return result;
            }
            catch (WebException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError")
                    {
                        if (!string.IsNullOrEmpty(categoryTypeCode) && categoryTypeCode.ToUpper() == GiftCategoryTypeCode.CashOut)
                        {
                            _logger.LogInformation("retryRevertCashOutToken start");
                            var requestRevert = new RewardRevertCashoutTransactionInput()
                            {
                                MerchantId = merchantId,
                                MemberCode = input.MemberCode,
                                TokenTransactionId = originalTokenTransID
                            };

                            await retryRevertCashOutToken(requestRevert);
                            _logger.LogInformation("retryRevertCashOutToken end");
                        }
                        else
                        {
                            _logger.LogInformation("retryRevertToken start");
                            var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
                            {
                                MerchantId = merchantId,
                                NationalId = input.MemberCode,
                                OrderCode = orderCode,
                                TokenAmount = input.TotalAmount,
                            };

                            await retryRevertToken(requestRevert);
                            _logger.LogInformation("retryRevertToken end");
                        }
                    }
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = res.Code == "SystemError" ? "504" : res.Code,
                        ErrorMessage = res.Code != "SystemError" ? res.Message?.ToString() : res.MessageDetail?.ToString(),
                    }, successPaymentToken);
                    throw ex;
                }
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError")
                    {
                        if (!string.IsNullOrEmpty(categoryTypeCode) && categoryTypeCode.ToUpper() == GiftCategoryTypeCode.CashOut)
                        {
                            _logger.LogInformation("retryRevertCashOutToken start");
                            var requestRevert = new RewardRevertCashoutTransactionInput()
                            {
                                MerchantId = merchantId,
                                MemberCode = input.MemberCode,
                                TokenTransactionId = originalTokenTransID
                            };

                            await retryRevertCashOutToken(requestRevert);
                            _logger.LogInformation("retryRevertCashOutToken end");
                        }
                        else
                        {
                            _logger.LogInformation("retryRevertToken start");
                            var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
                            {
                                MerchantId = merchantId,
                                NationalId = input.MemberCode,
                                OrderCode = orderCode,
                                TokenAmount = input.TotalAmount,
                            };

                            await retryRevertToken(requestRevert);
                            _logger.LogInformation("retryRevertToken end");
                        }
                    }
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = res.Code == "SystemError" ? "504" : res.Code,
                        ErrorMessage = res.Code != "SystemError" ? res.Message?.ToString() : res.MessageDetail?.ToString(),
                    }, successPaymentToken);
                    throw ex;
                }
                if (!cts.Token.IsCancellationRequested)
                {
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = "408",
                        ErrorMessage = "Timed Out with: " + ex.Message,
                    }, successPaymentToken);
                    throw new Exception("Timed Out with: ", ex);
                }
                else
                {
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = "504",
                        ErrorMessage = "Cancelled for some other reason: " + ex.Message,
                    }, successPaymentToken);
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError")
                    {
                        if (!string.IsNullOrEmpty(categoryTypeCode) && categoryTypeCode.ToUpper() == GiftCategoryTypeCode.CashOut)
                        {
                            _logger.LogInformation("retryRevertCashOutToken start");
                            var requestRevert = new RewardRevertCashoutTransactionInput()
                            {
                                MerchantId = merchantId,
                                MemberCode = input.MemberCode,
                                TokenTransactionId = originalTokenTransID
                            };

                            await retryRevertCashOutToken(requestRevert);
                            _logger.LogInformation("retryRevertCashOutToken end");
                        }
                        else
                        {
                            _logger.LogInformation("retryRevertToken start");
                            var requestRevert = new RewardRevertGiftRedeemTransactionRequest()
                            {
                                MerchantId = merchantId,
                                NationalId = input.MemberCode,
                                OrderCode = orderCode,
                                TokenAmount = input.TotalAmount,
                            };

                            await retryRevertToken(requestRevert);
                            _logger.LogInformation("retryRevertToken end");
                        }
                    }
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = res.Code == "SystemError" ? "504" : res.Code,
                        ErrorMessage = res.Code != "SystemError" ? res.Message?.ToString() : res.MessageDetail?.ToString(),
                    }, successPaymentToken);
                    throw ex;
                }
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    if (res.Code == "RedeemRewardTransactionStatus202")
                    {
                        await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                        {
                            TransactionCode = request.TransactionCode,
                            ErrorCode = "202",
                            ErrorMessage = "" + res.MessageDetail,
                        }, successPaymentToken);
                        throw new Exception("" + res.MessageDetail);
                    }
                }
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = request.TransactionCode,
                    ErrorCode = "504",
                    ErrorMessage = "Cancelled for some other reason: " + ex.Message,
                }, successPaymentToken);
                throw ex;
            }

        }
        public async Task<LoyaltyGetAllWithEGiftOutput> GetAllWithEGift(LoyaltyGetAllWithEGiftInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGetAllWithEGiftOutput>(LoyaltyApiUrl.GIFTTRANSACTION_GETALL_WITH_EGIFT_ALL_MERCHANT, input);
        }
        // Phien ban tu Common API gateway
        public async Task<MerchantGiftTransactionHistoryOutputDto> GiftTransactionHistory(MerchantGiftTransactionDetailInputDto input)
        {
            return await GetLoyaltyAsync<MerchantGiftTransactionHistoryOutputDto>(LoyaltyApiUrl.MERCHANT_GIFT_GET_ORDER_LIST, input);
        }

        public async Task<GetSingleCreateGiftRedeemTransactionOutput> GetSingleCreateGiftRedeemTransaction(
            GetSingleCreateGiftRedeemTransactionInput input)
        {
            return await GetLoyaltyAsync<GetSingleCreateGiftRedeemTransactionOutput>("services/app/GiftTransactions/GetSingleCreateGiftRedeemTransaction", input);
        }

        public async Task<LoyaltyGiftGetMerchantIdFromGiftCodeOutput> GetMerchantIdFromGiftCode(LoyaltyGiftGetMerchantIdFromGiftCodeInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGiftGetMerchantIdFromGiftCodeOutput>(LoyaltyApiUrl.GET_MERCHANT_ID_FROM_GIFT_CODE, input);
        }

        public bool CheckTenantIsInternal(int? merchantId)
        {
            var tenantIdInternal = Convert.ToInt32(_configuration.GetSection("RewardVPID:MerchantId").Value);
            if (!merchantId.HasValue || tenantIdInternal == merchantId.Value || merchantId.Value <= 0)
            {
                return true;
            }
            return false;
        }

        private async Task<Boolean> revertToken(RewardRevertGiftRedeemTransactionRequest request)
        {
            try
            {
                await _rewardGiftRedeemTransactionService.RevertRedeem(request);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task<Boolean> revertCashOutToken(RewardRevertCashoutTransactionInput request)
        {
            try
            {
                await _rewardCashoutTransactionService.RevertCashoutTransaction(request);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task UpdateErrorWhenCreateRedeem(UpdateErrorWhenCreateRedeemPayment input, bool successPaymentToken)
        {
            try
            {
                if (!successPaymentToken)
                {
                    input.Status = "Rejected";
                }
                await PostLoyaltyAsync<UpdateErrorWhenCreateRedeemPaymentOutput>(LoyaltyApiUrl.UPDATE_ERROR_WHEN_CREATE_REDEEM, input);
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Call UPDATE_ERROR_WHEN_CREATE_REDEEM fail: " + ex.Message);
            }
        }

        private async Task retryRevertToken(RewardRevertGiftRedeemTransactionRequest request)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await revertToken(request);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
            //var rejectEvent = new GiftRedemptionRejectedEvent
            //{
            //    MemberCode = request.NationalId,
            //    MerchantId = request.MerchantId,
            //    OrderCode = request.OrderCode,
            //    TokenAmount = request.TokenAmount,
            //};
            // _rabbitMQManager.Publish(rejectEvent, _rabbitExchange.EventExchange, ExchangeTypes.Topic, CommonConstants.GiftRedemptionRejectRouteKey);
            //return Task.FromResult(0);
        }

        private async Task retryRevertCashOutToken(RewardRevertCashoutTransactionInput request)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await revertCashOutToken(request);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private RewardDataExceptionResponse GetErrorValidation(string errorCode, string errorMessage)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = errorMessage
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }

        private string genOrderCode(string orderCode, string memberCode)
        {
            return LoyaltyHelper.GenTransactionCode(orderCode + memberCode + DateTime.Now.Ticks);
        }

        public async Task<GetMemberCodeOutput> UserVerifying(GetMemberCodeInput input)
        {
            return await PostLoyaltyAsync<GetMemberCodeOutput>(LoyaltyApiUrl.GIFTTRANSACTION_USER_VERIFYING, input);
        }
        public async Task<GiftTransferOutput> GiftTransfer(UpdateGiftTransactionsForTransferInput input)
        {
            var UrlViewGift = _configuration.GetSection("UrlViewGift").Value;
            // check member exit 
            // if No => create member
            if (!string.IsNullOrEmpty(input.Phone))
            {
                var memberReceiver = _rewardMemberService.GetFriendName(new FindOtherUserByPhoneNumberInput { PhoneNumber = input.Phone });
                if (memberReceiver.Result.Result == null)
                {
                    var registerMeber = RegisterMember(new RegisterMemberInput { Phone = input.Phone, ReferralCode = input.OwnerPhone });
                    input.ReceiverCode = registerMeber.Result.MemberCode;
                    input.isReceiverExit = false;
                }
            }
            var result = await PostLoyaltyAsync<GiftTransferOutput>(LoyaltyApiUrl.GIFTTRANSACTION_GIFT_TRANSFER, input);
            result.URLTransaction = $"{UrlViewGift}?code={input.GiftRedeemTransactionCode}";
            return result;
        }

        public async Task<LoyaltyCreateRedeemTransactionOutput> PostLoyaltyRedeem(LoyaltyCreateRedeemTransactionDto input, HttpContext context)
        {
            return await PostLoyaltyAsync<LoyaltyCreateRedeemTransactionOutput>(LoyaltyApiUrl.GIFTTRANSACTION_CREATEREDEEMTRANSACTION, input, context);
        }
        public async Task<GiftTransactionUpdateLegalIdOutput> UpdateRecipientLegalId(GiftTransactionUpdateLegalIdInput input)
        {
            return await PutLoyaltyAsync<GiftTransactionUpdateLegalIdOutput>(LoyaltyApiUrl.GIFTTRANSACTION_UPDATE_RECIPIENT_LEGALID, input);
        }
        public async Task<LoyaltyGetAllWithTopupPhoneOutput> GetAllWithTopupPhone(LoyaltyGetAllWithTopupPhoneInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGetAllWithTopupPhoneOutput>(LoyaltyApiUrl.GIFTTRANSACTION_GETALL_WITH_TOPUP_PHONE, input);
        }
        public async Task<LoyaltyGetAllWithTopupPhone_v1Output> GetAllWithTopupPhone_v1(LoyaltyGetAllWithTopupPhoneInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGetAllWithTopupPhone_v1Output>(LoyaltyApiUrl.GIFTTRANSACTION_GETALL_WITH_TOPUP_PHONE_V1, input);
        }
        public async Task<GetCustomerInfoFromTransactionOutput> GetCustomerInfoFromTransaction(GetCustomerInfoFromTransactionInput input)
        {
            return await GetLoyaltyAsync<GetCustomerInfoFromTransactionOutput>(LoyaltyApiUrl.GIFTTRANSACTION_GET_CUSTOMER_INFO_FROM_TRANSACTION, input);
        }
        public async Task<LoyaltyResponse<GetAddressBy3IdsOutput>> GetAddressBy3Ids(GetAddressBy3IdsInput input)
        {
            return await GetLoyaltyAsync<LoyaltyResponse<GetAddressBy3IdsOutput>>(LoyaltyApiUrl.GET_ADDRESS_BY_3IDS,
                input);
        }
        public async Task GetErrorFromExeption(Exception ex, string transactionCode, bool successPaymentToken)
        {
            if (ex.GetType() == typeof(LoyaltyException))
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = transactionCode,
                    ErrorCode = res.Code == "SystemError" ? "504" : res.Code,
                    ErrorMessage = res.Code != "SystemError" ? res.Message?.ToString() : res.MessageDetail?.ToString(),
                }, successPaymentToken);
                CommonHelper.GetErrorValidation(CommonHelper.GetCodeRedeemFromCode(res.Code), CommonHelper.GetMessageRedeemFromCode(res.Code));
            }
        }
        private async Task<RegisterMemberOutput> RegisterMember(RegisterMemberInput input)
        {
            RegisterMemberOutput item = new RegisterMemberOutput();
            _userService.checkPhone(input.Phone);
            var auth = FirebaseAuth.DefaultInstance;
            UserRecord user = null;
            try
            {
                user = await auth.CreateUserAsync(new UserRecordArgs()
                {
                    PhoneNumber = input.Phone
                });
            }
            catch (FirebaseAuthException ex)
            {
                _logger.LogError("Get firebase error from phone number", ex);
                if (ex.AuthErrorCode == AuthErrorCode.PhoneNumberAlreadyExists)
                {
                    _userService.getExceptionCustome("DuplicatePhoneNumber", "Duplicate phone number request");
                }
                _userService.getExceptionCustome("SystemError", "System error");
            }
            catch (Exception ex)
            {
                _logger.LogError("Get firebase error from phone number", ex);
                _userService.getExceptionCustome("SystemError", "System error");
            }
            var hasCreateMember = true;
            var memberCode = "";
            var isDeleteUserFirebase = false;

            try
            {
                var resultcreatemember = new RewardMemberCreateOutput();
                var memberReward = await _rewardMemberService.VerifyProviderIdByPhoneNumber(
                     new VerifyProviderIdByPhoneNumberRequest()
                     {
                         ProviderName = "Firebase",
                         ProviderId = user.Uid,
                         PhoneNumber = input.Phone
                     }
                 );
                memberCode = memberReward.Items.MemberCode;
                // If cannot member at loyalty then create member, else create return member code
                if (!memberReward.Items.MemberExist)
                {
                    try
                    {
                        var res = await _userService.VerifyOrCreateMember(new CreateUserMobileDTO { PhoneNumber = input.Phone, Name = string.Empty, Address = string.Empty, Gender = "O" }, user.Uid);
                        memberCode = res.Result.MemberCode;
                        hasCreateMember = true;
                    }
                    catch (Exception exCreate)
                    {
                        await auth.DeleteUserAsync(user.Uid);
                        isDeleteUserFirebase = true;
                        var res = await _exceptionReponseService.GetExceptionRewardReponse(exCreate);
                        if (exCreate.GetType() == typeof(RewardException))
                        {
                            throw exCreate;
                        }
                        _logger.LogError("Fail to create member for reward", exCreate);
                        _userService.getExceptionCustome("SystemError", "System error");
                    }
                }
                else
                {
                    var updateProvider = new VerifyProviderIdByPhoneNumberRequest()
                    {
                        ProviderName = "Firebase",
                        ProviderId = user.Uid,
                        PhoneNumber = input.Phone
                    };
                    await _rewardMemberService.UpdateProvider(updateProvider);
                }
            }
            catch (Exception exCreate)
            {
                // Nếu chưa xóa user firebase ở catch đầu tiên thì mới xóa
                if (!isDeleteUserFirebase)
                {
                    await auth.DeleteUserAsync(user.Uid);
                }
                if (exCreate.GetType() == typeof(RewardException))
                {
                    throw exCreate;
                }
                _logger.LogError("Fail to create member for reward", exCreate);
                _userService.getExceptionCustome("SystemError", "System error");
            }

            var customToken = await auth.CreateCustomTokenAsync(user.Uid);
            item.MemberCode = memberCode;
            item.Message = "Success";

            if (hasCreateMember && !string.IsNullOrWhiteSpace(input.ReferralCode))
            {
                try
                {
                    await _loyaltySecondaryCustomersService.VerifyReferralCode(new LoyaltyVerifyReferralCodeInput()
                    {
                        NationalId = user.Uid,
                        ReferralCode = input.ReferralCode,
                        DistributionChannelList = null,
                        ReferenceAmount = 0
                    });
                }
                catch { }
            }

            return new RegisterMemberOutput()
            {
                Message = "Success",
                Result = 200,
                MemberCode = memberCode,
            };
        }

        public async Task<LoyaltyResponse<GetListHistoryReferralOutput>> GetListHistoryReferral(GetListHistoryReferralInput input)
        {
            var result = await GetLoyaltyAsync<LoyaltyResponse<GetListHistoryReferralOutput>>(LoyaltyApiUrl.GetListHistoryReferral, input);
            return result;
        }

        public async Task<LoyaltyResponse<GetTransactionInforReferralOutput>> GetTransactionInforReferral(GetTransactionInforReferralInput input)
        {
            var result = await GetLoyaltyAsync<LoyaltyResponse<GetTransactionInforReferralOutput>>(LoyaltyApiUrl.GetTransactionInforReferral, input);
            return result;
        }
        
        
        
        public async Task<VerifyAndCreateRedeemOrderOutput> VerifyOrCreateRedeemOrder(MerchantGiftCreateRedeemInWebInputDto input)
        {
            var result = await PostLoyaltyAsync<VerifyAndCreateRedeemOrderOutput>(LoyaltyApiUrl.VERIFY_OR_CREATE_REDEEM_ORDER, input);
            return result;
        }

        public async Task<MerchantGiftCreateRedeemOutputDto> CreateRedeemMerchantGift(MerchantGiftCreateRedeemInWebInputDto input)
        {
            return await PostLoyaltyAsync<MerchantGiftCreateRedeemOutputDto>(LoyaltyApiUrl.MERCHANT_GIFT_CREATE_REDEEM, input);
        }
        
        public async Task retryWebstoreRevertToken(RewardMemberRevertRedeemInput request, string url = null)
        {
            var retryNum = 5;
            while (retryNum != 0)
            {
                var result = await revertToken(request, url);
                if (retryNum != 5)
                {
                    await Task.Delay(1500);
                }
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    retryNum--;
                }
            }
        }

        private async Task<bool> revertToken(RewardMemberRevertRedeemInput request, string url = null)
        {
            try
            {
                await _rewardGiftRedeemTransactionService.RevertRedeen_V2(request, url);
                return true;
            }
            catch
            {
                return false;
            }
        }

    }
}
