﻿using System;
using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class LoyaltyGetAllForSearchBarOutPut
    {
        public SearchBarResult Result { get; set; }

        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }


        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }

    }

    public class SearchBarResult
    {
        public int TotalCount { get; set; }

        public List<SearchBarGetAllForview> Items { get; set; }
    }

    public class SearchBarGetAllForview
    {
        public SearchBarDto SearchBar { get; set; }
    }

    public class SearchBarDto
    {
        public int Id { get; set; }
        public string Keyword { get; set; }
        public int DisplayOrder { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public DateTime? CreationTime { get; set; }
        public long? CreatorUserId { get; set; }
        public string CreatorUserName { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public long? LastModifierUserId { get; set; }
        public string LastModifierUserName { get; set; }
    }

}
