﻿using System;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.ThirdParty.VPBankS
{
    public class GetCifCodeByIdCardInput
    {
        [Required] public string IdCard { get; set; }
        public string PhoneNumber { get; set; }
    }

    public class VerifyIdCardOutput
    {
        public string OtpCode { get; set; }
    }

    public class VerifyIdCardInput
    {
        public string IdCard { get; set; }

        public string PhoneNumber { get; set; }
    }
    public class VerifyOTPOutputDto
    {
        public string Cif { get; set; }
        public string MemberCode { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Email { get; set; }
        public string Gender { get; set; }
        public DateTime? Dob { get; set; }
        public string Type { get; set; }
        public string RegionCode { get; set; }
        public string FullRegionCode { get; set; }
        public string MemberTypeCode { get; set; }
        public string FullMemberTypeCode { get; set; }
        public string ChannelType { get; set; }
        public string FullChannelTypeCode { get; set; }
        public string RankTypeCode { get; set; }
        public string StandardMemberCode { get; set; }
        public string IdCard { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public string Phone { get; set; }
        public string Avatar { get; set; }
        public string Segment { get; set; }
        public string VipType { get; set; }
    }
}