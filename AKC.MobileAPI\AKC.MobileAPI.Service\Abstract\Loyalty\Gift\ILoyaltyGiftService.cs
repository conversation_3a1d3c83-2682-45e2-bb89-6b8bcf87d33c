﻿using AKC.MobileAPI.DTO.Loyalty.Gift;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.TopUp;
using AKC.MobileAPI.DTO.Loyalty.GiftCategories;
using AKC.MobileAPI.DTO.Loyalty.GiftUsageAddress;
using AKC.MobileAPI.DTO.Loyalty.GiftSearch;
using AKC.MobileAPI.DTO.Webstore;

namespace AKC.MobileAPI.Service.Abstract.Loyalty.Gift
{
    public interface ILoyaltyGiftService
    {
        Task<LoyaltyGiftCategoryGetAllOutput> GetAll(LoyaltyGiftCategoryGetAllInput input);
        Task<LoyaltyGetAllForCategoryOutPut> GetAllForCategory(LoyaltyGetAllForCategoryInput input);
        Task<LoyaltyGiftGetAllWithImageOutput> GetAllWithImage(LoyaltyGiftGetAllWithImageInput input);
        Task<LoyaltyGiftGetAllWithImageOutput> GetAllWithImageByMemberCode(LoyaltyGetAllGiftGroupByMemberInput input);
        Task<LoyaltyGiftGetAllByMemberCodeOutput> GetAllByMemberCode(LoyaltyGiftGetAllByMemberCodeInput input);
        Task<LoyaltyGetGiftByByMemberCodeOutput> GetGiftByMemberCode(LoyaltyGetGiftByByMemberCodeInput input);
        Task<GetRecommendedGiftsOutput> GetRecommendedGifts(GetRecommendedGiftsInput input);
        Task<GetGiftCategoryAndInfoForView> GetAllGiftCategoriesAndInfo_V1(GetAllGiftCategoriesAndInfoInput input);
        Task<GetGiftCategoryAndInfoForView> GetAllGiftCategoriesAndInfo(GetAllGiftCategoriesAndInfoInput input);
        Task<GiftInforsOutPut> GetAllInfors(GiftInforsInput input);
        Task<GiftInforsOutPut> GetForHomePageNoMemberCode(GetForHomePageNoMemberCodeInput input);
        Task<GetByIdAndRelatedGiftOutput> GetByIdAndRelatedGift(GetByIdAndRelatedGiftInput input);
        Task<GetAllEffectiveCategoryOutput> GetAllEffectiveCategory_v1(GetAllEffectiveCategoryInput input);
        Task<GetRecommendedCategoriesOutput> GetRecommendedCategories(GetRecommendedCategoriesInput input);
        Task<GetAllEffectiveCategoryGroupByBrandOutput> GetAllEffectiveCategoryGroupByBrand(GetAllEffectiveCategoryGroupByBrandInput input);
        Task<GetAllEffectiveCategoryOutput> GetAllEffectiveCategory(GetAllEffectiveCategoryInput input);
        Task<LoyaltyGetWishlistByMemberOutput> GetWishlistByMember(LoyaltyGetWishlistByMemberInput input);
        Task<LoyaltyGetBrandWishlistByMemberOutput> GetBrandWishlistByMember(LoyaltyGetBrandWishlistByMemberInput input);
        Task<LoyaltyUpdateWishlistOutput> UpdateWishlist(LoyaltyUpdateWishlistInput input);
        Task<GetAllByMemberCodeOutput> GetAllByMemberCode(GetAllByMemberCodeGiftCategoriesInput input);
        Task<GetAllForCategoryByMemberCodeOutput> GetAllForCategoryByMemberCode(GetAllByMemberCodeGiftInforsInput input);

        Task<CheckMemberOutput> CheckMemberCodeBlocked(CheckMemberInput input);

        Task<CheckVoucherOutput> CheckEVoucherCode(CheckVoucherInput input);
        Task<RedeemVoucherOutput> UseEVoucherCode(CheckVoucherInput input);
        Task<RevertVoucherOutput> RevertEVoucherCode(RevertEVoucherInput input);
        Task<GiftCategoriesForView> GiftListCategories(GetAllGiftCategoriesAndInfoInput input);
        Task<GiftCategoriesForView> GiftListCategories_v1(GetAllGiftCategoriesAndInfoInput input);
        Task<GiftListCategoriesInTwoRowsOutput> GiftListCategoriesInTwoRows(GiftListCategoriesInTwoRowsInput input);
        Task<GiftAllInforOutput> GetGiftAllInfors(GiftInforsInput input);
        Task<GetGiftGroupForHomePageV1Dot1Output> GetGiftGroupForHomepageV1dot1(GetGiftGroupForHomePageV1Dot1Input input);
        Task<GiftInforByBrandOutput> GetGiftsByBrand(GiftInforByBrandInput input);
        Task<GiftUsageAddressOutput> GetListGiftUsageAddress(GiftUsageAddressInput input);

        Task<GetGiftInforByGiftGroupOutput> GetGiftbyGroupType(GetGiftGroupByGroupTypeInput input);

        Task<GetAllEffectiveCategoryOutput> GetAllEffectiveCategory_TopupPhone(GetAllEffectiveCategoryInput input);

        Task<GetGiftGroupByCateOutputForView> GetGiftByCateType_V2(GetGiftByCateTypeInput input);


        Task<SearchGiftAndGiftGroupAndBrandForViewOutput> SearchGiftAndGiftGroupAndBrand(SearchInput input);

        Task<SuggestOutputView> GetListSuggest(SuggestInput input);

        Task<CreateCSTicketOutput> CreateCSTicket(CreateCSTicketInput input);

        Task<GetListCSTicketOutput> GetListCSTicket(GetListCSTicketInput input);
        Task<GetOneCSTicketOutput> GetOne(GetOneCSTicketInput input);
        Task<SyncStringeeXOutput> SyncStringeeX(SyncStringeeXInput input);

        Task<ReactCSTicketOutput> ReactCSTicket(ReactCSTicketDTO input);

        Task<GetListThirdpartyVendorOutput> GetListThirdPartyVendors(GetListThirdpartyVendorInput input);

        Task<GetMiniAppOutput> GetMiniApps(GetMiniAppInput input);
        
        Task<CategoriesResponse> GetAllWithoutMemberCodeForHomePage(object input);
        Task<GiftListResponse> GetAllGiftWithoutMemberCode(object input);
        Task<GetByIdAndRelatedGiftOutput> GetGiftDetailWithoutMemberCode(object input);
    }
}
