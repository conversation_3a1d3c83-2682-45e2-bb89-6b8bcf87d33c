﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.GiftStoreExtends
{
    public class GiftStoreVendorGetListOrderInput
    {
        [Required]
        public int MerchantId { get; set; }
        [Required]
        public string MemberCode { get; set; }
        public string Sorting { get; set; }
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
    }

    public class GiftStoreVendorGetListOrderDto
    {
        public string MemberCode { get; set; }
        public string Sorting { get; set; }
        public int? SkipCount { get; set; }
        public int? MaxResultCount { get; set; }
    }
}
