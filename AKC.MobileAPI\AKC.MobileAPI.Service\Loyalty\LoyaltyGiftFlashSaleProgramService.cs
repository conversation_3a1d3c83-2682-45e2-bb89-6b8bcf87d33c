﻿using AKC.MobileAPI.DTO.Loyalty.GiftFlashSaleProgram;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyGiftFlashSaleProgramService : BaseLoyaltyService, ILoyaltyGiftFlashSaleProgramService
    {
        public LoyaltyGiftFlashSaleProgramService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<GiftDetailFlashSaleProgramOutput> GetGiftDetailFlashSaleProgram(GiftDetailFlashSaleProgramInput input)
        {
            return await GetLoyaltyAsync<GiftDetailFlashSaleProgramOutput>(LoyaltyApiUrl.GET_GIFT_DETAIL_FLASH_SALE_PROGRAM, input);
        }

        public async Task<GiftFlashSaleProgramForViewOutput> GetAllFlashSaleProgram(GetAllFlashSaleProgramInput input)
        {
            return await PostLoyaltyAsync<GiftFlashSaleProgramForViewOutput>(LoyaltyApiUrl.GET_LIST_FLASH_SALE_PROGRAM, input);
        }

        public async Task<GiftFlashSaleProgramOutput> ListGiftByFlashSaleProgram(GiftFlashSaleProgramInput input)
        {
            return await PostLoyaltyAsync<GiftFlashSaleProgramOutput>(LoyaltyApiUrl.GET_LIST_GIFT_BY_FLASH_SALE_PROGRAM, input);
        }
    }
}
