﻿using AKC.MobileAPI.DTO.Loyalty.Wheel;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{

    [Route("api/mini-app/wheel")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyWheelController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyWheelService _wheelService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        public LoyaltyWheelController(
            ILogger<LoyaltyWheelController> logger,
            ILoyaltyWheelService wheelService,
            IExceptionReponseService exceptionReponseService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _wheelService = wheelService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
        }

        [HttpGet]
        [Route("get-list")]
        public async Task<ActionResult<GetListWheelOutput>> GetAll([FromQuery] GetListWheelInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _wheelService.GetListWheel(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Wheel GetAll Error - " + JsonConvert.SerializeObject(ex));

                var cooked = new LoyaltyResponeOutput<GetListWheelOutput>()
                {
                    Success = false, Result = null, Error = res.Message ??  "Error Happened", TargetUrl = "", UnAuthorizedRequest = false, __abp = null
                };
                return StatusCode(400, cooked);
            }
        }
        [HttpGet]
        [Route("get-list-system-wheel")]
        public async Task<ActionResult<GetListWheelOutput>> GetSystemWheels([FromQuery] GetListWheelInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _wheelService.GetSystemWheels(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Wheel GetAll Error - " + JsonConvert.SerializeObject(ex));

                var cooked = new LoyaltyResponeOutput<GetListWheelOutput>()
                {
                    Success = false, Result = null, Error = res.Message ??  "Error Happened", TargetUrl = "", UnAuthorizedRequest = false, __abp = null
                };
                return StatusCode(400, cooked);
            }
        }
        [HttpGet]
        [Route("get-spin-history")]
        public async Task<ActionResult<GetSpinHistoryOutput>> GetSpinHistory([FromQuery] GetSpinHistoryInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _wheelService.GetSpinHistory(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Wheel GetAll Error - " + JsonConvert.SerializeObject(ex));
                var cooked = new LoyaltyResponeOutput<GetSpinHistoryOutput>()
                {
                    Success = false, Result = null, Error = res.Message ??  "Error Happened", TargetUrl = "", UnAuthorizedRequest = false, __abp = null
                };
                return StatusCode(400, cooked);
            }
        }

        [HttpGet]
        [Route("get-one")]
        public async Task<ActionResult<GetOneWheelOutput>> GetOneWheelByMemberCode([FromQuery] GetOneWheelInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _wheelService.GetOneWheelByMemberCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Wheel GetOne Error >>  - " + JsonConvert.SerializeObject(ex));
                var cooked = new LoyaltyResponeOutput<GetOneWheelOutput>()
                {
                    Success = false, Result = null, Error = res.Message ??  "Error Happened", TargetUrl = "", UnAuthorizedRequest = false, __abp = null
                };
                return StatusCode(400, cooked);
            }
        }


        [HttpPost]
        [Route("create")]
        public async Task<ActionResult<CreateOrUpdateWheelByMemberCodeOutput>> Create(CreateWheelInput input)
        {
            try
            {
                _logger.LogInformation(" >> Wheel >> Create " + JsonConvert.SerializeObject(input));
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _wheelService.CreateWheel(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Wheel Create Error - " + JsonConvert.SerializeObject(ex));
                var cooked = new LoyaltyResponeOutput<CreateOrUpdateWheelByMemberCodeOutput>()
                {
                    Success = false, Result = null, Error = res.Message ?? "Error Happened", TargetUrl = "", UnAuthorizedRequest = false, __abp = null
                };
                return StatusCode(400, cooked);
            }
        }

        [HttpPost]
        [Route("update")]
        public async Task<ActionResult<CreateOrUpdateWheelByMemberCodeOutput>> Update(UpdateWheelInput input)
        {
            try
            {
                _logger.LogInformation(" >> Wheel >> Update " + JsonConvert.SerializeObject(input));
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _wheelService.UpdateWheel(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Wheel Update Error - " + JsonConvert.SerializeObject(ex));

                var cooked = new LoyaltyResponeOutput<CreateOrUpdateWheelByMemberCodeOutput>()
                {
                    Success = false, Result = null, Error = res.Message ??  "Error Happened", TargetUrl = "", UnAuthorizedRequest = false, __abp = null
                };
                return StatusCode(400, cooked);
            }
        }

        [HttpPost]
        [Route("spin")]
        public async Task<ActionResult<SpinWheelOutput>> Spin(SpinWheelInput input)
        {
            _logger.LogInformation(" >> Spinning >> " + JsonConvert.SerializeObject(input));
            try{
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var res = await _wheelService.Spin(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Wheel >> Spin Error: - " + JsonConvert.SerializeObject(ex));

                var cooked = new LoyaltyResponeOutput<SpinWheelOutput>()
                {
                    Success = false, Result = null, Error = res.Message ??  "Error Happened", TargetUrl = "", UnAuthorizedRequest = false, __abp = null
                };
                return StatusCode(400, cooked);
            }
        }
        [HttpDelete]
        [Route("delete")]
        public async Task<ActionResult<DeleteWheelOutput>> DeleteWheel(DeleteWheelInput input)
        {
            _logger.LogInformation(" >> Deleting >> " + JsonConvert.SerializeObject(input));
            try{
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var res = await _wheelService.DeleteWheel(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Wheel Update Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}
