﻿using System;
using System.Collections.Generic;


namespace AKC.MobileAPI.DTO.Loyalty.FeedbackSuggesstion
{
    public class GetAllFeedbackSuggesstionOutputDto
    {
        public GetListFeedbackSuggesstionOutput Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class GetListFeedbackSuggesstionOutput
    {
        public int TotalCount { get; set; }
        public List<GetListFeedbackSuggesstionItems> Items { get; set; }
    }

    public class GetListFeedbackSuggesstionItems
    {
        public FeedbackSuggesstionDto feedbackSuggesstion { get; set; }
    }

    public class FeedbackSuggesstionDto
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string Group { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public long? LastModifierUserId { get; set; }
        public string LastModifierUserName { get; set; }
        public int Id { get; set; }
    }
}
