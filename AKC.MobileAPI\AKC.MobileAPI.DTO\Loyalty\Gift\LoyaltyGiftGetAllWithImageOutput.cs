﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{

    public class LoyaltyGiftGetAllWithImageOutput
    {
        public ResultData Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }


        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }

    public class ResultData
    {
        public int TotalCount { get; set; }
        public List<GetAllWithImageForView> Items { get; set; }
    }
    public class GetAllWithImageForView
    {
        public GiftGroupDto GiftGroup { get; set; }

        public ImageLinkDto ImageLink { get; set; }
    }

    public class GiftGroupDto
    {
        public string Code { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Status { get; set; }

        public string Type { get; set; }

        public int Id { get; set; }
    }



}
