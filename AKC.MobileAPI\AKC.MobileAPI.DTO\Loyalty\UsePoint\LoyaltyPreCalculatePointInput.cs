﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.UsePoint
{
    public class LoyaltyPreCalculatePointInput
    {
        public int TenantId { get; set; }
        public string MemberCode { get; set; }
        public string StandardMemberCode { get; set; }
        public string Reason { get; set; }
        public DateTime BusinessTime { get; set; }
        public decimal TotalAmountIncludeTax { get; set; }
        public decimal TotalAmountBeforeTax { get; set; }
        public decimal TotalCalcAmount { get; set; }
        public decimal TotalTaxAmount { get; set; }
        public string RankTypeCode { get; set; }
        public string FullMemberTypeCode { get; set; }
        public string FullRegionCode { get; set; }
        public string FullSalesRegionCode { get; set; }
        public string FullChannelTypeCode { get; set; }
        public int TotalDetailLine { get; set; }
        public List<OrderDetailDto> OrdersDetail { get; set; }
    }

    public class OrderDetailDto
    {
        public int SeqNo { get; set; }
        public string ProductCode { get; set; }
        public string FullDeptCode { get; set; }
        public string FullClassCode { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal AmountBeforeTax { get; set; }
        public decimal AmountIncludeTax { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal CalcAmount { get; set; }
    }
}
