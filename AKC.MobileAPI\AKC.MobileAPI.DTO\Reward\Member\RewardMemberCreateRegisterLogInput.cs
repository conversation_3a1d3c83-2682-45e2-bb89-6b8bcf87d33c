﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberCreateRegisterLog
    {
        public string Data { get; set; }
    }

    public class RewardMemberCreateRegisterLogInput
    {
        public string PhoneNumber { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime Time { get; set; }
        public string DeviceId { get; set; }
        public string Localtion { get; set; }
        public string ExtraData { get; set; }
        public bool Status { get; set; } = false;
    }

    public class RewardMemberCreateRegisterLogDto
    {
        public string Code { get; set; }
        public string PhoneNumber { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime Time { get; set; }
        public string DeviceId { get; set; }
        public string Localtion { get; set; }
        public string ExtraData { get; set; }
        public bool Status { get; set; } = false;
    }
}
