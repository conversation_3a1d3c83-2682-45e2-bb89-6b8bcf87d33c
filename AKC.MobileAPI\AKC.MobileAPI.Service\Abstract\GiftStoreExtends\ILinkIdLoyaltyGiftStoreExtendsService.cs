﻿using AKC.MobileAPI.DTO.GiftStoreExtends;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.GiftStoreExtends
{
    public interface ILinkIdLoyaltyGiftStoreExtendsService
    {
        Task<GiftStoreExtendsCreateRedeemTransactionOutput> CreateRedeemTransaction(GiftStoreExtendsCreateRedeemTransactionInput input);
        Task<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetBrandOutput>> GetBrands(GiftStoreVendorGetBrandInput input);
        Task<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetCategoryOutput>> GetCategories(GiftStoreVendorGetCategoryInput input);
        Task<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetGiftListOutput>> GetGiftList(GiftStoreVendorGetGiftListInput input);
        Task<GiftStoreExtendsLoyaltyResponse<GiftStoreVendorGetGiftDetailOutput>> GetGiftDetail(GiftStoreVendorGetGiftDetailInput input);
        Task<GiftStoreExtendsLoyaltyResponse<GiftStoreVendorGetDetailOrderOutput>> GetDetailOrder(GiftStoreVendorGetDetailOrderInput input);
        Task<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetDetailOrderOutput>> GetListOrder(GiftStoreVendorGetListOrderInput input);
        Task<GiftStoreVendorGetMerchantListOutput> GetMerchantList(GiftStoreVendorGetMerchantListInput input);
        Task<LoyaltyCreateRedeemTransactionOutput> CreateRedeemMerchantTransaction(int tenantIdRequest, LoyaltyCreateRedeemTransactionDto input);
    }
}
