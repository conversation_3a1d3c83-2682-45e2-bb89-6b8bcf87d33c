﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Reward.Merchant;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Reward
{
    [Route("api/Merchant")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    //[Authorize]
    public class RewardMerchantController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IRewardMerchantService _rewardMerchantService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IDistributedCache _cache;
        public RewardMerchantController(
            ILogger<RewardMerchantController> logger,
            IExceptionReponseService exceptionReponseService,
            IRewardMerchantService rewardMerchantService,
            ICommonHelperService commonHelperService,
            IDistributedCache cache
            )
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _rewardMerchantService = rewardMerchantService;
            _commonHelperService = commonHelperService;
            _cache = cache;
        }

        [HttpGet]
        [Route("GetAll")]
        public async Task<ActionResult<RewardGetAllMerchantOutput>> GetAll([FromQuery] RewardGetAllMerchantInput input)
        {
            try
            {
                var CacheKey = "GET_ALL_MERCHANT_2022DEC26" + input.TypeFilter;
                var cachedData = await _cache.GetStringAsync(CacheKey);
                if (!string.IsNullOrEmpty(cachedData))
                {
                    var convertedObj = JsonConvert.DeserializeObject<RewardGetAllMerchantOutput>(cachedData);
                    _logger.LogInformation(">> GET_ALL_MERCHANT_2022DEC26 >> CACHE HIT");
                    
                    convertedObj.Items = convertedObj.Items.Select(x => x)
                        .Where(x => x.WalletAddress != "d7cb1c1b4058918fcce1e126acf0037925500f" 
                                    && x.WalletAddress != "ec0d4aa2a43d1d9f93c50a264f95f5bbfa6804" // be UAT
                                    && x.WalletAddress != "ef8383f57102c53dd86f2c49014e23e8364d76" // izota prod
                                    && x.WalletAddress != "37c576208bb8de601a77e7b24d8ca0764eca75" // izota uat
                                    ).ToList();
                    // Wallet UAT x Production
                    // If AllowConnectFromLynkId == true, then set 
                    foreach (var item in convertedObj.Items)
                    {
                        if (item.AllowConnectFromLynkId)
                        {
                            item.Type = "Exchange";
                        }
                    }
                    _logger.LogInformation(" >> Get All Merchants (incache) >> " + input.PhoneNumber + " >> Result length " + convertedObj.Items.Count);
                    return StatusCode(200, convertedObj);
                }
                var result = await _rewardMerchantService.GetAll(input);
                // Remove Doi Tac Be
                if (result.Items != null)
                {
                    result.Items = result.Items.Select(x => x)
                        .Where(x => x.WalletAddress != "d7cb1c1b4058918fcce1e126acf0037925500f" 
                                    && x.WalletAddress != "ec0d4aa2a43d1d9f93c50a264f95f5bbfa6804"
                                    && x.WalletAddress != "ef8383f57102c53dd86f2c49014e23e8364d76" // izota prod
                                    && x.WalletAddress != "37c576208bb8de601a77e7b24d8ca0764eca75" // izota uat
                                    ).ToList();
                    // Wallet UAT x Production
                    // If AllowConnectFromLynkId == true, then set 
                    foreach (var item in result.Items)
                    {
                        if (item.AllowConnectFromLynkId)
                        {
                            item.Type = "Exchange";
                        }

                        if (!item.LimitExchangePerTime.HasValue)
                        {
                            item.LimitExchangePerTime = 0;
                        }
                        if (!item.MinExchangePerTime.HasValue)
                        {
                            item.MinExchangePerTime = 0;
                        }
                    }
                }
                await _cache.SetStringAsync(CacheKey, JsonConvert.SerializeObject(result),
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(10)));
                _logger.LogInformation(" >> Get All Merchants (From DB) >> " + input.PhoneNumber + " >> Result length " + result.Items.Count);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Merchant GetAll Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetListExchangeMerchant")]
        public async Task<ActionResult<RewardGetAllMerchantExchangeOutput>> GetListExchangeMerchant([FromQuery] RewardGetAllMerchantExchangeInput input)
        {
            try
            {
                var result = await _rewardMerchantService.GetListExchangeMerchant(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetListExchangeMerchant Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetListExchangeMerchantByMember")]
        public async Task<ActionResult<RewardGetListExchangeMerchantConnectedOutput>> GetListExchangeMerchantByMember([FromQuery] RewardGetListExchangeMerchantConnectedInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _rewardMerchantService.GetListExchangeMerchantByMember(input);
                _logger.LogInformation("GetListExchangeMerchantByMember Response - " + JsonConvert.SerializeObject(result));
                if (result.Items != null)
                {
                    result.Items = result.Items.Select(x => x)
                        .Where(x => x.MerchantId != 241
                                    && x.MerchantId != 248).ToList();
                    // merchant id on  UAT x Production
                    foreach (var item in result.Items)
                    {
                        if (item.IsVerified == "Confirmed")
                        {
                            item.BindingStatus = true;
                        }
                    }
                }
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                

                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError(ex, "GetListExchangeMerchantByMember Error - " + JsonConvert.SerializeObject(ex));
                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogError(ex, "GetListExchangeMerchantByMember Error - " + JsonConvert.SerializeObject(ex));
                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("GetAllMerchantPayment")]
        public async Task<ActionResult<RewardGetAllMerchantPaymentOutput>> GetAllMerchantPayment([FromQuery] RewardGetAllMerchantPaymentInput input)
        {
            try
            {
                var result = await _rewardMerchantService.GetAllMerchantPayment(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {


                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetAllMerchantPayment Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }
    }
}
