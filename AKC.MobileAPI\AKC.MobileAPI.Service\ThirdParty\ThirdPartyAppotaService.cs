﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.ExchangeTransaction;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.PartnerPointCaching;
using AKC.MobileAPI.DTO.ThirdParty.Appota;
using AKC.MobileAPI.DTO.ThirdParty.VPBank;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Abstract.ThirdParty;
using AKC.MobileAPI.Service.Constants.ThirdParty;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Loyalty;
using AKC.MobileAPI.Service.ThirdParty.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;
using System.Security.Cryptography;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.Service.Constants;

namespace AKC.MobileAPI.Service.ThirdParty
{
    public class ThirdPartyAppotaService : BaseThirdPartyAppotaService, IThirdPartyAppotaService
    {
        private readonly IRewardExchangeTransactionService _rewardExchangeTransactionService;
        private readonly ILoyaltyAuditLogService _loyaltyAuditLogService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IDistributedCache _cache;
        private int APPOTA_100 = 100;

        public ThirdPartyAppotaService(
            IConfiguration configuration,
            ILogger<ThirdPartyAppotaService> logger,
            IRewardExchangeTransactionService rewardExchangeTransactionService,
            IRewardMemberService rewardMemberService,
            ILoyaltyAuditLogService loyaltyAuditLogService,
            IExceptionReponseService exceptionReponseService,
            IDistributedCache cache) : base(configuration, logger, loyaltyAuditLogService)
        {
            _rewardExchangeTransactionService = rewardExchangeTransactionService;
            _loyaltyAuditLogService = loyaltyAuditLogService;
            _rewardMemberService = rewardMemberService;
            _exceptionReponseService = exceptionReponseService;
            _cache = cache;
        }

        private string AppotaGetSignatureFromPayload(long amount, string extraData, string orderId, string orderInfo,
            long redeepPoint)
        {
            var secretKey = _configuration.GetSection("ThirdPartyMerchant:Appota:SecretKeyForSignature").Value;
            _logger.LogInformation(" Secret Key when gen Signature: " + secretKey);
            var rawData = "amount=" + amount + "&extraData=" + extraData + "&orderId=" + orderId + "&orderInfo=" + orderInfo + "&redeemApoint=" + redeepPoint;
            using (HMACSHA256 hmac = new HMACSHA256(Encoding.ASCII.GetBytes(secretKey)))
            {
                // ComputeHash - returns byte array  
                var bytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(rawData));

                // Convert byte array to a string   
                var builder = new StringBuilder();
                for (var i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }

                return builder.ToString();
            }
        }

        public async Task<LoyaltyThirdPartyPointExchangeOutput> PointExchange(LoyaltyThirdPartyPointExchangeInput input,
            HttpContext context, string orderCode = null)
        {
            _logger.LogInformation(" >> Appota PointExchange >>  " + JsonConvert.SerializeObject(input));
            var vpidTransactionId = string.Empty;
            
            if (string.IsNullOrWhiteSpace(orderCode))
            {
                vpidTransactionId = LoyaltyHelper.GenTransactionCode("PointExchange");
            }
            else
            {
                vpidTransactionId = orderCode;
            }

            var appotaRequest = new ThirdPartyAppotaPointExchangeInput()
            {
                OrderId = vpidTransactionId,
                Amount = input.ExchangeAmount * APPOTA_100,
                OrderInfo = "AppointToLinkId",
                RedeemApoint = input.ExchangeAmount,
                ExtraData = "",
                Signature = AppotaGetSignatureFromPayload(input.ExchangeAmount * APPOTA_100, "", vpidTransactionId,
                    "AppointToLinkId", input.ExchangeAmount)
            };
            _logger.LogInformation("Appota - About to do exchange >> " + JsonConvert.SerializeObject(appotaRequest));
            var appotaRes = await SendPostExchangeAsync<ThirdPartyAppotaPointExchangeOutput>(
                _configuration.GetSection("ThirdPartyMerchant:Appota:ExchangePointUrl").Value, input.AccessToken, appotaRequest, context,
                input.MemberCode);
            _logger.LogInformation("Response from Appota When Exchange >> " + JsonConvert.SerializeObject(appotaRes));
            var requestReward = new RewardCreateExchangeTransactionInput()
            {
                TransactionCode = vpidTransactionId,
                ExchangeAmount = input.ExchangeAmount, // Điểm này cứ giữ nguyên như số truyền lên, operator api sẽ base on rate và tính toán ở trên đó
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                PartnerBindingTxId = appotaRes.Transaction?.AppotapayTransId,
            };
            var allowRevert = true;
            try
            {
                var rewardExchange =
                    await _rewardExchangeTransactionService.CreateExchangeTransactionIntegration(requestReward, MerchantNameConfig.VPID);
                if (rewardExchange.Result == 202)
                {
                    _logger.LogError(
                        $"Create exchange appota reward with Status 202 Accepted {JsonConvert.SerializeObject(rewardExchange)}");
                    allowRevert = false;
                    GetErrorValidation("ExchangeTransactionStatusAccepted", "Status 202 Accepted");
                }

                return new LoyaltyThirdPartyPointExchangeOutput()
                {
                    Error = null,
                    Success = true,
                    Result = 200,
                    Items = new LoyaltyThirdPartyPointExchangeResult()
                    {
                        Transaction = new LoyaltyThirdPartyPointExchangeItem()
                        {
                            EquivalentTokenAmount = rewardExchange.Items.EquivalentTokenAmount,
                            ExchangeAmount = input.ExchangeAmount,
                            PartnerBindingTxId = rewardExchange.Items.PartnerBindingTxId,
                            LinkIDOrderCode = vpidTransactionId,
                        }
                    },
                };
            }
            catch (Exception e)
            {
                if (e.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(e);
                    if (res.Code == "ExchangeTransactionTimeout" || res.Code == "ExchangeTransactionStatusAccepted")
                    {
                        GetErrorValidation("ExchangeTransactionPending", "Transaction pending");
                    }
                }
                _logger.LogError(">> Error when exchange Appota >> " + JsonConvert.SerializeObject(input) + "; " + JsonConvert.SerializeObject(e));
                throw e;
            }
        }
        
        public async Task<LoyaltyThirdPartyRequestAccessTokenOutput> RequestAccessToken(LoyaltyThirdPartyRequestAccessTokenInput input, HttpContext context)
        {
            var resultRefreshToken = await _rewardMemberService.GetRefreshToken(new RewardMemberGetRefreshTokenInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId
            });
            var appotaReq = new ThirdPartyAppotaRequestAccessTokenInput()
            {
                memberCode = resultRefreshToken.Items.IdCard,
                refreshToken = resultRefreshToken.Items.ItemPartner[0].RefreshToken,
            };
            // T ODO: REMOVE WHEN GOLIVE
            // if (DateTime.UtcNow.Minute % 5 > 0)
            // {
            //     appotaReq.refreshToken += "AAA"; // lafm baat hoat token de expect 401 error
            // }
            // END
            var urlGetAccessTokenFromRefresh =
                _configuration.GetSection("ThirdPartyMerchant:Appota:ExchangeRefreshForAccessToken").Value + "?client_key=" + apiKey + "&grant_type=authorization_code&refresh_token="
                +appotaReq.refreshToken;
            ExchangeCodeForTokenOutput appotaRes = null;
            try
            {
                appotaRes = await GetAccessFromRefresh<ExchangeCodeForTokenOutput>(
                    urlGetAccessTokenFromRefresh, context, input.MemberCode);
            }
            catch (UnauthorizedAccessException e)
            {
                GetLoyaltyErrorValidation("LID401", "UnauthorizedAccess");
                throw e;
            }

            var pointView = await GetMemberInfor<ThirdPartyAppotaPointViewOutput>(
                _configuration.GetSection("ThirdPartyMerchant:Appota:GetAccountInfoUrl").Value,  appotaRes.data.access_token, context, input.MemberCode);
            
            await _rewardMemberService.SaveRefreshTokenMobileAPIAppota(new CreateConnectForAppotaXSkyJoyInput()
            {
                MemberCode = input.MemberCode,
                MerchantId = input.MerchantId,
                RefreshToken = appotaRes.data.refresh_token,
                IsChangedLoyalty = false,
                ReferenceData = JsonConvert.SerializeObject(new object()),
                MemberLoyaltyInfo = new MemberConnectLoyaltyInfoInput()
                {
                    Cif = pointView.data.user_id
                },
                CreateMemProfile = "NO"
            });
            return new LoyaltyThirdPartyRequestAccessTokenOutput()
            {
                Success = true,
                Result = 200,
                Message = "Success",
                Items = new LoyaltyThirdPartyRequestAccessTokenItems()
                {
                    AccessToken = appotaRes.data.access_token,
                    IdNumber = resultRefreshToken.Items.IdCard,
                    MemberCode = resultRefreshToken.Items.MemberCode,
                }
            };
        }

        public async Task<LoyaltyThirdPartyPointViewOutput> PointView(LoyaltyThirdPartyPointViewInput input,
            HttpContext context)
        {
            var appotaResult = await GetMemberInfor<ThirdPartyAppotaPointViewOutput>(
                _configuration.GetSection("ThirdPartyMerchant:Appota:GetAccountInfoUrl").Value, input.AccessToken, context, input.MemberCode);
            
            // Cập nhật vào partner point caching
            return new LoyaltyThirdPartyPointViewOutput()
            {
                Success = true,
                Result = new LoyaltyThirdPartyPointViewResult()
                {
                    Member = new LoyaltyThirdPartyPointViewItem()
                    {
                        CoinBalance = decimal.Parse(appotaResult.data?.apoint_balance),
                        MemberCode = appotaResult.data?.user_id,
                    }
                }
            };
        }


        public async Task<LoyaltyThirdPartyRevertPointOutput> RevertPoint(LoyaltyThirdPartyRevertPointInput input,
            HttpContext context)
        {
            return new LoyaltyThirdPartyRevertPointOutput()
            {
                Error = null,
                Success = true,
                Message = "Success",
            };
        }

        public async Task<RewardPartnerPoingCachingItems> UpdatePartnerCaching(
            LoyaltyThirdPartyAppotaUpdatePartnerCachingInput input, HttpContext context)
        {
            try
            {
                var request = new LoyaltyThirdPartyPointViewInput()
                {
                    AccessToken = input.AccessToken,
                    IdNumber = input.IdNumber,
                    MemberCode = input.MemberCode,
                };
                var response = await PointView(request, context);
                return new RewardPartnerPoingCachingItems()
                {
                    PointBalance = response.Result.Member.CoinBalance,
                    Status = "S",
                    MerchantId = input.MerchantId
                };
            }
            catch (Exception ex)
            {
                return new RewardPartnerPoingCachingItems()
                {
                    PointBalance = 0,
                    Status = "F",
                    MerchantId = input.MerchantId,
                    HaveException = true,
                    ExceptionCode = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result.Code
                };
            }
        }

        private RewardDataExceptionResponse GetErrorValidation(string errorCode, string errorMessage)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = errorMessage
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }
        private LoyaltyExceptionResponse GetLoyaltyErrorValidation(string errorCode, string errorMessage)
        {
            var ex = new LoyaltyException();
            var error = new LoyaltyExceptionResponse()
            {
                result = 400,
                success = false,
                error = new LoyaltyExceptionErrorItem()
                {
                    code = errorCode,
                    message = errorMessage
                },
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }
    }
}