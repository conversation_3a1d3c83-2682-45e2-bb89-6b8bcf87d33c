﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.FAQ;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/FAQ")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyFAQController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyFAQService _fAQService;
        private readonly IExceptionReponseService _exceptionReponseService;
        public LoyaltyFAQController(
            ILogger<LoyaltyFAQController> logger,
            ILoyaltyFAQService faqService,
            IExceptionReponseService exceptionReponseService)
        {
            _logger = logger;
            _fAQService = faqService;
            _exceptionReponseService = exceptionReponseService;
        }

        [HttpGet]
        [Route("GetAll")]
        public async Task<ActionResult<GetAllFAQOutputDto>> GetAll([FromQuery] GetAllFAQInputDto input)
        {
            try
            {
                if (input.LevelFilter.HasValue == false || input.LevelFilter is 0)
                {
                    input.LevelFilter = 1000;
                }
                var result = await _fAQService.GetAll(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Get FAQ Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllAdvanceByCode")]
        public async Task<ActionResult<GetAllFaqAdvanceOutputWrapperDTO>> GetAllAdvanceByCode([FromQuery] GetAllAdvancebyCode input)
        {
            try
            {
                var result = await _fAQService.GetAllAdvanceByCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Get FAQ Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

    }
}
