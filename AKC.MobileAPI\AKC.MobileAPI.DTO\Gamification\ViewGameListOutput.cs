﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Gamification
{
    public class ViewGameListOutput
    {
        public int result { get; set; }
        public List<ViewGameListOutputDto> item { get; set; }
        public int totalCount { get; set; }
        public string message { get; set; }
        public string messageDetail { get; set; }
    }
    public class ViewGameListOutputDto
    {
        public int? QuestID { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Icon { get; set; }
        public DateTime? CreateDate { get; set; }
        public int? CreatorUserID { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public Boolean? IsDeleted { get; set; }
        public int? DeleteUserID { get; set; }
        public DateTime? DeletionTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public int? LastModifierUserID { get; set; }
        public string JoinType { get; set; }
        public int? JoinLevelFrom { get; set; }
        public int? JoinLevelTo { get; set; }
        public int? PassPercentage { get; set; }
        public string GameCode { get; set; }
        public List<QuestReward> QuestReward { get; set; }
    }
    public class QuestReward
    {
        public int? ItemID { get; set; }
        public int? Quantity { get; set; }

    }
}
