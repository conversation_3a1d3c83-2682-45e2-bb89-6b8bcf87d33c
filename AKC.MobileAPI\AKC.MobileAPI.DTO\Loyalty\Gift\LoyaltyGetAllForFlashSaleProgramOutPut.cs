﻿using System;
using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class LoyaltyGetAllForFlashSaleProgramOutPut
    {
        public FlashSaleProgramResult Result { get; set; }

        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }


        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }

    }

    public class FlashSaleProgramResult
    {
        public int TotalCount { get; set; }

        public List<FlashSaleProgramGetAllForview> Items { get; set; }
    }

    public class FlashSaleProgramGetAllForview
    {
        public FlashSaleProgramDto FlashSaleProgram { get; set; }
        public int TotalProduct { get; set; }
    }

    public class FlashSaleProgramDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public DateTime? CreationTime { get; set; }
        public long? CreatorUserId { get; set; }
        public string CreatorUserName { get; set; }
        public string Status { get; set; }
        public int MaxGiftPerCustomer { get; set; }
        public DateTime DisplayTime { get; set; }
        public string Url { get; set; }
        public string Image { get; set; }
    }

}
