﻿using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.AirlineDto;
using Microsoft.AspNetCore.Http;
using AKC.MobileAPI.DTO.Reward.Member;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyThirdPartyService
    {
        Task<LoyaltyThirdPartyPointViewOutput> PointView(LoyaltyThirdPartyPointViewInput input, HttpContext context);
        Task<LoyaltyThirdPartyVerifyNationalIdOutput> VerifyNationalId(LoyaltyThirdPartyVerifyNationalIdInput input, HttpContext context, string authorization);
        Task<LoyaltyThirdPartyVerifyOTPOutput> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input, HttpContext context, string authorization);
        Task<LoyaltyThirdPartyPointExchangeOutput> PointExchange(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null);
        Task<ExchangeLynkiDToPartnerPointRes> ExchangeLynkiDToPartnerPoint(ExchangeLynkiDToPartnerPointReq input);
        Task<LoyaltyThirdPartyRevertPointOutput> RevertPoint(LoyaltyThirdPartyRevertPointInput input, HttpContext context);
        Task<LoyaltyThirdPartyRequestAccessTokenOutput> RequestAccessToken(LoyaltyThirdPartyRequestAccessTokenInput input, HttpContext context);
        Task<LoyaltyThirdPartyUpdatePartnerCachingOutput> UpdatePartnerCaching(LoyaltyThirdPartyUpdatePartnerCachingInput input, HttpContext context);
        Task<LoyaltyThirdPartyPointExchangeOutput> PointExchangeIntegration(LoyaltyThirdPartyPointExchangeInput input, HttpContext context, string orderCode = null);
        Task<LoyaltyThirdPartyRemoveConnectedMerchantOutput> RemoveConnectedMerchant(RewardMemberUpdateOutputDto member, LoyaltyThirdPartyRemoveConnectedMerchant input, GetPartnerMemberInfoByConnectionOutput cif);
        Task<LoyaltyThirdPartyConfirmConnectOutput> ConfirmConnect(LoyaltyThirdPartyConfirmConnectInput input, string authorization);
        Task<LoyaltyThirdPartyUpdatePhoneNumberOutput> UpdatePhoneNumber(LoyaltyThirdPartyUpdatePhoneNumberInput input);
        Task<LoyaltyThirdPartySendOtpConfirmConnectOutput> SendOtpConfirmConnect(LoyaltyThirdPartySendOtpConfirmConnectInput input, string authorization);
        Task<RemoveConnectRespone> RemoveConnect(RemoveConnectInput Input);
    }
}
