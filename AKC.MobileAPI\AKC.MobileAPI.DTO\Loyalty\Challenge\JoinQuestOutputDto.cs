﻿using System;
using System.Collections.Generic;


namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class JoinQuestOutputDto
    {
        public ListResultJoinQuest Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class ListResultJoinQuest
    {

        public string Id { get; set; }
        public string MemberCode { get; set; }
        public string QuestCode { get; set; }
        public DateTime Date { get; set; }
        public string State { get; set; }
    }
}
