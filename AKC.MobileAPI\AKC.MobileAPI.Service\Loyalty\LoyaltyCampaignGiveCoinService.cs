﻿using System;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyCampaignGiveCoinService : BaseLoyaltyService, ILoyaltyCampaignGiveCoinService
    {
        public LoyaltyCampaignGiveCoinService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<GetActiveCampaignOutput> GetActiveCampaign()
        {
            return await GetLoyaltyAsync<GetActiveCampaignOutput>(LoyaltyApiUrl.CAMPAIGN_GIVE_COIN_GET_ACTIVE_CAMPAIGN);
        }

        public async Task<CreateOrEditMemberCampaignGiveOutput> Create(CreateOrEditMemberCampaignGiveCoinDto input)
        {
            return await PostLoyaltyAsync<CreateOrEditMemberCampaignGiveOutput>(LoyaltyApiUrl.CAMPAIGN_GIVE_COIN_CREATE_MEMBER, input);
        }

        public async Task<GetMemberByCifOutput> GetMemberByCif(GetMemberByCifInput input)
        {
            return await GetLoyaltyAsync<GetMemberByCifOutput>(LoyaltyApiUrl.CAMPAIGN_GIVE_COIN_GET_MEMBER_BY_CIF, input);
        }

        public async Task<GetTransactionForCampaignOutput> GetTransactionForCampaign(GetTransactionForCampaignInput input)
        {
            var tenantLinkid = Convert.ToInt32(_configuration.GetSection("LINKIDTENANTIDONCOMMONLOYALTY").Value);
            return await PostCommonLoyaltyLoyaltyAsync<GetTransactionForCampaignOutput>("services/app/MoneyCard/GetTransactionForCampaign", tenantLinkid, new {Membercode = input.MemberCodeFilter});
        }

        public async Task<GetListRegistrationByMemberCodeOutput> GetListRegistrationByMemberCode(
            GetListRegistrationByMemberCodeInput input)
        {
            return await GetLoyaltyAsync<GetListRegistrationByMemberCodeOutput>(LoyaltyApiUrl.GetListRegistrationByMemberCode, input);
        }
    }
}
