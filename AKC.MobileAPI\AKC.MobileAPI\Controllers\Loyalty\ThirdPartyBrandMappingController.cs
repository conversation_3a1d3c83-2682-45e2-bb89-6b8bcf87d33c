﻿using AKC.MobileAPI.DTO.Loyalty.ThirdPartyBrandMapping;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/ThirdPartyBrandMapping")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    //[AllowAnonymous]
    public class ThirdPartyBrandMappingController : ControllerBase
    {
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILogger _logger;
        private readonly IThirdPartyBrandMappingService _thirdPartyBrandMappingService;
        private readonly ICommonHelperService _commonHelperService;
        public ThirdPartyBrandMappingController(
            IExceptionReponseService exceptionReponseService,
            ILogger<ThirdPartyBrandMappingController> logger,
            IThirdPartyBrandMappingService thirdPartyBrandMappingService,
            ICommonHelperService commonHelperService)
        {
            _exceptionReponseService = exceptionReponseService;
            _logger = logger;
            _thirdPartyBrandMappingService = thirdPartyBrandMappingService;
            _commonHelperService = commonHelperService;
        }

        [HttpGet]
        [Route("GetAllThirdPartyBrandByVendorName")]
        public async Task<ActionResult<GetAllThirdPartyBrandMappingByVendorNameOutput>> GetAllThirdPartyBrandByVendorName([FromQuery] GetAllThirdPartyBrandMappingByVendorNameInput input)
        {
            try
            {
                if (!string.IsNullOrEmpty(input.MemberCode))
                {
                    var authorization = Request.Headers["Authorization"].ToString();
                    var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                    if (checkAuthen != null)
                    {
                        return StatusCode(401, checkAuthen);
                    }
                }
                var result = await _thirdPartyBrandMappingService.GetAllThirdPartyBrandByVendorName(input);

                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetAllThirdPartyBrandByVendorName Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllThirdPartyBrandByVendorType")]
        public async Task<ActionResult<GetAllThirdPartyBrandMappingByVendorNameOutput>> GetAllThirdPartyBrandByVendorType([FromQuery] GetAllThirdPartyBrandMappingByVendorTypeInput input)
        {
            try
            {
                if (!string.IsNullOrEmpty(input.MemberCode))
                {
                    var authorization = Request.Headers["Authorization"].ToString();
                    var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                    if (checkAuthen != null)
                    {
                        return StatusCode(401, checkAuthen);
                    }
                }
                var result = await _thirdPartyBrandMappingService.GetAllThirdPartyBrandByVendorType(input);

                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetAllThirdPartyBrandByVendorType Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

    }
}
