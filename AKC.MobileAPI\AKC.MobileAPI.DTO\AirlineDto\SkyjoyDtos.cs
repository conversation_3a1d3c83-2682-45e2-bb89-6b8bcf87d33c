using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace AKC.MobileAPI.DTO.AirlineDto
{
    public class SkyjoyContentMessage
    {
        private static IDictionary<string, string> _dictionary = new Dictionary<string, string>()
        {
            {"USER_INFO_NOT_FOUND", "Không tìm thấy hồ sơ người dùng"},
            {"TRANSACTION_ALREADY_PROCESSED", "Mã giao dịch này đã được xử lý"},
            {"TRANSACTION_CREATE_FAILED", "<PERSON><PERSON> thống ghi giao dịch thất bại, điể<PERSON> chưa được cộng sang bên đối tác"},
            {"VERIFY_TOKEN_FAILED", "Token xác thực không hợp lệ hoặc đã hết hạn"},
            {"USER_CONSENT_NOT_ACCEPTED", "Người dùng chưa chấp thuận điều khoản liên kết"},
            {"PARTNER_MEMBER_ID_ALREADY_LINKED", "LynkiD Member ID đã được liên kết với tài khoản khác"},
            {"MEMBER_IS_LINKED", "Tài khoản đã được liên kết trước đó rồi"},
            {"RELINK_ACCOUNT_MISMATCH", "Tài khoản hiện tại khác với tài khoản đã liên kết trước đó"},
            {"USER_NOT_FOUND", "Không tìm thấy hồ sơ người dùng"},
            {"MEMBER_IS_NOT_LINKED_YET", "Không tồn tại liên kết để huỷ"},
            {"GENERAL_ERROR", "Có lỗi xảy ra khi kết nối, vui lòng liên hệ DVKH để được trợ giúp" }
        };
        public static string GetMessageFromCode(string code)
        {
            var errorMessage = "";
            if (string.IsNullOrEmpty(code))
            {
                errorMessage = "Mã lỗi không hợp lệ";
            }
            else
            {
                if (code == "RELINK_ACCOUNT_MISMATCH")
                    code = "GENERAL_ERROR";
                var searchInDictionary = _dictionary.TryGetValue(code, out var message);
                errorMessage = searchInDictionary ? message : "Có lỗi xảy ra, vui lòng thử lại sau";
            }
            return errorMessage;
        }
    }
    public class SJUnlinkMemberInput
    {
        public string skyjoyId { get; set; }
        public string partnerMemberId { get; set; }
    }
    public class SJUnlinkMemberOutput{}
    public class SJLinkMemberInput
    {
        public string sjToken { get; set; }
        public string partnerMemberId { get; set; }
        public SJLinkMemberInputInner profile { get; set; }
    }

    public class SJLinkMemberInputInner
    {
        public string firstName { get; set; } // VAN KHUONG
        public string lastName { get; set; } // DAO
        public string idNumber { get; set; } // *********
    }

    public class SJLinkMemberOutput
    {
        public string code { get; set; }
        public string message { get; set; }
        public SJLinkMemberOutputData data { get; set; }
    }

    public class SJLinkMemberOutputData
    {
        public string skyjoyId { get; set; }
        public string phone { get; set; }
        public string resultUrl { get; set; }
    }
    public class SJRequestAccessTokenInput
    {
        public string Code { get; set; }
    }

    public class SJRequestAccessTokenOutput
    {
        public string access_token { get; set; }
        public decimal expires_in { get; set; }
        public string refresh_token { get; set; }
        public decimal refresh_expires_in { get; set; }
        public string token_type { get; set; }
        public string session_state { get; set; }
        public string scope { get; set; }
        public string code { get; set; }
        public string message { get; set; }
    }

    public class SJPointAccrualErrorOutput
    {
        // TODO
    }
    public class SJPointAccrualOutput
    {
        public int StatusCode { get; set; }
        public string Code { get; set; }
        public string Message { get; set; }
        public SJPointAccrualOutputData Data { get; set; }
    }

    public class SJPointAccrualOutputData
    {
        public int SponsorId { get; set; }
        public string TransactionId { get; set; }
        public string BitReference { get; set; }
        public string SkyjoyId { get; set; }
        public DateTime Timestamp { get; set; }
        public string RequestId { get; set; }
    }
    
    public class SJPointAccrualInput
    {
        public string skyjoyId { get; set; }
        public string transactionId { get; set; }
        public decimal transactionAmount { get; set; }
        public string transactionCurrency { get; set; } = "VND";
        public SJPointAccrualInputExtraData extraData { get; set; }
        
        public string MemberCode { get; set; }
        public int MerchantId { get; set; }
    }

    public class SJPointAccrualInputExtraData
    {
        public decimal partnerPoint { get; set; }
        public decimal points { get; set; }
        public string LynkidId { get; set; }
    }
    
    public class GJBeToBeTokenResponse
    {
        public string access_token { get; set; }
        public int expires_in { get; set; }
        public int refresh_expires_in { get; set; }
        public string refresh_token { get; set; }
        public string token_type { get; set; }
        public int not_before_policy { get; set; }
        public string session_state { get; set; }
        public string scope { get; set; }
    }

    public class SJApiErrorDetail
    {
        public string Code { get; set; }
        public string Message { get; set; }
        public string Name { get; set; }
    }

    public class SJApiErrorResponse
    {
        public int StatusCode { get; set; }
        public SJApiErrorDetail Errors { get; set; }
        public Dictionary<string, object> Data { get; set; }
    }
    public class SJAlternativeExchangeTokenErrorResponse
    {
        public string Error { get; set; }
        [JsonProperty("error_description")]
        public string ErrorDescription { get; set; }
    }
    
    public class GetCifByMemberRequest
    {
        public int MemberId;
        public string NationalId;
        public int MerchantId;
    }

    public class GetPartnerMemberInfoByConnectionOutput
    {
        [JsonProperty("item")]
        public GetPartnerMemberInfoByConnectionOutputItem Item { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("result")]
        public int Result { get; set; }
    }

    public class GetPartnerMemberInfoByConnectionOutputItem
    {
        [JsonProperty("RelatedCif")]
        public string RelatedCif { get; set; }

        [JsonProperty("HasConnection")]
        public bool HasConnection { get; set; }
    }

    public class JPPointAccrualErrorResponse
    {
        public ErrorDetails Errors { get; set; }
        public int StatusCode { get; set; }
    }

    public class ErrorDetails
    {
        public string Code { get; set; }
        public string Message { get; set; }
        public string Name { get; set; }
    }
}