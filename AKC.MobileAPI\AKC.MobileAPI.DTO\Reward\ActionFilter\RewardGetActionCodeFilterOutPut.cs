﻿using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.Reward.ActionFilter
{
    public class RewardGetActionCodeFilterOutPut
    {
        public RewardGetActionCodeFilterOutPutResult Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }

    public class RewardGetActionCodeFilterOutPutResult
    {
        public List<ActionCodeFilterOutPut> items { get; set; }
    }

    public class ActionCodeFilterOutPut
    {
        public string ActionFilterCode { get; set; }
    }
}
