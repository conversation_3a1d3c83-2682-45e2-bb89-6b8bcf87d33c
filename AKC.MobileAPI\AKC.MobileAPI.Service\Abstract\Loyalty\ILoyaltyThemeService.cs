using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.User;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyThemeService
    {
        Task<ThemeSettingsInfoDtoWrapper> GetCurrentTheme(GetCurrentThemeInput input);
    }

    public class ThemeSettingsInfoDtoWrapper
    {
        public ThemeSettingsInfoDto Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }
    public class ThemeSettingsInfoDto
    {
        public int? TenantId { get; set; }
        public int Id { get; set; }
        public string ThemeCode { get; set; }
        public string ThemeName { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public DateTime EffectiveTo { get; set; }
        public string ImgSplashScreen { get; set; }
        public string ImgLoginScreen { get; set; }
        public string ImgHeaderHomePage { get; set; }
        public string ImgFloatingIcon { get; set; }
        public string ImgHomepageIcon { get; set; }
        public string DiamondScreen { get; set; }
        public string FriendRefScreen { get; set; }
        public string BackGroundQRScreen { get; set; }
        public string GiftCardScreen { get; set; }
        public string InsuranceScreen { get; set; }
        public string Screen1 { get; set; }
        public string Screen2 { get; set; }
        public string Screen3 { get; set; }
        public string Screen4 { get; set; }
        public string Screen5 { get; set; }
        public string Screen6 { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public DateTime CreationTime { get; set; }
        public string LastModificationBy { get; set; }
        public long LastModificationById { get; set; }
    }
}