﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class GetPaymentMethodInput
    {
        [Required]
        public string MemberCode { get; set; }
        [Required]
        public string GiftCode { get; set; }
    }
    public class RewardMemberGetPaymentMethodInput
    {
        public string MemberCode { get; set; }
        public string MemberType { get; set; } = "KHCN";
        public List<string> ListGiftGroupCode { get; set; }
    }

    public class PaymentMethodDto
    {
        public string Type { get; set; }

        public double Balance { get; set; }
        
        public string? CardCode { get; set; }
        
        public DateTime? ExpiryDate { get; set; }
    }

    public class GetMemberPaymentMethodDto
    {
        public List<PaymentMethodDto> ListPaymentMethod { get; set; }
    }
    
    public class RewardMemberGetPaymentMethodOutput
    {
        public GetMemberPaymentMethodDto Item { get; set; }
        public string Message { get; set; } = "Success";
        public string MessageDetail { get; set; }
        public int Result { get; set; }
    }


}
