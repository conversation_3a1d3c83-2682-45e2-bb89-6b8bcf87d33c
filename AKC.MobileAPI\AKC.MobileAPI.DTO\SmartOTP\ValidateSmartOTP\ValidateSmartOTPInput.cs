﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.SmartOTP.ValidateSmartOTP
{
    public class ValidateSmartOTPInput
    {
        public string SessionId { get; set; }
        public string MemberCode { get; set; }
        public string Token { get; set; }
        public string OTP { get; set; }
        public string OTPType { get; set; }
        public string Flow { get; set; }

    }

    public class ValidateSmartOTPRequest
    {
        public string partner_id { get; set; }
        public string partner_key { get; set; }
        public string token { get; set; }
        public string app_id { get; set; }
        public string otp { get; set; }
        public string otp_type { get; set; }
    }
}
