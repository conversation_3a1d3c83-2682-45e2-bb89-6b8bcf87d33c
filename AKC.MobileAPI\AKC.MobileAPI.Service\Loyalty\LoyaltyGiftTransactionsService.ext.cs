using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.DTO.Loyalty.GiftSearch;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.Rewards;
using AKC.MobileAPI.DTO.Reward;
using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.Loyalty
{
    public partial class LoyaltyGiftTransactionsService
    {

        public async Task<GetGroupCodesOfGiftOutput> GetGroupCodesOfGift(string GiftCode)
        {
            // Assume that we have _cache of type IDistributedCache. You help me to write cache for this API Call , cache in 1hr
            // return await GetLoyaltyAsync<GetGroupCodesOfGiftOutput>("services/app/GiftGroups/GetWhichGroupsAGiftIn", new {GiftCode = GiftCode});
            
            var cacheKey = $"GetGroupCodesOfGift_{GiftCode}";
            
            var cachedData = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(cachedData))
            {
                return JsonConvert.DeserializeObject<GetGroupCodesOfGiftOutput>(cachedData);
            }
        
            var result = await GetLoyaltyAsync<GetGroupCodesOfGiftOutput>(
                "services/app/GiftGroups/GetWhichGroupsAGiftIn",
                new { GiftCode });
        
            var cacheEntryOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1)
            };
        
            var serializedResult = JsonConvert.SerializeObject(result);
            await _cache.SetStringAsync(cacheKey, serializedResult, cacheEntryOptions);
        
            return result;
        }
        
        public async Task<RedeemWithMoneyCardOutput> RedeemWithMoneyCard(RedeemWithMoneyCardInput input, HttpContext context)
        {
            var requestId = DateTime.UtcNow.Ticks + input.MemberCode;
            _logger.LogInformation($" {requestId} - RedeemWithMoneyCard start >> " + JsonConvert.SerializeObject(input));
            //Lấy từ cache hoặc gọi xuống loyalty để check category type của quà
            var categoryTypeCode = string.Empty;
            try
            {
                var keyOfCategoryType = "MobileApi_GiftCategoryType_" + input.GiftCode;
                categoryTypeCode = await GetCacheValueByKey(keyOfCategoryType);
                //Không lấy đc từ cache thì get từ loyalty
                if (string.IsNullOrEmpty(categoryTypeCode))
                {
                    var inputGetGiftCategoryType = new LoyaltyGetGiftCategoryTypeInput()
                    {
                        GiftCode = input.GiftCode
                    };
                    _logger.LogInformation($" {requestId} - RedeemWithMoneyCard GET_GIFT_CATEGORY_TYPE_CODE start"  + input.GiftCode);
                    var getGiftCategoryTypeResult = await GetLoyaltyAsync<LoyaltyGetGiftCategoryTypeOutput>(LoyaltyApiUrl.GET_GIFT_CATEGORY_TYPE_CODE, inputGetGiftCategoryType);
                    if (getGiftCategoryTypeResult.Success)
                    {
                        categoryTypeCode = getGiftCategoryTypeResult.Result.GiftCategoryTypeCode;
                        await SetCacheByKeyValueExpiredInMinute(keyOfCategoryType, getGiftCategoryTypeResult.Result.GiftCategoryTypeCode, 30);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($" {requestId} - RedeemWithMoneyCard GET_GIFT_CATEGORY_TYPE_CODE error: " + ex.Message);
                var result = new RedeemWithMoneyCardOutput()
                {
                    Error = "CantGetGiftCategoryType",
                    Result = new RedeemWithMoneyCardOutputInner()
                    {
                        IsNotEnoughBalance = false,
                        SuccessedRedeem = false,
                        Messages = "Không thể lấy được category type lúc này"
                    }
                };
                return result;
            }
            if (!string.IsNullOrEmpty(categoryTypeCode)
                && (categoryTypeCode.ToUpper() == GiftCategoryTypeCode.TopUpData || categoryTypeCode.ToUpper() == GiftCategoryTypeCode.TopUpPhone)
                && input.Quantity > 1)
            {
                var result = new RedeemWithMoneyCardOutput()
                {
                    Error = "QuantityMustBeOne",
                    Result = new RedeemWithMoneyCardOutputInner()
                    {
                        IsNotEnoughBalance = false,
                        SuccessedRedeem = false,
                        Messages = "Đối với TopUpPhone or TopUpData thì quantity phải bằng 1"
                    }
                };

                return result;
            }
            // KHÔNG CHECK CREDIT BALANCE
            // KHỞI TẠO merchantId bằng config: LINKID merchant id
            var merchantId = Convert.ToInt32(_configuration.GetSection("Reward" + MerchantNameConfig.VPID + ":MerchantId").Value);
            var checkTenantInternal = CheckTenantIsInternal(input.MerchantId);
            try
            {
                var merchantIdFromGiftCode = await GetMerchantIdFromGiftCode(new LoyaltyGiftGetMerchantIdFromGiftCodeInput()
                {
                    GiftCode = input.GiftCode
                });
                _logger.LogInformation($" {requestId} - GetMerchantIdFromGiftCode({input.GiftCode})___Result:{JsonConvert.SerializeObject(merchantIdFromGiftCode)}");
                if (merchantIdFromGiftCode.Success && merchantIdFromGiftCode.Result != null)
                {
                    if (merchantIdFromGiftCode.Result.MerchantId.HasValue)
                    {
                        merchantId = merchantIdFromGiftCode.Result.MerchantId.Value;
                    }
                }
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);

                    var result = new RedeemWithMoneyCardOutput()
                    {
                        Error = res.Code,
                        Result = new RedeemWithMoneyCardOutputInner()
                        {
                            Exception = res.Code,
                            IsNotEnoughBalance = false,
                            SuccessedRedeem = false,
                            Messages = res.Message
                        }
                    };
                    return result;
                }
                throw ex;
            }
            var orderCode = genOrderCode(input.GiftCode, input.MemberCode);
            var cts = new CancellationTokenSource();
            //Nếu là đổi quà cashout => check điểm cash out, nếu ko => check token balance
            if (!string.IsNullOrEmpty(categoryTypeCode) && categoryTypeCode.ToUpper() == GiftCategoryTypeCode.CashOut)
            {
                _logger.LogInformation("QUÀ TYPE CASHOUT HIỆN KHÔNG HỖ TRỢ THANH TOÁN BẰNG THẺ ĐIỂM");
                var result = new RedeemWithMoneyCardOutput()
                {
                    Error = "InvalidGift",
                    Result = new RedeemWithMoneyCardOutputInner()
                    {
                        IsNotEnoughBalance = true,
                        SuccessedRedeem = false,
                        Messages = "Quà này không thể đổi bằng thẻ điểm"
                    }
                };
                return result;
                
            }
            _logger.LogInformation($" {requestId} - RedeemWithMoneyCard check the moneycard in operator...");
                
            var resultBalance = await _rewardMemberService.GetMoneyCardOfMember(new GetMoneyCardOfMemberInput()
            {
                MemberCode = input.MemberCode,
                AvailableRedeem = true
            });
            _logger.LogInformation($" {requestId} - GetBalanceMember end " + JsonConvert.SerializeObject(resultBalance));
            if (resultBalance != null && resultBalance.Items != null && resultBalance.Items.Count > 0 
                && resultBalance.Items.Where(x => input.MoneyCardCodes.Contains(x.CardCode)).Sum(x => x.RemainingAmount) < input.TotalAmount)
            {
                // NẾU TẤT CẢ REMAINING AMOUNT CỦA LÔ THẺ TRUYỀN LÊN, SUM LẠI MÀ NHỎ HƠN SỐ AMOUNT CẦN ĐỔI, THÌ BÁO KO ĐỦ SỐ DƯ.
                var result = new RedeemWithMoneyCardOutput()
                {
                    Error = "BalanceNotEnough",
                    Result = new RedeemWithMoneyCardOutputInner()
                    {
                        IsNotEnoughBalance = true,
                        SuccessedRedeem = false,
                        Messages = "Tổng giá trị còn lại của các thẻ điểm không đủ để đổi quà này"
                    }
                };

                return result;
            }

            var request = new 
            {
                Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"), //input.Date,
                Description = input.Description,
                GiftCode = input.GiftCode,
                MemberCode = input.MemberCode,
                Quantity = input.Quantity,
                TotalAmount = input.TotalAmount,
                TransactionCode = orderCode,
                MerchantIdRedeem = merchantId,
                FlashSaleProgramCode = input.FlashSaleProgramCode,
                CardCode = string.Join(";", input.MoneyCardCodes),
                CardCodeTx = orderCode
            };

            // Kiểm tra quà bên loyalty và tạo đơn hàng redeem tạm. nếu tạo thành công thì mới process thanh toán (gọi sang reward)
            try
            {
                _logger.LogInformation($" {requestId} - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER start");
                var checkTransactionPending = await PostLoyaltyAsync<VerifyAndCreateRedeemOrderOutput>(LoyaltyApiUrl.VERIFY_OR_CREATE_REDEEM_ORDER, request, context);
                _logger.LogInformation($" {requestId} - RedeemWithMoneyCard VERIFY_OR_CREATE_REDEEM_ORDER end");
                if ((!checkTransactionPending.Success || (checkTransactionPending.Success && !checkTransactionPending.Result.IsSuccess)))
                {
                    var result = new RedeemWithMoneyCardOutput()
                    {
                        Error = "1022",
                        Result = new RedeemWithMoneyCardOutputInner()
                        {
                            Exception = "1022",
                            IsNotEnoughBalance = false,
                            SuccessedRedeem = false,
                            Messages = "Không thể đổi quà lúc này. Vui lòng thử lại sau."
                        }
                    };
                    return result;
                }
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);

                    var result = new RedeemWithMoneyCardOutput()
                    {
                        Error = res.Code,
                        Result = new RedeemWithMoneyCardOutputInner()
                        {
                            Exception = res.Code,
                            IsNotEnoughBalance = false,
                            SuccessedRedeem = false,
                            Messages = res.Message
                        }
                    };
                    return result;
                }
                throw ex;
            }

            var successPaymentToken = false;
            try
            {
                {
                    var requestRedeem = new RedeemUsingMoneyCardRequest()
                    {
                        OrderCode = orderCode,
                        MerchantId = merchantId,
                        MoneyCardIds = input.MoneyCardCodes,
                        PhoneNumber = null,
                        Reason = "REDEEM",
                        MemberCode = input.MemberCode,
                        TokenAmount = input.TotalAmount
                    };
                    _logger.LogInformation($" {requestId} - RedeemWithMoneyCard >> Call Reward RedeemUsingMoneyCard start... " + JsonConvert.SerializeObject(requestRedeem));
                    var resultRedeemReward = await _rewardGiftRedeemTransactionService.RedeemUsingMoneyCard(requestRedeem);
                    if (resultRedeemReward.Result == 202)
                    {
                        successPaymentToken = true;
                        _logger.LogError($" {requestId} - RedeemUsingMoneyCard Create redeem reward with status 202 {JsonConvert.SerializeObject(resultRedeemReward)}");
                        GetErrorValidation("RedeemRewardTransactionStatus202", "Create redeem reward with status 202");
                    }
                    _logger.LogInformation($" {requestId} - RedeemUsingMoneyCard CreateRedeem end");

                    successPaymentToken = true;
                }

                var result = new RedeemWithMoneyCardOutput()
                {
                    Result = new RedeemWithMoneyCardOutputInner()
                    {
                        SuccessedRedeem = false,
                    }
                };
                // Nếu là tenant internal (LinkID) thì redeem như cũ, ngược lại thì redeem sang merchant kho quà
                if (checkTenantInternal)
                {
                    _logger.LogInformation($" {requestId} - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION start");
                    result = await PostLoyaltyAsync<RedeemWithMoneyCardOutput>(LoyaltyApiUrl.GIFTTRANSACTION_CREATEREDEEMTRANSACTION, request, context);
                    _logger.LogInformation($" {requestId} - RedeemUsingMoneyCard GIFTTRANSACTION_CREATEREDEEMTRANSACTION end");
                }
                else
                {
                    _logger.LogInformation($" {requestId} - CreateRedeemMerchantTransaction start");
                    // result = await _merchantGiftStoreService.CreateRedeemMerchantTransaction(tenantIdWithMerchant, request); FIXME
                    _logger.LogInformation($" {requestId} - CreateRedeemMerchantTransaction end");
                }
                if (!string.IsNullOrWhiteSpace(result.Result.Exception) || !result.Result.SuccessedRedeem)
                {

                    if (!result.Result.Timeout)
                    {
                        _logger.LogInformation("retryRevertToken start");
                        var requestRevert = new RevertRedeemUsingMoneyCardRequest()
                        {
                            MemberCode = input.MemberCode,
                            OrderCode = orderCode,
                            Reason = "Revert Tx When Redeem",
                        };

                        await RetryRevertMoneyCardTransaction(requestRevert);
                        _logger.LogInformation("retryRevertToken end");
                    }
                }

                if (result.Success && result.Result.SuccessedRedeem)
                {
                    // Ghi nhận action cho các hành động nạp viễn thông IMEDIA
                    if (!string.IsNullOrEmpty(result.Result.ActionCode) && !string.IsNullOrEmpty(result.Result.ActionFilter))
                    {
                        try
                        {
                            var inputActionRequest = new LoyaltyInputActionInput()
                            {
                                Actions = new List<ActionRequestDto>()
                                {
                                    new ActionRequestDto()
                                    {
                                        TransactionCode = orderCode,
                                        Type = 1,
                                        OriginalTransactionCode = "",
                                        ActionCode = result.Result.ActionCode,
                                        ActionFilter = new ActionListValueFilterReward()
                                        {
                                            ListCodeFilter = result.Result.ActionFilter,
                                        },
                                        MemberCode = input.MemberCode,
                                        Value = (int)Decimal.Truncate(input.TotalAmount),
                                        ActionTime = DateTime.UtcNow,
                                        MlmApplied = false,
                                        MLMDealerChannel = null,
                                        MLMDistributionChannel = null,
                                        ReferenceCode = null,
                                        Tag = "",
                                        RankCode = ""
                                    }
                                }
                            };
                            // Send to LinkID
                            await _rewardsService.InputAction(inputActionRequest);
                        }
                        catch (Exception e)
                        {
                            _logger.LogError($" {requestId} -  >> Error while sending InputAction to LinkId tenant for Topup Transaction");
                            _logger.LogError(e.StackTrace);
                        }
                    }
                }
                return result;
            }
            catch (WebException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError")
                    {
                        _logger.LogInformation($" {requestId} - retryRevertToken start");
                        var requestRevert = new RevertRedeemUsingMoneyCardRequest()
                        {
                            MemberCode = input.MemberCode,
                            OrderCode = orderCode,
                            Reason = "Revert Tx When Redeem",
                        };

                        await RetryRevertMoneyCardTransaction(requestRevert);
                        _logger.LogInformation($" {requestId} - retryRevertToken end");
                    }
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = res.Code == "SystemError" ? "504" : res.Code,
                        ErrorMessage = res.Code != "SystemError" ? res.Message?.ToString() : res.MessageDetail?.ToString(),
                    }, successPaymentToken);
                    throw ex;
                }
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError")
                    {
                        _logger.LogInformation($" {requestId} - retryRevertToken start");
                        var requestRevert = new RevertRedeemUsingMoneyCardRequest()
                        {
                            MemberCode = input.MemberCode,
                            OrderCode = orderCode,
                            Reason = "Revert Tx When Redeem",
                        };

                        await RetryRevertMoneyCardTransaction(requestRevert);
                        _logger.LogInformation($" {requestId} - retryRevertToken end");
                    }
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = res.Code == "SystemError" ? "504" : res.Code,
                        ErrorMessage = res.Code != "SystemError" ? res.Message?.ToString() : res.MessageDetail?.ToString(),
                    }, successPaymentToken);
                    throw ex;
                }
                if (!cts.Token.IsCancellationRequested)
                {
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = "408",
                        ErrorMessage = "Timed Out with: " + ex.Message,
                    }, successPaymentToken);
                    throw new Exception("Timed Out with: ", ex);
                }
                else
                {
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = "504",
                        ErrorMessage = "Cancelled for some other reason: " + ex.Message,
                    }, successPaymentToken);
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (LoyaltyTimeoutException ex)
            {
                _logger.LogInformation($"RedeemWithMoneyCard___ {requestId} - LoyaltyTimeoutException - retryRevertToken start");
                var requestRevertWhenTimeout = new RevertRedeemUsingMoneyCardRequest()
                {
                    MemberCode = input.MemberCode,
                    OrderCode = orderCode,
                    Reason = "Revert Tx When Redeem - TimeOut",
                };

                await RetryRevertMoneyCardTransaction(requestRevertWhenTimeout);
                _logger.LogInformation($"RedeemWithMoneyCard___ {requestId} - LoyaltyTimeoutException - retryRevertToken end");
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = request.TransactionCode,
                    ErrorCode = "408",
                    ErrorMessage = "Timed Out with: " + ex.Message,
                }, successPaymentToken);
                throw ex;
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    if (res.Code != "0" && res.Code != "SystemError")
                    {
                        _logger.LogInformation($" {requestId} - retryRevertToken start");
                        var requestRevert = new RevertRedeemUsingMoneyCardRequest()
                        {
                            MemberCode = input.MemberCode,
                            OrderCode = orderCode,
                            Reason = "Revert Tx When Redeem",
                        };

                        await RetryRevertMoneyCardTransaction(requestRevert);
                        _logger.LogInformation($" {requestId} - retryRevertToken end");
                    }
                    await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                    {
                        TransactionCode = request.TransactionCode,
                        ErrorCode = res.Code == "SystemError" ? "504" : res.Code,
                        ErrorMessage = res.Code != "SystemError" ? res.Message?.ToString() : res.MessageDetail?.ToString(),
                    }, successPaymentToken);
                    throw ex;
                }
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    if (res.Code == "RedeemRewardTransactionStatus202")
                    {
                        await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                        {
                            TransactionCode = request.TransactionCode,
                            ErrorCode = "202",
                            ErrorMessage = "" + res.MessageDetail,
                        }, successPaymentToken);
                        throw new Exception("" + res.MessageDetail);
                    }
                }

                _logger.LogInformation($"RedeemWithMoneyCard___ {requestId} - General Exception - retryRevertToken start");
                var requestRevertWhenOtherError = new RevertRedeemUsingMoneyCardRequest()
                {
                    MemberCode = input.MemberCode,
                    OrderCode = orderCode,
                    Reason = "Revert Tx When Redeem - Other Error",
                };

                await RetryRevertMoneyCardTransaction(requestRevertWhenOtherError);
                _logger.LogInformation($"RedeemWithMoneyCard___ {requestId} - General Exception - retryRevertToken end");
                await UpdateErrorWhenCreateRedeem(new UpdateErrorWhenCreateRedeemPayment()
                {
                    TransactionCode = request.TransactionCode,
                    ErrorCode = "504",
                    ErrorMessage = "Cancelled for some other reason: " + ex.Message,
                }, successPaymentToken);
                throw ex;
            }

        }
        
        private async Task RetryRevertMoneyCardTransaction(RevertRedeemUsingMoneyCardRequest request)
        {
            var retryNum = 3;
            while (retryNum != 0)
            {
                var result = await RevertMoneyCardTransaction(request);
                if (result)
                {
                    retryNum = 0;
                }
                else
                {
                    Thread.Sleep(1000);
                    retryNum--;
                }
            }
        }
        private async Task<bool> RevertMoneyCardTransaction(RevertRedeemUsingMoneyCardRequest request)
        {
            try
            {
                await _rewardGiftRedeemTransactionService.RevertRedeemUsingMoneyCard(request);
                return true;
            }
            catch
            {
                return false;
            }
        }
        public async Task<LoyaltyGetGiftInfoOutput> GetGiftInfo(LoyaltyGetGiftInfoInput input)
        {
            var cacheKey = $"GetGiftInfo_{input.MemberCode}_{input.OrderCode}";

            var cachedData = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(cachedData))
            {
                return JsonConvert.DeserializeObject<LoyaltyGetGiftInfoOutput>(cachedData);
            }

            var result = await GetLoyaltyAsync<LoyaltyGetGiftInfoOutput>(LoyaltyApiUrl.GET_GIFT_INFO_BY_ORDER_CODE,
                new { OrderCode = input.OrderCode, MemberCode = input.MemberCode });

            var cacheEntryOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(10)
            };

            var serializedResult = JsonConvert.SerializeObject(result);
            await _cache.SetStringAsync(cacheKey, serializedResult, cacheEntryOptions);

            return result;
        }

    }
}
