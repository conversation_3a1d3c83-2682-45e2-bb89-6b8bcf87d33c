﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftTransactions
{
    public class LoyaltyGetAllWithTopupPhoneInput
    {
        [Required]
        public string MemberCodeFilter { get; set; }
        public string StatusFilter { get; set; }
        public DateTime? FromDateFilter { get; set; }
        public DateTime? ToDateFilter { get; set; }
        public string ThirdPartyCategoryIdFilter { get; set; }
        public string FullGiftCategoryCodeFilter { get; set; }
        public int? ThirdPartyBrandMappingIdFilter { get; set; }
        public int? BrandIdFilter { get; set; }
        [Range(0, int.MaxValue)]
        public int MaxResultCount { get; set; }
        [Range(0, int.MaxValue)]
        public int SkipCount { get; set; }
        public string Sorting { get; set; }
        public string EgiftStatusFilter { get; set; }
    }
}
