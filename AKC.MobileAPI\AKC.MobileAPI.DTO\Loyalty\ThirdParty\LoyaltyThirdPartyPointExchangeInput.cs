﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.ThirdParty
{
    public class LoyaltyThirdPartyPointExchangeInput
    {
        public string AccessToken { get; set; }
        [Required]
        [Range(1, Double.MaxValue)]
        public int MerchantId { get; set; }
        // [Required]
        public string IdNumber { get; set; }
       // [Required]
        public string MemberCode { get; set; }
        [Required]
        [Range(1, Double.MaxValue)]
        public long ExchangeAmount { get; set; }
        public long PointExchangeRate { get; set; }
        public long BaseUnit { get; set; }
    }

    public class ExchangeLynkiDToPartnerPointRes : LoyaltyThirdPartyPointExchangeItem
    {
        public string Code { get; set; }
        public string Message { get; set; }
    }
    public class ExchangeLynkiDToPartnerPointReq
    {
        public int MerchantId { get; set; }
        public string MemberCode { get; set; }
        public decimal Amount { get; set; }
        public string Note {get; set;  }
        public string TransactionCode {get; set;  }
    }
}
