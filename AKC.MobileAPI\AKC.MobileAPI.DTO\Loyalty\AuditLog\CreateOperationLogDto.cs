﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.AuditLog
{
    public class CreateOperationLogDto
    {
        public CreateOperationLogDto()
        {
            OperationLogs = new List<OperationLogDto>();
        }
        public List<OperationLogDto> OperationLogs { get; set; }
    }
    public partial class OperationLogDto
    {
        public int? TenantId { get; set; }

        public string Code { get; set; }

        public string ReferenceKey { get; set; }

        public string MemberId { get; set; }

        public string PartnerCode { get; set; }

        public string DestIP { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public bool Status { get; set; }

        public string ErrorCode { get; set; }

        public string RequestMsg { get; set; }

        public string ResponseMsg { get; set; }

        public string ServiceName { get; set; }

        public bool IsRewardTranfered { get; set; }
    }
}
