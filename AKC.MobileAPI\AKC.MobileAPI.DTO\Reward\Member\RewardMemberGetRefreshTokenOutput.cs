﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberGetRefreshTokenOutput
    {
        public RewardMemberGetRefreshTokenItems Items { get; set; }
        public string Message { get; set; } = "Success";
        public string MessageDetail { get; set; }
        public int Result { get; set; }
    }

    public class RewardMemberGetRefreshTokenItems
    {
        public string MemberCode { get; set; }
       
        public string IdCard { get; set; }

        public List<ItemPartnerOutPut> ItemPartner { get; set; }
    }
    public class ItemPartnerOutPut
    {
        public string RefreshToken { get; set; }

        public string PartnerKey { get; set; }
    }
}