﻿using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.Reward;
using Microsoft.Extensions.Caching.Distributed;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Common
{
    public class ValidationMemberCode: IValidationMemberCode
    {
        private readonly IDistributedCache _cache;
        private readonly IRewardMemberService _memberService;

        public ValidationMemberCode(
            IDistributedCache cache,
            IRewardMemberService memberService
        )
        {
            _cache = cache;
            _memberService = memberService;
        }
        public async Task<bool> ValidateToken(string authToken, string memberCode)
        {
            var idToken = authToken.Replace("Bearer ", "").Replace("bearer ", "");
            var token = await FirebaseAdmin.Auth.FirebaseAuth.DefaultInstance.VerifyIdTokenAsync(idToken);
            if (token == null || string.IsNullOrWhiteSpace(token.Uid))
                return false;

            var memberCodeCache =  await _cache.GetStringAsync(token.Uid);
            if (string.IsNullOrWhiteSpace(memberCodeCache)) {
                //get memberCode theo firebase Id lên rồi campare
                //sau do set vao cache
                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(6),
                    SlidingExpiration = TimeSpan.FromDays(3)
                };
                var check = await _memberService.VerifyAuthenWithMemberCode(new VerifyAuthenWithMemberCodeInput()
                {
                    FirebaseId = token.Uid,
                    MemberCode = memberCode,
                });
                if (check != null && check.items.IsMapping)
                {
                    await _cache.SetStringAsync(token.Uid, memberCode, options);
                    return true;
                }
                return false; // Member code và firebase không khớp
            }
            return memberCodeCache == memberCode;
        }
    }
}
