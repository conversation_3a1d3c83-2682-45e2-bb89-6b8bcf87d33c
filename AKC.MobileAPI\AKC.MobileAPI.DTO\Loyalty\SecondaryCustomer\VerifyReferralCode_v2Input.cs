﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer
{
    public class VerifyReferralCode_v2DTO 
    {
        public string InviterRefCode { get; set; }
        // số điện thoại của người đ<PERSON> mời
        public string InvitedPhoneNumber { get; set; }
        public string InvitedMemberCode { get; set; }

    }

    public class VerifyReferralCode_v2Input : VerifyReferralCode_v2DTO
    {
        public string MemberCode { get; set; }
    }

    public class VerifyRefferralCode_v2Output
    {
        public bool isSuccess { get; set; }
        public string Message { get; set; }
    }

    public class GetInforCampaignRefOutput
    {
        public string Description { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public DateTime EffectiveTo { get; set; }
        public string CampaignName { get; set; }
        public string ImageLink { get; set; }
        public int CampaignId { get; set; }
        public string LinkReferralRules { get; set; }
    }

    public class CheckMemberRegisterCampaignOutput
    {
        public string MemberCode { get; set; }
        public bool isRegister { get; set; }
        public int? CampaignId { get; set; }
    }
    public class CheckMemberRegisterCampaignInput
    {
        [Required]
        public string MemberCode { get; set; }
        [Required]
        public int CampaignId { get; set; }
    }
    public class RegisterCampaignFriendReferralInput
    {
        public string MemberCode { get; set; }
        public int? CampaignId { get; set; }
    }

    public class RegisterCampaignFriendReferralOutput
    {
       public string isSuccess { get; set; }
       public string Message { get; set; }
    }
}
