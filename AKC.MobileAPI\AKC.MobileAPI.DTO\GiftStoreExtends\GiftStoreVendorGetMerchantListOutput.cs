﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.GiftStoreExtends
{
    public class GiftStoreVendorGetMerchantListOutput
    {
        public int Result { get; set; }
        public string Message { get; set; }
        public List<GiftStoreVendorGetMerchantListItem> Items { get; set; }
    }

    public class GiftStoreVendorGetMerchantListItem
    {
        public int MerchantId { get; set; }
        public string WalletAddress { get; set; }
        public string MerchantName { get; set; }
        public string Logo { get; set; }
        public string MerchantType { get; set; }
        public decimal MerchantExchangeRate { get; set; }
        public decimal BaseUnit { get; set; }
        public decimal CurrencyExchangeRate { get; set; }
    }
}
