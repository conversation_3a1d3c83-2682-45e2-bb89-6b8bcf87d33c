﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Article
{
    public class GetAllArticleAndRelatedNewsInput_Optimize_New
    {
        public string PhoneNumber { get; set; }
        public string MemberCode { get; set; }
        public int MaxItem { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
        public string Sorting { get; set; }
    }

    public class GetPopupOnAppInput
    {
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
    }
}
