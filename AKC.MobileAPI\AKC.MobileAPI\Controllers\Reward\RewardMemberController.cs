﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Runtime.Intrinsics.X86;
using System.Text;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.DTO.Loyalty.Member;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.MobileAPI;
using AKC.MobileAPI.DTO.Reward;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.MemberOtp;
using AKC.MobileAPI.DTO.ThirdParty.Appota;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Microsoft.Extensions.Caching.Distributed;

namespace AKC.MobileAPI.Controllers.Reward
{
    [Route("api/Member")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class RewardMemberController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IRewardMemberService _memberService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltySecondaryCustomersService _loyaltySecondaryCustomersService;
        private readonly ILoyaltyThirdPartyService _loyaltyThirdPartyService;
        private readonly IConfiguration _configuration;
        private readonly ICommonHelperService _commonHelperService;
        private readonly ILoyaltyGiftTransactionsService _giftTransactionsService;
        private readonly IDistributedCache _cache;
        public RewardMemberController(
            ILogger<RewardMemberController> logger,
            ILoyaltyGiftTransactionsService x,
            IRewardMemberService memberService,
            IConfiguration configuration,
            IExceptionReponseService exceptionReponseService,
            ILoyaltySecondaryCustomersService loyaltySecondaryCustomersService,
            ILoyaltyThirdPartyService loyaltyThirdPartyService,
            ICommonHelperService commonHelperService, IDistributedCache cache)
        {
            _logger = logger;
            _giftTransactionsService = x;
            _configuration = configuration;
            _memberService = memberService;
            _exceptionReponseService = exceptionReponseService;
            _loyaltySecondaryCustomersService = loyaltySecondaryCustomersService;
            _loyaltyThirdPartyService = loyaltyThirdPartyService;
            _commonHelperService = commonHelperService;
            _cache = cache;
        }

        [HttpGet]
        [Route("View")]
        public async Task<ActionResult<RewardMemberViewOutput>> View([FromQuery] RewardMemberRequestInput request)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, request.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.View(request, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "View Member Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        
        [HttpPost]
        [Route("GetFriendName")]
        public async Task<ActionResult<FindOtherUserByPhoneNumberOutput>> GetFriendName(FindOtherUserByPhoneNumberInput request)
        {
            try
            {
                var result = await _memberService.GetFriendName(request);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Get Name Of Friend Error: - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        
        [HttpPost]
        [Route("UpdateDeviceId")]
        public async Task<ActionResult<UpdateMemberDeviceIdOutput>> UpdateDeviceId(UpdateDeviceIdInput request)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, request.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.UpdateDeviceId(request);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Get Name Of Friend Error: - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        // Called from LinkID Mobile API
        [HttpPost]
        [Route("RemoveLienKet")]
        public async Task<ActionResult<RemoveLienKetOutput>> RemoveLienKet(RemoveLienKetInput input)
        {
            try
            {
                _logger.LogInformation("RemoveLienKet >> " + JsonConvert.SerializeObject(input));
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.NationalId);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }

                var memberInfo = await _memberService.GetInfo(new RewardMemberGetInfoInput()
                {
                    NationalId = input.NationalId
                });
                var cif = _memberService.GetPartnerMemberInfoByConnection(
                    new GetCifByMemberRequest()
                    {
                        MemberId = memberInfo.Id, MerchantId = input.MerchantId
                    }).Result;
                var removedConnectionResult = await _memberService.DisconnectMerchant(input);
                _logger.LogInformation("RemoveLienKet >> " + input.NationalId + " >> Removed in operator");
                var inputRemove = new LoyaltyThirdPartyRemoveConnectedMerchant()
                {
                    MemberCode = removedConnectionResult.Item.MemberCode,
                    LinkID_MemberID = removedConnectionResult.Item.MemberId,
                };
                
                await _loyaltyThirdPartyService.RemoveConnectedMerchant(removedConnectionResult, inputRemove, cif);
                _logger.LogInformation("RemoveLienKet >> " + input.NationalId + " >> Removed in partner site");
                await _memberService.Disconnect(new RemoveConnectionInput()
                {
                    LinkIdMemberCode = input.NationalId, MerchantIdInLinkid = input.MerchantId
                });
                _logger.LogInformation("RemoveLienKet >> " + input.NationalId + " >> optional >> call to integration-partner-api");
                await _memberService.RewardSendAckAfterDisconnected(new RewardSendAckAfterConnectedInput()
                {
                    MemberId = inputRemove.LinkID_MemberID, MerchantId = input.MerchantId, NationalId = inputRemove.MemberCode
                });
                // Ddasnh dau ack cua viec remove cnntion
                _logger.LogInformation("RemoveLienKet >> " + input.NationalId + " >> Send ACK! Done!");
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Update Member User Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }

            var result = new RemoveLienKetOutput()
            {
                Message = "Success",
                MessageDetail = null,
                Result = 200,
            };
            return StatusCode(200, result);
        }
        [HttpPost]
        [Route("Update")]
        public async Task<ActionResult<RewardMemberUpdateOutput>> Update(RewardMemberUpdateInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.NationalId);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var resultUpdate = await _memberService.Update(input);
                _logger.LogInformation("Update member - " + JsonConvert.SerializeObject(resultUpdate));
                if (resultUpdate.Item.ListMerchantRemovedConnectManual != null && resultUpdate.Item.ListMerchantRemovedConnectManual.Count > 0)
                {
                    // var memberInfo = resultUpdate.Item;
                    // var inputRemove = new LoyaltyThirdPartyRemoveConnectedMerchant()
                    // {
                    //     MemberCode = memberInfo.MemberCode,
                    //     LinkID_MemberID = memberInfo.MemberId,
                    // };
                    // UPDATE MEMBER - API NÀY TỪ LÂU ĐÃ KHÔNG CHO PHÉP GỠ KẾT NỐI. CÓ API RemoveLienket dành riêng cho việc gỡ kết nối
                    // await _loyaltyThirdPartyService.RemoveConnectedMerchant(resultUpdate, inputRemove);
                }
                //if (resultUpdate.Item.IsChangePhoneNumber && resultUpdate.Item.ListMerchantNeedUpdatePhone != null && resultUpdate.Item.ListMerchantNeedUpdatePhone.Count > 0)
                //{
                //    await _loyaltyThirdPartyService.UpdatePhoneNumber(new LoyaltyThirdPartyUpdatePhoneNumberInput()
                //    {
                //        LinkIdMemberCode = resultUpdate.Item.MemberCode,
                //        LinkIdMemberId = resultUpdate.Item.MemberId,
                //        MerchantIds = resultUpdate.Item.ListMerchantNeedUpdatePhone,
                //        PhoneNumber = input.Phone,
                //    });
                //}
                var result = new RewardMemberUpdateOutput()
                {
                    Message = "Success",
                    MessageDetail = null,
                    Result = 200,
                };
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Update Member User Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("ViewPoint")]
        public async Task<ActionResult<RewardMemberViewPointOutput>> ViewPoint([FromQuery] RewardMemberRequestInput request)
        {
            //_logger.LogInformation("ViewPoint_step0");
            try
            {
                //_logger.LogInformation("ViewPoint_step1");
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, request.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                //_logger.LogInformation("ViewPoint_step2");
                var result = await _memberService.ViewPoint(request, authorization);
                //_logger.LogInformation("ViewPoint_step3");
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                //_logger.LogInformation("ViewPoint_step5");
                //_logger.LogInformation("ViewPoint_Header_" + JsonConvert.SerializeObject(Request.Headers));
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "View point Member User Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("Create")]
        public async Task<ActionResult<RewardMemberVerifyOrCreateOutput>> Create(RewardMemberCreateInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var result = await _memberService.Create(input, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Create Member User Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        // [HttpPost]
        // [Route("CreateRegisterLog")]
        // [AllowAnonymous]
        // public async Task<ActionResult<RewardMemberCreateRegisterLogOutput>> CreateRegisterLog(RewardMemberCreateRegisterLog input)
        // {
        //     try
        //     {
        //         var secretKey = _configuration.GetSection("AES:SecretKey").Value;
        //         var rawData = AESEncrytDecry.DecryptStringAES(input.Data, secretKey);
        //
        //         if (rawData != "keyError")
        //         {
        //             var dto = JsonConvert.DeserializeObject<RewardMemberCreateRegisterLogInput>(rawData);
        //             var result = await _memberService.CreateRegisterLog(dto);
        //             return StatusCode(200, result);
        //         }
        //
        //         return StatusCode(400, new Exception("Invalid request"));
        //     }
        //     catch (Exception ex)
        //     {
        //         var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //         _logger.LogError(ex, "Create register member log - " + JsonConvert.SerializeObject(ex));
        //
        //         return StatusCode(400, res);
        //     }
        // }

 
        [HttpPost]
        [Route("VerifyReferralCode")]
        public async Task<ActionResult<VerifyRefferralCode_v2Output>> VerifyReferralCode(LoyaltyVerifyReferralCodeInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.NationalId);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltySecondaryCustomersService.VerifyReferralCode_v2(new VerifyReferralCode_v2DTO { InviterRefCode = input.ReferralCode, InvitedMemberCode = input.NationalId });
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Verify referral code error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("CreateOrUpdatePinCode")]
        public async Task<ActionResult<RewardMemberCreateOrUpdatePinCodeResponse>> CreateOrUpdatePinCode(MobileAPICreateOrUpdatePinCodeInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.CreateOrUpdatePinCode(input, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "CreateOrUpdatePinCode Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        //[HttpGet]
        //[Route("HasPinCode")]
        //public async Task<ActionResult<RewardMemberHasPinCodeResponse>> HasPinCode(RewardMemberHasPinCodeRequest input)
        //{
        //    try
        //    {
        //        var result = await _memberService.HasPinCode(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "HasPinCode Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        [HttpPost]
        [AllowAnonymous]
        [Route("VerifyPinCode")]
        public async Task<ActionResult<RewardMemberVerifyPinCodeResponse>> VerifyPinCode(RewardMemberVerifyPinCodeRequest input)
        {
            try
            {
                var result = await _memberService.VerifyPinCode(input);

                if (!result.Success)
                {
                    return StatusCode(400, new RewardErrorResponse()
                    {
                        Result = 400,
                        Code = "CanNotVerifyPinCodeNow",
                        Message = "Can Not Verify PinCode Now"
                    });
                }

                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                _logger.LogError(ex, "Verify PinCode Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("CheckReferralCodeExistance")]
        public async Task<ActionResult<LoyaltyMemberCheckReferralCodeExistanceOutput>> CheckReferralCodeExistance(LoyaltyMemberCheckReferralCodeExistanceInput input)
        {
            try
            {
                var result = await _loyaltySecondaryCustomersService.CheckReferralCodeExistance(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Check ReferralCode Existance - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("TempPointTrans/GetByMemberId")]
        public async Task<ActionResult<RewardMemberTempPointTransGetByIdOutput>> GetTempPointTransByMemberId([FromQuery] RewardMemberTempPointTransGetByIdInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.NationalId);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.GetTempPointTransByMemberId(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetAllByMemberID error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("TokenTrans/GetByMemberId")]
        public async Task<ActionResult<RewardMemberTokenTransGetByIdOutput>> GetTokenTransByMemberId([FromQuery] RewardMemberTokenTransGetByIdInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.NationalId);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.GetTokenTransByMemberId(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetAllByMemberID error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        
        [HttpPost]
        [Route("TokenTrans/GetTokenTransById")]
        public async Task<ActionResult<RewardMemberGetTokenTransDetailOutput>> GetTokenTransById(RewardMemberGetTokenTransDetailInput input)
        {
            try
            {
                _logger.LogInformation("GetTokenTransById >> MemberCode = " + input.MemberCode + "; TokenTransId = " + input.TokenTransId); 
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.GetTokenTransById(input);
                if (result == null)
                {
                    return StatusCode(400, "TokenTransId or MemberCode not match");
                }
                var OrderCode = result.OrderCode;
                if ("Redeem".Equals(result.ActionType))
                {
                    var giftRedeemSearch = _giftTransactionsService.GetAllWithEGift(new LoyaltyGetAllWithEGiftInput()
                    {
                        OwnerCodeFilter = input.MemberCode,
                        SkipCount = 0, MaxResultCount =  10, GiftTransactionCode = OrderCode
                    }).Result;
                    if (giftRedeemSearch != null)
                    {
                        if (giftRedeemSearch.Success)
                        {
                            if (giftRedeemSearch.Result != null && giftRedeemSearch.Result.Items.Count > 0)
                            {
                                var giftRT = giftRedeemSearch.Result.Items[0];
                                var giftCode = giftRT.GiftTransaction.GiftCode;
                                var egift = giftRT.EGift;
                                
                                result.GiftId = giftRT.GiftTransaction.GiftId?.ToString() ?? "";
                                result.GiftName = giftRT.GiftTransaction.GiftName;
                                result.GiftImage = giftRT.ImageLinks?.Where(x => x.Code == giftCode).FirstOrDefault()?.Link;
                                result.GiftPaidCoin = giftRT.GiftTransaction.Coin ?? 0;
                                result.Vendor = giftRT.VendorInfo;
                                result.BrandImage = giftRT.BrandInfo?.BrandImage;
                                if (egift != null)
                                {
                                    result.EGiftExpiredDate = egift.ExpiredDate;
                                }
                            }
                        }
                    }
                }
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetTokenTransById error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        //Loyalty
        [HttpPut]
        //[ApiExplorerSettings(IgnoreApi = true)]
        [Route("UpdateNotificationSetting")]
        public async Task<ActionResult<LoyaltyResponse<string>>> UpdateNotificationSetting([FromBody] UpdateNotificationSettingInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltySecondaryCustomersService.UpdateNotificationSetting(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "UpdateNotificationSetting Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
		
        
        [HttpPost]
        [Route("VerifyProviderIdByPhoneNumber")]
        public async Task<ActionResult<VerifyProviderIdByPhoneNumberResponse>> VerifyProviderIdByPhoneNumber(VerifyProviderIdByPhoneNumberRequest input)
        {
            try
            {
                var result = await _memberService.VerifyProviderIdByPhoneNumber(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Verify Provider Id By PhoneNumber Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }
        
        [HttpPost]
        [Route("GiveFeedback")]
        public async Task<ActionResult<VerifyProviderIdByPhoneNumberResponse>> GiveFeedback(GiveFeedbackInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.GiveFeedback(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Verify Provider Id By PhoneNumber Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        //[HttpPost]
        //[Route("VerifyOrCreate")]
        //public async Task<ActionResult<RewardMemberVerifyOrCreateOutput>> VerifyOrCreate(RewardMemberVerifyOrCreateInput input)
        //{
        //    try
        //    {
        //        var authorization = Request.Headers["Authorization"].ToString();
        //        var result = await _memberService.VerifyOrCreate(input, authorization);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //        _logger.LogError(ex, "Verify or create Error - " + JsonConvert.SerializeObject(ex));
        //        return StatusCode(400, res);
        //    }
        //}

        [HttpPut]
        //[ApiExplorerSettings(IgnoreApi = true)]
        [Route("UpdatePhoneNumber")]
        public async Task<ActionResult<UpdatePhoneNumberOutput>> UpdatePhoneNumber([FromBody] MobileUpdatePhoneNumberInput input)
        {
            var authorization = Request.Headers["Authorization"].ToString();
            var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
            if (checkAuthen != null)
            {
                return StatusCode(401, checkAuthen);
            }
            var resultUpdate = await _memberService.UpdatePhoneNumber(input, authorization);
            _logger.LogDebug("Update phone number result API: " + JsonConvert.SerializeObject(resultUpdate));
            if (resultUpdate.Status == UpdatePhoneNumberStatus.Success)
            {
                if (resultUpdate.HasUpdatePartnerPhone && resultUpdate.ListMerchantNeedUpdatePhone != null && resultUpdate.ListMerchantNeedUpdatePhone.Count > 0)
                {
                    await _loyaltyThirdPartyService.UpdatePhoneNumber(new LoyaltyThirdPartyUpdatePhoneNumberInput()
                    {
                        LinkIdMemberCode = resultUpdate.MemberCode,
                        LinkIdMemberId = resultUpdate.MemberId.Value,
                        MerchantIds = resultUpdate.ListMerchantNeedUpdatePhone,
                        PhoneNumber = input.PhoneNumber,
                    });
                }
            }
            var result = new UpdatePhoneNumberOutput()
            {
                Message = resultUpdate.Message,
                Status = resultUpdate.Status,
            };
            return StatusCode(200, result);
        }

        [HttpPost]
        [Route("AccountHavePhoneNumber")]
        public async Task<ActionResult<RewardMemberAccountHavePhoneNumberOutput>> AccountHavePhoneNumber(RewardMemberAccountHavePhoneNumberInput input)
        {
            var authorization = Request.Headers["Authorization"].ToString();
            var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
            if (checkAuthen != null)
            {
                return StatusCode(401, checkAuthen);
            }
            var result = await _memberService.AccountHavePhoneNumber(input);
            return StatusCode(200, result);
        }

        [HttpGet]
        [Route("RevokeToken")]
        public async Task<ActionResult<RewardMemberRevokeTokenResponse>> RevokeToken()
        {
            var authorization = Request.Headers["Authorization"].ToString();
            var result = await _memberService.RevokeToken(authorization);
            return StatusCode(200, result);
        }

        [HttpGet]
        [Route("GetMemberLoginByFirebaseId")]
        public async Task<ActionResult<RewardMemberGetMemberLoginByFirebaseIdOutput>> GetMemberLoginByFirebaseId()
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var result = await _memberService.GetMemberLoginByFirebaseId(authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetMemberLoginByFirebaseId Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetUsagePriority")]
        public async Task<ActionResult<RewardMemberGetUsagePriorityOutput>> GetUsagePriority([FromQuery] RewardMemberGetUsagePriorityInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.GetUsagePriority(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetUsagePriority Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetCommonUsingFirebaseOtp")]
        [AllowAnonymous]
        public async Task<ActionResult<RewardGetUsingFirebaseOtpDto>> GetCommonUsingFirebaseOtp()
        {
            try
            {
                var result = await _memberService.GetCommonUsingFirebaseOtp();
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetCommonUsingFirebaseOtp Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        // [HttpPost]
        // [Route("UpdateUsagePriority")]
        // public async Task<ActionResult<RewardMemberUpdateUsagePriorityOutput>> UpdateUsagePriority([FromBody] RewardMemberUpdateUsagePriorityInput input)
        // {
        //     try
        //     {
        //         var authorization = Request.Headers["Authorization"].ToString();
        //         var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
        //         if (checkAuthen != null)
        //         {
        //             return StatusCode(401, checkAuthen);
        //         }
        //         var result = await _memberService.UpdateUsagePriority(input);
        //         return StatusCode(200, result);
        //     }
        //     catch (Exception ex)
        //     {
        //         var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //         _logger.LogError(ex, "UpdateUsagePriority Error - " + JsonConvert.SerializeObject(ex));
        //
        //         return StatusCode(400, res);
        //     }
        // }

        // [HttpPost]
        // [Route("UpdatePointUsageType")]
        // public async Task<ActionResult<RewardMemberUpdatePointUsageTypeOutput>> UpdatePointUsageType([FromBody] RewardMemberUpdatePointUsageTypeInput input)
        // {
        //     try
        //     {
        //         var authorization = Request.Headers["Authorization"].ToString();
        //         var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
        //         if (checkAuthen != null)
        //         {
        //             return StatusCode(401, checkAuthen);
        //         }
        //         var result = await _memberService.UpdatePointUsageType(input);
        //         return StatusCode(200, result);
        //     }
        //     catch (Exception ex)
        //     {
        //         var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //         _logger.LogError(ex, "UpdateUsagePriority Error - " + JsonConvert.SerializeObject(ex));
        //
        //         return StatusCode(400, res);
        //     }
        // }

        [HttpGet]
        [Route("GetCashoutAndTopupInfo")]
        public async Task<ActionResult<RewardMemberGetCashoutAndTopupInfoOutput>> GetCashoutAndTopupInfo([FromQuery] RewardMemberGetCashoutAndTopupInfoInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.GetCashoutAndTopupInfo(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetCashoutAndTopupInfo Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("RequestOtpChangePhone")]
        public async Task<ActionResult<ResponseDataOutput<RewardMemberCheckSumOutput>>> SendOtpChangePhone([FromBody] SendOtpChangePhoneInput input, [FromHeader] string OtpSessionId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(OtpSessionId))
                {
                    return StatusCode(400, _commonHelperService.GetResponseEmptyDataHeader());
                }
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.SendOtpChangePhone(input, OtpSessionId, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Member request otp change phone error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("VerifyOtpChangePhone")]
        public async Task<ActionResult<ResponseNoDataOutput>> VerifyOtpChangePhone([FromBody] VerifyOtpChangePhoneInput input, [FromHeader] string OtpSessionId, [FromHeader] string SmsOtpCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(OtpSessionId) || string.IsNullOrWhiteSpace(SmsOtpCode))
                {
                    return StatusCode(400, _commonHelperService.GetResponseEmptyDataHeader());
                }
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.VerifyOtpChangePhone(input, OtpSessionId, SmsOtpCode, authorization);
                _logger.LogDebug("Update phone number result API: " + JsonConvert.SerializeObject(result));
                if (result.Status == UpdatePhoneNumberStatus.Success)
                {
                    if (result.HasUpdatePartnerPhone && result.ListMerchantNeedUpdatePhone != null && result.ListMerchantNeedUpdatePhone.Count > 0)
                    {
                        await _loyaltyThirdPartyService.UpdatePhoneNumber(new LoyaltyThirdPartyUpdatePhoneNumberInput()
                        {
                            LinkIdMemberCode = result.MemberCode,
                            LinkIdMemberId = result.MemberId.Value,
                            MerchantIds = result.ListMerchantNeedUpdatePhone,
                            PhoneNumber = input.PhoneNumber,
                        });
                    }
                }
                var resultDto = new ResponseNoDataOutput()
                {
                    Message = result.Message,
                    Result = 200,
                    MessageDetail = null,
                };
                return StatusCode(200, resultDto);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                _logger.LogError(ex, "Member verify otp change phone error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("RequestOtpChangePinCode")]
        public async Task<ActionResult<ResponseDataOutput<RewardMemberCheckSumOutput>>> SendOtpVerifyPinCode([FromBody] SendOtpChangePinCodeInput input, [FromHeader] string OtpSessionId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(OtpSessionId))
                {
                    return StatusCode(400, _commonHelperService.GetResponseEmptyDataHeader());
                }
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.SendOtpChangePinCode(input, OtpSessionId, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Member request otp change pin code error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("VerifyOtpChangePinCode")]
        public async Task<ActionResult<RewardMemberUpdatePinCodeResponse>> VerifyOtpVerifyPinCode([FromBody] VerifyOtpChangePinCodeInput input, [FromHeader] string OtpSessionId, [FromHeader] string SmsOtpCode, [FromHeader] string deviceId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(OtpSessionId) || string.IsNullOrWhiteSpace(SmsOtpCode))
                {
                    return StatusCode(400, _commonHelperService.GetResponseEmptyDataHeader());
                }
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.VerifyOtpChangePinCode(input, OtpSessionId, SmsOtpCode, authorization, deviceId);
                var output = new RewardMemberUpdatePinCodeResponse()
                {
                    Item = new RewardMemberUpdatePinCodeResponseItem()
                    {
                        CustomToken = result.CustomToken,
                        MemberCode = input.MemberCode,
                    },
                    Message = "Success",
                    Result = 200,
                };
                return StatusCode(200, output);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Member verify otp change pin code error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("RequestOtpForgetPinCode")]
        [AllowAnonymous]
        public async Task<ActionResult<ResponseNoDataOutput>> RequestOtpForgetPinCode([FromBody] SendOtpForgetPinCodeInput input, [FromHeader] string OtpSessionId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(OtpSessionId))
                {
                    return StatusCode(400, _commonHelperService.GetResponseEmptyDataHeader());
                }
                var result = await _memberService.SendOtpForgetPinCode(input, OtpSessionId);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Member request otp forget pin code error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("VerifyOtpForgetPinCode")]
        [AllowAnonymous]
        public async Task<ActionResult<RewardMemberForgetPinCodeResponse>> VerifyOtpVerifyPinCode([FromBody] VerifyOtpForgetPinCodeInput input, [FromHeader] string OtpSessionId, [FromHeader] string SmsOtpCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(OtpSessionId) || string.IsNullOrWhiteSpace(SmsOtpCode))
                {
                    return StatusCode(400, _commonHelperService.GetResponseEmptyDataHeader());
                }
                var result = await _memberService.VerifyOtpForgetPinCode(input, OtpSessionId, SmsOtpCode);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                _logger.LogError(ex, "Member verify otp forget pin code error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("UpdatePinCodeForget")]
        [AllowAnonymous]
        public async Task<ActionResult<RewardMemberUpdatePinCodeForgetOutput>> UpdatePinCodeForget([FromBody] RewardMemberUpdatePinCodeForgetInput input, [FromHeader] string PinCode, [FromHeader] string DeviceId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(PinCode))
                {
                    return StatusCode(400, _commonHelperService.GetResponseEmptyDataHeader());
                }
                var result = await _memberService.UpdatePinCodeForget(input, PinCode, DeviceId);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Member update forget pin code error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("RequestOtpAddPhone")]
        public async Task<ActionResult<ResponseDataOutput<RewardMemberCheckSumOutput>>> SendOtpAddPhone([FromBody] SendOtpAddPhoneInput input, [FromHeader] string OtpSessionId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(OtpSessionId))
                {
                    return StatusCode(400, _commonHelperService.GetResponseEmptyDataHeader());
                }
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.SendOtpAddPhone(input, OtpSessionId, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Member request otp add phone error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("VerifyOtpAddPhone")]
        public async Task<ActionResult<ResponseNoDataOutput>> VerifyOtpAddPhone([FromBody] VerifyOtpAddPhoneInput input, [FromHeader] string OtpSessionId, [FromHeader] string SmsOtpCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(OtpSessionId) || string.IsNullOrWhiteSpace(SmsOtpCode))
                {
                    return StatusCode(400, _commonHelperService.GetResponseEmptyDataHeader());
                }
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.VerifyOtpAddPhone(input, OtpSessionId, SmsOtpCode, authorization);
                var resultDto = new ResponseNoDataOutput()
                {
                    Message = result.Message,
                    Result = 200,
                    MessageDetail = null,
                };
                return StatusCode(200, resultDto);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                _logger.LogError(ex, "Member verify otp add phone error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("DeleteAccount")]
        public async Task<ActionResult<ResponseNoDataOutput>> DeleteAccount([FromBody] DeleteAccountInput input)
        {
            try {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                _logger.LogInformation(" >> User about to delete his account >> " + input.MemberCode);
                var member = await _memberService.GetInfo(new RewardMemberGetInfoInput()
                {
                    NationalId = input.MemberCode,
                });
                if(member==null)
                {
                    var result = new RewardInvalidResponse();

                    result.Code = "400";
                    result.Message = "Member Code không hợp lệ.";
                    return StatusCode(400, result);

                }
                var httpCode = 200;
                var resultCommon = await _loyaltyThirdPartyService.RemoveConnect(new RemoveConnectInput() { 
                    LinkID_MemberID = member.Id,
                    LinkID_WalletAddress = member.UserAddress,
                    MemberCode = input.MemberCode,
                });
                var res = await _memberService.DeleteAccount(input, member);
                if (res.Error != "00")
                {
                    httpCode = 400;
                }
                return StatusCode(httpCode, res);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                _logger.LogError(ex, "Member verify otp add phone error - " + JsonConvert.SerializeObject(ex));
                if (res.Code == "PleaseChangePinCode")
                {
                    res.Code = "1000";
                    res.Message = "Device này không thể xoá tài khoản. Vui lòng liên hệ hỗ trợ hoặc thử lại sau.";
                } 
                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("GetCifByNationalId")]
        public async Task<ActionResult<RewardMemberGetCifByMemberCodeOutput>> GetCifByNationalId([FromBody] RewardMemberGetCifByMemberCodeInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.NationalId);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var res = await _memberService.GetCifByNationalId(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                _logger.LogError(ex, "GetCifByNationalId error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetListHistoryReferral")]
        public async Task<ActionResult<LoyaltyResponse<GetListHistoryReferralOutput>>> GetListHistoryReferral([FromQuery] GetListHistoryReferralInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.InviterCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var res = await _giftTransactionsService.GetListHistoryReferral(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetListHistoryReferral error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("CheckMemberHasInvited")]
        public async Task<ActionResult<LoyaltyResponse<CheckMemberHasInvitedOutput>>> CheckMemberHasInvited([FromQuery] CheckMemberHasInvitedInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var res = await _loyaltySecondaryCustomersService.CheckMemberHasInvited(input);
                return StatusCode(200, res);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetListHistoryReferral error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }


        [HttpPost]
        [Route("VerifyReferralCode_v2")]
        public async Task<ActionResult<VerifyRefferralCode_v2Output>> VerifyReferralCode_v2(VerifyReferralCode_v2Input input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltySecondaryCustomersService.VerifyReferralCode_v2(new VerifyReferralCode_v2DTO { InviterRefCode = input.InviterRefCode, InvitedPhoneNumber = input.InvitedPhoneNumber});
                return StatusCode(200, result.Result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Verify referral code error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }


        [HttpGet]
        [Route("GetInforCampaignRef")]
        public async Task<ActionResult<GetInforCampaignRefOutput>> GetInforCampaignRef()
        {
            try
            {
                var result = await _loyaltySecondaryCustomersService.GetInforCampaignRef();
                return StatusCode(200, result.Result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetInforCampaignRef error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("RegisterCampaignFriendReferral")]
        public async Task<ActionResult<RegisterCampaignFriendReferralOutput>> RegisterCampaignFriendReferral(RegisterCampaignFriendReferralInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltySecondaryCustomersService.RegisterCampaignFriendReferral(input);
                return StatusCode(200, result.Result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "RegisterCampaignFriendReferral " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("CheckMemberRegisterCampaign")]
        public async Task<ActionResult<CheckMemberRegisterCampaignOutput>> CheckMemberRegisterCampaign(CheckMemberRegisterCampaignInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltySecondaryCustomersService.CheckMemberRegisterCampaign(input);
                return StatusCode(200, result.Result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "CheckMemberRegisterCampaign " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route(("GetMoneyCardOfMember"))]
        public async Task<ActionResult<GetMoneyCardOfMemberOutput>> GetMoneyCardOfMember([FromQuery]GetMoneyCardOfMemberInput input)
        {
            try
            {
                var reqId = DateTime.UtcNow.ToString("yyyyMMddHHmmss") + "_" + DateTime.UtcNow.Millisecond;
                _logger.LogInformation($" {reqId} >> GetMoneyCardOfMember >> " + JsonConvert.SerializeObject(input));
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.GetMoneyCardOfMember(input);
                _logger.LogInformation($" {reqId} >> GetMoneyCardOfMember Result >> " + (result?.Items?.Count ?? 0));
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetMoneyCardOfMember " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        
        [HttpGet]
        [Route(("GetCardTransactionOfMember"))]
        public async Task<ActionResult<GetCardTransactionOfMemberOutput>> GetCardTransactionOfMember([FromQuery]GetCardTransactionOfMemberInput input)
        {
            try
            {
                var reqId = DateTime.UtcNow.ToString("yyyyMMddHHmmss") + "_" + DateTime.UtcNow.Millisecond; 
                _logger.LogInformation($" {reqId} >> GetCardTransactionOfMember >> " + JsonConvert.SerializeObject(input));
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _memberService.GetCardTransactionOfMember(input);
                _logger.LogInformation($" {reqId} >> GetCardTransactionOfMember Result >> " + (result?.Items?.Count ?? 0));
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetCardTransactionOfMember " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
        
        [HttpPost]
        [Route(("GetPaymentMethods"))]
        // [AllowAnonymous]
        public async Task<ActionResult<RewardMemberGetPaymentMethodOutput>> GetPaymentMethods(
            [FromBody] GetPaymentMethodInput input)
        {
            try
            {
                var reqId = DateTime.UtcNow.ToString("yyyyMMddHHmmss") + "_" + DateTime.UtcNow.Millisecond; 
                _logger.LogInformation($" {reqId} >> GetPaymentMethods >> " + JsonConvert.SerializeObject(input));
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var cachedKey = "GET_PAYMENT_METHOD_" + input.MemberCode + "_" + input.GiftCode;
                var cachedData = await _cache.GetStringAsync(cachedKey);
                
                if (!string.IsNullOrEmpty(cachedData))
                {
                    try
                    {
                        var retCache = JsonConvert.DeserializeObject<RewardMemberGetPaymentMethodOutput>(cachedData);
                        _logger.LogInformation($" {reqId} >> GetPaymentMethods Result ListPaymentMethod From Cache >> " + (retCache?.Item?.ListPaymentMethod?.Count ?? 0));
                        return StatusCode(200, retCache);
                    }
                    catch (Exception e)
                    {
                        await _cache.RemoveAsync(cachedKey);
                    }
                }
                var listGiftGroupCode = new List<string>();
                var listGiftCodeResult = await _giftTransactionsService.GetGroupCodesOfGift(input.GiftCode);
                if (listGiftCodeResult?.Result != null && listGiftCodeResult.Result.Items.Count > 0)
                {
                    listGiftGroupCode = listGiftCodeResult.Result.Items;
                }
                var rewardPayload = new RewardMemberGetPaymentMethodInput()
                {
                    MemberCode = input.MemberCode,
                    ListGiftGroupCode = listGiftGroupCode,
                    MemberType = "KHCN"
                };
                var result = await _memberService.GetPaymentMethod(rewardPayload);
                await _cache.SetStringAsync(cachedKey, JsonConvert.SerializeObject(result),
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(5)));
                _logger.LogInformation($" {reqId} >> GetPaymentMethods Result ListPaymentMethod From DB >> " + (result?.Item?.ListPaymentMethod?.Count ?? 0));
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "GetPaymentMethods " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}
