﻿using AKC.RabbitMQ;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.ObjectPool;
using RabbitMQ.Client;

namespace AKC.RabbitMQ
{
    public static class RabitMQExtensions
    {
        public static IServiceCollection AddRabbit(this IServiceCollection services, IConfiguration configuration)
        {
            var rabbitConfig = new RabbitOptions
            {
                HostName = configuration["RabbitMQ:HostName"],
                VHost = configuration["RabbitMQ:VHost"],
                Password = configuration["RabbitMQ:Password"],
                UserName = configuration["RabbitMQ:UserName"],
                Port = int.Parse(configuration["RabbitMQ:Port"]),
                QueueNameFormat = configuration["RabbitMQ:QueueNameFormat"]
            };

            services.AddSingleton(rabbitConfig);

            var rabbitExchange = new RabbitExchange
            {
                EventExchange = configuration["RabbitMQ:EventExchange"],
                GamificationExchange = configuration["RabbitMQ:GamificationExchange"]
            };

            services.AddS<PERSON>leton(rabbitExchange);

            services.AddSingleton<ObjectPoolProvider, DefaultObjectPoolProvider>();
            services.AddSingleton<IPooledObjectPolicy<IModel>, RabbitModelPooledObjectPolicy>();

            services.AddSingleton<IRabbitMQManager, RabbitMQManager>();

            return services;
        }
    }
}
