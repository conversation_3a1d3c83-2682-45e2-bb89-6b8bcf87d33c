﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardRegisterWithEmailAndPasswordInput
    {
        [Required]
        [EmailAddress(ErrorMessage = "Wrong email format")]
        public string Email { get; set; }

        [Required]
        [MinLength(6, ErrorMessage = "Passwords must be at least 6 characters")]
        public string Password { get; set; }
    }
}
