﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction
{
    public class RewardCreateGiftRedeemTransactionRequest
    {
        public string NationalId { get; set; }
        public string OrderCode { get; set; }
        public long MerchantId { get; set; }
        public decimal TotalRequestedAmount { get; set; }
        public string RedeemFilterValue { get; set; }
    }
}
