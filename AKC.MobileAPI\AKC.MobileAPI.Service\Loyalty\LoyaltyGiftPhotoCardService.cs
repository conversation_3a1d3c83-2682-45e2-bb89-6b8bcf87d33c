﻿using AKC.MobileAPI.DTO.Loyalty.GiftPhotCard;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyGiftPhotoCardService : BaseLoyaltyService, ILoyaltyGiftPhotoCardService
    {
        private readonly IDistributedCache _cache;
        public LoyaltyGiftPhotoCardService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
            _cache = cache;
        }

        public async Task<LoyaltyGiftPhotoCardGetAllOutput> GetAll(SearchGiftPhotoCardRequest input)
        {
            return await GetLoyaltyAsync<LoyaltyGiftPhotoCardGetAllOutput>(LoyaltyApiUrl.GET_GITF_PHOTO_CARD, input);
        }
    }
}
