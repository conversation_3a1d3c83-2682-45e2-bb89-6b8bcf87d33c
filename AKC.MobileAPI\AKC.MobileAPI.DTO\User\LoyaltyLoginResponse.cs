﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.DTO.User
{
    public class LoginResult
    {
        public string AccessToken { get; set; }
        public string EncryptedAccessToken { get; set; }
        public long ExpireInSeconds { get; set; }
        public bool ShouldResetPassword { get; set; }
        public string PasswordResetCode { get; set; }
        public string TwoFactorAuthProviders { get; set; }
        public string TwoFactorRememberClientToken { get; set; }
        public string ReturnUrl { get; set; }
    }

    public class LoyaltyLoginResponse
    {
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public LoginResult Result { get; set; }
    }
}
