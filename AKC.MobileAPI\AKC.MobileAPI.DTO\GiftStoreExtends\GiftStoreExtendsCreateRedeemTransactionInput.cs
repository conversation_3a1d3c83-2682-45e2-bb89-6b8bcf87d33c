﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.GiftStoreExtends
{
    public class GiftStoreExtendsCreateRedeemTransactionInput
    {
        [Required]
        public int MerchantId { get; set; }
        [Required]
        public string MemberCode { get; set; }
        [Required]
        public string CustomerPhoneNumber { get; set; }
        [Required]
        public string CustomerFullName { get; set; }
        [Required]
        public string CustomerGender { get; set; }
        [Required]
        public string CustomerEmail { get; set; }
        [Required]
        public string CustomerNote { get; set; }
        [Required]
        public string GiftCode { get; set; }
        [Required]
        public int Quantity { get; set; }
        [Required]
        public decimal TotalAmount { get; set; }
        [Required]
        public string ShipAddress { get; set; }
        [Required]
        public string FullLocationShip { get; set; }
        public string Description { get; set; }
    }

    public class GiftStoreExtendsCreateRedeemTransactionDto
    {
        public string TransactionCode { get; set; }
        public string CustomerFullName { get; set; }
        public string CustomerPhoneNumber { get; set; }
        public string CustomerMemberCode { get; set; }
        public string CustomerEmail { get; set; }
        public string CustomerGender { get; set; }
        public string CustomerNote { get; set; }
        public string GiftCode { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public string ShipAddress { get; set; }
        public string CityId { get; set; }
        public string DistrictId { get; set; }
        public string WardId { get; set; }
        public string FullLocationShip { get; set; }
    }
}
