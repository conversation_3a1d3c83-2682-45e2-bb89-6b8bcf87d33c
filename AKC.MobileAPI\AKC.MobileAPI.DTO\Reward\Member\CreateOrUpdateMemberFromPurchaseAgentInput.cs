namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class CreateOrUpdateMemberFromPurchaseAgentInput
    {
        public int MerchantId { get; set; }
        public string PartnerCode { get; set; }
        public string Address { get; set; }
        public string MemberCode { get; set; }
    }
    
    public class CreateOrUpdateMemberFromPurchaseAgentOutput
    {
    }

    public class GetCreditBalanceByMemberCodeInput
    {
        public string MemberCode { get; set; }
    }

    public class GetCreditBalanceByMemberCodeOutput
    {
        public int Result { get; set; }
        public GetCreditBalanceByMemberCodeOutputInner Items { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }

    public class GetCreditBalanceByMemberCodeOutputInner
    {
        public string MemberCode { get; set; }
        public decimal CreditBalance { get; set; }
    }
}