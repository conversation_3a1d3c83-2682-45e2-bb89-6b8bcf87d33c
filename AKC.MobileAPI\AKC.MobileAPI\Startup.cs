using AKC.MobileAPI.AuditLog;
using AKC.MobileAPI.Helper;
using AKC.MobileAPI.Service;
using AKC.MobileAPI.Service.Abstract;
using AKC.MobileAPI.Service.Abstract.ApiSMS;
using AKC.MobileAPI.Service.Abstract.Gamification;
using AKC.MobileAPI.Service.Abstract.GiftStoreExtends;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Loyalty.EGiftInfors;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Abstract.Loyalty.Quest;
using AKC.MobileAPI.Service.Abstract.LoyaltyVpbank;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Abstract.SmartOTP;
using AKC.MobileAPI.Service.Abstract.ThirdParty;
using AKC.MobileAPI.Service.ApiSMS;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.CronServices;
using AKC.MobileAPI.Service.CronServices.MerchantRedeemtion;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Gamification;
using AKC.MobileAPI.Service.GiftStoreExtends;
using AKC.MobileAPI.Service.Loyalty;
using AKC.MobileAPI.Service.Loyalty.Gift;
using AKC.MobileAPI.Service.Loyalty.Quest;
using AKC.MobileAPI.Service.LoyaltyVpbank;
using AKC.MobileAPI.Service.Reward;
using AKC.MobileAPI.Service.SmartOTP;
using AKC.MobileAPI.Service.ThirdParty;
using AKC.RabbitMQ;
using FirebaseAdmin;
using FirebaseAdmin.Auth;
using Google.Apis.Auth.OAuth2;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Threading;
using AKC.MobileAPI.Service.Abstract.NotiV2;
using AKC.MobileAPI.Service.NotiV2;
using AKC.MobileAPI.Service.Astrologee;
using AKC.MobileAPI.Service.Webstore;
using AspNetCoreRateLimit;

namespace AKC.MobileAPI
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
            var path = Path.Combine(AppContext.BaseDirectory, @"App_Data/adminSDKKey.json");
            FirebaseApp.Create(new AppOptions()
            {
                Credential = GoogleCredential.FromFile(path),
            });
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            ThreadPool.SetMaxThreads(32_767, 2_000);
            ThreadPool.SetMinThreads(1_000, 1_000);
            // Config redis.
            services.AddStackExchangeRedisCache(options =>
            {
                options.InstanceName = Configuration.GetSection("Redis:InstanceName").Value;
                options.Configuration = Configuration.GetSection("Redis:Configuration").Value;
            });
            //services.AddDistributedMemoryCache();
            var projectId = Configuration.GetSection("Firebase:ProjectId").Value;

            services.Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders =
                    ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
            });

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
             .AddJwtBearer(options =>
             {
                 options.Authority = $"https://securetoken.google.com/{projectId}";
                 options.TokenValidationParameters = new TokenValidationParameters
                 {
                     ValidateIssuer = true,
                     ValidIssuer = $"https://securetoken.google.com/{projectId}",
                     ValidateAudience = true,
                     ValidAudience = projectId,
                     ValidateLifetime = true
                 };
                 options.Events = new JwtBearerEvents()
                 {
                     OnTokenValidated = async context =>
                     {
                         try
                         {
                             await FirebaseAuth.DefaultInstance.VerifyIdTokenAsync((context.SecurityToken as JwtSecurityToken).RawData, true);
                         }
                         catch
                         {
                             context.NoResult();
                             context.Response.Headers.Add("Token-Expired", "true");
                             context.Response.ContentType = "application/json";
                             context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                             await context.Response.WriteAsync("Un-Authorized");
                             throw;
                         }
                     }
                 };
             });


            services.AddControllers();
            services.AddMemoryCache();
            // RATELIMIT CONFIGURATION
            services.AddHttpContextAccessor();
            // services.Configure<IpRateLimitOptions>(Configuration.GetSection("IpRateLimiting"));
            services.Configure<IpRateLimitOptions>(options =>
            {
                // Check if IpRateLimiting section exists in config
                var ipRateLimitingSection = Configuration.GetSection("IpRateLimiting");
                if (!ipRateLimitingSection.Exists())
                {
                    // Default configuration if section is missing
                    options.EnableEndpointRateLimiting = true;
                    options.StackBlockedRequests = false;
                    options.HttpStatusCode = 429;
                    options.GeneralRules = new List<RateLimitRule>
                    {
                        new RateLimitRule
                        {
                            Endpoint = "*/api/webstore/public/*",
                            Period = "5m",
                            Limit = 50
                        },
                        new RateLimitRule
                        {
                            Endpoint = "*/api/webstore/auth/*",
                            Period = "5m",
                            Limit = 50
                        }
                    };
                }
                else
                {
                    ipRateLimitingSection.Bind(options);
                }
            });
            services.AddSingleton<IIpPolicyStore, MemoryCacheIpPolicyStore>();
            services.AddSingleton<IRateLimitCounterStore, MemoryCacheRateLimitCounterStore>();
            services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
            services.AddCors(options =>
            {
                options.AddPolicy("WEBSTOREAPI", builder =>
                {
                    var corsOrigins = Configuration.GetSection("CorsOrigins").Get<string[]>();
                    builder
                        .WithOrigins(corsOrigins)
                        .SetIsOriginAllowedToAllowWildcardSubdomains()
                        .AllowAnyHeader()
                        .AllowAnyMethod()
                        .AllowCredentials()
                        .SetPreflightMaxAge(TimeSpan.FromMinutes(5));
                });
            });
            // END RATELIMIT CONFIG
            
            if (bool.Parse(Configuration.GetSection("EnableSwagger").Value))
            {
                services.ConfigureSwaggerGen(options =>
                {
                    //UseFullTypeNameInSchemaIds replacement for .NET Core
                    options.CustomSchemaIds(x => x.FullName);
                });

                // Register the Swagger generator, defining 1 or more Swagger documents
                services.AddSwaggerGen(c =>
                {
                    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Mobile API", Version = "v1" });

                    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                    {
                        Description = @"JWT Authorization header using the Bearer scheme. \r\n\r\n 
                          Enter 'Bearer' [space] and then your token in the text input below.
                          \r\n\r\nExample: 'Bearer 12345abcdef'",
                        Name = "Authorization",
                        In = ParameterLocation.Header,
                        Type = SecuritySchemeType.ApiKey,
                        Scheme = "Bearer"
                    });

                    // Set the comments path for the Swagger JSON and UI.
                    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                    c.IncludeXmlComments(xmlPath);
                    c.OperationFilter<SwaggerAuthResponsesOperationFilter>();
                });
            }

            // Config Dependency Injection.
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IRewardMemberService, RewardMemberService>();
            services.AddScoped<ILoyaltyUsePointService, LoyaltyUsePointService>();
            services.AddScoped<ILoyaltyOrderService, LoyaltyOrderService>();
            services.AddScoped<ILoyaltyRewardsService, LoyaltyRewardsService>();
            services.AddScoped<ILoyaltyRewardsService, LoyaltyRewardsService>();
            services.AddScoped<ILoyaltyGiftTransactionsService, LoyaltyGiftTransactionsService>();
            services.AddScoped<ILoyaltyArticleService, LoyaltyArticleService>();
            services.AddScoped<ILoyaltyGiftService, LoyaltyGiftService>();
            services.AddScoped<ILoyaltyChallengeService, LoyaltyChallengeService>();
            services.AddScoped<IExceptionReponseService, ExceptionReponseService>();
            services.AddScoped<ILoyaltySecondaryCustomersService, LoyaltySecondaryCustomersService>();
            services.AddScoped<ILoyaltyFAQService, LoyaltyFAQService>();
            services.AddScoped<ILoyaltyThirdPartyService, LoyaltyThirdPartyService>();
            services.AddScoped<IRewardMerchantService, RewardMerchantService>();
            services.AddScoped<IRewardExchangeTransactionService, RewardExchangeTransactionService>();
            services.AddScoped<IRewardGiveTokenService, RewardGiveTokenService>();
            services.AddScoped<IRewardGiftRedeemTransactionService, RewardGiftRedeemTransactionService>();
            services.AddScoped<IRewardTopUpTransactionService, RewardTopUpTransactionService>();
            services.AddScoped<IRewardPayByTokenTransactionService, RewardPayByTokenTransactionService>();
            services.AddScoped<IThirdPartyVPBankService, ThirdPartyVPBankService>();
            services.AddScoped<IThirdPartyAppotaService, ThirdPartyAppotaService>();
            services.AddScoped<IThirdPartyDummyService, ThirdPartyDummyService>();
            services.AddScoped<IRewardPartnerPointCachingService, RewardPartnerPointCachingService>();
            services.AddScoped<ILoyaltyNotificationHistoryService, LoyaltyNotificationHistoryService>();
            services.AddScoped<IUploadImageSevice, UploadImageSevice>();
            services.AddScoped<IItemManagementService, ItemManagementService>();
            services.AddScoped<IGameManagementService, GamificationManagementService>();
            services.AddScoped<IExchangeManagementService, ExchangeManagementService>();
            services.AddScoped<IMemberManagementService, MemberManagementService>();
            services.AddScoped<ILoyaltyAdjustService, LoyaltyAdjustService>();
            services.AddScoped<IApiSMSService, ApiSMSService>();
            services.AddScoped<ILoyaltyAuditLogService, LoyaltyAuditLogService>();
            services.AddScoped<ILoyaltyEGiftInforsService, LoyaltyEGiftInforsService>();
            services.AddScoped<ILoyaltyLanguageService, LoyaltyLanguageService>(); 
            services.AddScoped<ILoyaltyLocationService, LoyaltyLocationService>(); 
            services.AddScoped<ILoyaltyLanguageService, LoyaltyLanguageService>();
            services.AddScoped<IStorageS3Service, StorageS3Service>();
            services.AddScoped<IRewardCreateExchangeAndRedeemService, RewardCreateExchangeAndRedeemService>();
            services.AddScoped<IRewardCashoutTransactionService, RewardCashoutTransactionService>();
            services.AddScoped<IRewardPaymentFailService, RewardPaymentFailService>();
            services.AddScoped<ILoyaltyPaymentService, LoyaltyPaymentService>();
            services.AddScoped<IThirdPartyVPBankLoyaltyExchangeService, ThirdPartyVPBankLoyaltyExchangeService>();
            services.AddScoped<ILinkIdLoyaltyGiftStoreExtendsService, LinkIdLoyaltyGiftStoreExtendsService>();
            services.AddScoped<ICommonHelperService, CommonHelperService>();
            services.AddScoped<IValidationMemberCode, ValidationMemberCode>();
            services.AddScoped<ILoyaltyInsuranceService, LoyaltyInsuranceService>();
            services.AddScoped<ILoyaltyBrandService, LoyaltyBrandService>();
            services.AddScoped<ILoyaltyMemberService, LoyaltyMemberService>();
            services.AddScoped<IThirdPartyBrandMappingService, ThirdPartyBrandMappingService>();
            services.AddScoped<ILoyaltyFlashSaleProgramService, LoyaltyFlashSaleService>();
            services.AddScoped<ILoyaltyGiftFlashSaleProgramService, LoyaltyGiftFlashSaleProgramService>();
            services.AddScoped<ISmartOTPService, SmartOTPService>();
            services.AddScoped<ILoyaltyCampaignGiveCoinService, LoyaltyCampaignGiveCoinService>();
            services.AddScoped<ILoyaltyQuestService, LoyaltyQuestService>();
            services.AddScoped<ILoyaltyThemeService, LoyaltyThemeService>();
            services.AddScoped<ILoyaltyEarningRuleService, LoyaltyEarningRuleService>();
            services.AddScoped<ILoyaltyFeedbackSuggesstionService, LoyaltyFeedbackSuggesstionService>();
            services.AddScoped<ILoyaltySearchBarService, LoyaltySearchBarService>();
            services.AddScoped<ILoyaltyFeedbackSuggesstionService, LoyaltyFeedbackSuggesstionService>();
            services.AddScoped<IThirdPartyVPBankSecuritiesService, ThirdPartyVPBankSecuritiesService>();
            services.AddScoped<INotificationHistoryV2Service, NotificationHistoryV2Service>();
            services.AddScoped<ILoyaltyGiftPhotoCardService, LoyaltyGiftPhotoCardService>();
            services.AddScoped<IAstrologeeService, AstrologeeService>();
            services.AddScoped<IMemberFirstUseTokenService, MemberFirstUseTokenService>();
            services.AddScoped<ILoyaltyWheelService, LoyaltyWheelService>();
            services.AddScoped<IThirdPartySIAService, ThirdPartySIAService>();
            // Rabbit MQ
            services.AddRabbit(Configuration);
            services.AddScoped<ILoyaltyCampaignGiftService, LoyaltyCampaignGiftService>();
            // services.AddScoped<WebstorePartnerOrgToCheckSmeService>();
            services.AddScoped<ISmeTransactionService, SmeTransactionService>();
            services.AddScoped<ISkyJoyIntegrationService, SkyJoyIntegrationService>();

            // Backgroud jobs
            services.AddCronJob<RefreshLoyaltyTokenJob>(c =>
            {
                c.TimeZoneInfo = TimeZoneInfo.Local;
                c.CronExpression = Configuration.GetSection("Loyalty:CronExpressionRefreshToken").Value;
            });
            // Backgroud jobs merchant redeemtion
            services.AddCronJob<RefreshLoyaltyMerchantRedeemtionTokenJob>(c =>
            {
                c.TimeZoneInfo = TimeZoneInfo.Local;
                c.CronExpression = Configuration.GetSection("LoyaltyGiftStoreRefreshToken").Value ?? "* * */10 * *";
            });
            
            services.AddLocalization(options =>
            {
                // Set the path where resource files (.resx) are located
                options.ResourcesPath = "Resources";
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory)
        {
            loggerFactory.AddLog4Net();

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            if (bool.Parse(Configuration.GetSection("EnableSwagger").Value))
            {
                // Enable middleware to serve generated Swagger as a JSON endpoint.
                app.UseSwagger();

                // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.),
                // specifying the Swagger JSON endpoint.
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Mobile API V1");
                    c.RoutePrefix = string.Empty;
                });
            }

            app.UseHttpsRedirection();

            app.UseRouting();
            app.UseIpRateLimiting();
            app.AddValidateSignature();


            app.UseCors("WEBSTOREAPI");
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseMiddleware<RequestLoginMiddleware>();
            app.UseMiddleware<CSTicketAnnonymousRateLimitingMiddleware>();
            app.UseMiddleware<WebstoreCustomAuthenticationMiddleware>();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
