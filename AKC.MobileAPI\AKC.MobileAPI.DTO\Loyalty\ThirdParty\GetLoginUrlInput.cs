﻿using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Loyalty.ThirdParty
{
    public class GetLoginUrlInput
    {
        public int MerchantId { get; set; }
        public string MerchantName { get; set; }
    }

    public class CallbackForwarderInput
    {
        public string code { get; set; }
        public string state { get; set; }
        public string session_state { get; set; }
    }

    public class UseCodeToConnectMerchantOutput
    {
        public string Code { get; set; }
        public string Message { get; set; }
        public string ParnterMemberCode { get; set; }
    }
    public class UseCodeToConnectMerchantInput
    {
        [Required]
        public string Code { get; set; }
        [Required]
        public int MerchantId { get; set; }
        
        public string MemberCode { get; set; }
    }
    public class ExchangeCodeForTokenInput
    {
        [Required]
        public string Code { get; set; }
        [Required]
        public int MerchantId { get; set; }
        [Required]
        public string MerchantName { get; set; }
        
        public string MemberCode { get; set; }
    }

    public class ExchangeCodeForTokenOutput
    {
        public int code { get; set; }
        public ExchangeCodeForTokenOutputInner data { get; set; }
    }

    public class ExchangeCodeForTokenOutputInner
    {
        public string access_token { get; set; }
        public string token_type { get; set; }
        public string refresh_token { get; set; }
        public string expiry_in { get; set; }
        public string refresh_token_expiry_in { get; set; }
    }
}