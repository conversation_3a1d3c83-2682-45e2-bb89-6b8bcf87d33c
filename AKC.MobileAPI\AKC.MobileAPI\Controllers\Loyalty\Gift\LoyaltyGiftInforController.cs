﻿using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.DTO.Reward.CashoutTransaction;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;
using AKC.MobileAPI.DTO.Loyalty.GiftUsageAddress;
using AKC.MobileAPI.DTO.Loyalty.GiftSearch;

namespace AKC.MobileAPI.Controllers.Loyalty.Gift
{
    [Route("api/GiftInfos")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    //[AllowAnonymous]
    public class LoyaltyGiftInforController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyGiftService _loyaltyGiftService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IDistributedCache _cache;
        private readonly IRewardCashoutTransactionService _rewardCashoutTransactionService;
        public LoyaltyGiftInforController
        (
              ILogger<LoyaltyGiftInforController> logger,
              ILoyaltyGiftService loyaltyGiftService,
              IExceptionReponseService exceptionReponseService,
              ICommonHelperService commonHelperService,
              IDistributedCache cache,
              IRewardCashoutTransactionService rewardCashoutTransactionService
        )
        {
            _logger = logger;
            _loyaltyGiftService = loyaltyGiftService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
            _cache = cache;
            _rewardCashoutTransactionService = rewardCashoutTransactionService;
        }

        [HttpGet]
        [Route("GetAllForCategory")]
        public ActionResult<LoyaltyGetAllForCategoryOutPut> GetAllForCategory([FromQuery] LoyaltyGetAllForCategoryInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = _loyaltyGiftService.GetAllForCategory(input).Result;
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                
                var res = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result;
                _logger.LogError(ex, "GetAllForCategory Infor Error - " + JsonConvert.SerializeObject(res));
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllByMemberCode")]
        public async Task<ActionResult<LoyaltyGiftGetAllByMemberCodeOutput>> GetAllByMemberCode([FromQuery] LoyaltyGiftGetAllByMemberCodeInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetAllByMemberCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetAllByMemberCode Infor Error - " + JsonConvert.SerializeObject(res));

                return StatusCode(400, res);
            }
        }


        [HttpGet]
        [Route("GetGiftByMemberCode")]
        public async Task<ActionResult<LoyaltyGetGiftByByMemberCodeOutput>> GetGiftByMemberCode([FromQuery] LoyaltyGetGiftByByMemberCodeInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetGiftByMemberCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetAllByMemberCode Infor Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllInfors")]
        public async Task<ActionResult<GiftInforsOutPut>> GetAllInfors([FromQuery] GiftInforsInput input)
        {
            try
            {
                var CacheKey = "GET_GIFTGROUP_GetAllInfors_2022DEC26";
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var cachedData = await _cache.GetStringAsync(CacheKey);
                if (!string.IsNullOrEmpty(cachedData))
                {
                    var convertedObj = JsonConvert.DeserializeObject<GiftInforsOutPut>(cachedData);
                    _logger.LogInformation(">> GET_GIFTGROUP_GetAllInfors_2022DEC26 >> CACHE HIT");
                    return StatusCode(200, convertedObj);
                }
                var result = await _loyaltyGiftService.GetAllInfors(input);
                _logger.LogInformation(">> GET_GIFTGROUP_GetAllInfors_2022DEC26 >> CACHE MISS; GET FROM DB");
                await _cache.SetStringAsync(CacheKey, JsonConvert.SerializeObject(result),
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(3)));
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetAllInfors Infor Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetByIdAndRelatedGift")]
        public async Task<ActionResult<GetByIdAndRelatedGiftOutput>> GetByIdAndRelatedGift([FromQuery] GetByIdAndRelatedGiftInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                input.ChannelCode = "LinkId";
                var result = await _loyaltyGiftService.GetByIdAndRelatedGift(input);
                if (!string.IsNullOrEmpty(result.Result.GiftCategoryTypeCode) && result.Result.GiftCategoryTypeCode.ToUpper() == GiftCategoryTypeCode.CashOut)
                {
                    try
                    {
                        _logger.LogInformation("GetGlobalSetting Start");
                        var resultFee = await _rewardCashoutTransactionService.GetGlobalSetting();
                        result.Result.FeeInfor = string.Format("Phí: {0}%, Phí Min: {1:n0}, Phí Max: {2:n0}", resultFee.FeeAmountCashoutAmountRatio, resultFee.MinimumCashoutFeeAmount, resultFee.MaximumCashoutFeeAmount);
                        _logger.LogInformation("GetGlobalSetting End");

                        var inputGetBalanceAbleToCashOut = new RewardValidateBalanceAbleToCashoutInput()
                        {
                            MemberId = 0,
                            MemberCode = input.MemberCode,
                            UserAddress = ""
                        };
                        _logger.LogInformation("ValidateBalanceAbleToCashout Start");
                        var resultBalance = await _rewardCashoutTransactionService.ValidateBalanceAbleToCashout(inputGetBalanceAbleToCashOut);
                        if (resultBalance != null && resultBalance.items != null)
                        {
                            result.Result.BalanceAbleToCashout = resultBalance.items.AbleToCashoutBalance;
                        }
                        _logger.LogInformation("ValidateBalanceAbleToCashout End");
                    }
                    catch (Exception ex)
                    {
                        result.Result.FeeInfor = string.Empty;
                        _logger.LogError(ex, "GetByIdAndRelatedGift GetGlobalSetting Error");
                    }
                }

                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetAllByMemberCode Infor Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllEffectiveCategory")]
        public async Task<ActionResult<GetAllEffectiveCategoryOutput>> GetAllEffectiveCategory([FromQuery] GetAllEffectiveCategoryInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetAllEffectiveCategory(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetAllEffectiveCategory Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllEffectiveCategory_v1")]
        public async Task<ActionResult<GetAllEffectiveCategoryOutput>> GetAllEffectiveCategory_v1([FromQuery] GetAllEffectiveCategoryInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetAllEffectiveCategory_v1(input);
                if (!string.IsNullOrEmpty(input.CategoryTypeCode) && input.CategoryTypeCode.ToUpper() == GiftCategoryTypeCode.CashOut)
                {
                    try
                    {
                        var inputGetBalanceAbleToCashOut = new RewardValidateBalanceAbleToCashoutInput()
                        {
                            MemberId = 0,
                            MemberCode = input.MemberCode,
                            UserAddress = ""
                        };
                        _logger.LogInformation("ValidateBalanceAbleToCashout Start");
                        var resultBalance = await _rewardCashoutTransactionService.ValidateBalanceAbleToCashout(inputGetBalanceAbleToCashOut);
                        if (resultBalance != null && resultBalance.items != null)
                        {
                            result.Result.BalanceAbleToCashout = resultBalance.items.AbleToCashoutBalance;
                        }
                        _logger.LogInformation("ValidateBalanceAbleToCashout End");
                    }
                    catch (Exception ex)
                    {
                        result.Result.BalanceAbleToCashout = 0;
                        _logger.LogError(ex, "GetAllEffectiveCategory_v1 ValidateBalanceAbleToCashout Error");
                    }
                }
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetAllEffectiveCategory_v1 Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllEffectiveCategory_TopUpPhone")]
        public async Task<ActionResult<GetAllEffectiveCategoryOutput>> GetAllEffectiveCategory_TopUpPhone([FromQuery] GetAllEffectiveCategoryInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetAllEffectiveCategory_TopupPhone(input);
                if (result.Result != null)
                {
                    _commonHelperService.ApplyDecimalFormatting(result.Result);
                }
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetAllEffectiveCategory_TopUpPhone Error {ex.Message} Stacke: {ex.StackTrace}");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetGiftByGroupType")]
        public async Task<ActionResult<GetGiftInforByGiftGroupOutput>> GetGiftByGroupType([FromQuery] GetGiftGroupByGroupTypeInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetGiftbyGroupType(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetAllEffectiveCategory_v1 Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllEffectiveCategoryGroupByBrand")]
        public async Task<ActionResult<GetAllEffectiveCategoryGroupByBrandOutput>> GetAllEffectiveCategoryGroupByBrand([FromQuery] GetAllEffectiveCategoryGroupByBrandInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetAllEffectiveCategoryGroupByBrand(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetAllEffectiveCategoryGroupByBrand Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetWishlistByMember")]
        public async Task<ActionResult<LoyaltyGetWishlistByMemberOutput>> GetWishlistByMember([FromQuery] LoyaltyGetWishlistByMemberInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetWishlistByMember(input);
                if (result != null && result.Result != null && result.Result.Items != null &&
                    result.Result.Items.Count > 0)
                {
                    var hasTypeCashout =
                        result.Result.Items.Any(x => x.WishListItem != null && x.WishListItem.IsTypeCashout);
                    if (hasTypeCashout)
                    {
                        var inputGetBalanceAbleToCashOut = new RewardValidateBalanceAbleToCashoutInput()
                        {
                            MemberId = 0,
                            MemberCode = input.MemberCode,
                            UserAddress = ""
                        };
                        try
                        {
                            var resultBalance = await _rewardCashoutTransactionService.ValidateBalanceAbleToCashout(inputGetBalanceAbleToCashOut);
                            if (resultBalance != null && resultBalance.items != null)
                            {
                                result.Result.BalanceAbleToCashout = resultBalance.items.AbleToCashoutBalance;
                            }
                        }
                        catch (Exception e)
                        {
                            result.Result.BalanceAbleToCashout = 0;
                            _logger.LogError("GetWishlistByMember Error >> Không get được số dư cashout của khách >> " + input.MemberCode, e.Message + e.StackTrace);
                        }
                    }
                }
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError("GetWishlistByMember Infor Error", ex);
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }
        [HttpGet]
        [Route("GetBrandWishlistByMember")]
        public async Task<ActionResult<LoyaltyGetBrandWishlistByMemberOutput>> GetBrandWishlistByMember([FromQuery] LoyaltyGetBrandWishlistByMemberInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetBrandWishlistByMember(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError("GetWishlistByMember Infor Error", ex);
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpPut]
        [Route("UpdateWishlist")]
        public async Task<ActionResult<LoyaltyUpdateWishlistOutput>> UpdateWishlist(LoyaltyUpdateWishlistInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.UpdateWishlist(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError("Quest join Error", ex);
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetAllForCategoryByMemberCode")]
        public async Task<ActionResult<GetAllForCategoryByMemberCodeOutput>> GetAllForCategoryByMemberCode([FromQuery] GetAllByMemberCodeGiftInforsInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetAllForCategoryByMemberCode(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError("GetAllForCategoryByMemberCode Infor Error", ex);
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetGiftAllInfors")]
        public async Task<ActionResult<GiftAllInforOutput>> GetGiftAllInfors([FromQuery] GiftInforsInput input)
        {
            try
            {
                var CacheKey = "GET_GIFTGROUP_GetGiftAllInfors_2022DEC26";
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var cachedData = await _cache.GetStringAsync(CacheKey);
                if (!string.IsNullOrEmpty(cachedData))
                {
                    var convertedObj = JsonConvert.DeserializeObject<GiftAllInforOutput>(cachedData);
                    _logger.LogInformation(">> GET_GIFTGROUP_GetGiftAllInfors_2022DEC26 >> CACHE HIT");
                    return StatusCode(200, convertedObj);
                }
                var result = await _loyaltyGiftService.GetGiftAllInfors(input);
                _logger.LogInformation(">> GET_GIFTGROUP_GetGiftAllInfors_2022DEC26 >> CACHE MISS; GET FROM DB");
                await _cache.SetStringAsync(CacheKey, JsonConvert.SerializeObject(result),
                    new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(3)));
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetGiftAllInfors Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }
        /**
         * LINKID-1352: API trả về giftgroup chỉ trả 1 cái active và kèm theo 4 quà thuộc group đó...
         */
        [HttpGet]
        [Route("appv1dot1/get-gift-group-for-home-page")]
        public async Task<ActionResult<GiftInforsOutPutForMB>> GetGiftGroupForHomePageV1Dot1([FromQuery] GetGiftGroupForHomePageV1Dot1Input input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetGiftGroupForHomepageV1dot1(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetGiftAllInfors Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }
        /**
         * LINKID-1352: API trả về giftgroup chỉ trả 1 cái active và kèm theo 4 quà thuộc group đó...
         */

        [HttpGet]
        [Route("GetGiftsByBrand")]
        public async Task<ActionResult<GiftInforByBrandOutput>> GetGiftsByBrand([FromQuery] GiftInforByBrandInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetGiftsByBrand(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetGiftsByBrand Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetGiftUsageAddress")]
        public async Task<ActionResult<GiftUsageAddressOutput>> GetGiftUsageAddress([FromQuery] GiftUsageAddressInput input)
        {
            try
            {
                var result = await _loyaltyGiftService.GetListGiftUsageAddress(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetGiftUsageAddress Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }



        [HttpGet]
        [Route("GetGiftByCateType_V2")]
        public async Task<ActionResult<GetGiftGroupByCateOutputForView>> GetGiftByCateType_V2([FromQuery] GetGiftByCateTypeInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyGiftService.GetGiftByCateType_V2(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetGiftByCateType_V2 Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }


        [HttpGet]
        [Route("SearchGiftAndGiftGroupAndBrand")]
        public async Task<ActionResult<SearchGiftAndGiftGroupAndBrandForViewOutput>> SearchGiftAndGiftGroupAndBrand([FromQuery] SearchInput input)
        {
            try
            {
                var result = await _loyaltyGiftService.SearchGiftAndGiftGroupAndBrand(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetListSuggest Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetListSuggest")]
        public async Task<ActionResult<SuggestOutputView>> GetListSuggest([FromQuery] SuggestInput input)
        {
            try
            {
                var result = await _loyaltyGiftService.GetListSuggest(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetListSuggest Error");
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }
    }
}
