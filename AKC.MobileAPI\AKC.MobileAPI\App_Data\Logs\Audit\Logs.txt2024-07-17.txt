INFO  2024-07-17 10:43:24,215 [56   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"CreateTransaction","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/create-transaction","QueryString":"{}","Body":"{\"quantity\":1,\"totalAmount\":210000,\"sessionId\":\"LynkID1721185771699\",\"giftCode\":\"GiftInfor_202310250101_1\",\"cifCode\":\"1400192\",\"memberCode\":\"n1miDgAwluNhr9I4BAXJxdMQpqN2\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-17 10:43:29,223 [56   ] iftService - GetBalanceMember start
INFO  2024-07-17 10:43:35,482 [57   ] iftService - GetBalanceMember end
INFO  2024-07-17 10:43:46,454 [57   ] iftService - Error message from code System error
ERROR 2024-07-17 10:43:46,619 [57   ] Controller - >> Partner# CreateTransaction Error - {"StackTrace":"   at AKC.MobileAPI.Service.Helpers.CommonHelper.GetErrorValidation(String errorCode, String errorMessage) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Helpers\\CommonHelper.cs:line 26\r\n   at AKC.MobileAPI.Service.SDK.MerchantGiftService.SendOtpCreateRedeem(MerchantGiftSendOtpCreateRedeemInput input, String phone) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\SDK\\MerchantGiftService.cs:line 124\r\n   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.CreateTransaction(MerchantGiftSendOtpCreateRedeemInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\SDKV1\\SDKV1Controller.cs:line 413","Message":"Exception of type 'AKC.MobileAPI.Service.Exceptions.RewardException' was thrown.","Data":{"ErrorData":"{\"status\":500,\"result\":{\"code\":\"SystemError\",\"message\":\"System error\"}}","StatusCode":400},"InnerException":null,"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-**********}
AKC.MobileAPI.Service.Exceptions.RewardException: Exception of type 'AKC.MobileAPI.Service.Exceptions.RewardException' was thrown.
   at AKC.MobileAPI.Service.Helpers.CommonHelper.GetErrorValidation(String errorCode, String errorMessage) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Helpers\CommonHelper.cs:line 26
   at AKC.MobileAPI.Service.SDK.MerchantGiftService.SendOtpCreateRedeem(MerchantGiftSendOtpCreateRedeemInput input, String phone) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\SDK\MerchantGiftService.cs:line 124
   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.CreateTransaction(MerchantGiftSendOtpCreateRedeemInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\SDKV1\SDKV1Controller.cs:line 413
INFO  2024-07-17 10:44:02,587 [56   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"CreateTransaction","Browser":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/create-transaction","QueryString":"{}","Body":"{\"quantity\":1,\"totalAmount\":210000,\"sessionId\":\"LynkID1721185771699\",\"giftCode\":\"GiftInfor_202310250101_1\",\"cifCode\":\"1400192\",\"memberCode\":\"n1miDgAwluNhr9I4BAXJxdMQpqN2\"}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-17 10:44:07,193 [56   ] iftService - GetBalanceMember start
INFO  2024-07-17 10:44:08,329 [59   ] iftService - GetBalanceMember end
ERROR 2024-07-17 10:44:37,669 [59   ] Controller - >> Partner# CreateTransaction Error - {"StackTrace":"   at AKC.MobileAPI.Service.Helpers.CommonHelper.GetErrorValidation(String errorCode, String errorMessage) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Helpers\\CommonHelper.cs:line 26\r\n   at AKC.MobileAPI.Service.SDK.MerchantGiftService.CreateRedeemHandle(MerchantGiftCreateRedeemInputDto input, String phoneNumber, Boolean isOtpSend, Int32 memberId, String otpCode, String sessionId) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\SDK\\MerchantGiftService.cs:line 400\r\n   at AKC.MobileAPI.Service.SDK.MerchantGiftService.SendOtpCreateRedeem(MerchantGiftSendOtpCreateRedeemInput input, String phone) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\SDK\\MerchantGiftService.cs:line 164\r\n   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.CreateTransaction(MerchantGiftSendOtpCreateRedeemInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\SDKV1\\SDKV1Controller.cs:line 413","Message":"Exception of type 'AKC.MobileAPI.Service.Exceptions.RewardException' was thrown.","Data":{"ErrorData":"{\"status\":500,\"result\":{\"code\":\"SystemProcessData\",\"message\":\"System error\"}}","StatusCode":400},"InnerException":null,"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-**********}
AKC.MobileAPI.Service.Exceptions.RewardException: Exception of type 'AKC.MobileAPI.Service.Exceptions.RewardException' was thrown.
   at AKC.MobileAPI.Service.Helpers.CommonHelper.GetErrorValidation(String errorCode, String errorMessage) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Helpers\CommonHelper.cs:line 26
   at AKC.MobileAPI.Service.SDK.MerchantGiftService.CreateRedeemHandle(MerchantGiftCreateRedeemInputDto input, String phoneNumber, Boolean isOtpSend, Int32 memberId, String otpCode, String sessionId) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\SDK\MerchantGiftService.cs:line 400
   at AKC.MobileAPI.Service.SDK.MerchantGiftService.SendOtpCreateRedeem(MerchantGiftSendOtpCreateRedeemInput input, String phone) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\SDK\MerchantGiftService.cs:line 164
   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.CreateTransaction(MerchantGiftSendOtpCreateRedeemInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\SDKV1\SDKV1Controller.cs:line 413
INFO  2024-07-17 10:44:57,175 [72   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"CreateTransaction","Browser":"PostmanRuntime/7.40.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/create-transaction","QueryString":"{}","Body":"{\r\n    \"quantity\": 1,\r\n    \"totalAmount\": 210000,\r\n    \"sessionId\": \"LynkID1721185771699\",\r\n    \"giftCode\": \"GiftInfor_202310250101_1\",\r\n    \"cifCode\": \"1400192\",\r\n    \"memberCode\": \"n1miDgAwluNhr9I4BAXJxdMQpqN2\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-17 10:45:05,217 [72   ] iftService - GetBalanceMember start
INFO  2024-07-17 10:45:07,194 [67   ] iftService - GetBalanceMember end
ERROR 2024-07-17 10:48:28,099 [109  ] Controller - >> Partner# CreateTransaction Error - {"StackTrace":"   at AKC.MobileAPI.Service.Helpers.CommonHelper.GetErrorValidation(String errorCode, String errorMessage) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Helpers\\CommonHelper.cs:line 26\r\n   at AKC.MobileAPI.Service.SDK.MerchantGiftService.CreateRedeemHandle(MerchantGiftCreateRedeemInputDto input, String phoneNumber, Boolean isOtpSend, Int32 memberId, String otpCode, String sessionId) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\SDK\\MerchantGiftService.cs:line 400\r\n   at AKC.MobileAPI.Service.SDK.MerchantGiftService.SendOtpCreateRedeem(MerchantGiftSendOtpCreateRedeemInput input, String phone) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\SDK\\MerchantGiftService.cs:line 164\r\n   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.CreateTransaction(MerchantGiftSendOtpCreateRedeemInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\SDKV1\\SDKV1Controller.cs:line 413","Message":"Exception of type 'AKC.MobileAPI.Service.Exceptions.RewardException' was thrown.","Data":{"ErrorData":"{\"status\":500,\"result\":{\"code\":\"SystemProcessData\",\"message\":\"System error\"}}","StatusCode":400},"InnerException":null,"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-**********}
AKC.MobileAPI.Service.Exceptions.RewardException: Exception of type 'AKC.MobileAPI.Service.Exceptions.RewardException' was thrown.
   at AKC.MobileAPI.Service.Helpers.CommonHelper.GetErrorValidation(String errorCode, String errorMessage) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Helpers\CommonHelper.cs:line 26
   at AKC.MobileAPI.Service.SDK.MerchantGiftService.CreateRedeemHandle(MerchantGiftCreateRedeemInputDto input, String phoneNumber, Boolean isOtpSend, Int32 memberId, String otpCode, String sessionId) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\SDK\MerchantGiftService.cs:line 400
   at AKC.MobileAPI.Service.SDK.MerchantGiftService.SendOtpCreateRedeem(MerchantGiftSendOtpCreateRedeemInput input, String phone) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\SDK\MerchantGiftService.cs:line 164
   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.CreateTransaction(MerchantGiftSendOtpCreateRedeemInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\SDKV1\SDKV1Controller.cs:line 413
ERROR 2024-07-17 10:59:14,282 [211  ] tyTokenJob - Job: RenewAccessToken error{"ClassName":"System.AggregateException","Message":"One or more errors occurred.","Data":null,"InnerException":{"StackTrace":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean allowHttp2, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.GetHttpConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":10060},"HelpLink":null,"Source":"System.Net.Http","HResult":-2147467259},"HelpURL":null,"StackTraceString":"   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)\r\n   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)\r\n   at System.Threading.Tasks.Task`1.get_Result()\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.LoginLoyalty(Nullable`1 tenantId, String baseURL, String userName, String password) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyHelper.cs:line 50\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.GetNewAccessTokenVPBankLoyaltyExchange() in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyHelper.cs:line 139\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.RenewAccessTokenVPBankExchangeCacheValue(IDistributedCache cache, TimeSpan delay, Boolean isForceRenew) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyHelper.cs:line 245\r\n   at AKC.MobileAPI.Service.CronServices.RefreshLoyaltyTokenJob.DoWork(CancellationToken cancellationToken) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\CronServices\\Jobs\\RefreshLoyaltyTokenJob.cs:line 89","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-**********,"Source":"System.Private.CoreLib","WatsonBuckets":null,"InnerExceptions":[{"StackTrace":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean allowHttp2, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.GetHttpConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":10060},"HelpLink":null,"Source":"System.Net.Http","HResult":-2147467259}]}
ERROR 2024-07-17 11:00:28,082 [224  ] tyTokenJob - Job: RenewAccessToken error{"ClassName":"System.AggregateException","Message":"One or more errors occurred.","Data":null,"InnerException":{"StackTrace":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean allowHttp2, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.GetHttpConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":10060},"HelpLink":null,"Source":"System.Net.Http","HResult":-2147467259},"HelpURL":null,"StackTraceString":"   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)\r\n   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)\r\n   at System.Threading.Tasks.Task`1.get_Result()\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.LoginLoyalty(Nullable`1 tenantId, String baseURL, String userName, String password) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyHelper.cs:line 50\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.GetNewAccessTokenVPBankLoyaltyExchange() in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyHelper.cs:line 139\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.RenewAccessTokenVPBankExchangeCacheValue(IDistributedCache cache, TimeSpan delay, Boolean isForceRenew) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyHelper.cs:line 245\r\n   at AKC.MobileAPI.Service.CronServices.RefreshLoyaltyTokenJob.DoWork(CancellationToken cancellationToken) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\CronServices\\Jobs\\RefreshLoyaltyTokenJob.cs:line 89","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-**********,"Source":"System.Private.CoreLib","WatsonBuckets":null,"InnerExceptions":[{"StackTrace":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean allowHttp2, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.GetHttpConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":10060},"HelpLink":null,"Source":"System.Net.Http","HResult":-2147467259}]}
INFO  2024-07-17 13:46:34,654 [29   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"CreateTransaction","Browser":"PostmanRuntime/7.40.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/create-transaction","QueryString":"{}","Body":"{\r\n    \"quantity\": 1,\r\n    \"totalAmount\": 210000,\r\n    \"sessionId\": \"LynkID1721185771699\",\r\n    \"giftCode\": \"GiftInfor_202310250101_1\",\r\n    \"cifCode\": \"1400192\",\r\n    \"memberCode\": \"n1miDgAwluNhr9I4BAXJxdMQpqN2\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-17 13:46:37,565 [29   ] iftService - GetBalanceMember start
INFO  2024-07-17 13:46:39,151 [28   ] iftService - GetBalanceMember end
ERROR 2024-07-17 13:50:03,079 [48   ] Controller - >> Partner# CreateTransaction Error - {"StackTrace":"   at AKC.MobileAPI.Service.Helpers.CommonHelper.GetErrorValidation(String errorCode, String errorMessage) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Helpers\\CommonHelper.cs:line 26\r\n   at AKC.MobileAPI.Service.SDK.MerchantGiftService.CreateRedeemHandle(MerchantGiftCreateRedeemInputDto input, String phoneNumber, Boolean isOtpSend, Int32 memberId, String otpCode, String sessionId) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\SDK\\MerchantGiftService.cs:line 400\r\n   at AKC.MobileAPI.Service.SDK.MerchantGiftService.SendOtpCreateRedeem(MerchantGiftSendOtpCreateRedeemInput input, String phone) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\SDK\\MerchantGiftService.cs:line 164\r\n   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.CreateTransaction(MerchantGiftSendOtpCreateRedeemInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\SDKV1\\SDKV1Controller.cs:line 413","Message":"Exception of type 'AKC.MobileAPI.Service.Exceptions.RewardException' was thrown.","Data":{"ErrorData":"{\"status\":500,\"result\":{\"code\":\"SystemProcessData\",\"message\":\"System error\"}}","StatusCode":400},"InnerException":null,"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-**********}
AKC.MobileAPI.Service.Exceptions.RewardException: Exception of type 'AKC.MobileAPI.Service.Exceptions.RewardException' was thrown.
   at AKC.MobileAPI.Service.Helpers.CommonHelper.GetErrorValidation(String errorCode, String errorMessage) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\Helpers\CommonHelper.cs:line 26
   at AKC.MobileAPI.Service.SDK.MerchantGiftService.CreateRedeemHandle(MerchantGiftCreateRedeemInputDto input, String phoneNumber, Boolean isOtpSend, Int32 memberId, String otpCode, String sessionId) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\SDK\MerchantGiftService.cs:line 400
   at AKC.MobileAPI.Service.SDK.MerchantGiftService.SendOtpCreateRedeem(MerchantGiftSendOtpCreateRedeemInput input, String phone) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI.Service\SDK\MerchantGiftService.cs:line 164
   at AKC.MobileAPI.Controllers.SDKV1.SDKV1Controller.CreateTransaction(MerchantGiftSendOtpCreateRedeemInput input) in D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI\Controllers\SDKV1\SDKV1Controller.cs:line 413
INFO  2024-07-17 13:50:06,935 [61   ]            - Http Request Information: {"ControllerName":"SDKV1","ActionName":"CreateTransaction","Browser":"PostmanRuntime/7.40.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/sdk-v1/create-transaction","QueryString":"{}","Body":"{\r\n    \"quantity\": 1,\r\n    \"totalAmount\": 210000,\r\n    \"sessionId\": \"LynkID1721185771699\",\r\n    \"giftCode\": \"GiftInfor_202310250101_1\",\r\n    \"cifCode\": \"1400192\",\r\n    \"memberCode\": \"n1miDgAwluNhr9I4BAXJxdMQpqN2\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-07-17 13:50:10,263 [61   ] iftService - GetBalanceMember start
INFO  2024-07-17 13:50:13,634 [51   ] iftService - GetBalanceMember end
