using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;

namespace AKC.MobileAPI.Helper
{
    public class CSTicketAnnonymousRateLimitingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IDistributedCache _cache;
        private readonly ILogger<CSTicketAnnonymousRateLimitingMiddleware> _logger;

        private const int TimeWindowInSeconds = 1800; // 30 minute
        private const int MaxRequests = 5; // Limit requests to 5 per 30min

        public CSTicketAnnonymousRateLimitingMiddleware(RequestDelegate next, IDistributedCache cache,
            ILogger<CSTicketAnnonymousRateLimitingMiddleware> logger)
        {
            _next = next;
            _cache = cache;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (context.Request.Path.StartsWithSegments("/api/cs-ticket/create-anonymously") || context.Request.Path.StartsWithSegments("/api/cs-ticket/upload-image"))
            {
                var clientIp = context.Connection.RemoteIpAddress?.ToString();
                if (clientIp != null)
                {
                    var cacheKey = $"RateLimitCsticketAnonymous_{clientIp}";
                    var requestCount = await _cache.GetStringAsync(cacheKey);

                    if (requestCount != null && int.TryParse(requestCount, out var currentCount))
                    {
                        if (currentCount >= MaxRequests)
                        {
                            _logger.LogWarning($"Rate limit exceeded for {clientIp}");
                            context.Response.StatusCode = StatusCodes.Status429TooManyRequests;
                            await context.Response.WriteAsync("Rate limit exceeded. Try again later.");
                            return;
                        }

                        currentCount++;
                        await _cache.SetStringAsync(cacheKey, currentCount.ToString(), new DistributedCacheEntryOptions
                        {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(TimeWindowInSeconds)
                        });
                    }
                    else
                    {
                        // New request within the window
                        await _cache.SetStringAsync(cacheKey, "1", new DistributedCacheEntryOptions
                        {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(TimeWindowInSeconds)
                        });
                    }
                }
            }

            // Proceed to the next middleware
            await _next(context);
        }
    }
}