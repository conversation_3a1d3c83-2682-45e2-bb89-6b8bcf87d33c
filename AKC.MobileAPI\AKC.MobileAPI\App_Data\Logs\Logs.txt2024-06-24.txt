INFO  2024-06-24 11:00:00,410 [1    ] Microsoft.Hosting.Lifetime               - Application started. Press Ctrl+C to shut down.
INFO  2024-06-24 11:00:00,424 [1    ] Microsoft.Hosting.Lifetime               - Hosting environment: Development
INFO  2024-06-24 11:00:00,426 [1    ] Microsoft.Hosting.Lifetime               - Content root path: D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI
ERROR 2024-06-24 11:00:21,380 [30   ] vice.CronServices.RefreshLoyaltyTokenJob - Job: RenewAccessToken error{"ClassName":"System.AggregateException","Message":"One or more errors occurred.","Data":null,"InnerException":{"StackTrace":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean allowHttp2, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.GetHttpConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":10060},"HelpLink":null,"Source":"System.Net.Http","HResult":-2147467259},"HelpURL":null,"StackTraceString":"   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)\r\n   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)\r\n   at System.Threading.Tasks.Task`1.get_Result()\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.LoginLoyalty(Nullable`1 tenantId, String baseURL, String userName, String password) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyHelper.cs:line 50\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.GetNewAccessTokenVPBankLoyaltyExchange() in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyHelper.cs:line 139\r\n   at AKC.MobileAPI.Service.Loyalty.LoyaltyHelper.RenewAccessTokenVPBankExchangeCacheValue(IDistributedCache cache, TimeSpan delay, Boolean isForceRenew) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyHelper.cs:line 245\r\n   at AKC.MobileAPI.Service.CronServices.RefreshLoyaltyTokenJob.DoWork(CancellationToken cancellationToken) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\CronServices\\Jobs\\RefreshLoyaltyTokenJob.cs:line 89","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-**********,"Source":"System.Private.CoreLib","WatsonBuckets":null,"InnerExceptions":[{"StackTrace":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean allowHttp2, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.GetHttpConnectionAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithRetryAsync(HttpRequestMessage request, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.FinishSendAsyncBuffered(Task`1 sendTask, HttpRequestMessage request, CancellationTokenSource cts, Boolean disposeCts)","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":10060},"HelpLink":null,"Source":"System.Net.Http","HResult":-2147467259}]}
INFO  2024-06-24 11:04:04,786 [1    ] Microsoft.Hosting.Lifetime               - Application started. Press Ctrl+C to shut down.
INFO  2024-06-24 11:04:04,798 [1    ] Microsoft.Hosting.Lifetime               - Hosting environment: Development
INFO  2024-06-24 11:04:04,800 [1    ] Microsoft.Hosting.Lifetime               - Content root path: D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI
INFO  2024-06-24 11:14:23,149 [1    ] Microsoft.Hosting.Lifetime               - Application started. Press Ctrl+C to shut down.
INFO  2024-06-24 11:14:23,162 [1    ] Microsoft.Hosting.Lifetime               - Hosting environment: Development
INFO  2024-06-24 11:14:23,164 [1    ] Microsoft.Hosting.Lifetime               - Content root path: D:\working\vpid-mobile-api\AKC.MobileAPI\AKC.MobileAPI
