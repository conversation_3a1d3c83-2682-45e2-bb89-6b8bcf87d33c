﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.SmartOTP.ActiveSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.DeactivateSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.RegisterSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.RegisterSmartOTPStatus;
using AKC.MobileAPI.DTO.SmartOTP.StatusSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.SyncTimeSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.ValidateSmartOTP;
using AKC.MobileAPI.DTO.SmartOTP.VerifySmartOTP;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Abstract.SmartOTP;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Reward;
using FirebaseAdmin.Auth;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.SmartOTP
{
    public class SmartOTPService : RewardBaseService, ISmartOTPService
    {
        private readonly IRewardMemberService _rewardMemberService;
        private readonly ILogger<SmartOTPService> _logger;
        private readonly ILoyaltySecondaryCustomersService _loyaltySecondaryCustomersService;
        private readonly IExceptionReponseService _exceptionReponseService;

        public SmartOTPService(IConfiguration configuration, IDistributedCache cache, IRewardMemberService rewardMemberService, ILogger<SmartOTPService> logger, ILoyaltySecondaryCustomersService loyaltySecondaryCustomersService, IExceptionReponseService exceptionReponseService) : base(configuration)
        {
            _rewardMemberService = rewardMemberService;
            _logger = logger;
            _loyaltySecondaryCustomersService = loyaltySecondaryCustomersService;
            _exceptionReponseService = exceptionReponseService;
        }

        public async Task<ActiveSmartOTPOutput> ActiveSmartOTP(ActiveSmartOTPInput input)
        {
            return await PostRewardAsync<ActiveSmartOTPOutput>(RewardApiUrl.ACTIVE_SMART_OTP, input);
        }

        public async Task<DeactivateSmartOTPOutput> DeactivateSmartOTP(DeactivateSmartOTPRequest input)
        {
            return await PostRewardAsync<DeactivateSmartOTPOutput>(RewardApiUrl.DEACTIVATE_SMART_OTP, input);
        }

        public async Task<RegisterSmartOTPOutput> RegisterSmartOTP(RegisterSmartOTPInput input)
        {
            return await PostRewardAsync<RegisterSmartOTPOutput>(RewardApiUrl.REGISTER_SMART_OTP, input);
        }

        public async Task<ValidateSmartOTPOutput> ValidateSmartOTP(ValidateSmartOTPInput input)
        {
            return await PostRewardAsync<ValidateSmartOTPOutput>(RewardApiUrl.VALIDATE_SMART_OTP, input);
        }

        public async Task<VerifyOTPOutput> VerifySmartOTP(CreateUserMobileDTO input)
        {
            checkPhone(input.PhoneNumber);
            await _rewardMemberService.VerityOtp(new RewardMemberVerifyOtpInput()
            {
                OtpCode = input.OtpCode,
                PhoneNumber = input.PhoneNumber,
                SessionId = input.SessionId,
                SmsType = "RegisterSmartOTP",
            });
            var item = new VerifyOTPItemOutput()
            {
                Status = StatusReturnConst.IncorrectPinCode,
                CustomToken = null,
                Message = "Incorrect OTP Code"
            };

            var auth = FirebaseAuth.DefaultInstance;
            UserRecord user = null;
            try
            {
                user = await auth.CreateUserAsync(new UserRecordArgs()
                {
                    PhoneNumber = input.PhoneNumber
                });
            }
            catch (FirebaseAuthException ex)
            {
                _logger.LogError("Get firebase error from phone number", ex);
                if (ex.AuthErrorCode == AuthErrorCode.PhoneNumberAlreadyExists)
                {
                    getExceptionCustome("DuplicatePhoneNumber", "Duplicate phone number request");
                }
                getExceptionCustome("SystemError", "System error");
            }
            catch (Exception ex)
            {
                _logger.LogError("Get firebase error from phone number", ex);
                getExceptionCustome("SystemError", "System error");
            }
            var hasCreateMember = true;
            var memberCode = "";
            var isDeleteUserFirebase = false;
            try
            {
                var resultcreatemember = new RewardMemberCreateOutput();
                var memberReward = await _rewardMemberService.VerifyProviderIdByPhoneNumber(
                     new VerifyProviderIdByPhoneNumberRequest()
                     {
                         ProviderName = "Firebase",
                         ProviderId = user.Uid,
                         PhoneNumber = input.PhoneNumber
                     }
                 );
                memberCode = memberReward.Items.MemberCode;
                // If cannot member at loyalty then create member, else create return member code
                if (!memberReward.Items.MemberExist)
                {
                    try
                    {
                        var res = await VerifyOrCreateMember(input, user.Uid);
                        memberCode = res.Result.MemberCode;
                        hasCreateMember = true;
                    }
                    catch (Exception exCreate)
                    {
                        await auth.DeleteUserAsync(user.Uid);
                        isDeleteUserFirebase = true;
                        var res = await _exceptionReponseService.GetExceptionRewardReponse(exCreate);
                        if (exCreate.GetType() == typeof(RewardException))
                        {
                            throw exCreate;
                        }
                        _logger.LogError("Fail to create member for reward", exCreate);
                        getExceptionCustome("SystemError", "System error");
                    }
                }
                else
                {
                    var updateProvider = new VerifyProviderIdByPhoneNumberRequest()
                    {
                        ProviderName = "Firebase",
                        ProviderId = user.Uid,
                        PhoneNumber = input.PhoneNumber
                    };
                    await _rewardMemberService.UpdateProvider(updateProvider);
                }
            }
            catch (Exception exCreate)
            {
                // Nếu chưa xóa user firebase ở catch đầu tiên thì mới xóa
                if (!isDeleteUserFirebase)
                {
                    await auth.DeleteUserAsync(user.Uid);
                }
                if (exCreate.GetType() == typeof(RewardException))
                {
                    throw exCreate;
                }
                _logger.LogError("Fail to create member for reward", exCreate);
                getExceptionCustome("SystemError", "System error");
            }

            var customToken = await auth.CreateCustomTokenAsync(user.Uid);
            item.CustomToken = customToken;
            item.MemberCode = memberCode;
            item.Message = "Success";

            if (hasCreateMember && !string.IsNullOrWhiteSpace(input.ReferralCode))
            {
                try
                {
                    await _loyaltySecondaryCustomersService.VerifyReferralCode(new LoyaltyVerifyReferralCodeInput()
                    {
                        NationalId = user.Uid,
                        ReferralCode = input.ReferralCode,
                        DistributionChannelList = null,
                        ReferenceAmount = 0
                    });
                }
                catch { }
            }

            return new VerifyOTPOutput()
            {
                Item = item,
                Message = "Success",
                Result = 200,
            };
        }

        private void checkPhone(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                getExceptionCustome("CannotEmptyPhoneNumber", "Cannot empty phone number");
            }
            if (!phoneNumber.StartsWith("+84"))
            {
                getExceptionCustome("InvalidFormatPhone", "Invalid format phone");
            }
            var temp = phoneNumber.Replace("+", "");
            var regOnlyNumberChar = new Regex(@"^[0-9]+$");
            if (!regOnlyNumberChar.IsMatch(temp))
            {
                getExceptionCustome("InvalidFormatPhone", "Invalid format phone");
            }
            if (temp.Length <= 10 || temp.Length >= 12)
            {
                getExceptionCustome("InvalidFormatPhone", "Invalid format phone");
            }
        }
        private void getExceptionCustome(string errorCode, string message)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = message,
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }

        public async Task<RewardMemberVerifyOrCreateOutput> VerifyOrCreateMember(CreateUserMobileDTO input, string firebaseId)
        {
            var createMemberDto = new RewardMemberVerifyOrCreateDto()
            {
                NationalId = firebaseId,
                IdCard = "",
                PartnerPhoneNumber = null,
                Type = "Member",
                Phone = input.PhoneNumber,
                Gender = !(new List<string> { "M", "F", "O" }).Contains(input.Gender) ? "O" : input.Gender,
                Status = "A",
                RankTypeCode = "Customer",
                IsDeleted = false,
                Address = input.Address,
                Dob = null, // Fix bug LA-665: Set default DOB is null
                Name = input.Name,
                Email = "",
                PointUsingOrdinary = "",
                HashAddress = "",
                RegionCode = "",
                FullRegionCode = "",
                MemberTypeCode = "",
                FullMemberTypeCode = "",
                ChannelType = "",
                FullChannelTypeCode = "",
                StandardMemberCode = firebaseId,
                ReferralCode = GenReferralCode(input.PhoneNumber),
                Avatar = "",
                FirebaseId = firebaseId,
                ReferenceId = input.PhoneNumber,
                RegisterType = "PhoneNumber",
            };

            var res = await PostRewardAsync<RewardMemberVerifyOrCreateOutputDto>(RewardApiUrl.MEMBER_VERIFY_OR_CREATE, createMemberDto);
            return new RewardMemberVerifyOrCreateOutput()
            {
                Messages = "Success",
                Result = new RewardMemberVerifyOrCreateOutputItem()
                {
                    MemberCode = res.Result.NationalId,
                    PhoneNumber = res.Result.Phone,
                },
                Status = 200,
            };
        }
        private string GenReferralCode(string phoneNumber)
        {
            if (!string.IsNullOrEmpty(phoneNumber))
            {
                return phoneNumber.Replace("+84", "0");
            }
            return null;
        }

        public async Task<RewardMemberSendOtpOutput> GetOTP(RewardMemberSendOtpInput input)
        {
            checkPhone(input.PhoneNumber);
            return await _rewardMemberService.SendOtp(input);
        }

        public async Task<SyncTimeSmartOTPOutput> SyncTimeSmartOTP(SyncTimeSmartOTPInput input)
        {
            return await PostRewardAsync<SyncTimeSmartOTPOutput>(RewardApiUrl.SYNC_TIME_SMART_OTP, input);
        }

        public async Task<RegisterSmartOTPStatusOutput> RegisterSmartOTPStatus(RegisterSmartOTPStatusInput input)
        {
            return await PostRewardAsync<RegisterSmartOTPStatusOutput>(RewardApiUrl.REGISTER_SMART_OTP_STATUS, input);
        }

        public async Task<RewardMemberVerifyOtpOutput> VerifyOTP(RewardMemberVerifyOtpInput input)
        {
            return await _rewardMemberService.VerityOtp(input);
        }

        public async Task<StatusSmartOTPOutput> GetStatusSmartOTP(StatusSmartOTPInput input)
        {
            return await PostRewardAsync<StatusSmartOTPOutput>(RewardApiUrl.GET_STATUS_SMART_OTP, input);
        }
    }
}
