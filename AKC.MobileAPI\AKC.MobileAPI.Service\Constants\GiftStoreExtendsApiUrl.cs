﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.Service.Constants
{
    public class GiftStoreExtendsApiUrl
    {
        public const string GET_MERCHANT_ID_FROM_GIFT_CODE = "services/app/GiftInfors/GetMerchantIdFromGiftCode";
        public const string GIFTTRANSACTION_CREATEREDEEMTRANSACTION = "services/app/GiftStoreVendor/CreateOrder";
        public const string GET_BRANDS = "services/app/GiftStoreVendor/GetBrands";
        public const string GET_CATEGORY = "services/app/GiftStoreVendor/GetCategories";
        public const string GET_GIFT_LIST = "services/app/GiftStoreVendor/GetGiftList";
        public const string GET_GIFT_DETAIL = "services/app/GiftStoreVendor/GetGiftDetail";
        public const string GET_ORDER_DETAIL = "services/app/GiftStoreVendor/GetDetailOrder";
        public const string GET_LIST_ORDER = "services/app/GiftStoreVendor/GetListOrder";
        public const string GIFTTRANSACTION_CREATE_REDEEM_MERCHANT = "services/app/MerchantGiftTransactions/CreateRedeemTransaction";
    }
}
