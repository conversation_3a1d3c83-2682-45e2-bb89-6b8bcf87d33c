using System;
using Newtonsoft.Json;

namespace AKC.MobileAPI.DTO.Base
{
    public class CustomDateTimeConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(DateTime);
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            if (reader.Value != null)
            {
                DateTime dt;
                if (DateTime.TryParse(reader.Value.ToString(), out dt))
                    return dt.ToUniversalTime();
                else
                    return null;
            }
            return reader.Value;
        }

        public override void Write<PERSON><PERSON>(JsonWriter writer, object value, JsonSerializer serializer)
        {
            writer.WriteValue(value);
        }
    }
}