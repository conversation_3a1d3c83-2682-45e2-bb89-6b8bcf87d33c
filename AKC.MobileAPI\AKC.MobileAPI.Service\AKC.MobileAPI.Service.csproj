﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.TotpGenerator" Version="1.0.0" />
    <PackageReference Include="AWSSDK.S3" Version="3.5.7.3" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.4.3" />
    <PackageReference Include="Cronos" Version="0.7.0" />
    <PackageReference Include="FirebaseAdmin" Version="1.14.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="3.1.2" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="3.1.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="3.1.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="3.1.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="3.1.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="3.1.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="3.1.2" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="3.1.2" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="3.1.32" />
    <PackageReference Include="Minio" Version="3.1.13" />
    <PackageReference Include="Newtonsoft.Json" Version="12.0.2" />
    <PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
    <PackageReference Include="Quartz" Version="3.0.7" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="5.5.0" />
    <PackageReference Include="WindowsAzure.Storage" Version="9.3.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AKC.MobileAPI.DTO\AKC.MobileAPI.DTO.csproj" />
    <ProjectReference Include="..\AKC.RabbitMQ\AKC.RabbitMQ.csproj" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\Controllers.WebstoreController.vi.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Controllers.WebstoreController.vi.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Controllers.WebstoreController.en.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Controllers.WebstoreController.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\Controllers.WebstoreController.vi.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Controllers.WebstoreController.vi.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Controllers.WebstoreController.en.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Controllers.WebstoreController.en.resx</DependentUpon>
    </Compile>
  </ItemGroup>

</Project>
