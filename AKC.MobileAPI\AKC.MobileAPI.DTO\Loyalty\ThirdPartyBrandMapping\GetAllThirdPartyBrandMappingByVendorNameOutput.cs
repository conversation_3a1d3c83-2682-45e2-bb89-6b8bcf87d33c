﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.ThirdPartyBrandMapping
{
    public class GetAllThirdPartyBrandMappingByVendorNameOutput
    {
        public List<Result> Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }
    public class Result
    {
        public ThirdPartyBrandMappingDto BrandMapping { get; set; }
    }
    public class ThirdPartyBrandMappingDto
    {
        public int? Id { get; set; }
        public string Code { get; set; }
        public int? BrandId { get; set; }
        public string BrandName { get; set; }
        public int? VendorId { get; set; }
        public string ThirdPartyBrandId { get; set; }
        public string ThirdPartyBrandName { get; set; }
        public bool IsActive { get; set; }
        public string LinkLogo { get; set; }
        public double CommissPercent { get; set; }
    }
}
