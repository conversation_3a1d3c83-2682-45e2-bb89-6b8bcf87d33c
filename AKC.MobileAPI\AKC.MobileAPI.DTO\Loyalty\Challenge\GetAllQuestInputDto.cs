﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class GetAllQuestInputDto
    {
		public string Filter { get; set; }

		public string CodeFilter { get; set; }

		public string StatusFilter { get; set; }

		public DateTime? MaxFromDateFilter { get; set; }
		public DateTime? MinFromDateFilter { get; set; }

		public DateTime? MaxToDateFilter { get; set; }
		public DateTime? MinToDateFilter { get; set; }

		public int? QuestGroupIdFilter { get; set; }

		public string Sorting { get; set; }
		public int SkipCount { get; set; }
		public int MaxResultCount { get; set; }
	}

}
