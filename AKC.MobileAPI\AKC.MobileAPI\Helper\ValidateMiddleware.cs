﻿using Microsoft.AspNetCore.Builder;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Helper
{
    public static class ValidateMiddleware
    {
        public static void AddValidateSignature(this IApplicationBuilder app)
        {
            app.UseWhen(context => context.Request.Path == "/api/Member/VerifyOtpChangePinCode", appBuilder =>
            {
                appBuilder.UseMiddleware<ValidateSignatureMiddleware>();
            });
            app.UseWhen(context => context.Request.Path == "/api/Member/VerifyOtpChangePhone", appBuilder =>
            {
                appBuilder.UseMiddleware<ValidateSignatureMiddleware>();
            });
            app.UseWhen(context => context.Request.Path == "/api/Member/UpdatePinCodeForget", appBuilder =>
            {
                appBuilder.UseMiddleware<ValidateSignatureMiddleware>();
            });
            app.UseWhen(context => context.Request.Path == "/api/Member/VerifyOtpAddPhone", appBuilder =>
            {
                appBuilder.UseMiddleware<ValidateSignatureMiddleware>();
            });
        }
    }
}
