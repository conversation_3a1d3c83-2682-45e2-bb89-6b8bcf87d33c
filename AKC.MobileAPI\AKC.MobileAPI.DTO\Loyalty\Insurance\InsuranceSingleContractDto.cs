﻿using System;
using System.Collections.Generic;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;

namespace AKC.MobileAPI.DTO.Loyalty.Insurance
{
    public class GetListInsuranceContractOutput
    {
        public GetListInsuranceContractChildrenOutput Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class GetListInsuranceContractChildrenOutput
    {
        public int TotalCount { get; set; }
        public List<InsuranceSingleContractDto> Items { get; set; }
    }

    public class InsuranceSingleContractDto
    {
        public InsurancePackageInfo PackageInfo { get; set; }
        public InsuranceContractDto ContractInfo { get; set; }
        
        public ThirdPartyGiftVendorDto Vendor { get; set; }
    }
    public class InsuranceContractDto {
        public string Code { get; set; } // LINKID code
        public string InsuranceContractNo { get; set; }
        public string LinkIdCustomerName { get; set; }
        public string LinkIdMemberCode { get; set; }
        public string LinkIdPhoneNumber { get; set; }
        public string GiftCode { get; set; }
        public string VehicleLicensePlate { get; set; }
        public string VehicleFrameNumber { get; set; }
        public string VehicleEngineNumber { get; set; }
        public string VehicleEngineNo { get; set; }
        public string VehicleChassisNo { get; set; }
        public string VehicleType { get; set; }
        // Giá trị bảo hiểm
        public decimal InsuranceAmount { get; set; }
        // Phí bảo hiểm
        public decimal InsuranceFee { get; set; }
        public decimal GiftFullPrice { get; set; }
        public decimal GiftRequiredCoin { get; set; }
        public string CustomerName { get; set; }
        public DateTime CustomerDoB { get; set; }
        public string CustomerIdCard { get; set; }
        public string CustomerIdCardType { get; set; }
        public string CustomerEmailAddress { get; set; }
        public string CustomerAddress { get; set; }
        public string CustomerPhoneNumber { get; set; }
        // Đường dẫn đến chứng nhận BH
        public string DocumentLink { get; set; }
        public string Status { get; set; }
        public string InsuranceEffectiveTime { get; set; }
        public string InsuranceEffectiveTimeType { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public virtual DateTime? BeginDate { get; set; }
        public virtual DateTime? EndDate { get; set; }
    }

    public class InsurancePackageInfo
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public string ProfilePhoto { get; set; }
        public string PartnerCode { get; set; }
        public string ProgramCode { get; set; }
        public string ProgramName { get; set; }
        public string ProductCode { get; set; }
        public string ProductPhoto { get; set; }
        public string ProductName { get; set; }
        public string InsuranceBenefit { get; set; }
        public string InsuranceClaimGuideline { get; set; }
        public string GiftCode { get; set; }
        public string Description { get; set; }
        public decimal InsuranceAmount { get; set; }
        public decimal InsuranceFee { get; set; }
        public decimal GiftFullPrice { get; set; }
        public decimal GiftRequiredCoin { get; set; }
        public int EffectiveTime { get; set; } // So thang hay so nam hieu luc
        public string EffectiveTimeUnit { get; set; }
        public bool CanPayPartially { get; set; }
        public bool AutoRenew { get; set; }
    }
    // --- Search contracts
    public class GetListContractInput
    {
        public string Code { get; set; }
        public string MemberCode { get; set; }
        public int Skip { get; set; }
        public int MaxItem { get; set; }
    }
    
    // --- MUA
    public class PurchasePackageInput
    {
        public string MemberCode { get; set; }
        public string VendorCode { get; set; }
        public string PackageCode { get; set; }
        public string GiftCode { get; set; }
        public decimal TotalAmount { get; set; }
        public bool FullyPay { get; set; } // Default true
        public PurchasePackageContractInput ContractInfo { get; set; }
    }

    public class PurchasePackageContractInput
    {
        public string VehicleLicensePlate { get; set; }
        public string VehicleChassisNo { get; set; }
        public string VehicleEngineNo { get; set; }
        public string VehicleType { get; set; }
        public string CustomerName { get; set; }
        
        public DateTime CustomerDoB { get; set; }
        public string CustomerIdCard { get; set; }
        public string CustomerEmailAddress { get; set; }
        public string CustomerPhoneNumber { get; set; }
        public string CustomerAddress { get; set; }
        public DateTime BeginDate { get; set; }
        public int InsuranceEffectiveTime { get; set; }
        public string InsuranceEffectiveTimeType { get; set; }
    }

    public class ThirdPartyGiftVendorDto
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string ImageLink { get; set; }
        public string Hotline { get; set; }
    }
    
    // Temp DTO to serialize Vehicle/User info for Insurance. And write to DESCRIPTION
    public class InsuranceCustomerAndVehicleInfoDto
    {
        public string vehicleType { get; set; }
        public string vehiclePlateNo { get; set; }
        public string vehicleEngineNo { get; set; }
        public string vehicleChassisNo { get; set; }
        public string vehicleBrand { get; set; }
        public string vehicleColor { get; set; }
        public string ownerName { get; set; }
        public string ownerId { get; set; }
        public string ownerPhone { get; set; }
        public string ownerEmail { get; set; }
        public string ownerAddress { get; set; }
        public string effectiveDate { get; set; }
        public string durations { get; set; }
        public string premium { get; set; }
        public string customerDoB { get; set; }
    }
}