﻿using System;

namespace AKC.MobileAPI.DTO.Reward.CashoutTransaction
{
    public class RewardCreateCashoutTransactionInput
    {
        public int? MemberId { get; set; }
        public string MemberCode { get; set; }
        public int MerchantId { get; set; }
        public decimal TokenAmount { get; set; }
        public string OrderCode { get; set; }
    }

    public class RewardCreateCashoutTransactionDto
    {
        public int? MemberId { get; set; }
        public string NationalId { get; set; }
        public int MerchantId { get; set; }
        public decimal TokenAmount { get; set; }
        public string OrderCode { get; set; }
    }
}
