﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class GetListQuestStageJoinedByMemberOutPutDto
    {
        public ListResultQuestStageJoinedByMember Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }

    }
    public class ListResultQuestStageJoinedByMember
    {
        public int TotalCount { get; set; }

        public List<ListItemsQuestStageJoinedByMember> Items { get; set; }
    }

    public class ListItemsQuestStageJoinedByMember
    {
        public DateTime? CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string Code { get; set; }

        public string Name { get; set; }
        public virtual string Description { get; set; }

        public virtual string LinkAvatar { get; set; }

        public string Status { get; set; }

        public DateTime FromDate { get; set; }

        public DateTime ToDate { get; set; }

        public string Tag { get; set; }

        public string State { get; set; }

        public string Process { get; set; }
        public List<QuestStageLevelDto> Levels { get; set; }
        public int? LevelCount { get; set; }
        public DateTime? JoinQuestStageDate { get; set; }

        public long Id { get; set; }

        public int? QuestStageBaselineId { get; set; }
        public decimal Point { get; set; }
        public decimal Coin { get; set; }
    }

}
