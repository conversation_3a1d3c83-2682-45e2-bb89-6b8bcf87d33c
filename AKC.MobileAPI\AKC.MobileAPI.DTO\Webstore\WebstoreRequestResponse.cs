using AKC.MobileAPI.DTO.Downstream;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory.V2;
using AKC.MobileAPI.DTO.Reward.Member;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AKC.MobileAPI.DTO.Webstore
{
    public class WebstoreBaseOutputDto<T>
    {
        public string Code { get; set; }
        public string Message { get; set; }
        public T Result { get; set; }

        public WebstoreBaseOutputDto()
        {
            Code = string.Empty;
            Message = string.Empty;
        }

        public WebstoreBaseOutputDto(string code, string message)
        {
            if (string.IsNullOrEmpty(code))
            {
                throw new ArgumentNullException(nameof(code));
            }

            Code = code;
            Message = message;
            Result = default;
        }

        public WebstoreBaseOutputDto(string code, string message, T result)
        {
            if (code == null)
            {
                throw new ArgumentNullException(nameof(code));
            }

            Code = code;
            Message = message;
            Result = result;
        }

        public static WebstoreBaseOutputDto<T> Success(T result, string message = "Success") =>
            new WebstoreBaseOutputDto<T>("00", message, result);

        public static WebstoreBaseOutputDto<T> Error(string code, string message) =>
            new WebstoreBaseOutputDto<T>(code, message);
    }

    public class WebstoreExternalSmeCheckInput
    {
        public string SmeCode { get; set; }
        public string LicenseCode { get; set; }
        public string RepresentativePhoneNumber { get; set; }
    }

    public class WebstoreExternalSmeCheckOutput
    {
        public string Code { get; set; }
        public string Message { get; set; }
    }

    public class SMELoginRequest
    {
        public string SMEType { get; set; } // VPB or LYNKID
        public string SmeLicenseCode { get; set; }
        public string RepPhone { get; set; }
        public string Password { get; set; }
        public string CheckMode { get; set; } = "Simple"; // "Simple" and "CheckFullForRegister"
    }

    public class IndividualRegisterRequest
    {
        public string PhoneNumber { get; set; }
        public string CaptchaKey { get; set; }
        public string CaptchaAnswer { get; set; }

        public IndividualRegisterRequest(string phoneNumber, string captchaKey = null, string captchaAnswer = null)
        {
            PhoneNumber = phoneNumber;
            CaptchaKey = captchaKey;
            CaptchaAnswer = captchaAnswer;
        }
    }

    public class ValidateOtpRegisterSMERequest
    {
        public string SMEType { get; set; } // VPB or LYNKID
        public string OtpNumber { get; set; }
        public string SessionId { get; set; }
    }

    public class ValidateOtpRegisterSMEOutput
    {
        public string ActionKey { get; set; }
    }
    public class RequestOtpRegisterSMERequest
    {
        public string SMEType { get; set; } // VPB or LYNKID
        public string SMELicenseCode { get; set; }
        public string SessionId { get; set; }
        public string RepresentativePhoneNumber { get; set; }
    }

    public class RequestOtpRegisterSMEOutput
    {
        public bool IsOtpSent { get; set; }
    }

    public class LoginResponse
    {
        public string AccessToken { get; set; }
        public string RefreshToken { get; set; }
        public string MemberCode { get; set; }
        public string MemberType { get; set; }
        public DateTime ExpiresAt { get; set; }
    }
    public class RefreshTokenRes
    {
        public string AccessToken { get; set; }
        public string RefreshToken { get; set; }
        public string MemberCode { get; set; }
        public string MemberType { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    public class WebstoreErrorResponse
    {
        public string Code { get; set; }
        public string Message { get; set; }

        public WebstoreErrorResponse()
        {
        }

        public WebstoreErrorResponse(string code, string msg)
        {
            Code = code;
            Message = msg;
        }
    }

    public class SMEInfoResponse
    {
        public string Message { get; set; }
        public string SMECode { get; set; }
        public string RepresentativePhoneNumber { get; set; }
        public bool HasPassword { get; set; }
        public bool IsConnected { get; set; }
    }

    public class ValidatePasswordResponse
    {
        public bool IsValid { get; set; }
    }

    public class WebstoreRefreshTokenRequest
    {
        public string MemberCode { get; set; }
        public string RefreshToken { get; set; }
    }

    public class WebstoreRefreshTokenResponse
    {
        public int Result { get; set; }
        public WebstoreRefreshTokenResponseItem Item { get; set; }
        public string Message { get; set; }
        public string MessageDetail { get; set; }
    }
    public class WebstoreRefreshTokenResponseItem
    {
        public string AccessToken { get; set; }
        public string RefreshToken { get; set; }
        public string MemberCode { get; set; }
        public string MemberType { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    public class GetTransactionHistoryInput
    {
        [Required]
        public string MemberCode { get; set; }
        public string MemberType { get; set; }
        public string UserAddress { get; set; }
        public string OrderCode { get; set; }
        public int? MaxResultCount { get; set; }
        public int? SkipCount { get; set; }
        public DateTime? FromDateFilter { get; set; }
        public DateTime? ToDateFilter { get; set; }
        public string TransactionType { get; set; }
    }
    public class GetAllTokenTransInput
    {
        public string NationalId { get; set; }
        public string MemberType { get; set; }
        public int? MemberId { get; set; }
        public int? MerchantIdFilter { get; set; }
        public string PhoneNumber { get; set; }
        public string FromDateFilter { get; set; }
        public string ToDateFilter { get; set; }
        public string OrderCodeFilter { get; set; }
        public string ActionCodeFilter { get; set; }
        public string ActionTypeFilter { get; set; }
        public string DateTypeFilter { get; set; }
        public string Sorting { get; set; }
        public int maxResultCount { get; set; }
        public int skipCount { get; set; }
    }
    public class GetTransactionDto
    {
        public string TokenTransID { get; set; }
        public int MemberId { get; set; }
        public string ActionCode { get; set; }
        public string OrderCode { get; set; }

        public DateTime? Time { get; set; }
        public DateTime? BusinessTime { get; set; }
        public DateTime? ValidDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string OriginalTokenTransId { get; set; }
        public string ActionCodeDetail { get; set; }
        public string ActionName { get; set; }
        public string FromWalletAddress { get; set; }
        public string FromMerchantNameOrMember { get; set; }
        public string ToWalletAddress { get; set; }
        public string ToMerchantNameOrMember { get; set; }
        public int? StoreId { get; set; }
        public string StoreName { get; set; }
        public string NationalId { get; set; }
        public string UserAddress { get; set; }
        public string ActionType { get; set; }
        public decimal TokenAmount { get; set; }
        public decimal PartnerPointAmount { get; set; }
        public decimal PointExchangeRate { get; set; }
        public decimal CurrencyExchangeRate { get; set; }
        public decimal BaseUnit { get; set; }
        public string GrantType { get; set; }
        public string UsagePriority { get; set; }
        public string Reason { get; set; }
        public string MemberName { get; set; }
        public string PartnerPhone { get; set; }
        public string IDcard { get; set; }
        public int? MerchantId { get; set; }
        public string PhoneNumber { get; set; }
        public string BindingTxId { get; set; }
        public string PartnerBindingTxId { get; set; }
        public string MessagePhotoLink { get; set; }
        public string MessageTitle { get; set; }
        public string MerchantName { get; set; }
        public string MerchantLogo { get; set; }
        public List<RewardMemberTokenTransGetByIdItems> AdjustTransactionList { get; set; }

    }

    public class GetAllTokenTransRespone
    {
        public int result { get; set; }
        public int totalCount { get; set; }
        public string message { get; set; }
        public List<GetTransactionDto> items { get; set; }
    }
    public class WebstoreGetNotificationByMemberInput
    {
        public string MemberCode { get; set; }
        public string NotiCate { get; set; }
        public string Lang { get; set; }
    }

    public class CheckSmeLoginInfoOutput
    {
        public bool IsExisting { get; set; }
        public bool HasPassword { get; set; }
        public string WalletAddress { get; set; }
        public string MemberCode { get; set; }
        public string Name { get; set; }
    }

    public class WebstoreCheckSmeLoginInfoOutput
    {
        public CheckDataSMEResponse SmeLoginInfo { get; set; }
    }
    public class WebstoreGetNotificationByMemberOutput
    {
        public int Count { get; set; }
        public List<NotificationHistoryDtoV2> NotificationHistoryList { get; set; }
    }

    public class WebStoreVerifyPassWordInput
    {
        public string MemberCode { get; set; }

        public string PassWord { get; set; }
        public string ActionType { get; set; }
        public string SmeLicenseCode { get; set; }
        public string RepPhone { get; set; }
    }

    public class WebStoreVerifyPassWordOutput
    {
        public string ActionKey { get; set; }
    }
    public class WebStoreVerifyOtpOutput
    {
        public string ActionKey { get; set; }
    }

    public class SmeVerifyOtpChangePasswordRequest
    {
        public string SessionId { get; set; }
        public string OtpCode { get; set; }
    }
    public class SmeRequestOtpChangePasswordRequest
    {
        public string LicenseCode { get; set; }
        public string RepPhone { get; set; }
        public string SessionId { get; set; }
    }

    public class SmeForCacheChangePasswordDTO
    {
        public string SmeCode { get; set; }
        public string RepPhone { get; set; }
    }
    public class SmeRequestOtpChangePasswordResponse
    {

    }

    public class CheckSmePasswordRewardOutput
    {
        public int Result { get; set; }
        public CheckSmePasswordRewardOutputItem Item { get; set; }
        public string Message { get; set; }
        public string MessageDetail { get; set; }
    }

    public class CheckSmePasswordRewardOutputItem
    {
        public bool isValid { get; set; }
    }
    public class CheckSmePasswordRewardInput
    {
        public string RepPhone { get; set; }
        public string SmeLicenseCode { get; set; }
        public string Password { get; set; }
    }
    public class SmeChangePasswordAfterVerificationOutput { }

    public class SmeSetPasswordAfterOTPRegiseRequest
    {
        public string SessionId { get; set; }
        public string Password { get; set; }
        public string ActionKey { get; set; }
    }
    public class SmeChangePasswordAfterVerificationRequest
    {
        public string SessionId { get; set; }
        public string NewPassword { get; set; }
        public string ActionKey { get; set; }
    }
}