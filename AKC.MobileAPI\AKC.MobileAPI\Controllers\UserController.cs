﻿using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Abstract;
using Microsoft.AspNetCore.Mvc;
using System;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using AKC.MobileAPI.Service.Exceptions;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    public class UserController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IUserService _userService;
        private readonly IExceptionReponseService _exceptionReponseService;
        public UserController(ILogger<UserController> logger, IUserService userService,
            IExceptionReponseService exceptionReponseService)
        {
            _userService = userService;
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
        }

        [HttpPost]
        public async Task<ActionResult<ValidateUserOutput>> IsExisted(ValidateGetUserInput model)
        {
            try
            {
                return await _userService.IsExisted(model);
            } catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "IsExisted error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpPost]
        public async Task<ActionResult<GetOTPOutput>> GetOTP(ValidateUserInput model)
        {
            try
            {
                return await _userService.GetOTP(model);
            } catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                _logger.LogError(ex, "Get Otp error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpPost]
        public async Task<ActionResult<VerifyOTPOutput>> VerifyOTPAndLogin(SendOTPUserInput input)
        {
            try
            {
                var result = await _userService.VerifyOTPAndLogin(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                _logger.LogError(ex, "Verify Otp and login error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        [HttpPost]
        public async Task<ActionResult<VerifyOTPOutput>> VerifyOTPAndRegister(CreateUserMobileDTO input)
        {
            try
            {
                var result = await _userService.VerifyOTPAndRegister(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionWithDataRewardReponse(ex);
                _logger.LogError(ex, "Verify Otp and register error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }
    }
}
