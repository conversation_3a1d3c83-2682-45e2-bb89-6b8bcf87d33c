﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
	public class GetGiftCategoryAndInfoForView
	{
		public ListResultGetGiftCategoryAndInfoForView Result { get; set; }
		public string TargetUrl { get; set; }
		public bool Success { get; set; }
		public string Error { get; set; }
		public bool UnAuthorizedRequest { get; set; }
		public bool __abp { get; set; }
	}
	public class ListResultGetGiftCategoryAndInfoForView
	{
		public int TotalCount { get; set; }

		public List<ListResultGetGiftCategoryAndInfoForViewIteams> Items { get; set; }
	}

	public class ListResultGetGiftCategoryAndInfoForViewIteams
	{
		public GiftCategoryAndInfoDto GiftCategory { get; set; }
		public List<ListGetGiftCategoryAndInfoForView> GiftInfors
		{
			get; set;
		}
		public int TotalCountGiftInfors { get; set; }
	}
	public class ListGetGiftCategoryAndInfoForView
	{
		public GiftShortInforDto GiftInfor { get; set; }

		public List<ImageLinkDto> ImageLink { get; set; }

		public List<GiftShortInforForView> RelatedGiftInfor { get; set; }

	}
	public class GiftCategoryAndInfoDto
	{
		public string Code { get; set; }

		public string Name { get; set; }

		public string Description { get; set; }

		public string Status { get; set; }

		public int Level { get; set; }

		public string ParentCode { get; set; }


		public int? ParentId { get; set; }

		public ImageLinkDto ImageLink
		{
			get; set;
		}
	}
}
