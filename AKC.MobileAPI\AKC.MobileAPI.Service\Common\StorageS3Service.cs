﻿using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Common
{
    public class StorageS3Service : IStorageS3Service
    {
        private string bucketName = "linkid-uat";
        //private string keyName = "upload-member-exchange/member.txt";
        private string accessKey = "********************";
        private string secretKey = "MZa0GWH7EPZ/QdsbXl2tt1Qp4UBVwBy31VSsXWsI";
        // Specify your bucket region (an example region is shown).
        private static readonly RegionEndpoint bucketRegion = RegionEndpoint.APSoutheast1;
        private readonly IConfiguration _configuration;
        private readonly AmazonS3Client client;

        public StorageS3Service(IConfiguration configuration)
        {
            _configuration = configuration;
            bucketName = _configuration.GetSection("StoreFiles:AWSS3:BucketName").Value;
            //keyName = _configuration.GetSection("StoreFiles:AWSS3:KeyName").Value;
            accessKey = _configuration.GetSection("StoreFiles:AWSS3:AccessKey").Value;
            secretKey = _configuration.GetSection("StoreFiles:AWSS3:SecretKey").Value;

            var config = new AmazonS3Config();
            config.RegionEndpoint = bucketRegion;
            client = new AmazonS3Client(accessKey, secretKey, config);
            _configuration = configuration;
        }

        public async Task<string> DownloadFileAndGetData(string keyName)
        {
            var responseBody = "";
            try
            {
                GetObjectRequest request = new GetObjectRequest
                {
                    BucketName = bucketName,
                    Key = keyName
                };
                using (GetObjectResponse response = await client.GetObjectAsync(request))
                {
                    using (Stream responseStream = response.ResponseStream)
                    {
                        using (StreamReader reader = new StreamReader(responseStream))
                        {
                            string title = response.Metadata["x-amz-meta-title"]; // Assume you have "title" as medata added to the object.
                            string contentType = response.Headers["Content-Type"];

                            responseBody = reader.ReadToEnd(); // Now you process the response body.
                        }
                    }
                }
            }
            catch (AmazonS3Exception e)
            {
                // If bucket or object does not exist
                Console.WriteLine("Error encountered ***. Message:'{0}' when reading object", e.Message);
            }
            catch (Exception e)
            {
                Console.WriteLine("Unknown encountered on server. Message:'{0}' when reading object", e.Message);
            }

            return responseBody;
        }
    }
}
