﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Payment
{
    public class GetDataFromURLReturnUrlOutputDto
    {
        public int Result { get; set; }
        public GetDataFromURLReturnUrlItem Item { get; set; }
        public bool IsSuccessPayment { get; set; }
        public bool IsBackQuery { get; set; }
        public string ErrorCode { get; set; }
    }

    public class GetDataFromURLReturnUrlItem
    {
        public string MemberCode { get; set; }
        public string TokenTransactionId { get; set; }
        public string PaymentTransactionId { get; set; }
        public decimal TokenAmount { get; set; }
        public decimal MoneyAmount { get; set; }
    }
}
