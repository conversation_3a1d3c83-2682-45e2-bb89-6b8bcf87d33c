﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using Newtonsoft.Json;
using RabbitMQ.Client;
using System;
using System.Text;

namespace AKC.RabbitMQ
{
    public class RabbitMQManager: IRabbitMQManager
    {
        private readonly DefaultObjectPool<IModel> _objectPool;
        private readonly RabbitOptions _optionsAccs;
        private readonly ILogger<RabbitMQManager> _logger;

        public RabbitMQManager(IPooledObjectPolicy<IModel> objectPolicy, RabbitOptions optionsAccs, ILogger<RabbitMQManager> logger)
        {
            //Logger = NullLogger.Instance;
            _objectPool = new DefaultObjectPool<IModel>(objectPolicy, Environment.ProcessorCount * 2);
            _optionsAccs = optionsAccs;
            _logger = logger;
        }

        public void Publish<T>(T message, string exchangeName, string exchangeType, string routeKey) where T : class
        {
            if (message == null)
                return;

            var channel = _objectPool.Get();

            try
            {
                var queueName = string.Format(_optionsAccs.QueueNameFormat, routeKey);
                channel.ExchangeDeclare(exchangeName, exchangeType, durable: true, autoDelete: false);
                channel.QueueDeclare(queueName, true, false, false);
                channel.QueueBind(queueName, exchangeName, routeKey);

                var sendBytes = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(message));

                var properties = channel.CreateBasicProperties();
                properties.Persistent = true;

                channel.BasicPublish(exchangeName, routeKey, properties, sendBytes);
            }
            catch (Exception ex)
            {
                var queueMessage = typeof(T) == typeof(string) ? message.ToString() : JsonConvert.SerializeObject(message);
                _logger.LogError($"Error: {ex.Message}: \r\n- SendMessage: {queueMessage}", ex);
            }
            finally
            {
                _objectPool.Return(channel);
            }
        }
    }
}
