﻿using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.Loyalty
{
    public class GetMemberByCifInput
    {
        public string CIF { get; set; }
        public string MemberCode { get; set; }
    }

    public class GetListRegistrationByMemberCodeInput
    {
        public string MemberCode { get; set; }
    }

    public class MemberRejectCampaignOutput
    {
        public string Message { get; set; }
        public bool Status { get; set; }
    }
    public class GetListRegistrationByMemberCodeOutput
    {
        public GetListRegistrationByMemberCodeInner Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class GetListRegistrationByMemberCodeInner
    {
        public List<CreateOrEditMemberCampaignGiveCoinDto> RegList { get; set; }
    }

    public class MemberRejectCampaignInput
    {
        public string MemberCode { get; set; }
        public string CampaignCode { get; set; }
    }
}
