﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberHasPinCodeRequest
    {
        public string PhoneNumber { get; set; }
        public string DeviceId { get; set; }
    }
    public class RewardMemberSaveRefreshTokenInputV2
    {
        public string MemberCode { get; set; }

        public int MerchantId { get; set; }

        public string RefreshToken { get; set; }
        public bool IsChangedLoyalty { get; set; }
        public string ReferenceData { get; set; }
        public string ConnectSource { get; set; }
        public MemberConnectLoyaltyInfoInput MemberLoyaltyInfo { get; set; }
    }
}
