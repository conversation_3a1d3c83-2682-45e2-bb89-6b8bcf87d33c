﻿using AKC.MobileAPI.DTO.Loyalty.ThirdPartyBrandMapping;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface IThirdPartyBrandMappingService
    {
        Task<GetAllThirdPartyBrandMappingByVendorNameOutput> GetAllThirdPartyBrandByVendorName(GetAllThirdPartyBrandMappingByVendorNameInput input);
        Task<GetAllThirdPartyBrandMappingByVendorNameOutput> GetAllThirdPartyBrandByVendorType(GetAllThirdPartyBrandMappingByVendorTypeInput input);
    }
}
