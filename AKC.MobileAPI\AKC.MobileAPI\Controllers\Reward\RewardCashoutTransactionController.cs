﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Reward.CashoutTransaction;
using AKC.MobileAPI.DTO.Reward.TopUpTransaction;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Reward
{
    [Route("api/Cashout")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class RewardCashoutTransactionController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IRewardCashoutTransactionService _rewardCashoutTransactionService;
        private readonly ICommonHelperService _commonHelperService;
        public RewardCashoutTransactionController(
            ILogger<RewardCashoutTransactionController> logger,
            IExceptionReponseService exceptionReponseService,
            IRewardCashoutTransactionService rewardCashoutTransactionService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _rewardCashoutTransactionService = rewardCashoutTransactionService;
            _commonHelperService = commonHelperService;
        }

        [HttpPost]
        [Route("Create")]
        private async Task<ActionResult<RewardCreateCashoutTransactionOutput>> CreateCashoutTransaction(RewardCreateCashoutTransactionInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                _logger.LogInformation("cashout request " + input.MemberCode + "_" + JsonConvert.SerializeObject(input));
                var result = await _rewardCashoutTransactionService.CreateCashoutTransaction(input);
                _logger.LogInformation("cashout response result " + input.MemberCode + "_" + JsonConvert.SerializeObject(result));
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "cashout response error " + input.MemberCode + "_" + JsonConvert.SerializeObject(ex));
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("Revert")]
        private async Task<ActionResult<RewardRevertCashoutTransactionOutput>> RevertCashoutTransaction(RewardRevertCashoutTransactionInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                _logger.LogInformation("revert cashout request " + input.MemberCode + "_" + JsonConvert.SerializeObject(input));
                var result = await _rewardCashoutTransactionService.RevertCashoutTransaction(input);
                _logger.LogInformation("revert cashout response result " + input.MemberCode + "_" + JsonConvert.SerializeObject(result));
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "revert cashout response error " + input.MemberCode + "_" + JsonConvert.SerializeObject(ex));
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                return StatusCode(400, res);
            }
        }
    }
}
