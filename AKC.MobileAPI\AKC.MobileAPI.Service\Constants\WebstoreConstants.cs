namespace AKC.MobileAPI.Service.Constants
{
    public class WebstoreConstants
    {
        public static class LoginErrorCodes
        {
            public const string LOGIN_INVALID_CREDENTIALS = "L001";
            public const string LOGIN_REGISTRATIONREQUIRED = "L002";
            public const string LOGIN_REP_PHONE_NOT_MATCH = "L003";
            public const string LOGIN_ISSUE_IN3RDPARTY = "L004";
            public const string LOGIN_ISSUE_IN3RDPARTY_TooQuick = "L005";
            public const string LOGIN_AccountTemporarilyLocked = "L006";
            public const string REGISTER_AccountAlreadyExist = "L007";
            
            public const string REG_INFO_REQUIRED = "R001";
            public const string REG_INFO_EXIST__SAMEREPPHONE = "R002";
            public const string REG_INFO_EXIST__DIFFREPPHONE = "R003";
            
            public const string COMMON_MemberCode_NotMatch_Token = "C001";
            public const string COMMON_MemberCode_NotFound = "C002";
            public const string COMMON_SESSIONOTP_EXPIRED = "C003";
            public const string COMMON_INVALIDINPUT = "C004";
            public const string COMMON_NEWPASSWORD_INVALID = "C005";
            public const string COMMON_NEWPASSWORD_INVALID_FORMAT = "C006";
            public const string COMMON_INCORRECT_OLD_PASSWORD = "C007";
            public const string COMMON_TOO_MANY_ATTEMPTS_WRONGPASSWORD = "C008";
            public const string COMMON_WRONGACTIONKEY= "C009"; // Các action cần action key (action key đại diện cho 1 lần xác nhận password or otp) thì cần action key valid
            public const string COMMON_SessionId_Duplicated = "C010";
            public const string COMMON_LicenseCodeAlreadyHasAccount= "C011";
            public const string COMMON_PhonePlus84Required= "C012";
            public const string COMMON_InvalidOTP = "C013";
            public const string COMMON_PhoneBlockedFromOtp = "C014";
            public const string COMMON_PhoneReachMaxOtpAttempts = "C015";
            public const string COMMON_TooManyWrongOtpAttempts = "C016";
            public const string COMMON_NEW_AND_OLD_SAME = "C017";
            public const string COMMON_LICENSECODE_WITH_DIFF_PHONE_ALREADYEXISTS = "C018";
            public const string COMMON_CREDITBALANCEPOSITIVE = "C019";
            
            public const string INVALID_REFRESH_TOKEN = "E001";
            public const string REFRESHTOKEN_MEMBER_NOTACTIVEORNOTEXIST = "E002";
            public const string INTERNALSERVERERROR = "E500";
        }

        public static class RedeemGiftErrorCodes
        {
            public const string REDEEM_INTERNAL_SERVER = "R999";
            public const string REDEEM_INVALID_GIFTCODE = "R001";
            public const string REDEEM_INVALID_TOTALAMOUNT = "R002";
            public const string REDEEM_INVALID_QUANTITY = "R003";
            public const string REDEEM_INVALID_SESSIONID = "R004";
            public const string REDEEM_BALANCE_INSUFFICIENT = "R005";
            public const string REDEEM_CANT_REDEEM_THIS_TIME = "R006";
            public const string REQUEST_REDEEM_INVALID = "R007";
            public const string REQUEST_OTP_INVALID = "R008";
            public const string REQUEST_ACCESSTOKEN_INVALID = "R009";
            public const string REQUEST_TIMEOUT_API = "R010";
            public const string REDEEM_DUPLICATE_SESSIONID = "R011";
            public const string INVALID_PHONENUMBER = "R012";
        }
    }
}