﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.FAQ
{
    public class GetAllFaqAdvanceOutput
    {
        public long Id { get; set; }
        public string Code { get; set; }

        public string Question { get; set; }

        public string Answer { get; set; }

        public int? Ordinal { get; set; }

        public int Level { get; set; }
        public bool IsFaq { get; set; }
        public string ParentCode { get; set; }
        public string Status { get; set; }
        public string IconLink { get; set; }
        public int? TenantId { get; set; }
        public long? LastModifierUserId { get; set; }
        public string LastModifierUserName { get; set; }

        public DateTime? LastModificationTime { get; set; }
        public List<GetAllFaqAdvanceOutput> items { get; set; } = new List<GetAllFaqAdvanceOutput>();
    }

    public class GetAllFaqAdvanceOutputWrapperDTO
    {
        public GetAllFaqAdvanceOutputWrapper Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class GetAllFaqAdvanceOutputWrapper
    {
        public string Code { get; set; }
        public List<GetAllFaqAdvanceOutput> items { get; set; } = new List<GetAllFaqAdvanceOutput>();
    }
}
