﻿using AKC.MobileAPI.DTO.Loyalty.AuditLog;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyAuditLogService : BaseLoyaltyService, ILoyaltyAuditLogService
    {
        private readonly IDistributedCache _cache;
        private readonly TimeSpan _failedAttemptsWindow;
        private readonly int _maxFailedAttempts; 
        private readonly TimeSpan _requestWindow;
        private readonly int _maxRequestsPerMinute;

        public LoyaltyAuditLogService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
            _cache = cache;
            _failedAttemptsWindow = TimeSpan.FromMinutes(15);
            _maxFailedAttempts = 5;
            _requestWindow = TimeSpan.FromMinutes(1); // Fixed 1-minute window
            _maxRequestsPerMinute = 10;
        }
        public async Task<CreateOperationLogOutput> CreateOperationLog(CreateOperationLogDto input)
        {
            return await PostLoyaltyAsync<CreateOperationLogOutput>(LoyaltyApiUrl.CREATE_OPERATION_LOG, input);
        }

        public async Task<bool> IsSuspicious(string ip, string path)
        {
            try
            {
                // 1. Check failed login attempts from IP
                string failedAttemptsKey = $"webstore:failed_attempts:{ip}";
                int failedAttempts = await GetAndIncrementCounter(failedAttemptsKey, _failedAttemptsWindow);
                if (failedAttempts >= _maxFailedAttempts)
                {
                    await LogSuspiciousAccess(ip, path);
                    return true;
                }

                // 2. Check request frequency from IP
                string requestCountKey = $"webstore:request_count:{ip}:{DateTime.UtcNow:yyyyMMddHHmm}";
                int requestCount = await GetAndIncrementCounter(requestCountKey, _requestWindow);
                if (requestCount >= _maxRequestsPerMinute)
                {
                    await LogSuspiciousAccess(ip, path);
                    return true;
                }

                // 3. Check IP blacklist
                string blacklistKey = "webstore:blacklisted_ips";
                string blacklistJson = await _cache.GetStringAsync(blacklistKey);
                var blacklistedIps = string.IsNullOrEmpty(blacklistJson)
                    ? new string[0]
                    : JsonConvert.DeserializeObject<string[]>(blacklistJson);
                if (Array.Exists(blacklistedIps, ip => ip == ip))
                {
                    await LogSuspiciousAccess(ip, path);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                throw new Exception($" >> Error checking suspicious access for IP={ip}, Path={path}: {ex.Message}", ex);
            }
        }
        private async Task<int> GetAndIncrementCounter(string key, TimeSpan expiry)
        {
            var value = await _cache.GetStringAsync(key);
            var count = string.IsNullOrEmpty(value) ? 0 : int.Parse(value);
            count++;
            await _cache.SetStringAsync(key, count.ToString(), new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiry
            });
            return count;
        }

        private Task LogSuspiciousAccess(string ip, string path)
        {
            // CreateOperationLog();
            return Task.CompletedTask;
        }
    }
}

