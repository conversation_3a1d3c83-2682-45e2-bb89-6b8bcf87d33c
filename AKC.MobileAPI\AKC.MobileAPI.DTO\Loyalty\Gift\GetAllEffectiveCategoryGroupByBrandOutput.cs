﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class GetAllEffectiveCategoryGroupByBrandOutput
    {
        public ListResultGetAllEffectiveCategoryGroupByBrandOutput Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }
    public class ListResultGetAllEffectiveCategoryGroupByBrandOutput
    {
        public int TotalCount { get; set; }

        public List<GetGiftInforByGiftCategoryGroupByBrandForView> Items { get; set; }
    }
    public class GetGiftInforByGiftCategoryGroupByBrandForView
    {
        public BrandGiftDataInfoDto BrandGiftInfo { get; set; }
        public List<GiftShortInforAndImageDto> GiftInforAndImage { get; set; }
        public int TotalGift { get; set; }
    }

    public class GiftShortInforAndImageDto
    {
        public GiftShortInforDto GiftInfor { get; set; }
        public List<ImageLinkDto> ImageLink { get; set; }
        public FlashSaleProgramDto FlashSaleProgramInfor { get; set; }
        public GiftDiscountDto GiftDiscountInfor { get; set; }
    }
    public class BrandGiftDataInfoDto
    {
        public int? BrandId { get; set; }
        public string BrandName { get; set; }
        public string BrandImage { get; set; }
    }
}
