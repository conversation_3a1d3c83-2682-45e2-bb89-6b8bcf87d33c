﻿using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty.Gift
{
    public class LoyaltySearchBarService : BaseLoyaltyService, ILoyaltySearchBarService
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionResponseService;
        public LoyaltySearchBarService(IConfiguration configuration, IDistributedCache cache,
            ILogger<LoyaltySearchBarService> logger,
            IExceptionReponseService exceptionResponseService) : base(configuration, cache)
        {
            _logger = logger;
            _exceptionResponseService = exceptionResponseService;
        }

        public async Task<LoyaltyGetAllForSearchBarOutPut> GetAll(LoyaltyGetAllForSearchBarInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGetAllForSearchBarOutPut>(LoyaltyApiUrl.GETALL_SEARCH_BAR, input);
        }
    }
}
