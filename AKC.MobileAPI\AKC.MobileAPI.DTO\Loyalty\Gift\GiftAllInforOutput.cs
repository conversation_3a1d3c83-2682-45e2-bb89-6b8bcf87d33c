﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class GiftAllInforOutput
    {
        public GiftAllInforForView Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class GiftAllInforForView
    {
        public int TotalCount { get; set; }
        public List<GiftInforsOutPutForMB> Items { get; set; }

    }
    public class GiftInforsOutPutForMB
    {
        public GiftGroupForView GiftGroup { get; set; }
        public List<GiftsForView> Gifts { get; set; }
        public int? NumberOfGifts { get; set; }
    }
    public class GiftGroupForView
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public int? DisplayOrder { get; set; }
    }

    public class GiftsForView
    {
        public GiftInfoForView GiftInfo { get; set; }
        public GiftDiscountForView GiftDiscount { get; set; }
        public string FullLink { get; set; }
    }

    public class GiftDiscountForView
    {
        public string FlashSaleProgramName { get; set; }
        public decimal? SalePrice { get; set; }

        public int? RemainingQuantityFlashSale { get; set; }

        public float? ReductionRate { get; set; }

        public bool? WarningOutOfStock { get; set; }
        public int? UsedQuantityFlashSale { get; set; }

        public string Status { get; set; }
        public int? RedeemGiftQuantity { get; set; }
    }

    public class GiftInfoForView
    {
        public long Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal? FullPrice { get; set; }
        public decimal? RequiredCoin { get; set; }
        public decimal? DiscountPrice { get; set; }
        public int? TotalWish { get; set; }
        public int? DisplayOrder { get; set; }
    }
}
