﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.MemberOtp
{
    public class RewardMemberUpdatePinCodeForgetInput
    {
        [JsonProperty(PropertyName = "sessionId")]
        public string SessionId { get; set; }

        [JsonProperty(PropertyName = "firebaseId")]
        public string FirebaseId { get; set; }

        [JsonProperty(PropertyName = "memberCode")]
        public string MemberCode { get; set; }

        [JsonProperty(PropertyName = "checksum")]
        public string Checksum { get; set; }
    }

    public class RewardMemberUpdatePinCodeForgetChecksum
    {
        [JsonProperty(PropertyName = "sessionId")]
        public string SessionId { get; set; }

        [JsonProperty(PropertyName = "firebaseId")]
        public string FirebaseId { get; set; }

        [JsonProperty(PropertyName = "memberCode")]
        public string MemberCode { get; set; }
    }
}
