﻿using AKC.MobileAPI.DTO.LoyaltyVpbank.Member;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.LoyaltyVpbank
{
    public interface ILoyaltyMemberService
    {
        Task<GetMemberVpbankInforOutput> GetMemberVpbankInfor(GetMemberVpbankInforInput input);
        Task<VPBankCustomerInfoOutput> GetVPBankCustomerInfo(VPBankCustomerInfoInput input);
        Task<object> RemoveConnection(int linkidMemberId);
    }
}
