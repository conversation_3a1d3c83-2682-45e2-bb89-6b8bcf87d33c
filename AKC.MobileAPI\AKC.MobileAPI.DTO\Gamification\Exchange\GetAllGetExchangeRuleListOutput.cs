﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Gamification.Exchange
{
    public class GetAllGetExchangeRuleListOutput
    {
        public int result { get; set; }
        public List<GetAllGetExchangeRuleListDto> item { get; set; }
        public int totalCount { get; set; }
        public string message { get; set; }
        public string messageDetail { get; set; }
    }

    public class GetAllGetExchangeRuleListDto
    {
        public int? ID { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int? GamemasterID { get; set; }
        public int? GameID { get; set; }
        public int? CreatorUserID { get; set; }
        public DateTime? CreateDate { get; set; }
        public int? LastModifierUserID { get; set; }
        public DateTime? LastModificationDate { get; set; }
        public int? DeletorUserID { get; set; }
        public DateTime? DeletionTime { get; set; }
    }
}
