﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{

    public class RemoveConnectInput
    {
        public int? LinkID_MemberID { get; set; }
        public string LinkID_WalletAddress { get; set; }

        public string MemberCode { get; set; }
    }

    public class RemoveConnectRespone
    {
        public bool isSuccess { get; set; } = false;
        public int affectedRow { get; set; } = 0;

        public string Message { get; set; } = string.Empty;
    }

}
