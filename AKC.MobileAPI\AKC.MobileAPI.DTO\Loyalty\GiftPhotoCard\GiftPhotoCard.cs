﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftPhotCard
{

    public class SearchGiftPhotoCardRequest
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public int? Ordinal { get; set; }
        public string Status { get; set; }

        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
    }

    public class GiftPhotoCardDTO
    {
        public int? Id { get; set; }
        public string GiftPhotoCardCode { get; set; }
        public string Name { get; set; }
        public int? Ordinal { get; set; }
        public string Status { get; set; }
        public long? CreatorUserId { get; set; }
        public DateTime CreationTime { get; set; }
        public string CreatorUserName { get; set; }
        public long? LastModifierUserId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string LastModifierUserName { get; set; }

        public List<GiftPhotoLinkDTO> ImageLinks { get; set; } = new List<GiftPhotoLinkDTO>();

        public List<GiftPhotoCardSuggestionMessageGetByCardCode> GiftPhotoCardSuggestionMessages { get; set; } = new List<GiftPhotoCardSuggestionMessageGetByCardCode>();
    }

    public class GiftPhotoLinkDTO
    {
        public string Code { get; set; }
        public string Link { get; set; }
    }

    public class GiftPhotoCardSuggestionMessageGetByCardCode
    {
        public string Code { get; set; }
        public string Content { get; set; }
    }

    public class LoyaltyGiftPhotoCardGetAllOutput
    {
        public LoyaltyGiftPhotoCardGetAll Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class LoyaltyGiftPhotoCardGetAll
    {
        public int TotalCount { get; set; }
        public List<GiftPhotoCardDTO> Items { get; set; }
    }

}
