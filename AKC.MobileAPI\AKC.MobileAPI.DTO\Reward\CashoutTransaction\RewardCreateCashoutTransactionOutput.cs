﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.CashoutTransaction
{
    public class RewardCreateCashoutTransactionOutput
    {
        public int Result { get; set; }
        public RewardCreateCashoutTransactionResult Items { get; set; }
        public string Message { get; set; }
        public string MessageDetail { get; set; }
    }

    public class RewardCreateCashoutTransactionResult
    {
        public int MemberId { get; set; }
        public string MemberCode { get; set; }
        public string TokenTransactionId { get; set; }
        public decimal TokenAmount { get; set; }
        public decimal FeeAmount { get; set; }
        public decimal MoneyAmount { get; set; }
    }

    public class RewardCreateCashoutTransactionOutputDto
    {
        public int Result { get; set; }
        public RewardCreateCashoutTransactionResultDto Items { get; set; }
        public string Message { get; set; }
        public string MessageDetail { get; set; }
    }

    public class RewardCreateCashoutTransactionResultDto
    {
        public int MemberId { get; set; }
        public string NationalId { get; set; }
        public decimal TokenAmount { get; set; }
        public decimal MoneyAmount { get; set; }
        public decimal FeeAmount { get; set; }
        public string TokenTransactionId { get; set; }
    }
}
