﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/theme")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class ThemeController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltyThemeService _loyaltyService;
        private readonly ICommonHelperService _commonHelperService;
        public ThemeController(
        ILogger<ThemeController> logger,
        IExceptionReponseService exceptionReponseService,
        ILoyaltyThemeService loyaltyAdjustService,
        ICommonHelperService commonHelperService
        )
        {
            _loyaltyService = loyaltyAdjustService;
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
        }

        [HttpGet]
        [Route("get-current-theme")]
        [AllowAnonymous]
        public async Task<ActionResult<ThemeSettingsInfoDtoWrapper>> GetCurrentTheme()
        {
            try
            {
                var result = await _loyaltyService.GetCurrentTheme(new GetCurrentThemeInput());
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "UpdateMemberBalance Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

    }
}
