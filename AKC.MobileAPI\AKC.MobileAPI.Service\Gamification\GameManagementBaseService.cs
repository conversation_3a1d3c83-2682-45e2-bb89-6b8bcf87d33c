﻿using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.Gamification
{
    public class GameManagementBaseService
    {
        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        protected string baseURL;
        protected int tenantId;
        protected readonly IDistributedCache _cache;
        public GameManagementBaseService(IConfiguration configuration, IDistributedCache cache)
        {
            _configuration = configuration;
            baseURL = _configuration.GetSection("Gamifications:GameManagement:RemoteURL").Value;
            tenantId = Convert.ToInt32(_configuration.GetSection("Loyalty:TenantId").Value);
            _cache = cache;
        }

        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetGamificationManagementAsync<T>(string apiURL, object query = null, string rewardType = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };

            req.RequestUri = new Uri(requestURL);

            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();
            if (response.IsSuccessStatusCode == false)
            {
                var ex = new GamificationException();
                ex.Data.Add("ErrorData", rawData);
                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    ex.Data.Add("StatusCode", 400);
                }
                else
                {
                    ex.Data.Add("StatusCode", 500);
                }
                throw ex;
            }
            response.EnsureSuccessStatusCode();

            // Get respone result.
            var result = JsonConvert.DeserializeObject<T>(rawData);

            return result;
        }


        public async Task<T> PostGamificationManagementAsync<T>(string apiURL, object body)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            var data = new StringContent(JsonConvert.SerializeObject(body), Encoding.UTF8, "application/json");

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post,
                Content = data
            };

            req.RequestUri = new Uri(requestURL);

            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();
            if (response.IsSuccessStatusCode == false)
            {
                var ex = new GamificationException();
                ex.Data.Add("ErrorData", rawData);
                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    ex.Data.Add("StatusCode", 400);
                } 
                else if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    ex.Data.Add("StatusCode", 404);
                }
                else
                {
                    ex.Data.Add("StatusCode", 500);
                }
                throw ex;
            }
            response.EnsureSuccessStatusCode();

            // Get respone result.
            var result = JsonConvert.DeserializeObject<T>(rawData);

            return result;
        }


        /// <summary>
        /// Convert a object to query string format.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string GetQueryString(object obj)
        {
            var properties = from p in obj.GetType().GetProperties()
                             where p.GetValue(obj, null) != null
                             select p.Name + "=" + HttpUtility.UrlEncode(p.GetValue(obj, null).ToString());

            return string.Join("&", properties.ToArray());
        }

        public async Task<string> GetCacheValueByKey(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return "";
            }
            var ret = await _cache.GetStringAsync(key) ?? "";
            return ret;
        }
        public async Task SetCacheByKeyValue(string key, string valuee, int timeInDay = 1)
        {
            if (string.IsNullOrWhiteSpace(key) || string.IsNullOrWhiteSpace(valuee))
            {
                return;
            }
            var cacheOption = new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromDays(timeInDay));
            await _cache.SetStringAsync(key, valuee, cacheOption);
        }

    }
}
