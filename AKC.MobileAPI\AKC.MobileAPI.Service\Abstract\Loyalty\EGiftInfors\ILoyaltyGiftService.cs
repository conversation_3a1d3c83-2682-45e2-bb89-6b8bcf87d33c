﻿using AKC.MobileAPI.DTO.Loyalty.EGiftInfors;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty.EGiftInfors
{
    public interface ILoyaltyEGiftInforsService
    {
        Task<UpdateGiftStatusOutput> UpdateGiftStatus(UpdateGiftStatusInput input);
        // Task<UpdateEgiftCodeOutput> UpdateEgiftCode(UpdateEgiftCodeInputDto input);

        Task<GetVendorInfoFromTransCodeOutput> GetVendorInfoFromTransOfTopup(GetVendorInfoFromTransCodeInput input);

    }
}
