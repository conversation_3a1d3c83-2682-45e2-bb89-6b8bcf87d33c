﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer
{
    public class LoyaltyVerifyReferralCodeOutputDto
    {
        public ReferreeResult Referree { get; set; }
        public ReferrerResult Referrer { get; set; }
        public List<DistributionChannelResult> DistributionChannel { get; set; }
    }

    public class ReferreeResult
    {
        public string MemberCode { get; set; }
        public decimal TempPoint { get; set; }
        public decimal TempCoin { get; set; }
    }

    public class ReferrerResult
    {
        public string MemberCode { get; set; }
        public decimal TempPoint { get; set; }
        public decimal TempCoin { get; set; }
    }

    public class DistributionChannelResult
    {
        public string MemberCode { get; set; }
        public decimal TempPoint { get; set; }
        public decimal TempCoin { get; set; }
    }
}
