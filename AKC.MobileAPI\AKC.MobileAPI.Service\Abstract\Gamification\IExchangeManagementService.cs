﻿using AKC.MobileAPI.DTO.Gamification.Exchange;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Gamification
{
    public interface IExchangeManagementService
    {
        Task<CreateExchangeTransactionOutput> CreateExchangeTransaction(CreateExchangeTransactionInput input);
        Task<ExchangeTransactionHistoryOutput> ExchangeTransactionHistory(ExchangeTransactionHistoryInput input);
        
    }
}
