﻿using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.Gamification
{
    public class ItemManagementBaseService
    {
        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        protected string baseURL;

        public ItemManagementBaseService(IConfiguration configuration)
        {
            _configuration = configuration;
            baseURL = _configuration.GetSection("Gamifications:ItemManagement:RemoteURL").Value;
        }

        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetGamificationdAsync<T>(string apiURL, object query = null, string rewardType = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };

            req.RequestUri = new Uri(requestURL);

            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();
            if (response.IsSuccessStatusCode == false)
            {
                var ex = new GamificationException();
                ex.Data.Add("ErrorData", rawData);
                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    ex.Data.Add("StatusCode", 400);
                }
                else
                {
                    ex.Data.Add("StatusCode", 500);
                }
                throw ex;
            }
            response.EnsureSuccessStatusCode();

            // Get respone result.
            var result = JsonConvert.DeserializeObject<T>(rawData);

            return result;
        }
        /// <summary>
        /// Convert a object to query string format.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string GetQueryString(object obj)
        {
            var properties = from p in obj.GetType().GetProperties()
                             where p.GetValue(obj, null) != null
                             select p.Name + "=" + HttpUtility.UrlEncode(p.GetValue(obj, null).ToString());

            return string.Join("&", properties.ToArray());
        }
    }
}
