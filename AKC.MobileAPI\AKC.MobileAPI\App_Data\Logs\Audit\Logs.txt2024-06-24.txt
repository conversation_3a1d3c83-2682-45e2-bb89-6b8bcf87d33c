INFO  2024-06-24 11:04:46,050 [32   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"CreateRedeemTransaction","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/CreateRedeemTransaction","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"U3d8uZSaK8YMI85aTpNzgReKOEr1\",\r\n    \"quantity\": 1,\r\n    \"giftCode\": \"GiftInfor_20240523024811309_2589\",\r\n    \"date\": \"2024-06-24T01:02:28.279413Z\",\r\n    \"totalAmount\": 90000,\r\n    \"description\": \"{}\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-24 11:05:06,879 [32   ] Controller - redeem transaction U3d8uZSaK8YMI85aTpNzgReKOEr1_Error{"ClassName":"System.AggregateException","Message":"One or more errors occurred.","Data":null,"InnerException":{"AuthErrorCode":3,"ErrorCode":0,"HttpResponse":null,"StackTrace":"   at FirebaseAdmin.Auth.FirebaseTokenVerifier.VerifyTokenAsync(String token, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, Boolean checkRevoked, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken)\r\n   at AKC.MobileAPI.Service.Common.ValidationMemberCode.ValidateToken(String authToken, String memberCode) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Common\\ValidationMemberCode.cs:line 27","Message":"Firebase ID token has incorrect audience (aud) claim. Expected akc-vpid-uat1 but got vpid-uat-pf. Make sure the ID token comes from the same Firebase project as the credential used to initialize this SDK. See https://firebase.google.com/docs/auth/admin/verify-id-tokens for details on how to retrieve a value ID token.","Data":{},"InnerException":null,"HelpLink":null,"Source":"FirebaseAdmin","HResult":-2146233088},"HelpURL":null,"StackTraceString":"   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)\r\n   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)\r\n   at System.Threading.Tasks.Task`1.get_Result()\r\n   at AKC.MobileAPI.Service.Common.CommonHelperService.GetResponseInvalidToken(String authorization, String memberCodeInput) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Common\\CommonHelperService.cs:line 29\r\n   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyGiftTransactionsController.CreateRedeemTransaction(LoyaltyCreateRedeemTransactionInput input) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI\\Controllers\\Loyalty\\LoyaltyGiftTransactionsController.cs:line 56","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2146233088,"Source":"System.Private.CoreLib","WatsonBuckets":null,"InnerExceptions":[{"AuthErrorCode":3,"ErrorCode":0,"HttpResponse":null,"StackTrace":"   at FirebaseAdmin.Auth.FirebaseTokenVerifier.VerifyTokenAsync(String token, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, Boolean checkRevoked, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken, CancellationToken cancellationToken)\r\n   at FirebaseAdmin.Auth.FirebaseAuth.VerifyIdTokenAsync(String idToken)\r\n   at AKC.MobileAPI.Service.Common.ValidationMemberCode.ValidateToken(String authToken, String memberCode) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Common\\ValidationMemberCode.cs:line 27","Message":"Firebase ID token has incorrect audience (aud) claim. Expected akc-vpid-uat1 but got vpid-uat-pf. Make sure the ID token comes from the same Firebase project as the credential used to initialize this SDK. See https://firebase.google.com/docs/auth/admin/verify-id-tokens for details on how to retrieve a value ID token.","Data":{},"InnerException":null,"HelpLink":null,"Source":"FirebaseAdmin","HResult":-2146233088}]}
INFO  2024-06-24 11:05:22,928 [28   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"CreateRedeemTransaction","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/CreateRedeemTransaction","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"U3d8uZSaK8YMI85aTpNzgReKOEr1\",\r\n    \"quantity\": 1,\r\n    \"giftCode\": \"GiftInfor_20240523024811309_2589\",\r\n    \"date\": \"2024-06-24T01:02:28.279413Z\",\r\n    \"totalAmount\": 90000,\r\n    \"description\": \"{}\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-24 11:05:25,536 [41   ] onsService - GET_GIFT_CATEGORY_TYPE_CODE start
INFO  2024-06-24 11:05:25,956 [23   ] onsService - GET_GIFT_CATEGORY_TYPE_CODE end
INFO  2024-06-24 11:05:27,680 [43   ] onsService - CreateRedeemTransaction start
INFO  2024-06-24 11:05:27,931 [43   ] onsService - GetBalanceMember start
INFO  2024-06-24 11:05:28,083 [43   ] onsService - GetBalanceMember end
INFO  2024-06-24 11:05:28,085 [43   ] onsService - VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2024-06-24 11:05:28,351 [43   ] onsService - VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2024-06-24 11:05:28,353 [43   ] onsService - CreateRedeem start
INFO  2024-06-24 11:05:35,851 [43   ] Controller - redeem transaction U3d8uZSaK8YMI85aTpNzgReKOEr1_Error{"StackTrace":"   at AKC.MobileAPI.Service.Loyalty.LoyaltyGiftTransactionsService.CreateRedeemTransaction(LoyaltyCreateRedeemTransactionInput input, HttpContext context) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyGiftTransactionsService.cs:line 667\r\n   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyGiftTransactionsController.CreateRedeemTransaction(LoyaltyCreateRedeemTransactionInput input)","Message":"The requested name is valid, but no data of the requested type was found.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"The requested name is valid, but no data of the requested type was found.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":11004},"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-2147467259}
INFO  2024-06-24 11:06:12,591 [35   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"CreateRedeemTransaction","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/CreateRedeemTransaction","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"U3d8uZSaK8YMI85aTpNzgReKOEr1\",\r\n    \"quantity\": 1,\r\n    \"giftCode\": \"GiftInfor_20240523024811309_2589\",\r\n    \"date\": \"2024-06-24T01:02:28.279413Z\",\r\n    \"totalAmount\": 90000,\r\n    \"description\": \"{}\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-24 11:06:22,031 [45   ] onsService - CreateRedeemTransaction start
INFO  2024-06-24 11:06:42,218 [45   ] onsService - GetBalanceMember start
INFO  2024-06-24 11:06:48,299 [45   ] onsService - GetBalanceMember end
INFO  2024-06-24 11:06:51,391 [45   ] onsService - VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2024-06-24 11:06:55,286 [45   ] onsService - VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2024-06-24 11:07:01,368 [45   ] onsService - CreateRedeem start
INFO  2024-06-24 11:07:09,206 [47   ] Controller - redeem transaction U3d8uZSaK8YMI85aTpNzgReKOEr1_Error{"StackTrace":"   at AKC.MobileAPI.Service.Loyalty.LoyaltyGiftTransactionsService.CreateRedeemTransaction(LoyaltyCreateRedeemTransactionInput input, HttpContext context) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyGiftTransactionsService.cs:line 667\r\n   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyGiftTransactionsController.CreateRedeemTransaction(LoyaltyCreateRedeemTransactionInput input)","Message":"The requested name is valid, but no data of the requested type was found.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"The requested name is valid, but no data of the requested type was found.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":11004},"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-2147467259}
INFO  2024-06-24 11:07:11,583 [52   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"CreateRedeemTransaction","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/CreateRedeemTransaction","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"U3d8uZSaK8YMI85aTpNzgReKOEr1\",\r\n    \"quantity\": 1,\r\n    \"giftCode\": \"GiftInfor_20240523024811309_2589\",\r\n    \"date\": \"2024-06-24T01:02:28.279413Z\",\r\n    \"totalAmount\": 90000,\r\n    \"description\": \"{}\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-24 11:07:18,310 [48   ] onsService - CreateRedeemTransaction start
INFO  2024-06-24 11:07:30,709 [48   ] onsService - GetBalanceMember start
INFO  2024-06-24 11:07:31,690 [48   ] onsService - GetBalanceMember end
INFO  2024-06-24 11:07:34,143 [48   ] onsService - VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2024-06-24 11:07:35,383 [48   ] onsService - VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2024-06-24 11:07:41,354 [48   ] onsService - CreateRedeem start
INFO  2024-06-24 11:10:05,362 [83   ] Controller - redeem transaction U3d8uZSaK8YMI85aTpNzgReKOEr1_Error{"StackTrace":"   at AKC.MobileAPI.Service.Loyalty.LoyaltyGiftTransactionsService.CreateRedeemTransaction(LoyaltyCreateRedeemTransactionInput input, HttpContext context) in D:\\working\\vpid-mobile-api\\AKC.MobileAPI\\AKC.MobileAPI.Service\\Loyalty\\LoyaltyGiftTransactionsService.cs:line 667\r\n   at AKC.MobileAPI.Controllers.Loyalty.LoyaltyGiftTransactionsController.CreateRedeemTransaction(LoyaltyCreateRedeemTransactionInput input)","Message":"The requested name is valid, but no data of the requested type was found.","Data":{},"InnerException":{"ClassName":"System.Net.Sockets.SocketException","Message":"The requested name is valid, but no data of the requested type was found.","Data":{},"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Net.Http.ConnectHelper.ConnectAsync(String host, Int32 port, CancellationToken cancellationToken)","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-2147467259,"Source":"System.Private.CoreLib","WatsonBuckets":null,"NativeErrorCode":11004},"HelpLink":null,"Source":"AKC.MobileAPI.Service","HResult":-2147467259}
INFO  2024-06-24 11:10:20,380 [97   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"CreateRedeemTransaction","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/CreateRedeemTransaction","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"U3d8uZSaK8YMI85aTpNzgReKOEr1\",\r\n    \"quantity\": 1,\r\n    \"giftCode\": \"GiftInfor_20240523024811309_2589\",\r\n    \"date\": \"2024-06-24T01:02:28.279413Z\",\r\n    \"totalAmount\": 90000,\r\n    \"description\": \"{}\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-24 11:10:25,487 [68   ] onsService - CreateRedeemTransaction start
INFO  2024-06-24 11:10:25,958 [68   ] onsService - GetBalanceMember start
INFO  2024-06-24 11:10:26,116 [68   ] onsService - GetBalanceMember end
INFO  2024-06-24 11:10:26,120 [68   ] onsService - VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2024-06-24 11:10:26,367 [68   ] onsService - VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2024-06-24 11:10:26,369 [68   ] onsService - CreateRedeem start
INFO  2024-06-24 11:14:34,443 [32   ]            - Http Request Information: {"ControllerName":"LoyaltyGiftTransactions","ActionName":"CreateRedeemTransaction","Browser":"PostmanRuntime/7.39.0","Ip":"::1","Scheme":"https","Method":"POST","ServerHost":"localhost:44365","Path":"/api/GiftTransactions/CreateRedeemTransaction","QueryString":"{}","Body":"{\r\n    \"memberCode\": \"U3d8uZSaK8YMI85aTpNzgReKOEr1\",\r\n    \"quantity\": 1,\r\n    \"giftCode\": \"GiftInfor_20240523024811309_2589\",\r\n    \"date\": \"2024-06-24T01:02:28.279413Z\",\r\n    \"totalAmount\": 90000,\r\n    \"description\": \"{}\"\r\n}","TrueClientIp":"","XForwardedFor":""}
INFO  2024-06-24 11:14:42,705 [32   ] onsService - CreateRedeemTransaction start
INFO  2024-06-24 11:14:44,416 [32   ] onsService - GetBalanceMember start
INFO  2024-06-24 11:14:44,602 [32   ] onsService - GetBalanceMember end
INFO  2024-06-24 11:14:44,605 [32   ] onsService - VERIFY_OR_CREATE_REDEEM_ORDER start
INFO  2024-06-24 11:14:44,897 [32   ] onsService - VERIFY_OR_CREATE_REDEEM_ORDER end
INFO  2024-06-24 11:14:44,899 [32   ] onsService - CreateRedeem start
