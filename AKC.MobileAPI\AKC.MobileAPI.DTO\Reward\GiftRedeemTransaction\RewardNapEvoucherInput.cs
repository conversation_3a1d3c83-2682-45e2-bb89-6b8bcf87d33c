﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction
{
    public class RewardNapEvoucherInput
    {
        public string OrderCode { get; set; }
        public string ECode { get; set; }
        public decimal TokenAmount { get; set; }
        public long MerchantId { get; set; }
        public string NationalId { get; set; }
        public DateTime RequestDate { get; set; }
    }


    public class RewardNapEvoucherOutputSuccess
    {
        public int Result { get; set; }
        public RewardNapEvoucherOutputSuccessChild Items { get; set; }
        public string Message { get; set; }
        public string MessageDetail { get; set; }
    }
    public class RewardNapEvoucherOutputSuccessChild
    {
        public decimal TokenAmount { get; set; }
        public string TokenTransactionId { get; set; }
        public string ExpiryDate { get; set; }
    }
    public class RewardNapEvoucherOutputFail
    {
        public int Status { get; set; }
        public RewardNapEvoucherOutputFailChild Result { get; set; }
        
    }

    public class RewardNapEvoucherOutputFailChild
    {
        public string Code { get; set; }
        public string Message { get; set; }
    }

}
