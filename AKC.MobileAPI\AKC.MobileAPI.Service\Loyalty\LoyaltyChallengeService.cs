﻿using AKC.MobileAPI.DTO.Loyalty.Challenge;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Loyalty;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service
{
    public class LoyaltyChallengeService : BaseLoyaltyService, ILoyaltyChallengeService
    {
        public LoyaltyChallengeService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<GetAllQuestByGroupDto> GetListByGroup(GetAllQuestByGroupInput input)
        {
            return await PostLoyaltyAsync<GetAllQuestByGroupDto>(LoyaltyApiUrl.GET_LIST_BY_GROUP, input);

        }

        public async Task<GetQuestDetailOutputDto> GetDetail(GetQuestDetailInputDto input)
        {
            return await PostLoyaltyAsync<GetQuestDetailOutputDto>(LoyaltyApiUrl.GET_DETAIL, input);
        }

        public async Task<GetListQuestByActivityOutputDto> GetListQuestByActivity(GetListQuestByActivityInputDto input)
        {
            return await PostLoyaltyAsync<GetListQuestByActivityOutputDto>(LoyaltyApiUrl.GET_LIST_QUEST_BY_ACTIVITY, input);
        }

        public async Task<JoinQuestOutputDto> Join(JoinQuestInputDto input)
        {
            return await PostLoyaltyAsync<JoinQuestOutputDto>(LoyaltyApiUrl.QUEST_JOIN, input);
        }

        public async Task<ViewActualQuestOutputDto> ViewActual(ViewActualQuestInputDto input)
        {
            return await PostLoyaltyAsync<ViewActualQuestOutputDto>(LoyaltyApiUrl.QUEST_VIEW_ACTUAL, input);
        }

        public async Task<ClaimOutputDto> Claim(ClaimInputDto input)
        {
            return await PostLoyaltyAsync<ClaimOutputDto>(LoyaltyApiUrl.QUEST_CLAIM, input);
        }

        public async Task<GetAllQuestOutputDto> GetAll(GetAllQuestInputDto input)
        {
            return await GetLoyaltyAsync<GetAllQuestOutputDto>(LoyaltyApiUrl.QUEST_GET_ALL, input);
        }

        public async Task<GetListByMemberCodeOutputDto> GetListByMemberCode(GetListByMemberCodeInputDto input)
        {
            return await PostLoyaltyAsync<GetListByMemberCodeOutputDto>(LoyaltyApiUrl.QUEST_GET_LIST_BY_MEMBERCODE, input);
        }
        #region Loyalty challenge quest stage

        public async Task<GetAllQuestStageOutputDto> GetAllQuestStage(GetAllQuestStageInputDto input)
        {
            return await GetLoyaltyAsync<GetAllQuestStageOutputDto>(LoyaltyApiUrl.QUEST_STAGE_GET_ALL, input);
        }
        public async Task<JoinQuestStageOutputDto> JoinQuestStage(JoinQuestStageInputDto input)
        {
            return await PostLoyaltyAsync<JoinQuestStageOutputDto>(LoyaltyApiUrl.QUEST_STAGE_JOIN, input);
        }
        //public async Task<ClaimQuestStageOutputDto> ClaimQuestStage(ClaimQuestStageInputDto input)
        //{
        //    return await PostLoyaltyAsync<ClaimQuestStageOutputDto>(LoyaltyApiUrl.QUEST_STAGE_CLAIM, input);
        //}
        //public async Task<GetListQuestStageJoinedByMemberOutPutDto> GetListQuestStageJoinedByMember(GetListQuestStageJoinedByMemberInputDto input)
        //{
        //    return await PostLoyaltyAsync<GetListQuestStageJoinedByMemberOutPutDto>(LoyaltyApiUrl.GET_LIST_QUEST_STAGE_JOINED_BY_MEMBER, input);
        //}
        //public async Task<GetListQuestStageByMemberCodeOutputDto> GetListQuestStageByMemberCode(GetListQuestStageByMemberCodeInputDto input)
        //{
        //    return await PostLoyaltyAsync<GetListQuestStageByMemberCodeOutputDto>(LoyaltyApiUrl.QUEST_STAGE_GET_LIST_BY_MEMBERCODE, input);
        //}

        public async Task<GetQuestStageDetailOutputDto> GetQuestStageDetail(GetQuestStageDetailInputDto input)
        {
            return await PostLoyaltyAsync<GetQuestStageDetailOutputDto>(LoyaltyApiUrl.QUEST_STAGE_GET_DETAIL, input);
        }

        public async Task<QuestStageClaimOutputDto> ClaimQuestStageOrLevel(QuestStageClaimInputDto input)
        {
            return await PostLoyaltyAsync<QuestStageClaimOutputDto>(LoyaltyApiUrl.QUEST_STAGE_OR_LEVEL_CLAIM, input);
        }

        public async Task<GetListQuestStageByMemberCodeOutputDto> GetListQuestAndQuestStageByMemberCode(GetListQuestStageByMemberCodeInputDto input)
        {
            return await PostLoyaltyAsync<GetListQuestStageByMemberCodeOutputDto>(LoyaltyApiUrl.QUEST_STAGE_GET_LIST_BY_MEMBERCODE, input);
        }

        #endregion

    }
}
