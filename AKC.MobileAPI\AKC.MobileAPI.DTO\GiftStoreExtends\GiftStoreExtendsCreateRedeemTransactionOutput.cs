﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.GiftStoreExtends
{
    public class GiftStoreExtendsCreateRedeemTransactionOutput
    {
        public bool SuccessedOrder { get; set; } = true;
        public string ErrorCode { get; set; }
        public string TransactionCode { get; set; }
        public string Messages { get; set; }
        public decimal TotalRedeemToken { get; set; }
        public List<GiftStoreExtendsDataRedeem> Items { get; set; }
    }

    public class GiftStoreExtendsCreateRedeemTransactionOutputDto
    {
        public GiftStoreExtendsCreateRedeemTransaction Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class GiftStoreExtendsCreateRedeemTransaction
    {
        public List<GiftStoreExtendsDataRedeem> Items { get; set; }
        public string ErrorCode { get; set; }
        public string TransactionCode { get; set; }
        public string Messages { get; set; }
        public bool SuccessedOrder { get; set; } = true;
    }

    public class GiftStoreExtendsDataRedeem
    {
        public decimal TotalPrice { get; set; }
        public string Status { get; set; }
        public DateTime TransactionDate { get; set; }
        public GiftStoreExtendsEGiftData EGift { get; set; }
    }

    public class GiftStoreExtendsEGiftData
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string UsedStatus { get; set; }
        public DateTime? ExpiredDate { get; set; }
        public string QRCode { get; set; }
        public string CodeDisplay { get; set; }
    }
}
