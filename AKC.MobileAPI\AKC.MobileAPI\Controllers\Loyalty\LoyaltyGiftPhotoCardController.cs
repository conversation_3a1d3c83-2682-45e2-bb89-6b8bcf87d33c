﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.GiftPhotCard;
using Newtonsoft.Json;
using AKC.MobileAPI.Service.Exceptions;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/GiftPhotoCard")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyGiftPhotoCardController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyGiftPhotoCardService _giftPhotoCardService;
        private readonly IExceptionReponseService _exceptionReponseService;

        public LoyaltyGiftPhotoCardController(
        ILogger<LoyaltyArticleController> logger,
        ILoyaltyGiftPhotoCardService giftPhotoCardService,
        IExceptionReponseService exceptionReponseService)
        {
            _logger = logger;
            _giftPhotoCardService = giftPhotoCardService;
            _exceptionReponseService = exceptionReponseService;
        }

        [HttpGet]
        [Route("GetAll")]
        public async Task<ActionResult<LoyaltyGiftPhotoCardGetAllOutput>> GetAll([FromQuery] SearchGiftPhotoCardRequest input)
        {
            try
            {
                var result = await _giftPhotoCardService.GetAll(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Article GetAll Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}
