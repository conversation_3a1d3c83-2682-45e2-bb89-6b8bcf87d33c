﻿using AKC.MobileAPI.DTO.Loyalty.GiftFlashSaleProgram;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Quest
{
    public class ListGmQuestV2Output
    {
        public List<GmQuestV2Infor> result { get; set; }
        public object targetUrl { get; set; }
        public bool success { get; set; }
        public object error { get; set; }
        public bool unAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }
}
