﻿using System;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberGetTokenTransDetailInput
    {
        public string MemberCode { get; set; }
        public string TokenTransId { get; set; }
    }
    
    public class RewardMemberGetTokenTransDetailOutput
    {
        public string TokenTransID { get; set; }
        public int MemberId { get; set; }
        public DateTime? Time { get; set; }
        public DateTime? BusinessTime { get; set; }
        public DateTime? ValidDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string OrderCode { get; set; }
        public string ActionCode { get; set; }
        public string ActionCodeDetail { get; set; }        
        public string ActionName { get; set; }
        public string FromWalletAddress { get; set; }
        public string FromMerchantNameOrMember { get; set; }
        public string ToWalletAddress { get; set; }
        public string ToMerchantNameOrMember { get; set; }
        public int StoreId { get; set; }
        public string StoreName { get; set; }
        public string NationalId { get; set; }
        public string UserAddress { get; set; }
        public string ActionType { get; set; }
        public decimal TokenAmount { get; set; }
        public decimal PartnerPointAmount { get; set; }
        public decimal PointExchangeRate { get; set; }
        public decimal CurrencyExchangeRate { get; set; }
        public string GrantType { get; set; }
        public string UsagePriority { get; set; }
        public string Reason { get; set; }
        public string OriginalTokenTransId { get; set; }
        public int MerchantId { get; set; }
        public decimal BaseUnit { get; set; }
        
        // GIFT INFO (If any)
        public string GiftName { get; set; }
        public string GiftId { get; set; }
        public string GiftImage { get; set; }
        public float GiftPaidCoin { get; set; }
        public DateTime? EGiftExpiredDate { get; set; }
        public string BrandName { get; set; }
        public string BrandImage { get; set; }
        public string PartnerBindingTxId { get; set; }
        
        public ThirdPartyGiftVendorShortDto Vendor { get; set; }
    }
}