﻿using AKC.MobileAPI.DTO.Loyalty.Quest;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty.Quest
{
    public interface ILoyaltyQuestService
    {
        Task<GmQuestV2Output> GetQuestById(int id, string memberCode);
        Task<ListGmQuestV2Output> GetListQuestActive(SearchGmQuestV2Request request);
        Task<ViewMemberProgessQuestOutput> ViewMemberProgressOnQuest(ViewMemberProgessQuestInput request);
        Task<PerformActionOutput> PerformAction(PerformActionInput request);
        Task<GmQuestV2InforOutput> GetQuestActiveByCode(GmQuestV2InforInput request);
        Task<GmQuestEnrolmentV2Output> MemberEnrolmentQuest(GmQuestEnrolmentV2Request request);
        Task<PerformActionOutput> MemberClaimMission(MemberClaimMissionInput input);
    }
}
