﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class GetListQuestStageByMemberCodeOutputDto
    {
        public ListResultGetListQuestStageByMemberCode Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class ListResultGetListQuestStageByMemberCode
    {
        public int TotalCount { get; set; }

        public List<ListGetListQuestStageByMemberCodeItems> Items { get; set; }

    }

    public class ListGetListQuestStageByMemberCodeItems
    {
        public string Type { get; set; }
        public DateTime? CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string LinkAvatar { get; set; }
        public string Status { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int? ExpiryDuration { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public DateTime? JoinedDate { get; set; }
        public string State { get; set; }
        public bool IsQuestStageClaimed { get; set; }
        public string Process { get; set; }
        public decimal PercentCompleted { get; set; }
        public List<QuestStageLevelDto> Levels { get; set; }
        public List<MissionDto> Missions { get; set; }
        public int? ItemCount { get; set; }
        public int? QuestBaselineId { get; set; }
        public int? QuestStageBaselineId { get; set; }
        public decimal Point { get; set; }
        public decimal Coin { get; set; }

    }

    public class QuestStageLevelDto
    {
        public int Id { get; set; }

        public int? TenantId { get; set; }
        public string Code { get; set; }

        public string QuestStageCode { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Status { get; set; }

        public int? LevelOrder { get; set; }

        public string ListOfChallenge { get; set; }

        public string ImageLink { get; set; }

        public string State { get; set; }

        public int QuestStageLevelBaselineId { get; set; }

    }
}
