﻿using System;
using System.Collections.Generic;

namespace AKC.MobileAPI.DTO.Loyalty.TopUp
{
    public class CheckVoucherOutput
    {
        public CheckVoucherOutputResult Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class CheckVoucherOutputResult
    {
        public string ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public string VoucherCode { get; set; }
        public decimal? TopupAmount { get; set; }
        public string VendorName { get; set; }
        public string ExpiredDate { get; set; }
        public string VendorPhoto { get; set; }
        public string GiftRedeemTransactionCode { get; set; }
        public int? Count { get; set; }
        public int? RetryMax { get; set; }
    }

    public class RedeemVoucherOutput
    {
        public RedeemVoucherOutputResult Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class RedeemVoucherOutputResult
    {
        public string ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public string TransactionCode { get; set; }
        public string ExpireDate { get; set; }
        public int? Count { get; set; }
        public decimal? TopupAmount { get; set; }
    }
    
    public class RevertVoucherOutput
    {
        public RevertVoucherOutputResult Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }
    public class RevertVoucherOutputResult
    {
        public int ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public int? Count { get; set; }
    }


    public class CheckMemberOutput
    {
        public CheckMemberOutputResult Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class CheckMemberOutputResult
    {
        public int ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public string Membercode { get; set; }
        public bool isBlock { get; set; }
    }
}