﻿using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/Location")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyLocationController : ControllerBase
    {
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILogger _logger;
        private readonly ILoyaltyLocationService _loyaltyLocationService;
        public LoyaltyLocationController(
            IExceptionReponseService exceptionReponseService,
            ILogger<LoyaltyLocationController> logger,
            ILoyaltyLocationService loyaltyLocationService)
        {
            _exceptionReponseService = exceptionReponseService;
            _logger = logger;
            _loyaltyLocationService = loyaltyLocationService;
        }

        [HttpGet]
        [Route("GetAll")]
        public async Task<ActionResult<GetAllLocationManagementDto>> GetAll([FromQuery] GetAllLocationInput input)
        {
            try
            {
                var result = await _loyaltyLocationService.GetAll(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Location GetAll Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        //[HttpGet]
        //[Route("GetAllNoPaging")]
        //public async Task<ActionResult<GetAllLocationManagementDto>> GetAllNoPaging([FromQuery] GetAllLocationNoPagingInput input)
        //{
        //    try
        //    {
        //        var result = await _loyaltyLocationService.GetAllNoPaging(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "Location GetAll Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}

        [HttpPost]
        [Route("ViewLocationByIds")]
        public async Task<ActionResult<ViewLocationByIdsOutput>> ViewLocationByIds(ViewLocationByIds input)
        {
            try
            {
                var result = await _loyaltyLocationService.ViewLocationByIds(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Location GetAll Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}
