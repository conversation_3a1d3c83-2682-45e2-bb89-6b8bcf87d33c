﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Merchant
{
    public class RewardGetAllMerchantExchangeInput
    {
        public string sorting { get; set; }
        public int maxResultCount { get; set; }
        public int skipCount { get; set; }
    }

    public class RewardGetAllMerchantExchangeDto
    {
        public string StatusFilter { get; set; }
        public string TypeFilter { get; set; }
        public string sorting { get; set; }
        public int maxResultCount { get; set; }
        public int skipCount { get; set; }
    }
}
