﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/NotificationHistory")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyNotificationHistoryController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyNotificationHistoryService _loyaltyNotificationHistoryService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        public LoyaltyNotificationHistoryController(
            ILogger<LoyaltyNotificationHistoryController> logger,
            ILoyaltyNotificationHistoryService loyaltyNotificationHistoryService,
            IExceptionReponseService exceptionReponseService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _loyaltyNotificationHistoryService = loyaltyNotificationHistoryService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
        }

        [HttpGet]
        [Route("GetAllByMemberCode")]
        public async Task<ActionResult<LoyaltyNotificationHistoryGetAllOutput>> GetAllByMemberCode([FromQuery] MobileLoyaltyGetAllNotificationsInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyNotificationHistoryService.GetAllByMemberCode(input, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "NotificationHistory GetAll Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("ChangeStatus")]
        public async Task<ActionResult<LoyaltyNotificationHistoryChangeStatusOutput>> ChangeStatus([FromQuery] MobileLoyaltyChangeStatusInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyNotificationHistoryService.ChangeStatus(input, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "NotificationHistory ChangeStatus Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("ChangeNotificationCount")]
        public async Task<ActionResult<LoyaltyNotificationHistoryChangeCountOutput>> ChangeNotificationCount([FromQuery] MobileLoyaltyChangeNotificationCountInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyNotificationHistoryService.ChangeNotificationCount(input, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "NotificationHistory ChangeCount Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpDelete]
        [Route("Delete")]
        public async Task<ActionResult<LoyaltyNotificationHistoryDeleteOutput>> Delete([FromQuery] MobileLoyaltyDeleteInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyNotificationHistoryService.Delete(input, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "NotificationHistory Delete Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpDelete]
        [Route("HardDelete")]
        public async Task<ActionResult<LoyaltyNotificationHistoryDeleteOutput>> HardDelete([FromQuery] MobileLoyaltyDeleteInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyNotificationHistoryService.HardDelete(input, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "NotificationHistory Hard Delete Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}