﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Reward.GiveToken;
using AKC.MobileAPI.DTO.Reward.Merchant;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Reward
{
    [Route("api/GiveToken")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class RewardGiveTokenController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IRewardGiveTokenService _rewardGiveTokenService;
        private readonly ICommonHelperService _commonHelperService;

        public RewardGiveTokenController(
            ILogger<RewardMerchantController> logger,
            IExceptionReponseService exceptionReponseService,
            IRewardGiveTokenService rewardGiveTokenService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _rewardGiveTokenService = rewardGiveTokenService;
            _commonHelperService = commonHelperService;
        }

        [HttpPost]
        [Route("UserVerifying")]
        public async Task<ActionResult<RewardGiveTokenUserVerifyingOutput>> UserVerifying(RewardGiveTokenUserVerifyingInput input)
        {
            try
            {
                var result = await _rewardGiveTokenService.UserVerifying(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "UserVerifying Error" + JsonConvert.SerializeObject(ex));
                var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                return StatusCode(400, res);
            }
        }

        // [HttpPost]
        // [Route("TransferTransaction")]
        // public async Task<ActionResult<RewardGiveTokenTransferTransactionOutput>> TransferTransaction(RewardGiveTokenTransferTransactionInput input)
        // {
        //     try
        //     {
        //         var authorization = Request.Headers["Authorization"].ToString();
        //         var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.SenderCode);
        //         if (checkAuthen != null)
        //         {
        //             return StatusCode(401, checkAuthen);
        //         }
        //         var result = await _rewardGiveTokenService.TransferTransaction(input);
        //         return StatusCode(200, result);
        //     }
        //     catch (Exception ex)
        //     {
        //         var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
        //         _logger.LogError(ex, "TransferTransaction Error - " + JsonConvert.SerializeObject(ex));
        //
        //         return StatusCode(400, res);
        //     }
        // }
    }
}
