﻿using System;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.FeedbackSuggesstion;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/FBSuggesstion")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyFeedbackSuggesstionController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyFeedbackSuggesstionService _fBSuggesstionService;
        private readonly IExceptionReponseService _exceptionReponseService;
        public LoyaltyFeedbackSuggesstionController(
            ILogger<LoyaltyFeedbackSuggesstionController> logger,
            ILoyaltyFeedbackSuggesstionService fBSuggesstionService,
            IExceptionReponseService exceptionReponseService)
        {
            _logger = logger;
            _fBSuggesstionService = fBSuggesstionService;
            _exceptionReponseService = exceptionReponseService;
        }

        [HttpGet]
        [Route("GetAll")]
        public async Task<ActionResult<GetAllFeedbackSuggesstionOutputDto>> GetAll([FromQuery] GetAllFeedbackSuggesstionInputDto input)
        {
            try
            {
                var result = await _fBSuggesstionService.GetAll(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Get Feedback Suggesstion Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

    }
}
