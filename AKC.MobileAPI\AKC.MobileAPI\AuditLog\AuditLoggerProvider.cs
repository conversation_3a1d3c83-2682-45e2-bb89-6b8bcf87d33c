﻿using AKC.MobileAPI.Service;
using Gelf.Extensions.Logging;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.AuditLog
{
    public class AuditLoggerProvider
    {
        #region SingleTon
        private readonly ILogger log4netLogger;
        private readonly ILogger gelfLogger;
        private static AuditLoggerProvider instance;

        public static AuditLoggerProvider Instance
        {
            get
            {
                if (instance == null) instance = new AuditLoggerProvider();
                return instance;
            }
        }

        private AuditLoggerProvider()
        {
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var options = new Log4NetProviderOptions("log4net-audit.config");
            log4netLogger = new Log4NetProvider(options).CreateLogger();

            if (bool.Parse(configuration.GetSection("GrayLog:IsEnabled").Value))
            {
                GelfLoggerProvider gelfLoggerProvider = new GelfLoggerProvider(new GelfLoggerOptions()
                {
                    Host = configuration.GetSection("GrayLog:Host").Value,
                    Port = int.Parse(configuration.GetSection("GrayLog:Port").Value),
                    Protocol = GetGelfProtocol(configuration.GetSection("GrayLog:Protocol").Value),
                    LogSource = configuration.GetSection("GrayLog:LogSourceAudit").Value
                });

                gelfLogger = gelfLoggerProvider.CreateLogger(nameof(RequestLoginMiddleware));
            }
        }

        #endregion

        private GelfProtocol GetGelfProtocol(string protocol)
        {
            switch (protocol.Trim().ToLower())
            {
                case "udp":
                    return GelfProtocol.Udp;
                case "http":
                    return GelfProtocol.Http;
                case "https":
                    return GelfProtocol.Https;
                default:
                    throw new Exception("Wrong Gelf protocal name.");
            }
        }

        public void LogAudit(RequestLogData logData)
        {
            try
            {
                log4netLogger.LogInformation($"Http Request Information: {JsonConvert.SerializeObject(logData)}");

                if (gelfLogger != null)
                {
                    gelfLogger.LogInformation(JsonConvert.SerializeObject(logData));
                }
            }
            catch
            {
               // Do nothing
            }
        }
    }
}
