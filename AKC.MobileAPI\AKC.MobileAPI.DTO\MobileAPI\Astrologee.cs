using AKC.MobileAPI.DTO.Loyalty.Gift;
using System.Collections.Generic;
using System;

namespace AKC.MobileAPI.DTO.MobileAPI
{
    public class LunarCalendarCellDto
    {
        public bool IsToday { get; set; } = false;
        public string DayOfWeek { get; set; }

        public string LunarHolidayName { get; set; }

        public string DateName { get; set; }

        public string HourName { get; set; }

        public string MonthName { get; set; }

        public string YearName { get; set; }

        public int LunarDateNumber { get; set; }

        public int LunarMonthNumber { get; set; }

        public int LunarYearNumber { get; set; }

        public int SolarDateNumber { get; set; }

        public int SolarMonthNumber { get; set; }

        public int SolarYearNumber { get; set; }

        public List<LunarLuckyHour> LuckyHours { get; set; }
    }

    public class LunarLuckyHour
    {
        public string Name { get; set; }
        public List<int> Time { get; set; }
    }


    public class MonthAndYearNum
    {
        public int Year { get; set; }
        public int Month { get; set; }
    }

    public class CalOfMonthOutput
    {
        public string MonthName { get; set; }
        public string YearName { get; set; }
        public int LunarYear { get; set; }
        public int LunarMonth { get; set; }
        public List<LunarCalendarCellDto> DateList { get; set; }
    }

    public class YearDto
    {
        public int Year { get; set; }
        public string LunarYearName { get; set; }
        public string ZodiacSign { get; set; }
        public bool IsLeapLunarYear { get; set; }
    }
    public class GetYearsInput
    {
        public int SelectedYear { get; set; }
        public int TotalNumber { get; set; }
    }

    public class GetNDatesInput
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public int Day { get; set; }
        public int N { get; set; }
    }

    public class GetDocumentInput
    {
        public string Type { get; set; }
        public int? AppliedLunarDate { get; set; }

        public string RelatedOfferingsId { get; set; }

        public int skipCount { get; set; } = 0;

        public int maxResultCount { get; set; } = 10;

        public string sorting { get; set; }
    }

    public class TamLynkDocumentDTO
    {
        public int Id { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public DateTime? CreationTime { get; set; }
        public DateTime? DeletedTime { get; set; }
        public string Status { get; set; }
        public string Title { get; set; }
        public string Type { get; set; }
        public string Content { get; set; }
        public string RelatedGiftGroupCode { get; set; }
        public int? AppliedLunarDate { get; set; }
        public string ImageLink { get; set; }

        public string RelatedOfferingsId { get; set; }
    }

    public class TamLynkDocumentResponse
    {
        public List<TamLynkDocumentDTO> items { get; set; }
        public int result { get; set; }
        public int totalCount { get; set; }
        public string message { get; set; }
    }


    public class GetDocumentOutput
    {
        public List<TamLynkDocumentDTO> items { get; set; }
    }
}