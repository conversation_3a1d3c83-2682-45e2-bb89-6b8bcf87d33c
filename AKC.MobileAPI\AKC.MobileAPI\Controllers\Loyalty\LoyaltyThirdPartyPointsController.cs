﻿using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.NotificationHistory.V2;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.LoyaltyVpbank.Member;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
//using Microsoft.AspNetCore.Http;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/3rd")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyThirdPartyPointsController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ISkyJoyIntegrationService _skyJoyIntegrationService;
        private readonly ILoyaltyThirdPartyService _loyaltyThirdPartyService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IRewardExchangeTransactionService _rewardExchangeTransactionService;
        private readonly ICommonHelperService _commonHelperService;
        protected readonly IConfiguration _configuration;
        public LoyaltyThirdPartyPointsController(
            ILogger<LoyaltyThirdPartyPointsController> logger,
            IExceptionReponseService exceptionReponseService,
            ILoyaltyThirdPartyService loyaltyThirdPartyService,
            IRewardExchangeTransactionService rewardExchangeTransactionService,
            IRewardMemberService rewardMemberService,
            IConfiguration conf,
            ISkyJoyIntegrationService sj,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _loyaltyThirdPartyService = loyaltyThirdPartyService;
            _rewardExchangeTransactionService = rewardExchangeTransactionService;
            _rewardMemberService = rewardMemberService;
            _commonHelperService = commonHelperService;
            _configuration = conf;
            _skyJoyIntegrationService = sj;
        }

        private readonly string MERCHANT_Appota = "appota";
        private readonly string MERCHANT_Skyjoy = "SkyJoy";

        [HttpGet]
        [Route("Points/View")]
        public async Task<ActionResult<LoyaltyThirdPartyPointViewOutput>> PointView([FromQuery] LoyaltyThirdPartyPointViewInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var context = HttpContext;
                var result = await _loyaltyThirdPartyService.PointView(input, context);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "ThirdParty PointView Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("Verification/VerifyNationalId")]
        public async Task<ActionResult<LoyaltyThirdPartyVerifyNationalIdOutput>> VerifyNationalId(LoyaltyThirdPartyVerifyNationalIdInput input)
        {
            try
            {
                var context = HttpContext;
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyThirdPartyService.VerifyNationalId(input, context, authorization);
                if (!result.Success)
                {
                    return StatusCode(400, new RewardErrorResponse()
                    {
                        Result = 400,
                        Code = "VPB0001",
                        Message = "Member Code not exist",
                        MessageDetail = null,
                    });
                }
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError(ex, "ThirdParty VerifyNationalId Error - " + JsonConvert.SerializeObject(ex));

                    if (res.Code == ("AccountHasBeenBinded"))
                    {
                        var resultReward = await _rewardMemberService.getMemberByIdCardOrPhoneNumber(new GetMemberByIdCardOrPhoneNumberInput()
                        {
                            IdCard = input.IdNumber
                        });

                        var phone = resultReward.MemberPhoneNumber;
                        var hiddenPhone = phone.Replace(phone.Substring(0, phone.Length - 2), new string('x', phone.Length - 2));
                        var message = $"Thông tin bạn vừa nhập đang liên kết với SĐT {hiddenPhone}. Vui lòng liên hệ CSKH của LynkID để được hỗ trợ.";

                        res.Message = message;
                        res.MessageDetail = message;
                    }

                    return StatusCode(400, res);
                }
                else if (ex.Message == "YouAreNotInTheTestList")
                {
                    return StatusCode(400, new RewardErrorResponse()
                    {
                        Result = 400,
                        Code = "YouAreNotInTheTestList",
                        Message = "Bạn không nằm trong danh sách thử nghiệm, vui lòng liên hệ số hotline để biết thêm chi tiết.",
                        MessageDetail = "Bạn không nằm trong danh sách thử nghiệm, vui lòng liên hệ số hotline để biết thêm chi tiết."
                    });
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogError(ex, "ThirdParty VerifyNationalId Error - " + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("Verification/VerifyOTP")]
        public async Task<ActionResult<LoyaltyThirdPartyVerifyOTPOutput>> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var context = HttpContext;
                var result = await _loyaltyThirdPartyService.VerifyOTP(input, context, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError(ex, "ThirdParty VerifyOTP Error - " + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogError(ex, "ThirdParty VerifyOTP Error - " + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }

        /**
         * Entry point (level controller) để cho đổi điểm LynkiD Ra khỏi tài khoản LynkiD.
         */
        [HttpPost]
        [Route("Points/ExchangeLynkiDToPartnerPoint")]
        public async Task<ActionResult<ExchangeLynkiDToPartnerPointRes>> ExchangeLynkiDToPartnerPoint(
            ExchangeLynkiDToPartnerPointReq input)
        {
         try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var ipAddressList = Dns.GetHostEntry(Dns.GetHostName()).AddressList;
                var ipAddress = ipAddressList.GetValue(ipAddressList.Length - 1).ToString();
                _logger.LogInformation("ExchangeLynkiDToPartnerPoint >> Public_IP_" + ipAddress);
                var rewardResult = await _loyaltyThirdPartyService.ExchangeLynkiDToPartnerPoint(input);
                return StatusCode(rewardResult.Code == "00" ? 200 : 400, rewardResult);
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError("ExchangeLynkiDToPartnerPoint >> Error - " + input.MemberCode + " - " + ex.Message + " - " + ex.StackTrace);

                    var errorMessage = getMessageFromErrorCode(res.Code);

                    if (!string.IsNullOrWhiteSpace(errorMessage))
                    {
                        res.Message = errorMessage;
                        res.MessageDetail = errorMessage;
                    }

                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogError("ExchangeLynkiDToPartnerPoint >> Error - " + input.MemberCode + " - " + ex.Message + " - " + ex.StackTrace);
                    return StatusCode(400, res);
                }
            }   
        }
        [HttpPost]
        [Route("Points/Exchange")]
        public async Task<ActionResult<LoyaltyThirdPartyPointExchangeOutput>> PointExchange(LoyaltyThirdPartyPointExchangeInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var context = HttpContext;
                var ipAddressList = Dns.GetHostEntry(Dns.GetHostName()).AddressList;
                var ipAddress = ipAddressList.GetValue(ipAddressList.Length - 1).ToString();
                _logger.LogInformation("Public_IP_" + ipAddress);
                var rewardResult = await _loyaltyThirdPartyService.PointExchange(input, context);
                return StatusCode(200, rewardResult);
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError(ex, "ThirdParty Points/Exchange Error - " + JsonConvert.SerializeObject(ex));

                    if (res.Code == "MerchantNotExceedMinBalance")
                    {
                        res.Message = "Hệ thống tạm thời bị gián đoạn. Bạn vui lòng quay lại sau nhé.";
                        res.MessageDetail = "Hệ thống tạm thời bị gián đoạn. Bạn vui lòng quay lại sau nhé.";
                    }

                    return StatusCode(400, res);
                }
                else if (ex.Message == "YouAreNotInTheTestList")
                {
                    return StatusCode(400, new RewardErrorResponse()
                    {
                        Result = 400,
                        Code = "YouAreNotInTheTestList",
                        Message = "Bạn không nằm trong danh sách thử nghiệm, vui lòng liên hệ số hotline để biết thêm chi tiết.",
                        MessageDetail = "Bạn không nằm trong danh sách thử nghiệm, vui lòng liên hệ số hotline để biết thêm chi tiết."
                    });
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogError(ex, "ThirdParty Points/Exchange Error - " + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("Verification/RequestAccessToken")]
        public async Task<ActionResult<LoyaltyThirdPartyRequestAccessTokenOutput>> RequestAccessToken(LoyaltyThirdPartyRequestAccessTokenInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var context = HttpContext;
                var rewardResult = await _loyaltyThirdPartyService.RequestAccessToken(input, context);
                return StatusCode(200, rewardResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ThirdParty RequestAccessToken Error" + JsonConvert.SerializeObject(ex));
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);

                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("Verification/SendOtpConfirmConnect")]
        public async Task<ActionResult<LoyaltyThirdPartySendOtpConfirmConnectOutput>> SendOtpConfirmConnect(LoyaltyThirdPartySendOtpConfirmConnectInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyThirdPartyService.SendOtpConfirmConnect(input, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError(ex, "Send otp confirm error - " + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogError(ex, "Send otp confirm error - " + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("Verification/ConfirmConnect")]
        public async Task<ActionResult<LoyaltyThirdPartyConfirmConnectOutput>> ConfirmConnect(LoyaltyThirdPartyConfirmConnectInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyThirdPartyService.ConfirmConnect(input, authorization);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError(ex, "Confirm connect error - " + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogError(ex, "Confirm connect error - " + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }

        [HttpPost]
        [Route("UpdatePartnerCaching")]
        public async Task<ActionResult<LoyaltyThirdPartyUpdatePartnerCachingOutput>> UpdatePartnerCaching(LoyaltyThirdPartyUpdatePartnerCachingInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var context = HttpContext;
                var rewardResult = await _loyaltyThirdPartyService.UpdatePartnerCaching(input, context);

                return StatusCode(rewardResult.Result, rewardResult);
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError(ex, "ThirdParty UpdatePartnerCaching Error - " + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    _logger.LogError(ex, "ThirdParty UpdatePartnerCaching Error - " + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("callback-forwarder")]
        [AllowAnonymous]
        public async Task<ActionResult<CallbackForwarderInput>> CallbackForwarder([FromQuery] CallbackForwarderInput input)
        {
            return StatusCode(200, input);
        }
        [HttpGet]
        [Route("skyjoy-callback")]
        [AllowAnonymous]
        public async Task<ActionResult<CallbackForwarderInput>> SkyJoyCallBack([FromQuery] CallbackForwarderInput input)
        {
            _logger.LogInformation(" >> SkyJoyCallBack >> " + input.code?.Substring(0, 10));
            _logger.LogInformation(" >> SkyJoyCallBack >> " + input.state?.Substring(0, 10));
            _logger.LogInformation(" >> SkyJoyCallBack >> " + input.session_state?.Substring(0, 10));
            return StatusCode(200, input);
        }
        [HttpGet]
        [Route("GetLoginUrl")]
        [AllowAnonymous]
        public async Task<ActionResult<GetLoginUrlOutput>> GetLoginUrl([FromQuery] GetLoginUrlInput input)
        {
            var configuredMerchantId = _configuration.GetSection("ThirdPartyMerchant:" + input.MerchantName + ":MerchantId").Value;
            if (configuredMerchantId != (input.MerchantId + ""))
            {
                StatusCode(500,new 
                {
                    Code = 500, Message = "Merchant Id Is Not Configured"
                });
            }
            var AuthUrl = _configuration.GetSection("ThirdPartyMerchant:" + input.MerchantName + ":AuthUrl").Value;
            var apiKey = _configuration.GetSection("ThirdPartyMerchant:" + input.MerchantName + ":ApiKey").Value;
            var AuthScope = _configuration.GetSection("ThirdPartyMerchant:" + input.MerchantName + ":AuthScope").Value;
            var AuthRedirectUrl = _configuration.GetSection("ThirdPartyMerchant:" + input.MerchantName + ":AuthRedirectUrl").Value;
            if (input.MerchantName.ToLower() == "appota")
            {
                AuthUrl += "?client_key=" + apiKey  + "&scope=" + AuthScope + "&response_type=code&redirect_uri=" + AuthRedirectUrl;
            }

            if (string.Equals(input.MerchantName, MERCHANT_Skyjoy, StringComparison.CurrentCultureIgnoreCase))
            {
                AuthUrl = _skyJoyIntegrationService.GetLoginUri();
            }
            return StatusCode(200, new GetLoginUrlOutput()
            {
                AuthUrl = AuthUrl
            });
        }

        [HttpPost]
        [Route("UseCodeToConnectMerchant")]
        public async Task<ActionResult<UseCodeToConnectMerchantOutput>> UseCodeToConnectMerchant(
            [FromBody] UseCodeToConnectMerchantInput input)
        {
            _logger.LogInformation(" >> UseCodeToConnectMerchant >> " + input.MerchantId + " - " + input.MemberCode);
            // API này là API Compact, thực hiện tiếp nhận AuthCode, call sang đối tác để lấy AccessToken, Rồi thực hiện liên kết.
            // NẾu đối tác cần thì call đối tác, Vd Skyjoy thì call API /link-member, 
            // Sau đó save vào PartnerPointCaching
            if (input.MerchantId <= 0 || string.IsNullOrEmpty(input.MemberCode) ||
                string.IsNullOrEmpty(input.Code))
            {
                return StatusCode(400, new
                {
                    Code = "100", Message = "Invalid Input"
                });
            }
            var authorization = Request.Headers["Authorization"].ToString();
            var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
            if (checkAuthen != null)
            {
                return StatusCode(401, checkAuthen);
            }

            var merchantObj = await _rewardMemberService.GetMerchantInfo(new GetMerchantInfoInput()
            {
                MerchantId = input.MerchantId
            });
            if (merchantObj == null || merchantObj.Id == 0)
            {
                return StatusCode(400, new
                {
                    Code = "101", Message = "Invalid Merchant Id"
                });
            }
            var merchantName = merchantObj.Name;

            if (MERCHANT_Skyjoy.ToLower().Equals(merchantName.ToLower()))
            {
                // Make api call to get access token
                var accessTokenObj = await _skyJoyIntegrationService.ExchangeCodeForToken(new SJRequestAccessTokenInput()
                {
                    Code = input.Code // NEED HANDLE EXCEPTION HERE
                });
                if (accessTokenObj != null && !string.IsNullOrEmpty(accessTokenObj.access_token))
                {
                    var linkMemberResponse = await _skyJoyIntegrationService.LinkMember(new SJLinkMemberInput()
                    {
                        partnerMemberId = input.MemberCode, sjToken = accessTokenObj.access_token, profile = new SJLinkMemberInputInner()
                    });
                    if (linkMemberResponse.code != "00")
                    {
                        _logger.LogError(" >> IN CONTROLLER, linkMemberResponse IS NOT SUCCESS. HERE IS IT CONTENT >> " + JsonConvert.SerializeObject(linkMemberResponse));
                        var translatedMessage = SkyjoyContentMessage.GetMessageFromCode(linkMemberResponse.code);
                        return StatusCode(400, new
                        {
                            Code = linkMemberResponse.code, Message = translatedMessage
                        });
                    }
                    var partnerMemberCode = linkMemberResponse.data.skyjoyId;
                    var partnerMemberPhone = linkMemberResponse.data.phone;
                    
                    //await _rewardMemberService.SaveRefreshTokenMobileAPIAppota(new CreateConnectForAppotaXSkyJoyInput()
                    //{
                    //    MemberCode = input.MemberCode,
                    //    MerchantId = input.MerchantId,
                    //    RefreshToken = "NOT AVAILABLE",
                    //    IsChangedLoyalty = false,
                    //    ReferenceData = JsonConvert.SerializeObject(new { Cif = partnerMemberCode, PartnerPhoneNumber = partnerMemberPhone }),
                    //    MemberLoyaltyInfo = new MemberConnectLoyaltyInfoInput()
                    //    {
                    //        Cif = partnerMemberCode, PartnerPhoneNumber = partnerMemberPhone, MemberLoyaltyCode = partnerMemberCode
                    //    },
                    //    CreateMemProfile = "NO",
                    //    ConnectSource = "LynkiDApp"
                    //});

                    await _rewardMemberService.SaveRefreshTokenV2(new RewardMemberSaveRefreshTokenInputV2()
                    {
                        MemberCode = input.MemberCode,
                        MerchantId = input.MerchantId,
                        RefreshToken = "NOT AVAILABLE",
                        IsChangedLoyalty = false,
                        ReferenceData = JsonConvert.SerializeObject(new { Cif = partnerMemberCode, PartnerPhoneNumber = partnerMemberPhone }),
                        ConnectSource = "LynkiDApp",
                        MemberLoyaltyInfo = new MemberConnectLoyaltyInfoInput()
                        {
                            Cif = partnerMemberCode,
                            PartnerPhoneNumber = partnerMemberPhone,
                            MemberLoyaltyCode = partnerMemberCode
                        },
                    });

                    var memberInfor = await _rewardMemberService.GetInfo(new RewardMemberGetInfoInput()
                    {
                        NationalId = input.MemberCode
                    });

                    try
                    {
                       
                        var requestConnect = new AddConnectionRequest()
                        {
                            ConnectedToMerchantId = input.MerchantId,
                            PartnerMemberCode = partnerMemberCode,
                            LynkiDMemberCode = input.MemberCode,
                            LynkiDMemberId = memberInfor?.Id ?? 0
                        };
                        _logger.LogInformation($"UseCodeToConnectMerchant___AddConnection___Input:{JsonConvert.SerializeObject(requestConnect)}");
                        var resultConnect = await _rewardMemberService.AddConnection(requestConnect);
                        _logger.LogInformation($"UseCodeToConnectMerchant___AddConnection___Response:{JsonConvert.SerializeObject(resultConnect)}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"UseCodeToConnectMerchant___AddConnection___Error:{ex.Message}-{ex.StackTrace}");
                    }

                    try
                    {
                        var inputAfterConnect = new RewardSendAckAfterConnectedInput()
                        {
                            MemberId = memberInfor?.Id ?? -1,
                            MerchantId = input.MerchantId,
                            NationalId = input.MemberCode
                        };
                        _logger.LogInformation($"UseCodeToConnectMerchant___RewardSendAckAfterConnected___Input:{JsonConvert.SerializeObject(inputAfterConnect)}");
                        var result = await _rewardMemberService.RewardSendAckAfterConnected(inputAfterConnect);
                        _logger.LogInformation($"UseCodeToConnectMerchant___RewardSendAckAfterConnected___Response:{JsonConvert.SerializeObject(result)}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"UseCodeToConnectMerchant___RewardSendAckAfterConnected___Error:{ex.Message}-{ex.StackTrace}");
                    }

                    return new UseCodeToConnectMerchantOutput()
                    {
                        Code = "00", Message = "Success", ParnterMemberCode = partnerMemberCode
                    };
                }
                else
                {
                    return StatusCode(400, new
                    {
                        Code = accessTokenObj?.code ?? "100", Message =  accessTokenObj?.message?? "Có lỗi xảy ra, vui lòng thử lại sau!"
                    });
                }
            }
            throw new Exception("MerchantNotSupported");
        }
        [HttpPost]
        [Route("ExchangeCodeForToken")]
        public async Task<ActionResult<ExchangeCodeForTokenOutput>> ExchangeCodeForToken([FromBody] ExchangeCodeForTokenInput input)
        {
            var authorization = Request.Headers["Authorization"].ToString();
            var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
            if (checkAuthen != null)
            {
                return StatusCode(401, checkAuthen);
            }
            var configuredMerchantId = _configuration.GetSection("ThirdPartyMerchant:" + input.MerchantName + ":MerchantId").Value;
            if (configuredMerchantId != (input.MerchantId + ""))
            {
                return StatusCode(400, new 
                {
                    code = 400,
                    message = "Merchant Name and Merchant Id not match"
                });
            }
            var apiKey = _configuration.GetSection("ThirdPartyMerchant:" + input.MerchantName + ":ApiKey").Value;
            var ExchangeCodeForToken = _configuration.GetSection("ThirdPartyMerchant:" + input.MerchantName + ":ExchangeCodeForToken").Value;
            if (input.MerchantName.ToLower() == "appota")
            {
                ExchangeCodeForToken += "?client_key=" + apiKey  + "&code=" + input.Code + "&grant_type=authorization_code";
            }

            try
            {
                var response = await _commonHelperService.GetAccessTokenFromCode(ExchangeCodeForToken);
                // Get CIF cuar appota de lay
                var viewpointResultTogetCif =
                    await _loyaltyThirdPartyService.PointView(new LoyaltyThirdPartyPointViewInput()
                    {
                        AccessToken = response.data.access_token, IdNumber = "X", MemberCode = input.MemberCode, MerchantId = input.MerchantId
                    }, HttpContext);
                var foundCif = viewpointResultTogetCif.Result?.Member?.MemberCode ?? "";
                var checkExist = await _rewardMemberService.CheckCifExistWithMerchant(foundCif, input.MerchantId);
                if (checkExist.IsInUse)
                {
                    return StatusCode(400, new 
                    {
                        code = 400,
                        message = "Tài khoản Appota của bạn hiện đang kết nối với một tài khoản LynkiD khác. Vui lòng thử lại với tài khoản mới hoặc liên hệ LynkiD để được hỗ trợ."
                    });
                }
                var operatorRes = await _rewardMemberService.SaveRefreshTokenMobileAPIAppota(new CreateConnectForAppotaXSkyJoyInput()
                    {
                        MemberCode = input.MemberCode,
                        MerchantId = input.MerchantId,
                        RefreshToken = response.data.refresh_token,
                        IsChangedLoyalty = false,
                        ReferenceData = JsonConvert.SerializeObject(new object()),
                        MemberLoyaltyInfo = new MemberConnectLoyaltyInfoInput()
                        {
                            Cif = foundCif
                        }
                    });
                if (operatorRes.Result != 200)
                {
                    throw new Exception(operatorRes.Message);
                }
                return StatusCode(200, response);
            }
            catch (Exception e)
            {
                _logger.LogError("Error when get access token for " + input.MerchantName + ": " + e.StackTrace);
                return StatusCode(200, new
                {
                    code = 400,
                    message = "Unknown error"
                });
            }
        }

        //[HttpPost]
        //[Route("Points/Revert")]
        //public async Task<ActionResult<LoyaltyThirdPartyRevertPointOutput>> RevertPoint(LoyaltyThirdPartyRevertPointInput input)
        //{
        //    try
        //    {
        //        var result = await _loyaltyThirdPartyService.RevertPoint(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "ThirdParty Points/Revert Error - " + JsonConvert.SerializeObject(res));

        //        return StatusCode(400, res);
        //    }
        //}
        private static string getMessageFromErrorCode(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
            {
                return "";
            }

            switch (code)
            {
                case "MerchantNotExceedMinBalance": 
                    return "Hệ thống tạm thời bị gián đoạn. Bạn vui lòng quay lại sau nhé.";
                case "MerchantNotFound":
                    return "Hệ thống tạm thời bị gián đoạn, vui lòng liên hệ CSKH để được hỗ trợ.";
            }

            return "";
        }
    }
}
