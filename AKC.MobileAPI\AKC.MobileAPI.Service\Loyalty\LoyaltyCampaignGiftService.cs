﻿using AKC.MobileAPI.DTO.Loyalty.CampaignGift;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Amazon.Runtime.Internal.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyCampaignGiftService : BaseLoyaltyService, ILoyaltyCampaignGiftService
    {
        public LoyaltyCampaignGiftService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
            
        }
        public async Task<CheckCampaignGiftScanQrResponse> CheckCampaignGiftScanQr(CheckCampaignGiftScanQrInput input, HttpContext httpContext)
        {
            var response = await PostLoyaltyAsync<CheckCampaignGiftScanQrOutput>(LoyaltyApiUrl.CHECK_CAMPAIGN_GIFT_SCAN_QRCODE, input, httpContext);
            return response.Result;
        }

        public async Task<ScanQRCodeResponse> ScanCampaignQRCode(ScanQRCodeInput input, HttpContext httpContext)
        {
            var response = await PostLoyaltyAsync<ScanQRCodeOutput>(LoyaltyApiUrl.SCAN_QRCODE_GIFT_CAMPAIGN, input, httpContext);
            return response.Result;
        }
    }
}
