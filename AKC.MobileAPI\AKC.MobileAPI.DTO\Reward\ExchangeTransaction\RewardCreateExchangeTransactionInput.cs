﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.ExchangeTransaction
{
    public class RewardCreateExchangeTransactionInput
    {
        public string TransactionCode { get; set; }
        public int MerchantId { get; set; }
        public string MemberCode { get; set; }
        public double ExchangeAmount { get; set; }
        public string PartnerBindingTxId { get; set; }
    }

    public class RewardCreateExchangeTransactionDto
    {
        public string NationalId { get; set; }
        public int MerchantId { get; set; }
        public string TransactionCode { get; set; }
        public double ExchangeAmount { get; set; }
        public string PartnerBindingTxId { get; set; }
    }
}
