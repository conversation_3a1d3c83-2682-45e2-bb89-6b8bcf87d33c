﻿// using System;
// using System.Threading.Tasks;
// using AKC.MobileAPI.DTO.Reward.TopUpTransaction;
// using AKC.MobileAPI.Service.Abstract.Reward;
// using AKC.MobileAPI.Service.Common;
// using AKC.MobileAPI.Service.Exceptions;
// using AKC.MobileAPI.Service.Helpers;
// using Microsoft.AspNetCore.Authorization;
// using Microsoft.AspNetCore.Mvc;
// using Microsoft.Extensions.Logging;
// using Newtonsoft.Json;
//
// namespace AKC.MobileAPI.Controllers.Reward
// {
//     [Route("api/TopUpTransaction")]
//     [ApiController]
//     [ApiConventionType(typeof(DefaultApiConventions))]
//     [Authorize]
//     public class RewardTopUpTransactionController : ControllerBase
//     {
//         private readonly ILogger _logger;
//         private readonly IExceptionReponseService _exceptionReponseService;
//         private readonly IRewardTopUpTransactionService _rewardTopUpTransactionService;
//         private readonly ICommonHelperService _commonHelperService;
//         public RewardTopUpTransactionController(
//             ILogger<RewardTopUpTransactionController> logger,
//             IExceptionReponseService exceptionReponseService,
//             IRewardTopUpTransactionService rewardTopUpTransactionService,
//             ICommonHelperService commonHelperService)
//         {
//             _logger = logger;
//             _exceptionReponseService = exceptionReponseService;
//             _rewardTopUpTransactionService = rewardTopUpTransactionService;
//             _commonHelperService = commonHelperService;
//         }
//
//         [HttpPost]
//         [Route("Create")]
//         public async Task<ActionResult<RewardCreateTopUpTransactionOutput>> CreateTopUpTransaction(RewardCreateTopUpTransactionInput input)
//         {
//             try
//             {
//                 var authorization = Request.Headers["Authorization"].ToString();
//                 var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
//                 if (checkAuthen != null)
//                 {
//                     return StatusCode(401, checkAuthen);
//                 }
//                 _logger.LogInformation("topup request " + input.MemberCode + "_" + JsonConvert.SerializeObject(input));
//                 var result = await _rewardTopUpTransactionService.CreateTopUpTransaction(input, null);
//                 _logger.LogInformation("topup response result " + input.MemberCode + "_" + JsonConvert.SerializeObject(result));
//                 return StatusCode(200, result);
//             }
//             catch (Exception ex)
//             {
//                 _logger.LogError(ex, "topup response error " + input.MemberCode + "_" + JsonConvert.SerializeObject(ex));
//                 var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
//                 return StatusCode(400, res);
//             }
//         }
//     }
// }
