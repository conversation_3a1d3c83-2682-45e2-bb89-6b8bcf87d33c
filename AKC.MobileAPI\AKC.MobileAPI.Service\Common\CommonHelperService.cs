﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.Reward;
using Microsoft.Extensions.Caching.Distributed;
using System;
using System.Net.Http;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.Common
{
    public class CommonHelperService : ICommonHelperService
    {
        private readonly IDistributedCache _cache;
        private readonly IValidationMemberCode _validationMemberCode;
        protected readonly HttpClient _client = new HttpClient();
        public CommonHelperService(
            IDistributedCache cache,
            IValidationMemberCode validationMemberCode)
        {
            _cache = cache;
            _client.Timeout = TimeSpan.FromSeconds(300);
            _validationMemberCode = validationMemberCode;
        }

        public ResponseDataInvalidTokenDto GetResponseInvalidToken(string authorization, string memberCodeInput)
        {
            var checkValidationMemberCode = _validationMemberCode.ValidateToken(authorization, memberCodeInput).Result;
            if (checkValidationMemberCode)
            {
                return null;
            }
            return new ResponseDataInvalidTokenDto()
            {
                Code = "Unauthorized",
                Message = "Unauthorized access data",
                Result = 401,
            };
        }

        public ResponseDataInvalidTokenDto GetResponseEmptyDataHeader()
        {
            return new ResponseDataInvalidTokenDto()
            {
                Code = "InvalidDataHeader",
                Message = "Invalid data header",
                Result = 401,
            };
        }

        public  async Task<ExchangeCodeForTokenOutput> GetAccessTokenFromCode(string fullUrl)
        {
            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };
            req.RequestUri = new Uri(fullUrl);
            var response = await _client.SendAsync(req);
            var rawData = await response.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<ExchangeCodeForTokenOutput>(rawData);
            return result;
        }

        public void ApplyDecimalFormatting(object obj)
        {
            var properties = obj.GetType().GetProperties();

            foreach (var property in properties)
            {
                if (property.PropertyType == typeof(decimal))
                {
                    var currentValue = (decimal)property.GetValue(obj);

                    var formattedValue = RemoveTrailingZerosDecimal(currentValue);

                    property.SetValue(obj, formattedValue);
                }
            }
        }

        private static decimal RemoveTrailingZerosDecimal(decimal value)
        {
            return decimal.Parse(value.ToString("0.###############################"));
        }


    }
}
