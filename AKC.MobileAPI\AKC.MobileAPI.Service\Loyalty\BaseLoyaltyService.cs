﻿using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.CronServices;
using AKC.MobileAPI.Service.Exceptions;
using Cronos;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Net.Http.Headers;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class BaseLoyaltyService
    {

        protected readonly HttpClient _client = new HttpClient();
        protected readonly IConfiguration _configuration;
        private readonly string baseURL;
        protected readonly int tenantId;
        private readonly string defaultUserName;
        private readonly string defaultPassowrd;
        protected readonly IDistributedCache _cache;

        public BaseLoyaltyService(IConfiguration configuration, IDistributedCache cache)
        {
            _client.Timeout = TimeSpan.FromSeconds(300);
            _configuration = configuration;
            baseURL = _configuration.GetSection("Loyalty:RemoteURL").Value;
            tenantId = Convert.ToInt32(_configuration.GetSection("Loyalty:TenantId").Value);
            defaultUserName = _configuration.GetSection("Loyalty:Username").Value;
            defaultPassowrd = _configuration.GetSection("Loyalty:Password").Value;
            _cache = cache;
        }

        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetLoyaltyAsync<T>(string apiURL, object query = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };

            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            var token = GetAccessToken();
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();


                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Perform a DELETE obj to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> DeleteLoyaltyAsync<T>(string apiURL, object query = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var requestURL = $"{baseURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Delete
            };

            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            var token = GetAccessToken();
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();


                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Convert a object to query string format.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string GetQueryString(object obj)
        {
            var properties = from p in obj.GetType().GetProperties()
                             where p.GetValue(obj, null) != null
                             select p.Name + "=" + HttpUtility.UrlEncode(p.GetValue(obj, null).ToString());

            return string.Join("&", properties.ToArray());
        }

        public async Task<T> PostLoyaltyAsync<T>(string apiURL, object body = null, HttpContext request = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            var Client_Request_Address = request != null && request.Request.Headers.ContainsKey("Client-Request-Address") ?
                                            request?.Request.Headers["Client-Request-Address"].ToString() : request?.Connection.RemoteIpAddress.ToString();
            if (request != null && request.Request != null && request.Request.Headers.ContainsKey("Accept-Language"))
            {
                var headerValue = request.Request.Headers["Accept-Language"].ToString();
                req.Headers.TryAddWithoutValidation("Accept-Language", headerValue);
            }
            req.Headers.Add("Client-Request-Address", Client_Request_Address);
            req.Headers.Add("Abp.TenantId", tenantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
            try
            {
                var response = await _client.SendAsync(req, cts.Token);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone, cts.Token);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (HttpRequestException ex) when (ex.Message.Contains("timeout") || ex.Message.Contains("timed out"))
            {
                // HttpRequestException với timeout message
                throw new LoyaltyTimeoutException("HttpRequestException Timeout with API: " + req.RequestUri, req.RequestUri.ToString(), ex);
            }
            catch (TimeoutException ex)
            {
                // Direct TimeoutException
                throw new LoyaltyTimeoutException("Direct Timeout with API: " + req.RequestUri, req.RequestUri.ToString(), ex);
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                // Kiểm tra cả 2 loại timeout:
                // 1. CancellationToken timeout (cts.Token.IsCancellationRequested = true)
                // 2. HttpClient timeout (ex.CancellationToken != cts.Token hoặc ex.CancellationToken.IsCancellationRequested = false)

                if (cts.Token.IsCancellationRequested)
                {
                    // CancellationToken timeout (30s từ CancellationTokenSource)
                    throw new LoyaltyTimeoutException("CancellationToken Timeout with API: " + req.RequestUri, req.RequestUri.ToString(), ex);
                }
                else if (ex.InnerException is TimeoutException ||
                         ex.CancellationToken == CancellationToken.None ||
                         !ex.CancellationToken.IsCancellationRequested)
                {
                    // HttpClient timeout (30s từ _client.Timeout)
                    throw new LoyaltyTimeoutException("HttpClient Timeout with API: " + req.RequestUri, req.RequestUri.ToString(), ex);
                }
                else
                {
                    // Cancelled for other reasons (manual cancellation, etc.)
                    throw new Exception("Cancelled for some other reason: " + ex.Message, ex);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<T> PutLoyaltyAsync<T>(string apiURL, object body = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Put
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GetAccessToken());
            req.RequestUri = new Uri($"{baseURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetAccessToken(true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Get a new accessToken form Loyalty.
        /// </summary>
        /// <returns></returns>
        private string GetAccessToken(bool mustResetCache = false)
        {
            var token = _cache.GetString(CommonConstants.ACCESSS_TOKEN_CACHE_KEY);
            var configuration = AccessConfigurationService.Instance.GetConfiguration();
            var expression = CronExpression.Parse(configuration.GetSection("Loyalty:CronExpressionRefreshToken").Value);
            var timeZoneInfo = TimeZoneInfo.Local;

            // Request body
            if (string.IsNullOrEmpty(token) || mustResetCache)
            {
                return LoyaltyHelper.RenewAccessTokenCacheValue(_cache, CronHelper.GetDelayToNextRefreshToken(expression, timeZoneInfo), mustResetCache);
            }

            return token;
        }

        public async Task<string> GetCacheValueByKey(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return "";
            }
            var ret = await _cache.GetStringAsync(key) ?? "";
            return ret;
        }
        public async Task SetCacheByKeyValue(string key, string valuee, int timeInDay = 1)
        {
            if (string.IsNullOrWhiteSpace(key) || string.IsNullOrWhiteSpace(valuee))
            {
                return;
            }
            var cacheOption = new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromDays(timeInDay));
            await _cache.SetStringAsync(key, valuee, cacheOption);
        }

        public async Task SetCacheByKeyValueExpiredInMinute(string key, string value, int timeInMinute = 30)
        {
            if (string.IsNullOrWhiteSpace(key) || string.IsNullOrWhiteSpace(value))
            {
                return;
            }
            var cacheOption = new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(timeInMinute));
            await _cache.SetStringAsync(key, value, cacheOption);
        }

        public async Task<VerifyTokenResponseDTO> GetUserByFirebaseAccessToken(string accessToken)
        {
            var auth = FirebaseAdmin.Auth.FirebaseAuth.DefaultInstance;
            var rs = await auth.VerifyIdTokenAsync(accessToken);

            var result = new VerifyTokenResponseDTO()
            {
                Email = rs.Claims["email"].ToString(),
                ExpirationTimeSeconds = rs.ExpirationTimeSeconds,
                IssuedAtTimeSeconds = rs.IssuedAtTimeSeconds,
                PhoneNumber = rs.Claims["phone_number"].ToString(),
                UserId = rs.Claims["user_id"].ToString(),
            };

            return result;
        }

        public async Task<string> GetNational(string tokenBearer)
        {
            var idToken = tokenBearer.Replace("Bearer ", "").Replace("bearer ", "");
            var token = await FirebaseAdmin.Auth.FirebaseAuth.DefaultInstance.VerifyIdTokenAsync(idToken);
            return token.Uid;
        }

        //Clone a HttpRequest
        protected HttpRequestMessage CloneHttpRequest(HttpRequestMessage req)
        {
            HttpRequestMessage clone = new HttpRequestMessage(req.Method, req.RequestUri);

            clone.Content = req.Content;
            clone.Version = req.Version;

            foreach (KeyValuePair<string, object> prop in req.Properties)
            {
                clone.Properties.Add(prop);
            }

            foreach (KeyValuePair<string, IEnumerable<string>> header in req.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            return clone;
        }
        
        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetLoyaltyCustomMerchantAsync<T>(string apiURL, object query = null, string merchantName = "")
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }


            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };

            var accessToken = GetAccessTokenCustomized(merchantName);
            req.Headers.Add("Abp.TenantId", accessToken.TenantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken.AccessToken);
            var requestURL = $"{accessToken.BaseUrl}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    accessToken = GetAccessTokenCustomized(merchantName, true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken.AccessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();


                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }
        protected async Task<T> PostLoyaltyCustomMerchantAsync<T>(string apiURL, object body = null,
            HttpContext request = null, string merchantName = "")
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("Not allow type of T is string");
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            var Client_Request_Address = request != null && request.Request.Headers.ContainsKey("Client-Request-Address") ?
                                            request?.Request.Headers["Client-Request-Address"].ToString() : request?.Connection.RemoteIpAddress.ToString();
            req.Headers.Add("Client-Request-Address", Client_Request_Address);
            var accessToken = GetAccessTokenCustomized(merchantName);
            req.Headers.Add("Abp.TenantId", accessToken.TenantId.ToString());
            req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken.AccessToken);
            req.RequestUri = new Uri($"{accessToken.BaseUrl}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    accessToken = GetAccessTokenCustomized(merchantName, true);
                    req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken.AccessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        
        
        protected MerchantTenantMapping GetAccessTokenCustomized(string merchantName, bool forceNew = false)
        {
            var merchantTenantMapping = new MerchantTenantMapping();
            var token = _cache.GetString(CommonConstants.ACCESS_TOKEN_BY_MERCHANT + merchantName);
            var baseURL = _configuration.GetSection("MerchantUseLoyalty:" + merchantName + ":RemoteURL").Value;
            var tenantId = Convert.ToInt32(_configuration.GetSection("MerchantUseLoyalty:" + merchantName + ":TenantId").Value);
            merchantTenantMapping.AccessToken = token;
            merchantTenantMapping.BaseUrl = baseURL;
            merchantTenantMapping.TenantId = tenantId;
            if (!string.IsNullOrEmpty(token) && !forceNew) return merchantTenantMapping;
            // Gọi sang loyalty để get
            var defaultUserName = (_configuration.GetSection("MerchantUseLoyalty:" + merchantName + ":Username").Value);
            var defaultPassword = (_configuration.GetSection("MerchantUseLoyalty:" + merchantName + ":Password").Value);

            var res =
                LoyaltyHelper.LoginLoyalty(tenantId, baseURL, defaultUserName, defaultPassword);
            var cacheAccessTokenOptions = new DistributedCacheEntryOptions()
                .SetAbsoluteExpiration(TimeSpan.FromHours(1));
            _cache.SetString(CommonConstants.ACCESS_TOKEN_BY_MERCHANT + merchantName, res?.Result?.AccessToken, cacheAccessTokenOptions);
            merchantTenantMapping.AccessToken = res?.Result?.AccessToken;
            return merchantTenantMapping;
        }
        
        // COMMON LOYALTY ACCESS
        private IntegrationPartnerShortDto GetIntegrationPartnerShortDto(int customTenantId)
        {
            if (customTenantId < 0)
            {
                return null;
            }
            var ret = new IntegrationPartnerShortDto();
            ret.BASEURL = _configuration.GetSection($"CommonLoyalty{customTenantId}:RemoteURL").Value;
            ret.U = _configuration.GetSection($"CommonLoyalty{customTenantId}:Username").Value;
            ret.P = _configuration.GetSection($"CommonLoyalty{customTenantId}:Password").Value;
            ret.T = customTenantId;
            return ret.IsValid() == false ? null : ret;
        }
        

        /// <summary>
        /// Perform a GET request to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> GetCommonLoyaltyLoyaltyAsync<T>(string apiURL, int customTenantId, object query = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("CallCommonLoy >> Not allow type of T is string");
            }

            var cfg = GetIntegrationPartnerShortDto(customTenantId);
            if (cfg == null)
            {
                throw new Exception("CallCommonLoy >> Tenant Id not configured >> " + customTenantId);
            }

            var requestURL = $"{cfg.BASEURL}/{apiURL}";

            if (query != null)
            {
                requestURL = $"{requestURL}?{GetQueryString(query)}";
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Get
            };

            req.Headers.Add("Abp.TenantId", customTenantId + "");
            var token = GetCommonLoyaltyAccessToken(cfg);
            req.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri(requestURL);
            var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
            try
            {
                var response = await _client.SendAsync(req, cts.Token);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    var accessToken = GetCommonLoyaltyAccessToken(cfg, true);
                    req.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();


                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("CallCommonLoy >> Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("CallCommonLoy >> Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Perform a DELETE obj to loyalty server.
        /// </summary>
        /// <param name="apiURL"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<T> DeleteCommonLoyaltyLoyaltyAsync<T>(string apiURL, int customTenantId, object body = null, HttpContext request = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("CallCommonLoy >> Not allow type of T is string");
            }
            var cfg = GetIntegrationPartnerShortDto(customTenantId);
            if (cfg == null)
            {
                throw new Exception("CallCommonLoy >> Tenant Id not configured >> " + customTenantId);
            }
            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Delete
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            req.Headers.Add("Abp.TenantId", customTenantId + "");
            var token = GetCommonLoyaltyAccessToken(cfg);
            req.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
            req.RequestUri = new Uri($"{cfg.BASEURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetCommonLoyaltyAccessToken(cfg, true);
                    req.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();


                // Convert response to result object which is a instance of 'T'.
                var result = JsonConvert.DeserializeObject<T>(rawData);
                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("CallCommonLoy >> Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("CallCommonLoy >> Cancelled for some other reason: ", ex);
                }
            }
        }

        public async Task<T> PostCommonLoyaltyLoyaltyAsync<T>(string apiURL, int customTenantId, object body = null, HttpContext request = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("CallCommonLoy >> Not allow type of T is string");
            }
            var cfg = GetIntegrationPartnerShortDto(customTenantId);
            if (cfg == null)
            {
                throw new Exception("CallCommonLoy >> Tenant Id not configured >> " + customTenantId);
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Post
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
            var Client_Request_Address = request != null && request.Request.Headers.ContainsKey("Client-Request-Address") ?
                                            request?.Request.Headers["Client-Request-Address"].ToString() : request?.Connection.RemoteIpAddress.ToString();
            req.Headers.Add("Client-Request-Address", Client_Request_Address);
            req.Headers.Add("Abp.TenantId", customTenantId + "");
            req.Headers.Authorization = new AuthenticationHeaderValue("Bearer", GetCommonLoyaltyAccessToken(cfg));
            req.RequestUri = new Uri($"{cfg.BASEURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetCommonLoyaltyAccessToken(cfg, true);
                    req.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("CallCommonLoy >> Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("CallCommonLoy >> Cancelled for some other reason: ", ex);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<T> PutCommonLoyaltyLoyaltyAsync<T>(string apiURL, int customTenantId, object body = null)
        {
            if (typeof(T) == typeof(string))
            {
                throw new Exception("CallCommonLoy >> Not allow type of T is string");
            }
            var cfg = GetIntegrationPartnerShortDto(customTenantId);
            if (cfg == null)
            {
                throw new Exception("CallCommonLoy >> Tenant Id not configured >> " + customTenantId);
            }

            var req = new HttpRequestMessage
            {
                Method = HttpMethod.Put
            };

            // Add request body
            if (body != null)
            {
                var requestBody = JsonConvert.SerializeObject(body, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                req.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }

            req.Headers.Authorization = new AuthenticationHeaderValue("Bearer", GetCommonLoyaltyAccessToken(cfg));
            req.RequestUri = new Uri($"{cfg.BASEURL}/{apiURL}");
            var cts = new CancellationTokenSource();
            try
            {
                var response = await _client.SendAsync(req);
                var rawData = await response.Content.ReadAsStringAsync();

                //Recall API when Unauthorized
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    string accessToken = GetCommonLoyaltyAccessToken(cfg, true);
                    req.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                    var reqClone = CloneHttpRequest(req);

                    response = await _client.SendAsync(reqClone);
                    rawData = await response.Content.ReadAsStringAsync();
                }
                //End Recall API when Unauthorized

                if (response.IsSuccessStatusCode == false)
                {
                    var ex = new LoyaltyException();
                    ex.Data.Add("ErrorData", rawData);
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        ex.Data.Add("StatusCode", 400);
                    }
                    else
                    {
                        ex.Data.Add("StatusCode", 500);
                    }
                    throw ex;
                }

                response.EnsureSuccessStatusCode();

                // Get respone result.
                var result = JsonConvert.DeserializeObject<T>(rawData);

                return result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (TaskCanceledException ex)
            {
                if (!cts.Token.IsCancellationRequested)
                {

                    throw new Exception("Timed Out with API: " + req.RequestUri, ex);
                }
                else
                {
                    throw new Exception("Cancelled for some other reason: ", ex);
                }
            }
        }

        /// <summary>
        /// Get a new accessToken form Loyalty.
        /// </summary>
        /// <returns></returns>
        private string GetCommonLoyaltyAccessToken(IntegrationPartnerShortDto cfg, bool mustResetCache = false)
        {
            var cKey = CommonConstants.COMMONLOYACCESSS_TOKEN_CACHE_KEY + "_" + cfg.T;
            var token = _cache.GetString(cKey);
            // Request body
            if (string.IsNullOrEmpty(token) || mustResetCache)
            {
                LoyaltyLoginDTO loyaltyLoginDTO = new LoyaltyLoginDTO()
                {
                    UserNameOrEmailAddress = cfg.U,
                    Password = cfg.P
                };

                var body = JsonConvert.SerializeObject(loyaltyLoginDTO, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

                // Setup request.
                var req = new HttpRequestMessage { Method = HttpMethod.Post };
                req.Headers.Add("Abp.TenantId", cfg.T.ToString());
                req.RequestUri = new Uri($"{cfg.BASEURL}/TokenAuth/Authenticate");
                req.Content = new StringContent(body, Encoding.UTF8, "application/json");

                // Get Response.
                var response = _client.SendAsync(req).Result;
                var rawData = response.Content.ReadAsStringAsync().Result;

                // Make sure success status code.
                response.EnsureSuccessStatusCode();

                // Get respone result.
                var extracted = JsonConvert.DeserializeObject<LoyaltyLoginResponse>(rawData);
                token = extracted.Result.AccessToken;
                _cache.SetString(cKey, token, new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(30)));
            }

            return token;
        }
        protected bool CheckTenantIsInternal(int? merchantId)
        {
            var tenantIdInternal = Convert.ToInt32(_configuration.GetSection("RewardVPID:MerchantId").Value);
            if (!merchantId.HasValue || tenantIdInternal == merchantId.Value || merchantId.Value <= 0)
            {
                return true;
            }
            return false;
        }
    }
    public class MerchantTenantMapping
    {
        public string MerchantName { get; set; }
        public int TenantId {get; set; }
        public int MerchantId {get;set;}
        public string BaseUrl {get;set;}
        public string AccessToken { get; set; }
    }
    public class IntegrationPartnerShortDto 
    {
        public string BASEURL { get; set; } // URL
        public string U { get; set; } // Username
        public string P { get; set; } // Password
        public int T { get; set; } // TenantId

        public bool IsValid()
        {
            return T > 0
                   && !string.IsNullOrEmpty(U)
                   && !string.IsNullOrEmpty(P)
                   && !string.IsNullOrEmpty(BASEURL);
        }
    }
}
