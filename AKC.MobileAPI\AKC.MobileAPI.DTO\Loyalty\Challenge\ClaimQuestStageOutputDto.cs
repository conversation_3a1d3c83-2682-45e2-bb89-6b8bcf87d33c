﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class ClaimQuestStageOutputDto
    {
        public ListResultClaimQuestStage Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class ListResultClaimQuestStage
    {
        public decimal QuestStageRewardedCoin { get; set; }

        public decimal QuestStageRewardedPoint { get; set; }

    }
}
