﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Payment
{
    public class RewardCheckReturnPaymentLinkOutput
    {
        public int Result { get; set; }
        public string Message { get; set; }
        public string MessageDetail { get; set; }
        public RewardCheckReturnPaymentLinkItem Items { get; set; }
    }

    public class RewardCheckReturnPaymentLinkItem
    {
        public bool PaymentSuccess { get; set; }
        public bool ReturnValid { get; set; }
        public string PaymentCode { get; set; }
        public string PaymentStatusCode { get; set; }
        public string TransactionCode { get; set; }
        public string InternalTransactionCode { get; set; }
    }
}
