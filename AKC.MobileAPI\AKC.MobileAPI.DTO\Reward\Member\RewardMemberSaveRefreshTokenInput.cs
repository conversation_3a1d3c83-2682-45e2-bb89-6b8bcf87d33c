﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class CreateConnectForAppotaXSkyJoyInput: RewardMemberSaveRefreshTokenInput
    {
        public string ConnectSource { get; set; }
    }
    public class RewardMemberSaveRefreshTokenInput
    {
        public string MemberCode { get; set; }

        public int MerchantId { get; set; }

        public string RefreshToken { get; set; }
        public bool IsChangedLoyalty { get; set; }
        public string ReferenceData { get; set; }
        public string CreateMemProfile { get; set; }
        public MemberConnectLoyaltyInfoInput MemberLoyaltyInfo { get; set; }
    }

    public class MemberConnectLoyaltyInfoInput
    {
        public string Cif { get; set; }
        public string MemberLoyaltyCode { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Email { get; set; }
        public string Gender { get; set; } = "O";
        public DateTime? Dob { get; set; }
        public string Type { get; set; } = "Member";
        public string RegionCode { get; set; }
        public string FullRegionCode { get; set; }
        public string MemberTypeCode { get; set; }
        public string FullMemberTypeCode { get; set; }
        public string ChannelType { get; set; }
        public string FullChannelTypeCode { get; set; }
        public string RankTypeCode { get; set; }
        public string StandardMemberCode { get; set; }
        public string IdCard { get; set; }
        public string PartnerPhoneNumber { get; set; }
        public string Phone { get; set; }
        public string Avatar { get; set; }
        public string Segment { get; set; }
        public string VipType { get; set; }
    }
}
