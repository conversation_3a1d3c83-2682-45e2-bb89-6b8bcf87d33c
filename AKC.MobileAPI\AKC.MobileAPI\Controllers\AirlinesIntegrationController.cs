﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Controllers
{
    [Route("api/PartnerExchange")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class AirlinesIntegrationController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionResponseService;
        private readonly ILoyaltyLanguageService _loyaltyLanguageService;
        private readonly IRewardCreateExchangeAndRedeemService _rewardCreateExchangeAndRedeemService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IRewardMemberService _rewardMemberService;
        public AirlinesIntegrationController(
            ILogger<AirlinesIntegrationController> logger,
            IRewardMemberService x,
            IExceptionReponseService expr,
            ILoyaltyLanguageService loyaltyLanguageService,
            IRewardCreateExchangeAndRedeemService rewardCreateExchangeAndRedeemService,
            IDistributedCache c,
            ILoyaltyGiftService l,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _exceptionResponseService = expr;
            _loyaltyLanguageService = loyaltyLanguageService;
            _rewardCreateExchangeAndRedeemService = rewardCreateExchangeAndRedeemService;
            _commonHelperService = commonHelperService;
            _rewardMemberService = x;
        }

        [HttpPost]
        [Route("Exchange")]
        public async Task<ActionResult<LoyaltyThirdPartyPointExchangeOutput>> Exchange(CreateExchangeMilesAirlineRequest input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _rewardCreateExchangeAndRedeemService.CreateExchangeMilesAirline(input);
                _logger.LogInformation(">> Exchange " + input.MemberCode + "_Response" + JsonConvert.SerializeObject(result));
                if(result.ErrorCode != "00")
                {
                    return StatusCode(400, result);
                }    
                return new LoyaltyThirdPartyPointExchangeOutput()
                {
                    Success = true, Error = "", Result = 200, Items = new LoyaltyThirdPartyPointExchangeResult()
                    {
                        Transaction = new LoyaltyThirdPartyPointExchangeItem()
                        {
                            PartnerBindingTxId = result.Result.PartnerTransactionCode, LinkIDOrderCode = result.Result.LynkIdTransactionCode
                            , ExchangeAmount = (long) result.Result.UsedToken, EquivalentTokenAmount = result.Result.ExchangedValue
                        }
                    }
                };
               
            }
            catch (Exception ex)
            {

                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionResponseService.GetExceptionRewardReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogError(">> Exchange " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));
                    if (res.Code == "SystemProcessData" && res.Message == "BadRequest" && res.MessageDetail is string)
                    {
                        res.MessageDetail = res.MessageDetail.ToString();
                        res.Message = "Có lỗi xảy ra khi thực hiện trừ điểm LynkiD. Vui lòng thử lại sau hoặc liên hệ tổng đài.";
                    }

                    if (res.Code == "MerchantExchangeConfigLimitPerTime")
                    {
                        res.Code = AirlineIntegrationErrorCodes.MerchantExchangeConfigLimitPerTime;
                        res.Message = "Số điểm LynkiD không được vượt quá giới hạn của merchant";
                    }

                    if (res.Code == "MerchantExchangeConfigMinPerTime")
                    {
                        res.Code = AirlineIntegrationErrorCodes.MerchantExchangeConfigMinPerTime;
                        res.Message = "Số điểm LynkiD không được nhỏ hơn giá trị tối thiểu khi đổi điểm của merchant";
                    }

                    if (res.Code == "MerchantExchangeConfigLimitPerMonth")
                    {
                        res.Code = AirlineIntegrationErrorCodes.MerchantExchangeConfigLimitPerMonth;
                        res.Message = "Mechant đã tới hạn mức của việc đổi điểm của tháng này";
                    }

                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogError(">> Exchange >> " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }

        [HttpGet]
        [Route("GetExchangeTransactions")]
        public async Task<ActionResult<GetListMileExchangeTransactionRespone>> GetExchangeTransactions([FromQuery] GetListMileExchangeTransactionRequest input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }

                if (input.SkipCount < 0)
                {
                    input.SkipCount = 0;
                }

                if (input.MaxResultCount <= 0 || input.MaxResultCount >= 50)
                {
                    input.MaxResultCount = 50;
                }

                var listVendors = await _rewardCreateExchangeAndRedeemService.GetExchangeVendors();

                var memberObject = await _rewardMemberService.GetInfo(new RewardMemberGetInfoInput()
                    { NationalId = input.MemberCode });
                
                var result = await _rewardMemberService.GetTokenTransByMemberId(new RewardMemberTokenTransGetByIdInput()
                {
                    ActionTypeFilter = "PayByToken", ActionCodeFilter = "RedeemExchange", NationalId = input.MemberCode, SkipCount = input.SkipCount, MaxResultCount = input.MaxResultCount,
                    MerchantIdFilter = input.MerchantId
                });
                _logger.LogInformation("GetExchangeTransactions " + input.MemberCode + "_Response" + JsonConvert.SerializeObject(result));
                var retList = new List<MileExchangeTransaction>();
                foreach (var xResultItem in result.Items)
                {
                    var subItem = new MileExchangeTransaction()
                    {
                        AirlineCode = listVendors.Where(x => x.MerchantId == input.MerchantId).Select(x => x.VendorName).FirstOrDefault(),
                        MemberCode = input.MemberCode, MemberName = memberObject.Name, TransactionCode = xResultItem.OrderCode, AirlineTransactionCode = xResultItem.PartnerBindingTxId, 
                    };
                    retList.Add(subItem);
                }

                return new GetListMileExchangeTransactionRespone()
                {
                    totalCount = result.TotalCount, items = retList
                };
            }
            catch (Exception ex)
            {
                var res = await _exceptionResponseService.GetExceptionLoyaltyReponse(ex);
                var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                res.ListMessages = listMessages;
                _logger.LogInformation("GetExchangeTransactions " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }

        
    }
}

