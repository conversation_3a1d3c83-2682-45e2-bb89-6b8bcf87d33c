﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.ThirdParty;
using AKC.MobileAPI.DTO.Reward.Member;
using AKC.MobileAPI.DTO.Reward.PartnerPointCaching;
using AKC.MobileAPI.DTO.ThirdParty.VPBank;
using AKC.MobileAPI.DTO.ThirdParty.VPBankLoyaltyExchange;
using AKC.MobileAPI.DTO.ThirdParty.VPBankS;
using AKC.MobileAPI.DTO.User;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Abstract.ThirdParty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.ThirdParty.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AKC.MobileAPI.Service.ThirdParty
{
    public class ThirdPartyVPBankSecuritiesService : BaseThirdPartyVPBankSecuritiesService,
        IThirdPartyVPBankSecuritiesService
    {
        private readonly IRewardExchangeTransactionService _rewardExchangeTransactionService;
        private readonly ILoyaltyAuditLogService _loyaltyAuditLogService;
        private readonly IRewardMemberService _rewardMemberService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly IDistributedCache _cache;

        public ThirdPartyVPBankSecuritiesService(
            IConfiguration configuration,
            ILogger<ThirdPartyVPBankSecuritiesService> logger,
            IRewardExchangeTransactionService rewardExchangeTransactionService,
            IRewardMemberService rewardMemberService,
            ILoyaltyAuditLogService loyaltyAuditLogService,
            IExceptionReponseService exceptionReponseService,
            IDistributedCache cache) : base(configuration, cache,
            logger, loyaltyAuditLogService)
        {
            _rewardExchangeTransactionService = rewardExchangeTransactionService;
            _loyaltyAuditLogService = loyaltyAuditLogService;
            _rewardMemberService = rewardMemberService;
            _exceptionReponseService = exceptionReponseService;
            _cache = cache;
        }

        public async Task<LoyaltyThirdPartyRemoveConnectedMerchantOutput> RemoveConnectedMerchant(
            RewardMemberUpdateOutputDto member, LoyaltyThirdPartyRemoveConnectedMerchant input)
        {
            var retry = 4;
            while (retry != 0)
            {
                try
                {
                    _logger.LogInformation("Remove connected merchant request: " + JsonConvert.SerializeObject(input));
                    // Call api remove
                    await PostLoyaltyAsync<LoyaltyResponse<GetCifCodeByIdCardOutput>>(CommonLoyaltyUrls.RemoveConnect, "RemoveConnect", 
                        member.Item.MemberCode, new
                        {
                            LinkID_MemberID = member.Item.MemberId
                        });
                    retry = 0;
                }
                catch (Exception ex)
                {
                    retry--;
                    _logger.LogInformation("Remove connected merchant fail: " + JsonConvert.SerializeObject(ex));
                }
            }

            return new LoyaltyThirdPartyRemoveConnectedMerchantOutput()
            {
                Status = true
            };
        }

        // NOTE: API này và GET CIF hay VERIFYOPT đang binding với VPBanKS
        // Khi onboard merchabnt mơ2í sẽ phải handle cách thức và tham số khác nhau
        public async Task<LoyaltyThirdPartyVerifyNationalIdOutput> VerifyNationalId(
            LoyaltyThirdPartyVerifyNationalIdInput input, HttpContext context, string authorization)
        {
            // Call sang vpbanks loyalty để get cif code từ IDCard
            var cifObj = await GetLoyaltyAsync<LoyaltyResponse<GetCifCodeByIdCardOutput>>(CommonLoyaltyUrls.GetCifCodeByIdCard, new
            {
                IdCard = input.IdNumber, PhoneNumber = input.PhoneNumber
            });
            var cifCode = cifObj.Result.CifCode;

            await _rewardMemberService.VerifyIsIdCardVerified(new RewardMemberVerifyIsIdCardVerifiedInput()
            {
                MemberCode = input.MemberCode,
                IdCard = input.IdNumber,
                MemberLoyaltyCode = cifCode,
                MerchantId = input.MerchantId,
                CheckHistory = true,
            });
            // Request OTP
            await PostLoyaltyAsync<LoyaltyResponse<VerifyIdCardOutput>>(CommonLoyaltyUrls.RequestOtpDeKetNoi,
                "RequestOTP",
                input.MemberCode, new {IdCard = input.IdNumber, input.PhoneNumber});
            // Do ở vpbank bảng usermaping cần linkid phonenumber nên cần check xem member đó có số điện thoại hay chưa
            var rewardMember = await _rewardMemberService.View(new RewardMemberRequestInput()
            {
                MemberCode = input.MemberCode,
            }, authorization);
            if (string.IsNullOrWhiteSpace(rewardMember.Items.PhoneNumber))
            {
                _logger.LogDebug("Member cannot phone number when connect vpbanks loyalty new");
                return new LoyaltyThirdPartyVerifyNationalIdOutput()
                {
                    Result = null,
                    Error = "VPB0001",
                    Success = false,
                    IsChangedLoyalty = true,
                };
            }

            var cacheOption = new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(10));
            var cacheKey = "VPBSVerifyIdCard_" + input.PhoneNumber + input.IdNumber;
            await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(input), cacheOption);
            return new LoyaltyThirdPartyVerifyNationalIdOutput()
            {
                Result = null,
                Success = true,
                IsChangedLoyalty = true,
            };
        }

        public async Task<LoyaltyThirdPartyVerifyOTPOutput> VerifyOTP(LoyaltyThirdPartyVerifyOTPInput input,
            HttpContext context, string authorization)
        {
            var cacheKey = "VPBSVerifyIdCard_" + input.PhoneNumber + input.IdNumber;
            var cacheDataVerify = await _cache.GetStringAsync(cacheKey);

            if (!string.IsNullOrWhiteSpace(cacheDataVerify))
            {
                var rewardMember = await _rewardMemberService.View(new RewardMemberRequestInput()
                {
                    MemberCode = input.MemberCode,
                }, authorization);
                var response = await PostLoyaltyAsync<LoyaltyResponse<VerifyOTPOutputDto>>(
                    CommonLoyaltyUrls.VerifyOtpDeKetNoi, "VerifyOTP",
                    input.MemberCode, new
                    {
                        IdCard =input.IdNumber, PhoneNumber = input.PhoneNumber, SessionId = "A", 
                        OTPCode = input.OtpNumber, LinkID_PhoneNumber = input.PhoneNumber,
                        LinkID_MemberID = rewardMember.Items.Id, LinkID_WalletAddress = rewardMember.Items.UserAddress,
                        LinkID_MemberCode = rewardMember.Items.NationalId
                    });
                ; // call sang verify
                var referenceDataLoyalty = new ThirdPartyVPBankLoyaltyReferenceData()
                {
                    MemberCode = response.Result.MemberCode,
                    Cif = response.Result.MemberCode,
                };
                var memberLoyaltyInfo = response.Result;
                await _rewardMemberService.VerifyIsIdCardVerified(new RewardMemberVerifyIsIdCardVerifiedInput()
                {
                    MemberCode = input.MemberCode,
                    IdCard = input.IdNumber,
                    MemberLoyaltyCode = memberLoyaltyInfo.Cif,
                    MerchantId = input.MerchantId,
                    CheckHistory = true,
                });
                await _rewardMemberService.SaveRefreshToken(new RewardMemberSaveRefreshTokenInput()
                {
                    MemberCode = input.MemberCode,
                    MerchantId = input.MerchantId,
                    RefreshToken = "RefreshToken",
                    IsChangedLoyalty = true,
                    ReferenceData = JsonConvert.SerializeObject(referenceDataLoyalty),
                    MemberLoyaltyInfo = new MemberConnectLoyaltyInfoInput()
                    {
                        Cif = memberLoyaltyInfo.Cif,
                        MemberLoyaltyCode = memberLoyaltyInfo.MemberCode,
                        Segment = memberLoyaltyInfo.Segment,
                        VipType = memberLoyaltyInfo.VipType,
                        Gender = memberLoyaltyInfo.Gender,
                        FullRegionCode = memberLoyaltyInfo.FullRegionCode,
                        Name = memberLoyaltyInfo.Name,
                        Address = memberLoyaltyInfo.Address,
                        Avatar = memberLoyaltyInfo.Avatar,
                        ChannelType = memberLoyaltyInfo.ChannelType,
                        Dob = memberLoyaltyInfo.Dob.HasValue ? memberLoyaltyInfo.Dob : null,
                        Email = memberLoyaltyInfo.Email,
                        FullChannelTypeCode = memberLoyaltyInfo.FullChannelTypeCode,
                        FullMemberTypeCode = memberLoyaltyInfo.FullMemberTypeCode,
                        IdCard = memberLoyaltyInfo.IdCard,
                        MemberTypeCode = memberLoyaltyInfo.MemberTypeCode,
                        PartnerPhoneNumber = input.PhoneNumber,
                        Phone = memberLoyaltyInfo.Phone,
                        RankTypeCode = memberLoyaltyInfo.Phone,
                        RegionCode = memberLoyaltyInfo.RegionCode,
                        StandardMemberCode = memberLoyaltyInfo.StandardMemberCode,
                        Type = memberLoyaltyInfo.Type,
                    }
                });
                await _cache.RemoveAsync(cacheKey);
                // mark ack flag
                await _rewardMemberService.RewardSendAckAfterConnected(new RewardSendAckAfterConnectedInput()
                {
                    MemberId = rewardMember.Items.Id, MerchantId = input.MerchantId, NationalId = input.MemberCode
                });
                return new LoyaltyThirdPartyVerifyOTPOutput()
                {
                    Success = true,
                    Result = 200,
                    Items = new LoyaltyThirdPartyVerifyOTPResult()
                    {
                        Member = new LoyaltyThirdPartyVerifyOTPItem()
                        {
                            Name = rewardMember.Items.Name,
                            PartnerBalance = 0
                        }
                    }
                };
            }
            else
            {
                GetLoyaltyErrorValidation("5008", "Mã OTP hết hạn. Vui lòng thử lại");
            }
            throw new Exception();
        }


        public async Task<RewardPartnerPoingCachingItems> UpdatePartnerCaching(
            LoyaltyThirdPartyVPBankUpdatePartnerCachingInput input, HttpContext context)
        {
            try
            {
                var request = new LoyaltyThirdPartyPointViewInput()
                {
                    AccessToken = input.AccessToken,
                    IdNumber = input.IdNumber,
                    MemberCode = input.MemberCode,
                };
                return new RewardPartnerPoingCachingItems()
                {
                    PointBalance = 0,
                    Status = "S",
                    MerchantId = input.MerchantId
                };
            }
            catch (Exception ex)
            {
                return new RewardPartnerPoingCachingItems()
                {
                    PointBalance = 0,
                    Status = "F",
                    MerchantId = input.MerchantId,
                    HaveException = true,
                    ExceptionCode = _exceptionReponseService.GetExceptionLoyaltyReponse(ex).Result.Code
                };
            }
        }

        private RewardDataExceptionResponse GetErrorValidation(string errorCode, string errorMessage)
        {
            var ex = new RewardException();
            var error = new RewardDataExceptionResponse()
            {
                result = new RewardDataExceptionResultItem()
                {
                    code = errorCode,
                    message = errorMessage
                },
                status = 500
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }

        private LoyaltyExceptionResponse GetLoyaltyErrorValidation(string errorCode, string errorMessage)
        {
            var ex = new LoyaltyException();
            var error = new LoyaltyExceptionResponse()
            {
                result = 400,
                success = false,
                error = new LoyaltyExceptionErrorItem()
                {
                    code = errorCode,
                    message = errorMessage
                },
            };
            ex.Data.Add("ErrorData", JsonConvert.SerializeObject(error));
            ex.Data.Add("StatusCode", 400);
            throw ex;
        }

        public async Task<LoyaltyResponse<RemoveConnectRespone>> RemoveConnect(RemoveConnectInput Input)
        {
            try
            {
                _logger.LogInformation("Remove connected request: " + JsonConvert.SerializeObject(Input));
                // Call api remove
                return await PostLoyaltyAsync<LoyaltyResponse<RemoveConnectRespone>>(CommonLoyaltyUrls.RemoveLinkIDConnect,
                 "Remove",
                 Input.MemberCode, new { Input.LinkID_MemberID, Input.LinkID_WalletAddress});
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Remove connected merchant fail: " + JsonConvert.SerializeObject(ex));
                throw ex; 
            }
          
        }

        public async Task<LoyaltyThirdPartyUpdatePhoneNumberOutput> UpdatePhoneNumber(UpdatePhoneNumberInput input)
        {
            var retry = 4;
            while (retry != 0)
            {
                try
                {
                    _logger.LogInformation("Update phone number connect for vpbankS request: " + JsonConvert.SerializeObject(input));
                    await PostLoyaltyAsync<LoyaltyResponse<string>>(CommonLoyaltyUrls.UpdateConnectPhoneNumber, "Mobile",Convert.ToString(input.LinkID_MemberID),input);
                    retry = 0;
                }
                catch (Exception ex)
                {
                    retry--;
                    _logger.LogInformation("Update phone number connect for vpbankS fail: " + JsonConvert.SerializeObject(ex));
                }
            }
            return new LoyaltyThirdPartyUpdatePhoneNumberOutput()
            {
                Status = true
            };
        }
    }
}