using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.MobileAPI;
using AKC.MobileAPI.Service.Abstract;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;

namespace AKC.MobileAPI.Service.Astrologee
{
    public class AstrologeeService : BaseAstrologeeService, IAstrologeeService
    {
        public AstrologeeService(IConfiguration configuration,
            IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<LunarCalendarCellDto> Now()
        {
            return await GetAsync<LunarCalendarCellDto>("Integration/LunarCalendar/Now");
        }

        public async Task<List<YearDto>> Years(GetYearsInput input)
        {
            return await GetAsync<List<YearDto>>("Integration/LunarCalendar/Years", input);
        }

        public async Task<CalOfMonthOutput> CalOfMonth(MonthAndYearNum input)
        {
            return await GetAsync<CalOfMonthOutput>("Integration/LunarCalendar/CalOfMonth", input);
        }

        public async Task<List<LunarCalendarCellDto>> GetNDates(GetNDatesInput input)
        {
            return await GetAsync<List<LunarCalendarCellDto>>("Integration/LunarCalendar/GetNDates", input);
        }

        public async Task<TamLynkDocumentResponse> GetDocument(GetDocumentInput input)
        {
            return await GetAsync<TamLynkDocumentResponse>("TamLynkDocument/GetAll", input);
        }
    }
}