using System.Collections.Generic;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.MobileAPI;

namespace AKC.MobileAPI.Service.Abstract
{
    public interface IAstrologeeService
    {
        Task<LunarCalendarCellDto> Now();
        Task<List<YearDto>> Years(GetYearsInput input);
        Task<CalOfMonthOutput> CalOfMonth(MonthAndYearNum input);
        Task<List<LunarCalendarCellDto>> GetNDates(GetNDatesInput input);
        Task<TamLynkDocumentResponse> GetDocument(GetDocumentInput input);
    }
}