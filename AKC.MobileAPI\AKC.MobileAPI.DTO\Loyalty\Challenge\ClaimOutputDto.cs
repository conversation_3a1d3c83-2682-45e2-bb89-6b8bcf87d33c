﻿using System;
using System.Collections.Generic;


namespace AKC.MobileAPI.DTO.Loyalty.Challenge
{
    public class ClaimOutputDto
    {
        public ListResultClaim Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class ListResultClaim
    {
        public decimal QuestRewardedCoint { get; set; }

        public decimal QuestRewardedPoint { get; set; }

        public decimal MissionRewardedCoint { get; set; }

        public decimal MissionRewardedPoint { get; set; }
   
    }

}
