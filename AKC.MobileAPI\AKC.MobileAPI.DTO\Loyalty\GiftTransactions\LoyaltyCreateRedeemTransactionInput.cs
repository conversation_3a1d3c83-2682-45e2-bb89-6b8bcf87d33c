﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftTransactions
{
    public class LoyaltyCreateRedeemTransactionInput
    {
        public string MemberCode { get; set; }
        public string GiftCode { get; set; }
        public int Quantity { get; set; }
        public decimal TotalAmount { get; set; }
        //public string Date { get; set; }
        public string Description { get; set; }
        public int? MerchantId { get; set; }
        public string FlashSaleProgramCode { get; set; }
        public string FlashSaleProgramName { get; set; }
        public int? MemberId { get; set; }
        public string UserAddress { get; set; }
    }

    public class LoyaltyCreateRedeemTransactionDto
    {
        public string MemberCode { get; set; }
        public string GiftCode { get; set; }
        public int Quantity { get; set; }
        public decimal TotalAmount { get; set; }
        public string TransactionCode { get; set; }
        public string Date { get; set; }
        public string Description { get; set; }
        public int? TenantId { get; set; }
        public int? MerchantIdRedeem { get; set; }
        public string FlashSaleProgramCode { get; set; }
        public string PaymentType { get; set; }
    }

    public class LoyaltyGetGiftCategoryTypeInput
    {
        public string GiftCode { get; set; }
    }
}
