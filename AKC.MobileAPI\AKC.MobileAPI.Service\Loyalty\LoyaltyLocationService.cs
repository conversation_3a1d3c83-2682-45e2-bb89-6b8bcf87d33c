﻿using AKC.MobileAPI.DTO.Loyalty.LocationManagement;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyLocationService : BaseLoyaltyService, ILoyaltyLocationService
    {
        public LoyaltyLocationService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<GetAllLocationManagementDto> GetAll(GetAllLocationInput input)
        {
            return await GetLoyaltyAsync<GetAllLocationManagementDto>(LoyaltyApiUrl.LOCATION_GET_ALL, input);
        }

        public async Task<GetAllLocationManagementDto> GetAllNoPaging(GetAllLocationNoPagingInput input)
        {
            return await GetLoyaltyAsync<GetAllLocationManagementDto>(LoyaltyApiUrl.LOCATION_GET_ALL_NO_PAGING, input);
        }

        public async Task<ViewLocationByIdsOutput> ViewLocationByIds(ViewLocationByIds input)
        {
            return await PostLoyaltyAsync<ViewLocationByIdsOutput>(LoyaltyApiUrl.LOCATION_VIEW_BY_IDS, input);
        }
    }
}
