﻿using AKC.MobileAPI.DTO.Loyalty.Order;
using AKC.MobileAPI.DTO.Loyalty.Rewards;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace AKC.MobileAPI.Service.Loyalty
{
    public class LoyaltyRewardsService : BaseLoyaltyService, ILoyaltyRewardsService
    {
        public LoyaltyRewardsService(IConfiguration configuration, IDistributedCache cache) : base(configuration, cache)
        {
        }

        public async Task<LoyaltyInputActionDtoOutput> InputAction(LoyaltyInputActionInput input)
        {
           return await PostLoyaltyAsync<LoyaltyInputActionDtoOutput>(LoyaltyApiUrl.REWARDS_INPUTACTION, input);
        }
        
        public async Task<LoyaltyInputActionDtoOutput> InputAction(LoyaltyInputActionInput input, string merchantName)
        {
            return await PostLoyaltyCustomMerchantAsync<LoyaltyInputActionDtoOutput>(LoyaltyApiUrl.REWARDS_INPUTACTION, input, null, merchantName);
        }
        
    }
    
}
