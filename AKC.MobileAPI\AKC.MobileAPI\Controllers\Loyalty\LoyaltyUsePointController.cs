﻿using AKC.MobileAPI.DTO.Loyalty.UsePoint;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/UsePoint")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyUsePointController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyUsePointService _usePointService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ICommonHelperService _commonHelperService;
        public LoyaltyUsePointController(
            ILogger<LoyaltyUsePointController> logger,
            ILoyaltyUsePointService usePointService,
            IExceptionReponseService exceptionReponseService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _usePointService = usePointService;
            _exceptionReponseService = exceptionReponseService;
            _commonHelperService = commonHelperService;
        }

        [HttpPost]
        [Route("PreCalculatePoint")]
        public async Task<ActionResult<LoyaltyPreCalculatePointOutput>> PreCalculatePoint(LoyaltyPreCalculatePointInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _usePointService.PreCalculatePoint(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "Pre-CalculatePoint Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }
    }
}
