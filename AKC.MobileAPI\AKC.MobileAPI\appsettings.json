{"CorsOrigins": ["http://*.lynkid.vn", "http://localhost:4200", "http://*.linkid.vn", "https://*.linkid.vn", "https://*.lynkid.vn"], "AES": {"SecretKey": "nA9Fb5g8bXSMFMT0"}, "Loyalty": {"RemoteURL": "https://vpid-loyalty-api-uat.linkid.vn/api", "TenantId": "26", "Username": "mobile-api", "Password": "123qwe", "CronExpressionRefreshToken": "* * */10 * *"}, "LoyaltyNotification": {"RemoteURL": "http://notification-api.fpt-operator/api", "TenantId": "26", "Username": "mobile-api-notificationuser", "Password": "Abcd@123", "CronExpressionRefreshToken": "*/10 * * * *"}, "NotificationHistoryV2": {"RemoteURLV2": "http://notification-service-v2.fpt-operator/api", "ApiKey": "********9098765432********9"}, "Reward": {"RemoteURL": "https://vpid-operator-api-uat.linkid.vn", "MerchantId": "219", "AccessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************.Zg1Zp74c_ZVo_zQV47AJcJmzGf5JTR3QHzGr4WhYSEc"}, "RewardVPID": {"RemoteURL": "https://vpid-merchant-api-uat.linkid.vn", "MerchantId": "217", "AccessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************.p6O08PvyXKiQcyAJSQxDSGMnBtk4-r1mafNUR4G9etM"}, "RewardVPBank": {"RemoteURL": "https://vpbank-merchant-api-qa.akachains.io", "MerchantId": "183", "AccessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************.b9B1TaWUtN6fTKPi2i99Ufc_HyItdqWrMu5kkcIL-7I"}, "Gamifications": {"GameManagement": {"RemoteURL": "https://gamemgr.gamification.akachains.io"}, "ItemManagement": {"RemoteURL": "https://itemmgr.gamification.akachains.io"}, "ExchangeManagement": {"RemoteURL": "https://exchangemgr.gamification.akachains.io"}, "MemberManagement": {"RemoteURL": "https://membermgr.gamification.akachains.io"}}, "keys": {"publickey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu6/J9gpFUr8B0kKcqh7NJOlSpma9aqiuMOSZaOPlv3jbPru3UEBuoDPkmdkOWFy95sEei/wvGYC++H05WCuH8asVmZjBG9Bq5f0jkdqnMgFY70dtWKUxxGg9BZa1+3+Ng/k3t4KHUvehQewTp6svcjzpRk6Tl4uPgLZRwxNAj0wkoYodvyhdDSn2ncZDqSE3GIU9S7hQx7amqd39jwsUqOtHMtrJpyKlg0cJjqPiPv02fOSOzflz9YEl4/V5z5Yv/3fS/8XLneSwBt5r3FqMREgGfP7kIn3hn6U5mElf+j6TlV0kri/KL8l//DY8WAklh1X+FfCZyF7iZ/rwk3ff4QIDAQAB", "privatekey": "MIIEpAIBAAKCAQEAu6/J9gpFUr8B0kKcqh7NJOlSpma9aqiuMOSZaOPlv3jbPru3UEBuoDPkmdkOWFy95sEei/wvGYC++H05WCuH8asVmZjBG9Bq5f0jkdqnMgFY70dtWKUxxGg9BZa1+3+Ng/k3t4KHUvehQewTp6svcjzpRk6Tl4uPgLZRwxNAj0wkoYodvyhdDSn2ncZDqSE3GIU9S7hQx7amqd39jwsUqOtHMtrJpyKlg0cJjqPiPv02fOSOzflz9YEl4/V5z5Yv/3fS/8XLneSwBt5r3FqMREgGfP7kIn3hn6U5mElf+j6TlV0kri/KL8l//DY8WAklh1X+FfCZyF7iZ/rwk3ff4QIDAQABAoIBABaLRvcGxMzrpIxc8/R7PqjwXb0fGiF1oXa9/q9asH4PRkaCxcId+yvDD/Jq4A49FZJiIAkwgXfOvyGZ6QluMSYWJsIMVyXAIxb9EQiz+uFGyCf1rGy5awDshGfncnSDHbx8cvTX+Ok+VWBVuwaoyUovhtWrGo9ZdCNBwxBrf5W2mwSNG7mnaHRofi+eIWD4QJ2ZKIZzN6JYHqIhBbwcb4vJL+PHgAQ/uljDHJM65yIokmOvBAxbSLePUjp6CN0TFIin0N1wveKJDYEp3UfixU2T/N4T0jtl079TxoEMV+lJlE3Ii7mgrX9n0PGc5kVDyR4IyU+vRQeNDEY+p5TIOwECgYEA34RhbzETdX3HZ5U6zarReVdGV5hF53Wg9mik+TNsoHDmgP8KKOYzUyKdDXpDEPRX8qfp0BBRn/1ul+2aoTmDDaiYqHv8X+H7+QxX2UYIu8jvKVN9oRlV7hpmfZUProRcJKCPPtYJcvi2wTQd7xPoyC3v3kHRAccv8vbJX7CbIdcCgYEA1vZj/Wytz8LdyptlcdwtWSRPWTnUS3syy3+sRgMX1LtgffnRi47978KRWxfG/A3G8C5uAX4ylv+drvHLIK4kQKgpQ1HIiD7OLUfQgmZyr7Cjc3gNOCJj0QLrSvH2R6a/ITA5S8HkjEkcnrwylQAoON4FGt7FbJiHyVnxovxdRQcCgYEAkEiDIOS8G+7Khpn1MIgg2kQ1Oxwnm8b51bYtMFQ6vcLDeOepZkdbQEIqwWV333zrPM+Zhd7S4gUVm+scExDuASYhDTmj6z0Ui2qlVrL3tkh54eJsSU7yglX2bQCI8+2rEVW3wicmSFo2lrcOz6K5dGb63Sz9PzmWQS9/RE7bhVcCgYBcYbVlI0Crah6xRQla3nnqtm0Xdn/jFIYm6K6bxBaIySJV4XgJlyFQ2fhoApb0meMmM335TEfnluEoXyeh2tUfqGVbVhltXDh1Q0i70dV15d7I8A4WUsk8+Rkvsz7ZWjMHy1bEVQaot38CRPUkDdY7YkclXhnQatjoUqxDFFvtHwKBgQCnjknNlW9tFo7/WnbeW4JQ7hrBSVTq0lXNEm8ZJ2Fk5dSH998BUyqJ2Wi5H+O8lJ97BKjmb3ddk+B+SKrIXg1i1jtsx/lmHWjaDq38EYhCLNRJBGkNgeILX76SlU7pQvfBgLY6xNY7Uiu6RQn6cxCjjR4nBgD1hW/pk2nAWbKZhg=="}, "Firebase": {"ProjectId": "akc-vpid-uat1"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "GrayLog": {"IsEnabled": true, "Host": "odccantho.southeastasia.cloudapp.azure.com", "Port": 12201, "Protocol": "HTTP", "LogSource": "AKC.MobileAPI.UAT1", "LogSourceAudit": "AKC.MobileAPI.Audit"}, "AllowedHosts": "*", "ThirdPartyMerchant": {"VPBank": {"MerchantId": 183, "BaseURL": "https://**************/oapi/vpid/v1", "UserName": "vpid", "Password": "vpid@2020", "IDNApp": "VPID"}, "VPBankSecurities": {"MerchantId": 244, "BaseURL": "http://localhost:22742/api", "UserName": "admin", "Password": "1qaZ2wsX3edC", "TenantId": 26}, "LoyaltyDummy": {"MerchantId": 158, "BaseURL": "https://vpid-loyalty-api-qa.akcada-hcm.com/api", "TenantId": "18", "Username": "<EMAIL>", "Password": "********", "CronExpressionRefreshToken": "*/59 * * * *"}, "Appota": {"MerchantId": 238, "AuthUrl": "https://ewallet.dev.appotapay.com/oauth/login", "ApiKey": "ABC4JhoA14aLeHC8gtq05by4ASBSZBWD", "AuthScope": "user.apoint_payment", "AuthRedirectUrl": "https://vpid-mobile-api-uat.linkid.vn/api/3rd/callback-forwarder", "ExchangeCodeForToken": "https://ewallet.dev.appotapay.com/api/v1/oauth/access_token", "ExchangeRefreshForAccessToken": "https://ewallet.dev.appotapay.com/api/v1/oauth/refresh_token", "ExchangePointUrl": "https://payment.dev.appotapay.com/api/v1/ewallet/appota/pay", "GetAccountInfoUrl": "https://ewallet.dev.appotapay.com/api/v1/users/accounts/info", "SecretKeyForSignature": "mHSa441txm2AT3ILjJ3GguYQQ5FdYM68", "LinkIdCodeOnAppota": "khuongdv"}, "SkyJoy": {"MerchantId": 350, "ClientId": "b98f9a11-4cd4-4d22-b38f-9a9684effb06", "RedirectURI": "https://vpid-mobile-api-uat.linkid.vn/api/3rd/skyjoy-callback", "BaseWebViewAuthLink": "https://id.uat.skyjoy.io/realms/uat-loyalty/protocol/openid-connect/auth", "LinkMemberUrl": "https://api.uat.skyjoy.io/api-user/partner/v2/user/link-member", "UnlinkMemberUrl": "https://api.uat.skyjoy.io/api-user/partner/v1/user/unlink-member", "PointAccrualUrl": "https://api.uat.skyjoy.io/api-point/partner/v1/point-accrual", "ExchangeCodeForAccessTokenUrl": "https://id.uat.skyjoy.io/realms/uat-loyalty/protocol/openid-connect/token", "RetrieveAccessBe2BeTokenUrl": "https://id.uat.skyjoy.io/realms/loyalty-partner/protocol/openid-connect/token", "BeToBeLoginUrl": "https://id.uat.skyjoy.io/realms/loyalty-partner/protocol/openid-connect/token", "RetrieveAccessBe2BeTokenClientId": "1f8b45fd-9e35-484c-a337-a513976e1002", "RetrieveAccessBe2BeTokenUsername": "be_lynkid_partner", "RetrieveAccessBe2BeTokenPassword": "RE4sVu8x9IBRgg8vJ1uxPRMo", "RetrieveAccessBe2BeTokenSecret": "********************************"}}, "StoreFiles": {"AWSS3OrMinIO": {"Enable": "true", "AccessKey": "minioadmin", "SecretKey": "minioadmin", "LinkUpload": "minio.dentalgold.site", "Https": "true", "MemberAvatarBucket": "upload-member-avatar"}, "AzureStorage": {"Enable": "false", "Account": "storagebvl", "AccessKey": "****************************************************************************************", "LinkUpload": "https://storagebvl.blob.core.windows.net", "MemberAvatarBucket": "upload-member-avatar"}, "AWSS3": {"AccessKey": "", "SecretKey": "", "BucketName": "", "KeyName": "upload-member-exchange/member.txt"}}, "ApiSMS": {"api_url": "api_url", "api_key": "api_key", "api_secret": "api_secret", "brand_name": "brand_name"}, "EnableSwagger": "true", "RabbitMQ": {"UserName": "guest", "Password": "guest", "HostName": "localhost", "VHost": "/", "Port": 5672, "QueueNameFormat": "VPID.{0}", "EventExchange": "VPID.Event.Exchange", "GamificationExchange": "Gamification.Exchange"}, "IsCheckPhoneNumberExchangeList": "false", "VPBankLoyaltyExchange": {"BaseURL": "https://ltvpbank-loyalty-api.akcada-hcm.com/api", "TenantId": "9", "Username": "<EMAIL>", "Password": "123456", "CronExpressionRefreshToken": "*/59 * * * *", "MerchantId": "183"}, "LoyaltyGiftStoreExtends": {"VPBankGiftStore": {"RemoteUrl": "https://ltvpbank-loyalty-api.akcada-hcm.com/api", "TenantId": "9", "DefaultRedeemMerchantId": "178", "Username": "<EMAIL>", "Password": "123456"}}, "IsUsingVPBankLoyalty": "false", "LoyaltyGiftStoreRefreshToken": "* * */10 * *", "LoyaltyGiftCodeAllowBuyOneIn30": "GiftInfor_20220419111606284_1833;GiftInfor_20220419112123468_9355", "HmacSHA512Secret": "WbmHDSTOrRhr8yIILxc0hKOQnmHHYuSaTlluVqvPl55FeFQv7twSp3I219ikrYam", "Redis": {"InstanceName": "MobileApi", "Configuration": "localhost:6379,abortConnect=False,allowAdmin=true"}, "VPBankLoyaltyAPIUtils": {"BaseURL": "http://***********:32110/api", "Username": "linkidmobileapi", "Password": "AQAAAAEAACcQAAAAEMBQk5tKZWRNDlLRPv62ffyoBJnoXcG9n5m7bp4nvWcEi89N7jMhFCK8vLm6N9Fiyw==", "CronExpressionRefreshToken": "*/59 * * * *"}, "PartnerIntegrationByLinkId": {"BaseURL": "https://vpid-partner-integration-api-uat.linkid.vn/api", "AuthName": "LinkId", "ApiKey": "linkid-secret-key"}, "MerchantUseLoyalty": {"LinkId": {"RemoteURL": "https://vpid-loyalty-api-uat.linkid.vn/api", "TenantId": "26", "Username": "mobile-api", "Password": "123qwe"}, "OPES": {"RemoteURL": "https://vpid-loyalty-api-uat.linkid.vn/api", "TenantId": "40", "Username": "admin", "Password": "Gpvt#123!", "MerchantId": 243}}, "LinkIDGamePlatform": {"SecretKey": "5f96a604a55aa49d", "SecretIv": "5f96a6"}, "CateCodeOfNapDiem": "7ce79caf-4c5c-4baf-a41b-6c7d8f655040", "Astrologee": {"RemoteUrl": "http://linkid-util-api.fpt-operator:3000", "ApiKey": "111222333444555666"}, "UrlViewGift": "link.vn/view-gift", "SumNumerChar": 4, "FileUploadMaxSizeInByte": 5242880, "LINKIDTENANTIDONCOMMONLOYALTY": 46, "CommonLoyalty46": {"RemoteURL": "http://localhost:22742/api", "Username": "admin", "Password": "Qwerty!"}, "SingaporeAirlines": {"APIUrlCreditNonAirMiles": "https://apigw.singaporeair.com/v2/lsl-uat/miles/creditnonairmiles", "ParticipantCode": "OLYNK", "ChannelId": "LYNKID", "APIKey": "s7fykdc2jj76ar3vpy8zefry", "ClientSecret": "4954yAVBce"}, "IpRateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "HttpStatusCode": 429, "GeneralRules": [{"Endpoint": "*/api/webstore/public/*", "Period": "1m", "Limit": 10}]}, "SME_THRESHOLD_MONEY": 2000000, "WebStoreSecretKey": "zXq9vzGORhNClxV3NaK4ubkFdkAI0HpLw0jZPXwR", "WebStoreMaxQuantityDefault": 10, "WebstoreLimitLoginPerIpPerMin": 10000, "Downstream": {"Authenticate": {"BaseURL": "http://**********:9763", "GrantType": "client_credentials", "BasicToken": "Basic VXUzTXI0Z3IzT2tpTnlCZ0t0THZGS1dGZjNFYTpuRzRxZkR2T2R3eE84YnJSVVZtTlNjazZiSkFh"}, "Api": {"BaseURL": "http://**********:8280"}}}