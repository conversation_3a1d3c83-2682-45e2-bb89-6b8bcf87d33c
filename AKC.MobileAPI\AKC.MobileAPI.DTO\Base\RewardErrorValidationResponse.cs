﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Base
{
    public class RewardErrorValidationResponse
    {
        public int Code { get; set; }
        public List<RewardErrorValidationItem> Message { get; set; }
    }

    public class RewardErrorValidationItem {
        public Dictionary<string, string> Constraints { get; set; }
    }

    public class RewardErrorResponse
    {
        public int Result { get; set; }
        public string Code { get; set; }
        public string Message { get; set; }
        public object MessageDetail { get; set; }
        public object ListMessages { get; set; }
    }

    public class RewardInvalidResponse
    {
        public int Result { get; set; }
        public string Code { get; set; }
        public string Message { get; set; }
        public int? numberOfRetries { get; set; }
        public int? remainingTime { get; set; }
        public object MessageDetail { get; set; }
        public object ListMessages { get; set; }
    }
}
