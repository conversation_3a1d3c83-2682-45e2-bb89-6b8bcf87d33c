﻿using System.Collections.Generic;
using AKC.MobileAPI.DTO.Loyalty.Article;

namespace AKC.MobileAPI.DTO.Loyalty.LocationManagement
{
    public class GetAllLocationManagementDto
    {
		public ListResultGetLocationAll Result { get; set; }
		public string TargetUrl { get; set; }
		public bool Success { get; set; }
		public string Error { get; set; }
		public bool UnAuthorizedRequest { get; set; }
		public bool __abp { get; set; }
	}

	public class ListResultGetLocationAll
	{
		public int TotalCount { get; set; }

		public List<GetAllLocationItemDto> Items { get; set; }

	}

	public class ViewLocationByIdsOutput
	{
		public List<GetAllLocationItemDto> Result { get; set; }
	}
	public class LoyaltyBannerResponse
	{
		public LoyaltyBannerResponseInner Result { get; set; }
		public string TargetUrl { get; set; }
		public bool Success { get; set; }
		public string Error { get; set; }
		public bool UnAuthorizedRequest { get; set; }
		public string __abp { get; set; }
	}

	public class LoyaltyBannerResponseInner
	{
		public List<ArticleDto> Items { get; set; }
	}
	public class GetBannersOutput
	{
		public List<GetBannersOutputInner> ListBanners { get; set; }
	}

	public class GetBannersOutputInner
	{
		public int Id { get; set; }
		public string Code { get; set; }
		public string Description { get; set; }
		public string ImageLink { get; set; }
	}
	public class GetAllLocationItemDto
	{
		public int Id { get; set; }
		public virtual string Code { get; set; }
		public virtual string Name { get; set; }
		public virtual string Description { get; set; }
		public virtual string ParentCode { get; set; }
		public virtual string InternalCode { get; set; }
		public virtual string Level { get; set; }
	}
}
