﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.MemberOtp
{
    public class VerifyOtpAddPhoneInput
    {
        [JsonProperty(PropertyName = "memberCode")]
        public string MemberCode { get; set; }

        [JsonProperty(PropertyName = "phoneNumber")]
        public string PhoneNumber { get; set; }

        [JsonProperty(PropertyName = "checksum")]
        public string Checksum { get; set; }
    }
}
