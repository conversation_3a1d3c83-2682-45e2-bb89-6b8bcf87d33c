﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class GetRecommendedGiftsOutput
    {
        public GetRecommendedGiftsOutputResult Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }


        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }

    public class GetRecommendedGiftsOutputResult
    {
        public int TotalCount { get; set; }
        public List<GetRecommendedGiftsOutputResultElement> Items { get; set; }
    }

    public class GetRecommendedGiftsOutputResultElement
    {
        public GetRecommendedGiftsGiftDTO GiftInfor { get; set; }

        public List<ImageLinkDto> ImageLink { get; set; }
        public FlashSaleProgramDto FlashSaleProgramInfor { get; set; }
        public GiftDiscountDto GiftDiscountInfor { get; set; }

        public GetRecommendedGiftsOutputResultElement()
        {
            ImageLink = new List<ImageLinkDto>();
        }
    }

    public class GetRecommendedGiftsGiftDTO
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Introduce { get; set; }
        public string FullGiftCategoryCode { get; set; }
        public string BrandName { get; set; }
        public string ThirdPartyBrandName { get; set; }
        public string Vendor { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public DateTime EffectiveTo { get; set; }
        public int RequiredCoin { get; set; }
        public string Status { get; set; }
        public int TotalQuantity { get; set; }
        public int UsedQuantity { get; set; }
        public int RemainingQuantity { get; set; }
        public int FullPrice { get; set; }
        public int DiscountPrice { get; set; }
        public bool IsEGift { get; set; }
        public bool Is3rdPartyGift { get; set; }
        public int Id { get; set; }
        public int? TotalWish { get; set; }
        public string VendorHotline { get; set; }
        public int? TotalRedeem { get; set; }
        public string VendorName { get; set; }
        public string VendorType { get; set; }
        public string VendorImage { get; set; }
        public int? BrandId { get; set; }
        public int? DisplayOrder { get; set; }
        public bool? isGiftDiamond { get; set; }
    }
    public class LoyaltyGiftGetAllByMemberCodeOutput
    {

        public GiftGetAllByMemberCodeView Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }


        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }
    public class GiftGetAllByMemberCodeView
    {
        public int TotalCount { get; set; }
        public List<GiftInforByMemberCodeForView> Items { get; set; }
    }


    public class GiftInforByMemberCodeForView
    {
        public GiftInforByMemberCodeDto GiftInfor { get; set; }

        public List<ImageLinkDto> ImageLink { get; set; }
        public FlashSaleProgramDto FlashSaleProgramInfor { get; set; }
        public GiftDiscountDto GiftDiscountInfor { get; set; }

        public GiftInforByMemberCodeForView()
        {
            ImageLink = new List<ImageLinkDto>();
        }
    }

    public class GiftInforByMemberCodeDto
    {
        public string Code { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Introduce { get; set; }

        public string FullGiftCategoryCode { get; set; }

        public string BrandName { get; set; }
        public string ThirdPartyBrandName { get; set; }

        public string Vendor { get; set; }

        public DateTime EffectiveFrom { get; set; }

        public DateTime EffectiveTo { get; set; }

        public decimal RequiredCoin { get; set; }

        public string Status { get; set; }

        public decimal TotalQuantity { get; set; }

        public decimal UsedQuantity { get; set; }

        public decimal RemainingQuantity { get; set; }

        public decimal FullPrice { get; set; }
        public decimal DiscountPrice { get; set; }

        public bool IsEGift { get; set; }

        public SettingParamDto TargetAudience { get; set; }
        public int? TargetAudienceId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public DateTime CreationTime { get; set; }
        public string CreatedByUser { get; set; }
        public string UpdatedByUser { get; set; }

        public string Tag { get; set; }

        public int Id { get; set; }

        public List<OfficeInfo> Office { get; set; }
        
        public int? TotalWish { get; set; }
        public List<GiftGroupInforDto> GiftGroups { get; set; }
        public string VendorHotline { get; set; }
        // for sorting
        //public int? BrandOrdinal { get; set; }
        //public int? VendorOrdinal { get; set; }
        //public int? MerchantOrdinal { get; set; }
        public int? TotalRedeem { get; set; }
        public DateTime? GiftUpdatedTime { get; set; }
        public DateTime? GiftCreatedTime { get; set; }
        // info data gift
        public string VendorName { get; set; }
        public string VendorType { get; set; }
        public string VendorImage { get; set; }
        public int? MerchantId { get; set; }
        public string MerchantName { get; set; }
        public string MerchantAvatar { get; set; }
        public string MerchantDescription { get; set; }
        public int? BrandId { get; set; }
        public int? DisplayOrder { get; set; }
    }

    public class GiftGroupInforDto
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public int Id { get; set; }
    }
}
