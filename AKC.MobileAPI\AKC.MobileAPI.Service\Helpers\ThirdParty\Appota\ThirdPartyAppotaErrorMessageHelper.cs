﻿using System;
using System.Collections.Generic;

namespace AKC.MobileAPI.Service.Helpers.ThirdParty.Appota
{
    public class AppotaGeneralErrorObject
    {
        public string ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
    }
    public static class ThirdPartyAppotaErrorMessageHelper
    {
        public static string GetMessageFromCode(string errorCode, string apiGroup  = "1")
        {
            string ret = null;
            Dictionary<string, string> apiGroup1 = new Dictionary<string, string>
            {
                { "1", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "10", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "11", "Vui lòng đăng nhập để tiếp tục sử dụng nhé." },
                { "12", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "13", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "14", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "15", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "16", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "17", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "18", "Vui lòng hủy kết nối với đối tác và thực hiện liên kết lại để tiếp tục sử dụng." },
                { "19", "Vui lòng hủy kết nối với đối tác và thực hiện liên kết lại để tiếp tục sử dụng." },
                { "20", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "21", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "22", "Vui lòng hủy kết nối với đối tác và thực hiện liên kết lại để tiếp tục sử dụng." },
                { "23", "Vui lòng hủy kết nối với đối tác và thực hiện liên kết lại để tiếp tục sử dụng." },
                { "24", "Vui lòng hủy kết nối với đối tác và thực hiện liên kết lại để tiếp tục sử dụng." },
                { "25", "Vui lòng hủy kết nối với đối tác và thực hiện liên kết lại để tiếp tục sử dụng." },
                { "26", "Thông tin SĐT bạn vừa nhập hiện không tồn tại hoặc không hoạt động. Vui lòng nhập thông tin chính xác nhé." },
                { "27", "Vui lòng hủy kết nối với đối tác và thực hiện liên kết lại để tiếp tục sử dụng." },
                { "28", "Vui lòng chọn 'Gửi lại OTP' và thử lần nữa sau 180s nhé. " },
                { "29", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
            };
            Dictionary<string, string> apiGroup2 = new Dictionary<string, string>
            {
                { "1", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "2", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "12", "Bạn không thể thực hiện giao dịch do số dư điểm Appota hiện tại không đủ. Vui lòng thử lại sau." },
                { "32", "Số điểm thanh toán không hợp lệ" },
                { "65", "Tài khoản thanh toán không hợp lệ" },
                { "70", "Bạn đã giao dịch vượt quá hạn mức cho phép trong ngày. Vui lòng thử lại sau." },
                { "75", "Bạn không thể thực hiện giao dịch do số dư điểm Appota hiện tại không đủ. Vui lòng thử lại sau." },
                { "92", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "99", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
                { "154", "Thông tin SĐT bạn vừa nhập hiện không tồn tại hoặc không hoạt động. Vui lòng nhập thông tin chính xác." },
                { "160", "Vui lòng tiến hành xác thực tài khoản để tiếp tục thao tác." },
                { "161", "Bạn không thể thực hiện giao dịch do số dư điểm Appota hiện tại không đủ. Vui lòng thử lại sau." },
                { "162", "Bạn đã giao dịch vượt quá hạn mức cho phép trong tháng. Vui lòng thử lại sau." },
                { "401", "Vui lòng hủy kết nối với đối tác và thực hiện liên kết lại để tiếp tục sử dụng." },
                { "500", "Lỗi này là do chúng mình, không phải bạn đâu. Thông cảm và quay lại sau nhé!" },
            };
            var gr = apiGroup1;
            if (apiGroup == "2")
            {
                gr = apiGroup2;
            }
            ret = gr[errorCode];
            return ret ?? "Unknown Error";
        }

        public static AppotaGeneralErrorObject GetError(string errorCode, string apiGroup)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(errorCode))
                {
                    throw new Exception();
                }

                if (string.IsNullOrWhiteSpace(apiGroup))
                {
                    apiGroup = "1";
                }
                var errorMessage = GetMessageFromCode(errorCode, apiGroup);
                
                return new AppotaGeneralErrorObject()
                {
                    ErrorCode = errorCode,
                    ErrorMessage = errorMessage
                };
            } catch
            {
                return new AppotaGeneralErrorObject()
                {
                    ErrorCode = "1000",
                    ErrorMessage = "System error",
                };
            }
        }
    }
}
