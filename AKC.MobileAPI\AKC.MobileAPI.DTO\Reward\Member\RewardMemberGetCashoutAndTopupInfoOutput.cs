﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberGetCashoutAndTopupInfoOutput
    {
        public RewardMemberGetTokenAbleToCashoutResult Item { get; set; }
        public string Message { get; set; }
        public int Result { get; set; }
    }

    public class RewardMemberGetTokenAbleToCashoutResult
    {
        public string MemberCode { get; set; }
        public decimal? TokenAbleToCashout { get; set; }
        //public decimal? CurrencyExchangeRate { get; set; }
        //public decimal? PointExchangeRate { get; set; }
        //public decimal? BaseUnit { get; set; }
        public RewardMemberGetCashoutAndTopupFeeSetting FeeSetting { get; set; }
    }

    public class RewardMemberGetCashoutAndTopupFeeSetting
    {
        public decimal? FeeAmountCashoutAmountRatio { get; set; }
        public decimal? MaximumCashoutFeeAmount { get; set; }
        public decimal? MinimumCashoutFeeAmount { get; set; }
        public bool FreeFeeCashOut { get; set; }
    }
}
