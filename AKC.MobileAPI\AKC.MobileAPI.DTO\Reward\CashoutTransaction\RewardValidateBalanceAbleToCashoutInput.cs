﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.CashoutTransaction
{
    public class RewardValidateBalanceAbleToCashoutInput
    {
        public int MemberId { get; set; }
        public string MemberCode { get; set; }
        public string UserAddress { get; set; }
    }

    public class RewardValidateBalanceAbleToCashoutDto
    {
        public int MemberId { get; set; }
        public string NationalId { get; set; }
        public string WalletAddress { get; set; }
    }
}
