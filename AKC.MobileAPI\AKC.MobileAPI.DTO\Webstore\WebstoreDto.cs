using System;
using System.Collections.Generic;
using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.Loyalty.Gift;
using Newtonsoft.Json;

namespace AKC.MobileAPI.DTO.Webstore
{
    public class QueryGiftData
    {
        public string GiftCode { get; set; }
        public string MemberCode { get; set; }
        public string IntroduceMode { get; set; }
        public int? GiftId { get; set; }
        public string PartnerCode { get; set; }
    }

    public class CategoriesResponse
    {
        public CategoriesResultData Result { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
    }

    public class CategoriesResultData
    {
        public int TotalCount { get; set; }
        public List<CategoryItem> Items { get; set; }
    }

    public class CategoryItem
    {
        public GiftCategory GiftCategory { get; set; }
    }

    public class GiftCategory
    {
        public int? Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Link { get; set; }
        public ImageLinkDto ImageLink { get; set; }
        public string ParentCode { get; set; }
    }

    public class GiftListResponse
    {
        public ResultDataGift Result { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
    }

    public class ResultDataGift
    {
        public int TotalCount { get; set; }
        public List<GiftItem> Items { get; set; }
    }

    public class GiftItem
    {
        public GiftInfor GiftInfor { get; set; }
        public List<ImageLinkDto> ImageLink { get; set; }
    }

    public class GiftInfor
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Introduce { get; set; }
        public string FullGiftCategoryCode { get; set; }
        public string BrandName { get; set; }
        public string ThirdPartyBrandName { get; set; }
        public string Vendor { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public DateTime EffectiveTo { get; set; }
        public decimal RequiredCoin { get; set; }
        public string Status { get; set; }
        public decimal TotalQuantity { get; set; }
        public decimal UsedQuantity { get; set; }
        public decimal RemainingQuantity { get; set; }
        public decimal FullPrice { get; set; }
        public decimal DiscountPrice { get; set; }
        public bool IsEGift { get; set; }
        public string VendorHotline { get; set; }
        public string Tag { get; set; }
        public bool IsInWishlist { get; set; }
        public string RegionCode { get; set; }
        public string Office { get; set; }
        public string ExpireDuration { get; set; }
        public int TotalWish { get; set; }
        public string VendorImage { get; set; }
        public string VendorDescription { get; set; }
        public string BrandLinkLogo { get; set; }
        public string BrandAddress { get; set; }
        public string BrandDescription { get; set; }
        public int? BrandId { get; set; }
    }

    public class GiftDetailResponse
    {
        public GiftDetailData Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

    public class GiftDetailData
    {
        public GiftInforDetail GiftInfor { get; set; }
        public List<ImageLink> ImageLink { get; set; }
        public List<GiftUsageAddress> GiftUsageAddress { get; set; }
    }

    public class GiftInforDetail
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Introduce { get; set; }
        public string FullGiftCategoryCode { get; set; }
        public string BrandName { get; set; }
        public string ThirdPartyBrandName { get; set; }
        public string Vendor { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public DateTime EffectiveTo { get; set; }
        public decimal RequiredCoin { get; set; }
        public string Status { get; set; }
        public decimal TotalQuantity { get; set; }
        public decimal UsedQuantity { get; set; }
        public decimal RemainingQuantity { get; set; }
        public decimal FullPrice { get; set; }
        public decimal DiscountPrice { get; set; }
        public bool IsEGift { get; set; }
        public string VendorHotline { get; set; }
        public string Tag { get; set; }
        public bool IsInWishlist { get; set; }
        public string RegionCode { get; set; }
        public string Office { get; set; }
        public string ExpireDuration { get; set; }
        public int TotalWish { get; set; }
        public string VendorImage { get; set; }
        public string VendorDescription { get; set; }
        public string BrandLinkLogo { get; set; }
        public string BrandAddress { get; set; }
        public string BrandDescription { get; set; }
        public int? BrandId { get; set; }
    }

    public class ImageLink
    {
        public string Code { get; set; }
        public string Type { get; set; }
        public string Link { get; set; }
        public bool IsActive { get; set; }
        public int Ordinal { get; set; }
        public string FullLink { get; set; }
        public int Id { get; set; }
    }

    public class GiftUsageAddress
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Latitude { get; set; }
        public string Longtitude { get; set; }
    }
    
    
    public class RewardMemberRevertRedeemInput
    {
        public int MemberId { get; set; }
        public int MerchantId { get; set; }
        public string OrderCode { get; set; }
        public decimal TokenAmount { get; set; }
        public string OriginalTokenTransID { get; set; }
    }
    
    public class MerchantGiftCreateRedeemOutputDto
    {
        public MerchantGiftCreateRedeemTransactionDto Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public string __abp { get; set; }
    }

    public class MerchantGiftCreateRedeemTransactionDto
    {
        public List<MerchantGiftDataRedeemTransaction> Items { get; set; }
        public int TotalCount { get; set; }
        public string Exception { get; set; }
        public string Messages { get; set; }
        public bool SuccessedRedeem { get; set; }
        public bool IsNotEnoughBalance { get; set; }
        public string TransactionCode { get; set; }

        public bool Timeout { get; set; }
    }
    public class MerchantGiftDataRedeemTransaction
    {
        public string Code { get; set; }
        public decimal TotalCoin { get; set; }
        public string Status { get; set; }
        [JsonConverter(typeof(CustomDateTimeConverter))]
        public DateTime Date { get; set; }
        public string Description { get; set; }
        public MerchantGiftEGiftData EGift { get; set; }
    }
    public class MerchantGiftEGiftData
    {
        public string Code { get; set; }
        //public string Name { get; set; }
        public string Type { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string usedStatus { get; set; }
        [JsonConverter(typeof(CustomDateTimeConverter))]
        public DateTime? ExpiredDate { get; set; }
        public string QRCode { get; set; }
    }
    public class RewardMemberRedeemOutput
    {
        public string message { get; set; }
        public int result { get; set; }
        public RewardMemberRedeemOutputItemDto items { get; set; }
    }

    public class RewardMemberRedeemOutputItemDto
    {
        public long MemberId { get; set; }
        public string NationalId { get; set; }
        public string OrderCode { get; set; }
        public decimal TokenAmount { get; set; }
        public string TokenTransID { get; set; }
    }

    public class AgreeTAndCInput
    {
        public string MemberCode { get; set; }
        public string PhoneNumber { get; set; }
        public string Note { get; set; }
    }

    public class AgreeTAndCOutput
    {
        
    }

    public class CreateSmeMemberInput
    {
        public string Code { get; set; }
        public string LicenseCode { get; set; }
        public string TaxCode { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public bool IsDeleted { get; set; }
    }

    public class CreateSmeMemberOutput
    {
        public int Result { get; set; }
        public string Message { get; set; }
        public string MessageDetail { get; set; }
        public CreateSmeMemberOutputItem Items { get; set; }
    }

    public class CreateSmeMemberOutputItem
    {
        public int Id { get; set; }
        public string UserAddress { get; set; }
    }

    public class GetALLTokenTransBySmeCifInput
    {
        public int? SmeId { get; set; }
        public string SmeCif { get; set; }
        public string LicenseNumber { get; set; }
        public string FromDateFilter { get; set; }
        public string ToDateFilter { get; set; }
        public string ActionTypeFilter { get; set; }
        public string OrderCodeFilter { get; set; }
        public int maxResultCount { get; set; }
        public int skipCount { get; set; }
    }
}