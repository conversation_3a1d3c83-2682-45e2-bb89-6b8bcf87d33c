﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Article
{
    public class GetAllArticleAndRelatedNewsOutput
    {
        public ListResultGetAllArticleAndRelatedNewsOutput Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }
    public class ListResultGetAllArticleAndRelatedNewsOutput
    {
        public int TotalCount { get; set; }

        public List<DataResultGetAllArticle> Items { get; set; }
    }

    public class DataResultGetAllArticle
    {
        public CreateOrEditArticleDto Article { get; set; }

        public string CreatedByUser { get; set; }

        public List<RelatedNewsDto> RelatedNews { get; set; }
    }


    //PHUONGPV5
    public class GetAllArticleAndRelatedNewsOutput_Optimize
    {
        public List<ListResultGetAllArticleAndRelatedNewsItems> Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }
    public class ListResultGetAllArticleAndRelatedNewsItems
    {
        public int type { get; set; }
        public ListResultGetAllArticleAndRelatedNewsOutput_Optimize resultDto { get; set; }
    }

    public class ListResultGetAllArticleAndRelatedNewsOutput_Optimize
    {
        public int TotalCount { get; set; }
        public List<DataResultGetAllArticle_Optimize> Items { get; set; }
    }

    public class DataResultGetAllArticle_Optimize
    {
        public ArticleOptimizeDto Article { get; set; }
        public string CreatedByUser { get; set; }
        public List<ArticleOptimizeDto> RelatedNews { get; set; }
    }

    public class ArticleOptimizeDto
    {
        public long Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string LinkAvatar { get; set; }
        public string ExtraType { get; set; }
        public DateTime CreationTime { get; set; }
    }
}
