﻿using AKC.MobileAPI.DTO.Loyalty.Gift;
using AKC.MobileAPI.Service.Abstract.Loyalty.Gift;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.AirlineDto;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.TopUp;
using AKC.MobileAPI.DTO.Reward.GiftRedeemTransaction;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using AKC.MobileAPI.DTO.Loyalty.GiftCategories;
using AKC.MobileAPI.DTO.Loyalty.GiftUsageAddress;
using AKC.MobileAPI.DTO.Loyalty.GiftSearch;
using AKC.MobileAPI.DTO.Webstore;

namespace AKC.MobileAPI.Service.Loyalty.Gift
{
    public class LoyaltyGiftService: BaseLoyaltyService, ILoyaltyGiftService
    {
        private readonly ILogger _logger;
        private IRewardGiftRedeemTransactionService _rewardGiftRedeemTransactionService;
        private readonly IExceptionReponseService _exceptionResponseService;
        public LoyaltyGiftService(IConfiguration configuration, IDistributedCache cache,
            ILogger<LoyaltyGiftService> lg,
            IExceptionReponseService x,
            IRewardGiftRedeemTransactionService rewardGiftRedeemTransactionService) : base(configuration, cache)
        {
            _logger = lg;
            _exceptionResponseService = x;
            _rewardGiftRedeemTransactionService = rewardGiftRedeemTransactionService;
        }

        public async Task<LoyaltyGiftCategoryGetAllOutput> GetAll(LoyaltyGiftCategoryGetAllInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGiftCategoryGetAllOutput>(LoyaltyApiUrl.GETALL_GIFT_CATEGORY, input);
        }

        public async Task<LoyaltyGetAllForCategoryOutPut> GetAllForCategory(LoyaltyGetAllForCategoryInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGetAllForCategoryOutPut>(LoyaltyApiUrl.GIFT_INFORS_GETAll_FOR_CATEGORY_ALL_MERCHANT, input);
        }

        public async Task<LoyaltyGiftGetAllWithImageOutput> GetAllWithImage(LoyaltyGiftGetAllWithImageInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGiftGetAllWithImageOutput>(LoyaltyApiUrl.GIFT_GROUPS_GETAll_WITH_IMAGE, input);
        }

        public async Task<LoyaltyGiftGetAllWithImageOutput> GetAllWithImageByMemberCode(LoyaltyGetAllGiftGroupByMemberInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGiftGetAllWithImageOutput>(LoyaltyApiUrl.GIFT_GROUPS_GETAll_WITH_IMAGE_BY_MEMBER, input);
        }

        public async Task<LoyaltyGiftGetAllByMemberCodeOutput> GetAllByMemberCode(LoyaltyGiftGetAllByMemberCodeInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGiftGetAllByMemberCodeOutput>(LoyaltyApiUrl.GIFT_INFORS_GETAll_MEMBER_CODE_ALL_MERCHANT, input);
        }

        public async Task<LoyaltyGetGiftByByMemberCodeOutput> GetGiftByMemberCode(LoyaltyGetGiftByByMemberCodeInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGetGiftByByMemberCodeOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_GIFT_MEMBER_CODE_All_MERCHANT, input);
        }
        public async Task<GetRecommendedGiftsOutput> GetRecommendedGifts(GetRecommendedGiftsInput input)
        {
            return await GetLoyaltyAsync<GetRecommendedGiftsOutput>(LoyaltyApiUrl.GET_RECOMMENDED_GIFTS, input);
        }

        public async Task<GetGiftCategoryAndInfoForView> GetAllGiftCategoriesAndInfo(GetAllGiftCategoriesAndInfoInput input)
        {
            return await GetLoyaltyAsync<GetGiftCategoryAndInfoForView>(LoyaltyApiUrl.GIFT_INFORS_GET_GIFT_CATEGORIES_MEMBER_CODE_ALL_MERCHANT, input);
        }
        public async Task<GetGiftCategoryAndInfoForView> GetAllGiftCategoriesAndInfo_V1(GetAllGiftCategoriesAndInfoInput input)
        {
            return await GetLoyaltyAsync<GetGiftCategoryAndInfoForView>(LoyaltyApiUrl.GIFT_INFORS_GET_GIFT_CATEGORIES_MEMBER_CODE_V1_ALL_MERCHANT, input);
        }

        public async Task<GiftInforsOutPut> GetAllInfors(GiftInforsInput input)
        {
            return await GetLoyaltyAsync<GiftInforsOutPut>(LoyaltyApiUrl.GIFT_INFORS_GET_GIFT_ALL_INFORS_MEMBER_CODE_ALL_MERCHANT, input);
        }

        public async Task<GiftInforsOutPut> GetForHomePageNoMemberCode(GetForHomePageNoMemberCodeInput input)
        {
            return await GetLoyaltyAsync<GiftInforsOutPut>("services/app/GiftInfors/GetForHomePageNoMemberCode", input);
        }

        public async Task<GetByIdAndRelatedGiftOutput> GetByIdAndRelatedGift(GetByIdAndRelatedGiftInput input)
        {
            return await GetLoyaltyAsync<GetByIdAndRelatedGiftOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_GIFT_BY_ID_AND_RELATED_GIFT_MEMBER_CODE_ALL_MERCHANT, input);
        }

        public async Task<GetAllEffectiveCategoryOutput> GetAllEffectiveCategory(GetAllEffectiveCategoryInput input)
        {
            return await GetLoyaltyAsync<GetAllEffectiveCategoryOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY_ALL_MERCHANT, input);
        }
        public async Task<GetAllEffectiveCategoryOutput> GetAllEffectiveCategory_v1(GetAllEffectiveCategoryInput input)
        {
            return await GetLoyaltyAsync<GetAllEffectiveCategoryOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY_V1_ALL_MERCHANT, input);
        }
        public async Task<GetRecommendedCategoriesOutput> GetRecommendedCategories(GetRecommendedCategoriesInput input)
        {
            return await GetLoyaltyAsync<GetRecommendedCategoriesOutput>(LoyaltyApiUrl.GET_RECOMMENED_CATEGORIES, input);
        }

        public async Task<GetAllEffectiveCategoryOutput> GetAllEffectiveCategory_TopupPhone(GetAllEffectiveCategoryInput input)
        {
            var cacheKey = $"TopupPhone_FullGiftCategoryCode_{input.FullGiftCategoryCodeFilter}_BrandId_{input.BrandIdFilter}";
            var cacheString = await _cache.GetStringAsync(cacheKey);
            if(!string.IsNullOrEmpty(cacheString))
            {
                _logger.LogInformation($"TopupPhone hit cache {cacheKey}");
                return JsonConvert.DeserializeObject<GetAllEffectiveCategoryOutput>(cacheString);
            }
            else
            {
                _logger.LogInformation($"TopupPhone miss cache {cacheKey} call API");
                var result = await GetLoyaltyAsync<GetAllEffectiveCategoryOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY_TopUpPhone, input);
                await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(result),
                new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(10)));
                return result;
            }    
        }

        public async Task<GetAllEffectiveCategoryGroupByBrandOutput> GetAllEffectiveCategoryGroupByBrand(GetAllEffectiveCategoryGroupByBrandInput input)
        {
            return await GetLoyaltyAsync<GetAllEffectiveCategoryGroupByBrandOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY_GROUP_BY_BRAND_All_MERCHANT, input);
        }

        public async Task<LoyaltyGetWishlistByMemberOutput> GetWishlistByMember(LoyaltyGetWishlistByMemberInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGetWishlistByMemberOutput>(LoyaltyApiUrl.GIFT_INFORS_WISH_LIST_BY_MEMBER_All_MERCHANT, input);
        }

        public async Task<LoyaltyGetBrandWishlistByMemberOutput> GetBrandWishlistByMember(
            LoyaltyGetBrandWishlistByMemberInput input)
        {
            return await GetLoyaltyAsync<LoyaltyGetBrandWishlistByMemberOutput>(LoyaltyApiUrl.BRAND_WISH_LIST_BY_MEMBER_All_MERCHANT, input);
        }
        public async Task<LoyaltyUpdateWishlistOutput> UpdateWishlist(LoyaltyUpdateWishlistInput input)
        {
            // Prepare data to call new API of Loyalty
            var type = input.Type;
            if (string.IsNullOrEmpty(type) || (type != "G" && type != "B"))
            {
                // Default value
                type = "G";
            }

            var action = input.Action;
            var listIds = input.GiftId.Split(";").Select(int.Parse).ToList();
            var req = new LikeItemInput()
            {
                MemberCode = input.MemberCode, EntityIds = listIds, EntityType = type
            };
            string url = "";
            if (action == "A")
            {
                // Add, means LIKE
                url = LoyaltyApiUrl.LIKE_ITEM_GIFTORBRAND;
            }

            if (action == "R")
            {
                // Remove, means unlike
                url = LoyaltyApiUrl.UNLIKE_ITEM_GIFTORBRAND;
            }
            var res = await PostLoyaltyAsync<LikeItemOutputWrapper>(url, req);
            return new LoyaltyUpdateWishlistOutput()
            {
                __abp = res.__abp, Result = new LoyaltyUpdateWishlistForView()
                {
                    MemberCode = input.MemberCode, GiftId = res.Result.ListLiked
                }, Success = res.Success,
                UnAuthorizedRequest = res.UnAuthorizedRequest, Error = res.Error, TargetUrl = res.TargetUrl
            };
        }

        public async Task<GetAllByMemberCodeOutput> GetAllByMemberCode(GetAllByMemberCodeGiftCategoriesInput input)
        {
            return await GetLoyaltyAsync<GetAllByMemberCodeOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_ALL_BY_MEMBER_CODE, input);
        }

        public async Task<GetAllForCategoryByMemberCodeOutput> GetAllForCategoryByMemberCode(GetAllByMemberCodeGiftInforsInput input)
        {
            return await GetLoyaltyAsync<GetAllForCategoryByMemberCodeOutput>(LoyaltyApiUrl.GIFT_INFORS_GET_ALL_FOR_CATEGORY_BY_MEMBER_CODE_ALL_MERCHANT, input);
        }

        public async Task<CheckVoucherOutput> CheckEVoucherCode(CheckVoucherInput input)
        {
            return await PostLoyaltyAsync<CheckVoucherOutput>(LoyaltyApiUrl.EGIFT_INFORS_CHECK_VOUCHER_TOPUP, input);
        }

        public async Task<CheckMemberOutput> CheckMemberCodeBlocked(CheckMemberInput input)
        {
            return await PostLoyaltyAsync<CheckMemberOutput>(LoyaltyApiUrl.EGIFT_INFORS_CHECK_MEMBER_BLOCKED, input);
        }

        public async Task<RedeemVoucherOutput> UseEVoucherCode(CheckVoucherInput input)
        {
            decimal? topupAmount = null;
            var orderCode = LoyaltyHelper.GenTransactionCode(input.EGiftCode + input.MemberCode + DateTime.Now.Ticks);
            var cts = new CancellationTokenSource();
            var merchantId = Convert.ToInt32(_configuration.GetSection("RewardVPID" + ":MerchantId").Value);
            try
            {
                // Buoc 0: re-check egift, get transcode if any
                var checkEGiftCodeAgain = await CheckEVoucherCode(new CheckVoucherInput()
                {
                    MemberCode = input.MemberCode, EGiftCode = input.EGiftCode
                });
                var giftRedeemTransactionCode = checkEGiftCodeAgain?.Result?.GiftRedeemTransactionCode ?? "";
                if (!string.IsNullOrWhiteSpace(giftRedeemTransactionCode))
                {
                    orderCode = giftRedeemTransactionCode;
                }
                // Bước 1: Mark mã thẻ là đã-sử-dụng
                _logger.LogInformation(">> EvoucherTopup: Sending request to Loyalty To Mark the code as used >> " + input.MemberCode + " - " + input.EGiftCode);
                var result = await PostLoyaltyAsync<RedeemVoucherOutput>(LoyaltyApiUrl.EGIFT_INFORS_USE_VOUCHER_TOPUP, new
                {
                    MemberCode = input.MemberCode, EGiftCode = input.EGiftCode, TransactionCode = orderCode
                });
                if (result.Success && result.Result.ErrorCode == "0")
                {
                    topupAmount = result.Result.TopupAmount ?? 0;
                    try
                    {
                        var resultRedeemReward = await _rewardGiftRedeemTransactionService.CreateTransForTopupVoucher(new RewardNapEvoucherInput()
                        {
                            ECode = input.EGiftCode, OrderCode = orderCode, MerchantId = merchantId, NationalId = input.MemberCode,
                            RequestDate = DateTime.UtcNow, TokenAmount = topupAmount.Value
                        });
                        if (resultRedeemReward == null || resultRedeemReward.Result != 200)
                        {
                            // Revert if error
                            _logger.LogError(" >> Error when call to Rewardnetwork to topup evoucher >> " + input.MemberCode + " - " + input.EGiftCode + "; Error  = " + JsonConvert.SerializeObject(resultRedeemReward));
                            await RevertEVoucherCode(new RevertEVoucherInput()
                            {
                                MemberCode = input.MemberCode, TransactionCode = "REVERT_" + orderCode, EGiftCode = input.EGiftCode, OriginalTransactionCode = orderCode
                            });
                            result = new RedeemVoucherOutput()
                            {
                                Success = false, Result = new RedeemVoucherOutputResult()
                                {
                                    Count = 0, ErrorCode = "13", ErrorMessage = "Lỗi xảy ra ở tổng đài nạp điểm, vui lòng thử lại sau nhé"
                                }
                            };
                            return result;
                        }
                        else
                        {
                            var successRnRes = (RewardNapEvoucherOutputSuccess) resultRedeemReward;
                            result.Result.ExpireDate = successRnRes.Items.ExpiryDate;
                        }
                    }
                    catch (Exception e)
                    {
                        if (e.GetType() == typeof(RewardException))
                        {
                            var res = await _exceptionResponseService.GetExceptionRewardReponse(e);
                            _logger.LogError("Exception occurs when call to RN >> " + JsonConvert.SerializeObject(res));
                        }

                        throw e;
                    }
                }
                else
                {
                    await RevertEVoucherCode(new RevertEVoucherInput()
                    {
                        MemberCode = input.MemberCode, TransactionCode = "REVERT_" + orderCode, EGiftCode = input.EGiftCode, OriginalTransactionCode = orderCode
                    });
                    _logger.LogError(">> Issue calling Loyalty API: Error When Mark EVoucher as USED >> " + input.MemberCode + " - " + input.EGiftCode);
                }

                result.Result.TransactionCode = orderCode;
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(" Error when doing topup for evoucher - >> " + input.MemberCode + " - " + input.EGiftCode);
                await RevertEVoucherCode(new RevertEVoucherInput()
                {
                    MemberCode = input.MemberCode, TransactionCode = "REVERT_" + orderCode, EGiftCode = input.EGiftCode, OriginalTransactionCode = orderCode
                });
                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionResponseService.GetExceptionRewardReponse(ex);
                    _logger.LogError("Exception occurs when call to RN >> " + JsonConvert.SerializeObject(res));
                    throw ex;
                }
                var result = new RedeemVoucherOutput()
                {
                    Success = false, Result = new RedeemVoucherOutputResult()
                    {
                        Count = 0, ErrorCode = "13", ErrorMessage = "Lỗi xảy ra ở tổng đài nạp điểm, vui lòng thử lại sau nhé"
                    }
                };
                return result;
            }
        }


        public async Task<RevertVoucherOutput> RevertEVoucherCode(RevertEVoucherInput input)
        {
            return await PostLoyaltyAsync<RevertVoucherOutput>(LoyaltyApiUrl.EGIFT_INFORS_REVERT_VOUCHER_TOPUP, input);
        }

        public async Task<GiftCategoriesForView> GiftListCategories(GetAllGiftCategoriesAndInfoInput input)
        {
            return await GetLoyaltyAsync<GiftCategoriesForView>(LoyaltyApiUrl.GET_LIST_CATEGORIES, input);
        }

        public async Task<GiftCategoriesForView> GiftListCategories_v1(GetAllGiftCategoriesAndInfoInput input)
        {
            return await GetLoyaltyAsync<GiftCategoriesForView>(LoyaltyApiUrl.GET_LIST_CATEGORIES_V1, input);
        }
 
        public async Task<GiftListCategoriesInTwoRowsOutput> GiftListCategoriesInTwoRows(
            GiftListCategoriesInTwoRowsInput input)
        {
            // Get from Cache, if not exist, then call to loyalty
            var cacheKey = "GiftListCategoriesInTwoRows";
            var cacheString = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(cacheString))
            {
                try
                {
                    _logger.LogInformation(" >> GiftListCategoriesInTwoRows >> Cache hit");
                    return JsonConvert.DeserializeObject<GiftListCategoriesInTwoRowsOutput>(cacheString);
                }
                catch (Exception e)
                {
                    _logger.LogError(" >> GiftListCategoriesInTwoRows >> Cache issue");
                    await _cache.RemoveAsync(cacheKey);
                }
            }
            _logger.LogInformation(" >> GiftListCategoriesInTwoRows >> Cache miss. Querying from the loyalty system");
            var Row1 = await GetLoyaltyAsync<GiftCategoriesForView>(LoyaltyApiUrl.GET_LIST_CATEGORIES_V1,
                new { RowNum = 1, MaxItem = 100, input.MemberCode, Channel = "LinkId" });
            var Row2 = await GetLoyaltyAsync<GiftCategoriesForView>(LoyaltyApiUrl.GET_LIST_CATEGORIES_V1,
                new { RowNum = 2, MaxItem = 100, input.MemberCode, Channel = "LinkId"  });
            var res = new GiftListCategoriesInTwoRowsOutput()
            {
                Row2 = Row2.Result.Items,
                Row1 = Row1.Result.Items
            };
            await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(res),
                new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(3)));
            return res;
        }

        public async Task<GiftAllInforOutput> GetGiftAllInfors(GiftInforsInput input)
        {
            return await GetLoyaltyAsync<GiftAllInforOutput>(LoyaltyApiUrl.GET_GIFT_ALL_INFOR, input);
        }

        public async Task<GetGiftGroupForHomePageV1Dot1Output> GetGiftGroupForHomepageV1dot1(GetGiftGroupForHomePageV1Dot1Input input)
        {
            // Get from Cache, if not exist, then call to loyalty
            var cacheKey = "GetGiftGroupForHomepageV1dot1WN";
            var cacheString = await _cache.GetStringAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(cacheString))
            {
                try
                {
                    _logger.LogInformation(" >> GetGiftGroupForHomepageV1dot1 >> Cache hit");
                    return JsonConvert.DeserializeObject<GetGiftGroupForHomePageV1Dot1Output>(cacheString);
                }
                catch (Exception e)
                {
                    _logger.LogError(" >> GetGiftGroupForHomepageV1dot1 >> Cache issue");
                    await _cache.RemoveAsync(cacheKey);
                }
            }
            _logger.LogInformation(" >> GetGiftGroupForHomepageV1dot1 >> Cache miss. Querying from the loyalty system");
            var x = await GetLoyaltyAsync<GetGiftGroupForHomePageV1Dot1Output>(LoyaltyApiUrl.GetGiftGroupForHomepageV1dot1, input);
            await _cache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(x),
                new DistributedCacheEntryOptions().SetAbsoluteExpiration(TimeSpan.FromMinutes(3)));
            return x;
        }
        public async Task<GiftInforByBrandOutput> GetGiftsByBrand(GiftInforByBrandInput input)
        {
            return await PostLoyaltyAsync<GiftInforByBrandOutput>(LoyaltyApiUrl.GET_GIFTS_BY_BRAND, input);
        }

        public async Task<GiftUsageAddressOutput> GetListGiftUsageAddress(GiftUsageAddressInput input)
        {
            return await GetLoyaltyAsync<GiftUsageAddressOutput>(LoyaltyApiUrl.GET_GIFT_USAGE_ADDRESS, input);
        }

        public async Task<GetGiftInforByGiftGroupOutput> GetGiftbyGroupType(GetGiftGroupByGroupTypeInput input)
        {
            return await GetLoyaltyAsync<GetGiftInforByGiftGroupOutput>(LoyaltyApiUrl.GetGiftGroupbyGroupType, input);
        }

      

        public async Task<GetGiftGroupByCateOutputForView> GetGiftByCateType_V2(GetGiftByCateTypeInput input)
        {
            return await GetLoyaltyAsync<GetGiftGroupByCateOutputForView>(LoyaltyApiUrl.GetGiftByCateType_V2, input);
        }

        public async Task<SearchGiftAndGiftGroupAndBrandForViewOutput> SearchGiftAndGiftGroupAndBrand(SearchInput input)
        {
            return await GetLoyaltyAsync<SearchGiftAndGiftGroupAndBrandForViewOutput>(LoyaltyApiUrl.SearchGiftAndGiftGroupAndBrandForMB, input);
        }

        public async Task<SuggestOutputView> GetListSuggest(SuggestInput input)
        {
            return await GetLoyaltyAsync<SuggestOutputView>(LoyaltyApiUrl.GetListSuggest, input);
        }

        public async Task<CreateCSTicketOutput> CreateCSTicket(CreateCSTicketInput input)
        {
            var res = await PostLoyaltyAsync<LoyaltyResponse<CreateCSTicketOutput>>(LoyaltyApiUrl.TICKET_MNGMT_CREATE, input);
            return res.Result;
        }

        public async Task<GetListCSTicketOutput> GetListCSTicket(GetListCSTicketInput input)
        {
            if (input.SkipCount < 0)
            {
                input.SkipCount = 0;
            }

            if (input.MaxResultCount <= 0)
            {
                input.MaxResultCount = 10;
            }
            var res = await GetLoyaltyAsync<LoyaltyResponse<GetListCSTicketOutput>>(LoyaltyApiUrl.TICKET_MNGMT_SEARCH, input);
            return res.Result;
        }

        public async Task<GetOneCSTicketOutput> GetOne(GetOneCSTicketInput input)
        {
            var res = await GetLoyaltyAsync<LoyaltyResponse<GetOneCSTicketOutput>>(LoyaltyApiUrl.TICKET_MNGMT_VIEWDETAIL, input);
            return res.Result;
        }

        public async Task<SyncStringeeXOutput> SyncStringeeX(SyncStringeeXInput input)
        {
            var res = await PostLoyaltyAsync<LoyaltyResponse<SyncStringeeXOutput>>(LoyaltyApiUrl.TICKET_MNGMT_SYNC, input);
            return res.Result;
        }

        public async Task<ReactCSTicketOutput> ReactCSTicket(ReactCSTicketDTO input)
        {
            var res = await PostLoyaltyAsync<LoyaltyResponse<ReactCSTicketOutput>>(LoyaltyApiUrl.TICKET_MNGMT_React, input);
            return res.Result;
        }
        
        public async Task<GetListThirdpartyVendorOutput> GetListThirdPartyVendors(GetListThirdpartyVendorInput input)
        {
            var res = await GetLoyaltyAsync<LoyaltyResponse<GetListThirdpartyVendorOutput>>(LoyaltyApiUrl.GET_THIRDPARTY_GIFT_VENDORS, input);
            return res.Result;
        }

        public async Task<GetMiniAppOutput> GetMiniApps(GetMiniAppInput input)
        {
            var res = await GetLoyaltyAsync<LoyaltyResponse<GetMiniAppOutput>>(LoyaltyApiUrl.MiniApp_GetAll, input);
            return res.Result;
        }
        
        public async Task<CategoriesResponse> GetAllWithoutMemberCodeForHomePage(object query)
        {
            return await GetLoyaltyAsync<CategoriesResponse>(LoyaltyApiUrl.GetAllWithoutMemberCodeForHomePage, query);
        }

        public async Task<GiftListResponse> GetAllGiftWithoutMemberCode(object input)
        {
            return await GetLoyaltyAsync<GiftListResponse>(LoyaltyApiUrl.GetAllGiftWithoutMemberCode, input);
        }
        public async Task<GetByIdAndRelatedGiftOutput> GetGiftDetailWithoutMemberCode(object input)
        {
            var ret = await GetLoyaltyAsync<GetByIdAndRelatedGiftOutput>(LoyaltyApiUrl.GetByIdAndRelatedGiftWithoutMemberCode, input);
            if (string.IsNullOrWhiteSpace(ret.Result.ErrorCode))
            {
                return ret;
            }
            ret.Success = false;
            return ret;
        }
    }
}
