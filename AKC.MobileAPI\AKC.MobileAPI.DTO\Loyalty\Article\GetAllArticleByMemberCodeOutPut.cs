﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Article
{
    public class GetAllArticleByMemberCodeOutPut
    {
        public ListResultGetAllArticleAndRelatedNewsOutput Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }
    public class GetAllArticlePopupOutput
    {
        public GetAllArticlePopupOutputChildren Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }

    public class GetAllArticlePopupOutputChildren
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Content { get; set; }
        public string Avatar { get; set; }
        public string LinkAvatar { get; set; }
        public string Link { get; set; }
        public string ExtraType { get; set; }
        public string TargetID { get; set; }
    }
}
