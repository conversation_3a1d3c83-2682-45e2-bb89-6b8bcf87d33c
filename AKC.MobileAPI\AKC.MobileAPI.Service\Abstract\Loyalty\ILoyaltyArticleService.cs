﻿using AKC.MobileAPI.DTO.Loyalty.Article;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.LocationManagement;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyArticleService
    {
        Task<LoyaltyArticleGetAllOutput> GetAll(LoyaltyArticleGetAllInput input);
        Task<LoyaltyResponseList<ArticleDto>> GetBanners(int SkipCount, int MaxResultCount);

        Task<GetArticleForEditOutput> GetArticleByIdAndRelatedNews(GetArticleByIdAndRelatedNewsInput input);

        Task<GetArticleForEditOutput> GetArticleByIdByMemberCode(GetArticleByIdByMemberCodeInput input);

        Task<GetAllArticleAndRelatedNewsOutput> GetAllArticleAndRelatedNews(GetAllArticleAndRelatedNewsInput input);
        Task<GetAllArticleAndRelatedNewsOutput_Optimize> GetAllArticleAndRelatedNews_Optimize(GetAllArticleAndRelatedNewsInput_Optimize input);

        Task<GetAllArticleAndRelatedNewsOutput_Optimize> GetAllArticleAndRelatedNews_Optimize_V1(GetAllArticleAndRelatedNewsInput_Optimize_New input);
        Task<GetAllArticleByMemberCodeOutPut> GetAllArticleByMemberCode(GetAllArticleByMemberCodeInput input);
        Task<GetAllArticlePopupOutput> GetPopUpOnApp(GetPopupOnAppInput input);

    }
}
