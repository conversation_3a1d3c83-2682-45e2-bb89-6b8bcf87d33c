﻿using AKC.MobileAPI.DTO.Loyalty.EGiftInfors;
using AKC.MobileAPI.Service.Abstract.Loyalty.EGiftInfors;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/EGiftInfors")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyEGiftInfosController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltyEGiftInforsService _loyaltyEGiftInforsService;
        private readonly ICommonHelperService _commonHelperService;

        public LoyaltyEGiftInfosController(
            ILogger<LoyaltyThirdPartyPointsController> logger,
            IExceptionReponseService exceptionReponseService,
            ILoyaltyEGiftInforsService loyaltyEGiftInforsService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _exceptionReponseService = exceptionReponseService;
            _loyaltyEGiftInforsService = loyaltyEGiftInforsService;
            _commonHelperService = commonHelperService;
        }

        [HttpPut]
        [Route("UpdateGiftStatus")]
        public async Task<ActionResult<UpdateGiftStatusOutput>> UpdateGiftStatus(UpdateGiftStatusInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyEGiftInforsService.UpdateGiftStatus(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "EGiftInfors UpdateGiftStatus Error - " + JsonConvert.SerializeObject(ex));

                return StatusCode(400, res);
            }
        }

        //[HttpPut]
        //[Route("UpdateEgiftCode")]
        //public async Task<ActionResult<UpdateEgiftCodeOutput>> UpdateEgiftCode(UpdateEgiftCodeInputDto input)
        //{
        //    try
        //    {
        //        var result = await _loyaltyEGiftInforsService.UpdateEgiftCode(input);
        //        return StatusCode(200, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
        //        _logger.LogError(ex, "EGiftInfors UpdateEgiftCode Error - " + JsonConvert.SerializeObject(ex));

        //        return StatusCode(400, res);
        //    }
        //}
    }
}
