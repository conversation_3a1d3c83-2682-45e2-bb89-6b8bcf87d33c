﻿using System;

namespace AKC.MobileAPI.DTO.Reward.TopUpTransaction
{
    public class RewardCreateTopUpTransactionInput
    {
        public string MemberCode { get; set; }
        public DateTime RequestDate { get; set; }
        public double TokenAmount { get; set; }
        public string PaymentTransactionId { get; set; }
        public string PaymentTransactionStatus { get; set; }
        public int MerchantId { get; set; }
    }

    public class RewardCreateTopUpTransactionInputDto
    {
        public string NationalId { get; set; }
        public int MerchantId { get; set; }
        public DateTime RequestDate { get; set; }
        public double TokenAmount { get; set; }
        public string PaymentTransactionId { get; set; }
        public string PaymentTransactionStatus { get; set; }
        public string PartnerTransactionStatusCode { get; set; }
    }
}
