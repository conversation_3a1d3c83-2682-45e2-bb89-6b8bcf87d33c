﻿using AKC.MobileAPI.DTO.Gamification;
using AKC.MobileAPI.DTO.Loyalty;
using AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer;
using AKC.MobileAPI.Service.Abstract.Gamification;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace AKC.MobileAPI.Service.Gamification
{
    public class GamificationManagementService : GameManagementBaseService, IGameManagementService
    {
        protected readonly IConfiguration _configuration;
        protected static string pubKey;
        protected static string priKey;
        ILoyaltySecondaryCustomersService _customersService;
        public GamificationManagementService(
            IConfiguration configuration,
            IDistributedCache memoryCache,
            ILoyaltySecondaryCustomersService customersService
            ) : base(configuration, memoryCache)
        {
            _configuration = configuration;
            pubKey = _configuration.GetSection("keys:publickey").Value;
            priKey = _configuration.GetSection("keys:privatekey").Value;
            _customersService = customersService;
        }
        public async Task<ViewGameListOutput> ViewGameList(ViewGameListInput input)
        {
            return await GetGamificationManagementAsync<ViewGameListOutput>(GamificationApiUrl.VIEW_GAME_LIST, input);
        }

        public async Task<GetGamificationLinkOutput> GetGamificationLink(GetGamificationLinkInput input)
        {
            var tenantId = RSAHelper.Encrypt(input.TenantId.ToString(), pubKey);
            var memberCodeEncrypt = RSAHelper.Encrypt(input.MemberCode, pubKey);
            //var data = RSAHelper.Decrypt(memberCodeEncrypt, priKey);
            var link = new GetGamificationLinkDto()
            {
                url = GamificationApiUrl.GAMIFICATION_LINK + HttpUtility.UrlEncode(tenantId) + "&code=" + HttpUtility.UrlEncode(memberCodeEncrypt)
            };

            return new GetGamificationLinkOutput()
            {
                item = link
            };
        }


        public async Task<GetAuthenticationKeyOutput> GetAuthenticationKey(GetAuthenticationKeyInput input)
        {

            var authenCacheKey = "MobileApi_LinkIDGamePlatform_Authenticate";
            var cacheValue = await GetCacheValueByKey(authenCacheKey);
            string secretKey;
            string secretIv;
            //Không lấy đc từ cache thì get từ config
            if (string.IsNullOrEmpty(cacheValue))
            {
                secretKey = _configuration.GetSection("LinkIDGamePlatform:SecretKey").Value;
                secretIv = _configuration.GetSection("LinkIDGamePlatform:SecretIv").Value;
                cacheValue = string.Format("{0}_{1}", secretKey, secretIv);
                await SetCacheByKeyValue(authenCacheKey, cacheValue, 30);
            }
            else
            {
                var cacheArr = cacheValue.Split('_');
                secretKey = cacheArr[0];
                secretIv = cacheArr[1];
            }
            var timeStamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");
            var keybytes = Encoding.UTF8.GetBytes(secretKey);
            var iv = Encoding.UTF8.GetBytes(secretIv);
            var encryptObj = new AuthenticationKeyEncrypt()
            {
                memberCode = input.MemberCode,
                userName = input.MemberName,
                email = input.Email,
                phone = input.PhoneNumber,
                createdTime = timeStamp
            };
            var plainText = JsonConvert.SerializeObject(encryptObj);
            string encryptedText = AesEncryption.encrypt(plainText, keybytes, iv);

            var result = new GetAuthenticationKeyOutput();
            result.AuthenticationKey = encryptedText;

            return result;
        }
    }
}
