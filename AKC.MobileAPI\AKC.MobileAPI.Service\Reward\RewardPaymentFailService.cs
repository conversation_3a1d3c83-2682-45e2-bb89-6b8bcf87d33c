﻿using AKC.MobileAPI.DTO.Reward.CashoutTransaction;
using AKC.MobileAPI.DTO.Reward.PaymentFail;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardPaymentFailService : RewardBaseService, IRewardPaymentFailService
    {
        public RewardPaymentFailService(IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<RewardPaymentFailCreateOutput> Create(RewardPaymentFailCreateInput input)
        {
            return await PostRewardAsync<RewardPaymentFailCreateOutput>(RewardApiUrl.PAYMENT_FAIL_CREATE, input);
        }
    }
}
