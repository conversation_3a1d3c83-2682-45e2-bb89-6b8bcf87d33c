using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.Service;
using AKC.MobileAPI.Service.Exceptions;
using AKC.MobileAPI.Service.Helpers;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.IO;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Helper
{
    public class ValidateSignatureMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly RecyclableMemoryStreamManager _recyclableMemoryStreamManager;

        public ValidateSignatureMiddleware(RequestDelegate next)
        {
            _next = next;
            _recyclableMemoryStreamManager = new RecyclableMemoryStreamManager();
        }

        public async Task Invoke(HttpContext context)
        {
            try
            {
                context.Request.EnableBuffering();
                var requestStream = _recyclableMemoryStreamManager.GetStream();
                await context.Request.Body.CopyToAsync(requestStream);

                //var bodyString = await new StreamReader(requestStream, Encoding.Default).ReadToEndAsync();
                var bodyString = ReadStreamInChunks(requestStream);

                if (!string.IsNullOrEmpty(bodyString))
                {
                    var bodyRequest = JsonConvert.DeserializeObject<Dictionary<string, object>>(bodyString);
                    var body = bodyRequest.CreateObject();
                    var bodyNotSignature = body.Item1;
                    var signature = body.Item2;

                    //Retrive publickey config.
                    var configuration = AccessConfigurationService.Instance.GetConfiguration();
                    var secret = configuration.GetValue<string>("HmacSHA512Secret");

                    if (!SignatureHelper.ValidateSignature(bodyNotSignature, signature, secret))
                    {
                        context.Response.StatusCode = 400;
                        context.Response.ContentType = "application/json";
                        var error = new RewardDataExceptionResponse()
                        {
                            result = new RewardDataExceptionResultItem()
                            {
                                code = "InvalidSignature",
                                message = "Invalid signature",
                            },
                            status = 500
                        };

                       string jsonString = JsonConvert.SerializeObject(error);
                       await context.Response.WriteAsync(jsonString, Encoding.UTF8);
                       // to stop futher pipeline execution 
                       return;
                    }
                }

                await _next(context);
            }
            catch
            {
                context.Response.StatusCode = 400;
                context.Response.ContentType = "application/json";
                var error = new RewardDataExceptionResponse()
                {
                    result = new RewardDataExceptionResultItem()
                    {
                        code = "InvalidSignature",
                        message = "Invalid signature",
                    },
                    status = 500
                };

                string jsonString = JsonConvert.SerializeObject(error);
                await context.Response.WriteAsync(jsonString, Encoding.UTF8);
                // to stop futher pipeline execution 
                return;
            }
        }

        private static string ReadStreamInChunks(Stream stream)
        {
            const int readChunkBufferLength = 4096;
            stream.Seek(0, SeekOrigin.Begin);
            var textWriter = new StringWriter();
            var reader = new StreamReader(stream);
            var readChunk = new char[readChunkBufferLength];

            int readChunkLength;
            do
            {
                readChunkLength = reader.ReadBlock(readChunk, 0, readChunkBufferLength);
                textWriter.Write(readChunk, 0, readChunkLength);
            } while (readChunkLength > 0);

            return textWriter.ToString();
        }
    }
}
