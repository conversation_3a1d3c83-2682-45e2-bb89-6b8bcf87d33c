﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.GiftSearch
{
    public class SearchInput
    {
        public string MemberCode { get; set; }
        public string SearchKey { get; set; }
        public string SearchType { get; set; }
        public string GiftCategoryChannelCode { get; set; }
        public string FullGiftCategoryCodeFilter { get; set; } = string.Empty;
        public decimal? MinCoin { get; set; }
        public decimal? MaxCoin { get; set; }
        public bool? isEgift { get; set; }
        public string RegionCode { get; set; } = string.Empty;
        public int MaxResultCount { get; set; }
        public int SkipCount { get; set; }
        public string Sorting { get; set; } = string.Empty;
    }

    public class SuggestInput
    {
        public string Suggest { get; set; }
    }
    public class SuggestOutput
    {
        public List<string> ListSuggest { get; set; } = new List<string>();
    }

    public class SuggestOutputView
    {
        public SuggestOutput Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }

}
