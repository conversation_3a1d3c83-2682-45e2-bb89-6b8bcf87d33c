﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Article
{
    public class GetAllArticleAndRelatedNewsInput
    {
        public string Filter { get; set; }
        [Required]
        public string CategoryTypeFilter { get; set; }
        public string TagsFilter { get; set; }
        public int MaxItem { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
        public string Sorting { get; set; }

    }

    public class GetAllArticleAndRelatedNewsInput_Optimize
    {
        public string Filter { get; set; }
        public string CategoryTypeFilter { get; set; }
        public string TagsFilter { get; set; }
        public int MaxItem { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
        public string Sorting { get; set; }

    }
}
