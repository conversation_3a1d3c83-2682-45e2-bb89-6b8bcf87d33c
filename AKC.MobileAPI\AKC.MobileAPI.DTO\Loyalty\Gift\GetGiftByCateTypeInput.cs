﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class GetGiftByCateTypeInput
    {
        [Required]
        public string MemberCode { get; set; }
        [Required]
        public int GroupTypeFilter { get; set; }

        public string Language { get; set; } = "vi";

        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
        public string Sorting { get; set; }
        public string ChannelCode { get; set; }
    }

    public class GetGiftGroupByCateOutput
    {
        public int totalCount { get; set; }
        public List<GetGiftGroupByCateDto> items { get; set; } = new List<GetGiftGroupByCateDto>();
    }

    public class GetGiftGroupByCateDto
    {
        public string GroupName { get; set; }
        public string GroupCode { get; set; }

        public int GroupId { get; set; }

        public List<GiftInforByCateDTO> ListGift { get; set; } = new List<GiftInforByCateDTO> { };
    }

    public class GiftInforByCateDTO
    {
        public GiftShortInforDto GiftInfor { get; set; }
        public List<ImageLinkDto> ImageLink { get; set; }
        public FlashSaleProgramDto FlashSaleProgramInfor { get; set; }

        public GiftDiscountDto GiftDiscountInfor { get; set; }
    }

    public class GetGiftGroupByCateOutputForView
    {
        public GetGiftGroupByCateOutput Result { get; set; }
        public string TargetUrl { get; set; }

        public bool Success { get; set; }

        public string Error { get; set; }

        public bool UnAuthorizedRequest { get; set; }

        public bool __abp { get; set; }
    }
}
