﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.LoyaltyVpbank.Member
{
    public class VPBankCustomerInfo
    {
        public string customerNumber { get; set; }
        public List<contactList> contact { get; set; }
        public string legalId { get; set; }
    }
    public class VPBankCustomerInfoOutput
    {
        public bool success { get; set; }
        public string error { get; set; }
        public string errorCode { get; set; }
        public VPBankCustomerInfo customerInfor { get; set; }
    }
    public class contactList
    {
        public string contactType { get; set; }
        public string contactInfo { get; set; }
    }

    public class DeleteUserMappingResponse
    {
        public bool success { get; set; }
        public string errorCode { get; set; }
        public string errorMessage { get; set; }
    }
}
