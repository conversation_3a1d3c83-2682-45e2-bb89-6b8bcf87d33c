using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.Service.Constants
{
    public class LoyaltyApiUrl
    {
        public const string CREATE_SECONDARY_CUSTOMERS = "services/app/SecondaryCustomers/CreateOrEdit";
        public const string GETALL_GIFT_CATEGORY = "services/app/GiftCategory/GetAll";
        public const string GIFT_INFORS_GETAll_FOR_CATEGORY = "services/app/GiftInfors/GetAllForCategory";
        public const string GIFT_INFORS_GETAll_FOR_CATEGORY_ALL_MERCHANT = "services/app/GiftInfors/GetAllForCategoryForAllMerchant";
        public const string GIFT_GROUPS_GETAll_WITH_IMAGE = "services/app/GiftGroups/GetAllWithImage";
        public const string GIFT_GROUPS_GETAll_WITH_IMAGE_BY_MEMBER = "services/app/GiftGroups/GetAllWithImageByMemberCode";
        public const string GIFT_INFORS_GETAll_MEMBER_CODE = "services/app/GiftInfors/GetAllByMemberCode";
        public const string GIFT_INFORS_GETAll_MEMBER_CODE_ALL_MERCHANT = "services/app/GiftInfors/GetAllByMemberCodeForAllMerchant";
        public const string GIFT_INFORS_GET_GIFT_MEMBER_CODE = "services/app/GiftInfors/GetGiftByMemberCode";
        public const string GIFT_INFORS_GET_GIFT_MEMBER_CODE_All_MERCHANT = "services/app/GiftInfors/GetGiftByMemberCodeForAllMerchant";
        public const string GIFT_INFORS_GET_GIFT_CATEGORIES_MEMBER_CODE = "services/app/GiftInfors/GetAllGiftCategoriesAndInfo";
        public const string GIFT_INFORS_GET_GIFT_CATEGORIES_MEMBER_CODE_ALL_MERCHANT = "services/app/GiftInfors/GetAllGiftCategoriesAndInfoForAllMerchant";
        public const string GIFT_INFORS_GET_GIFT_CATEGORIES_MEMBER_CODE_V1 = "services/app/GiftInfors/GetAllGiftCategoriesAndInfo_V1";
        public const string GIFT_INFORS_GET_GIFT_CATEGORIES_MEMBER_CODE_V1_ALL_MERCHANT = "services/app/GiftInfors/GetAllGiftCategoriesAndInfo_V1ForAllMerchant";
        public const string GIFT_INFORS_GET_GIFT_ALL_INFORS_MEMBER_CODE = "services/app/GiftInfors/GetAllInfors";
        public const string GIFT_INFORS_GET_GIFT_ALL_INFORS_MEMBER_CODE_ALL_MERCHANT = "services/app/GiftInfors/GetAllInforsForAllMerchant";
        public const string GIFT_INFORS_GET_GIFT_BY_ID_AND_RELATED_GIFT_MEMBER_CODE = "services/app/GiftInfors/GetByIdAndRelatedGift";
        public const string GIFT_INFORS_GET_GIFT_BY_ID_AND_RELATED_GIFT_MEMBER_CODE_ALL_MERCHANT = "services/app/GiftInfors/GetByIdAndRelatedGiftForAllMerchant";

        public const string GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY = "services/app/GiftInfors/GetAllEffectiveCategory";
        public const string GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY_ALL_MERCHANT = "services/app/GiftInfors/GetAllEffectiveCategoryForAllMerchant";
        public const string GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY_V1 = "services/app/GiftInfors/GetAllEffectiveCategory_v1";
        public const string GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY_V1_ALL_MERCHANT = "services/app/GiftInfors/GetAllEffectiveCategory_v1ForAllMerchant";
        public const string GIFT_INFORS_WISH_LIST_BY_MEMBER = "services/app/GiftInfors/GetWishlistByMember";
        public const string GIFT_INFORS_WISH_LIST_BY_MEMBER_All_MERCHANT = "services/app/GiftInfors/GetWishlistByMemberForAllMerchant";
        public const string BRAND_WISH_LIST_BY_MEMBER_All_MERCHANT = "services/app/GiftInfors/GetBrandWishlistByMemberForAllMerchant";
        public const string GIFT_INFORS_UPDATE_WISH_LIST = "services/app/GiftInfors/UpdateWishlist";
        public const string GIFT_INFORS_UPDATE_WISH_LIST_ALL_MERCHANT = "services/app/GiftInfors/UpdateWishlistForAllMerchant";
        public const string GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY_GROUP_BY_BRAND = "services/app/GiftInfors/GetAllEffectiveCategoryGroupByBrand";
        public const string GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY_GROUP_BY_BRAND_All_MERCHANT = "services/app/GiftInfors/GetAllEffectiveCategoryGroupByBrandForAllMerchant";

        public const string NOTIFICATION_HISTORY_GET_ALL_BY_MEMBER_CODE = "services/app/NotificationHistory/GetAll";
        public const string NOTIFICATION_HISTORY_CHANGE_STATUS = "services/app/NotificationHistory/ChangeStatus";
        public const string NOTIFICATION_HISTORY_CHANGE_NOTIFICATION_COUNT = "services/app/NotificationHistory/ChangeNotificationCount";
        public const string NOTIFICATION_HISTORY_DELETE = "services/app/NotificationHistory/Delete";
        public const string NOTIFICATION_HISTORY_HARD_DELETE = "services/app/NotificationHistory/HardDelete";
        public const string NOTIFICATION_SEND_NOTI_TO_OTHERDEVICE_EXCEPT_NEWESTDEVICE
            = "services/app/NotificationHistory/SendNotiToOtherDeviceWhenDeviceChange";
        public const string MEMBER_UPDATE_PHONE_NUMBER = "services/app/SecondaryCustomers/UpdatePhoneNumber";



        public const string USEPOINT_PRECALCULATEPOINT = "services/app/UsePoint/PreCalculatePoint";

        public const string ORDER_PURCHASEAGENT = "services/app/Order/PurchaseAgent";

        public const string REWARDS_INPUTACTION = "services/app/Rewards/InputAction";

        public const string GIFTTRANSACTION_CREATEREDEEMTRANSACTION = "services/app/GiftTransactions/CreateRedeemTransaction";
        public const string GIFTTRANSACTION_GETALL_WITH_EGIFT = "services/app/GiftTransactions/GetAllWithEGift";
        public const string GIFTTRANSACTION_GETALL_WITH_EGIFT_ALL_MERCHANT = "services/app/GiftTransactions/GetAllWithEGiftForAllMerchant";
        public const string MERCHANT_GIFT_GET_ORDER_LIST = "services/app/GiftTransactions/GetAllWithEGiftForOpesEtAl";
        public const string GIFTTRANSACTION_GETALL_WITH_TOPUP_PHONE = "services/app/GiftTransactions/GetAllGiftTransactionTopupPhone";
        public const string GIFTTRANSACTION_GETALL_WITH_TOPUP_PHONE_V1 = "services/app/GiftTransactions/GetAllGiftTransactionTopupPhone_v1";
        public const string GIFTTRANSACTION_GET_CUSTOMER_INFO_FROM_TRANSACTION = "services/app/GiftTransactions/GetCustomerInfoFromTransaction";
        public const string GET_MERCHANT_ID_FROM_GIFT_CODE = "services/app/GiftInfors/GetMerchantIdFromGiftCode";

        public const string GIFTTRANSACTION_USER_VERIFYING = "services/app/GiftTransactions/UserVerifying";

        public const string GIFTTRANSACTION_GIFT_TRANSFER = "services/app/GiftTransactions/GiftTransfer";
        public const string GIFTTRANSACTION_UPDATE_RECIPIENT_LEGALID = "services/app/GiftTransactions/UpdateRecipientLegalId";

        public const string ARTICLE_GETALL = "services/app/Article/GetAll";
        public const string ARTICLE_GETALL_BANNERS = "services/app/Article/GetAllBanners";

        public const string GET_ARTICLE_BY_ID = "services/app/Article/GetArticleByIdAndRelatedNews";
        public const string GET_ALL_ARTICLE_BY_MEMBERCODE = "services/app/Article/GetAllArticleByMemberCode";

        public const string GET_ALL_ARTICLE_AND_RELATED_NEWS = "services/app/Article/GetAllArticleAndRelatedNews";
        public const string GET_ALL_ARTICLE_AND_RELATED_NEWS_OPTIMIZE = "services/app/Article/GetAllArticleAndRelatedNews_Optimize";
        public const string GET_ONE_ARTICLE_POPUP = "services/app/Article/GetPopUpOnApp";

        public const string GET_ARTICLE_BY_ID_BY_MEMBERCODE = "services/app/Article/GetArticleByIdByMemberCode";

        public const string GET_LIST_BY_GROUP = "services/app/Quest/GetListByGroup";

        public const string GET_DETAIL = "services/app/Quest/GetDetail";

        public const string GET_LIST_QUEST_BY_ACTIVITY = "services/app/Quest/GetListQuestByActivity";

        public const string QUEST_JOIN = "services/app/Quest/Join";

        public const string QUEST_VIEW_ACTUAL = "services/app/Quest/ViewActual";

        public const string QUEST_CLAIM = "services/app/Quest/Claim";

        public const string GET_PROFILE_BY_CODE = "services/app/SecondaryCustomers/GetProfileByCode";

        public const string GET_MEMBER_INFO_BY_CODE = "services/app/SecondaryCustomers/GetMemberInfoByCode";

        public const string VERIFY_REFERRAL_CODE = "services/app/ReferralReward/VerifyReferralCode";

        public const string QUEST_GET_ALL = "services/app/Quest/GetAll";

        public const string QUEST_GET_LIST_BY_MEMBERCODE = "services/app/Quest/GetListByMemberCode";


        public const string QUEST_STAGE_JOIN = "services/app/QuestStage/Join";
        public const string QUEST_STAGE_CLAIM = "services/app/QuestStage/Claim";
        public const string QUEST_STAGE_OR_LEVEL_CLAIM = "services/app/QuestStage/ClaimQuestStageOrLevel";
        public const string QUEST_STAGE_GET_ALL = "services/app/QuestStage/GetAll";
        public const string QUEST_STAGE_VIEW_ACTUAL = "services/app/QuestStage/ViewActual";
        public const string QUEST_STAGE_GET_LIST_BY_MEMBERCODE = "services/app/QuestStage/GetListQuestAndQuestStageByMemberCode";
        public const string GET_QUEST_STAGE_DETAIL = "services/app/QuestStage/GetQuestStageDetail";
        public const string GET_LIST_QUEST_STAGE_JOINED_BY_MEMBER = "services/app/QuestStage/GetListQuestStageJoinedByMember";
        public const string QUEST_OR_QUEST_STAGE_GET_LIST_BY_MEMBERCODE = "services/app/QuestStage/GetListQuestAndQuestStageByMemberCode";
        public const string QUEST_STAGE_GET_DETAIL = "services/app/QuestStage/GetDetail";


        public const string FAQ_GET_ALL = "services/app/FAQ/GetAll";
        public const string FAQ_GET_Advance = "services/app/FAQ/GetAllAdvanceForMB";


        public const string THIRD_PARTY_POINT_VIEW = "services/app/ThirdPartyPoints/View";

        public const string THIRD_PARTY_VERIFY_NATIONAL_ID = "services/app/ThirdPartyVerification/VerifyNationalId";

        public const string THIRD_PARTY_VERIFY_OTP = "services/app/ThirdPartyVerification/VerifyOTP";

        public const string THIRD_PARTY_POINT_EXCHANGE = "services/app/ThirdPartyPoints/Exchange";

        public const string THIRD_PARTY_REVERT_POINT = "services/app/ThirdPartyPoints/Revert";
        public const string CHECK_REFERRAL_CODE_EXISTANCE = "services/app/ReferralReward/CheckReferralCodeExistance";

        public const string GIFT_INFORS_GET_ALL_BY_MEMBER_CODE = "services/app/GiftCategory/GetAllByMemberCode";

        public const string GIFT_INFORS_GET_ALL_FOR_CATEGORY_BY_MEMBER_CODE = "services/app/GiftInfors/GetAllForCategoryByMemberCode";
        public const string EGIFT_INFORS_CHECK_VOUCHER_TOPUP = "services/app/EGiftInfors/CheckEGift";

        public const string EGIFT_INFORS_CHECK_MEMBER_BLOCKED = "services/app/EGiftInfors/CheckMemberBlocked";
        public const string EGIFT_INFORS_USE_VOUCHER_TOPUP = "services/app/EGiftInfors/UseEGift";
        public const string EGIFT_INFORS_REVERT_VOUCHER_TOPUP = "services/app/EGiftInfors/RevertEGift";
        public const string GIFT_INFORS_GET_ALL_FOR_CATEGORY_BY_MEMBER_CODE_ALL_MERCHANT = "services/app/GiftInfors/GetAllForCategoryByMemberCodeForAllMerchant";

        public const string UPDATE_MEMBER_BALANCE = "services/app/Adjust/UpdateMemberBalance";

        public const string CREATE_OPERATION_LOG = "services/app/AuditLog/CreateOperationLog";

        public const string UPDATE_NOTIFICATION_SETTING = "services/app/SecondaryCustomers/UpdateNotificationSetting";

        public const string EGIFT_INFORS_UPDATE_GIFT_STATUS = "services/app/EGiftInfors/UpdateGiftStatus";

        public const string GET_LANGUAGES_TEXT = "services/app/Language/GetListLanguageTexts";
        public const string LOCATION_GET_ALL = "services/app/LocationManagement/GetAllLocation";
        public const string LOCATION_GET_ALL_NO_PAGING = "services/app/LocationManagement/GetAllLocationNoPaging";
        public const string LOCATION_VIEW_BY_IDS = "services/app/LocationManagement/ViewLocationByIds";
        public const string UPDATE_EGIFT_CODE = "services/app/EGiftInfors/UpdateEgiftCode";
        public const string GET_LIST_EGIFT_TOPUP = "services/app/EGiftInfors/GetListEGiftTopup";

        // For vpbank loyalty exchange
        public const string VPBANK_LOYALTY_EXCHANGE_CHECK_ID_CARD = "services/app/SecondaryCustomers/CheckIdCard";
        public const string VPBANK_LOYALTY_EXCHANGE_VERIFY_ID_CARD = "services/app/SecondaryCustomers/VerifyIdCard";
        public const string VPBANK_LOYALTY_EXCHANGE_VERIFY_OTP = "services/app/SecondaryCustomers/VerifyOTP";
        public const string VPBANK_LOYALTY_REMOVE_CONNECT = "services/app/SecondaryCustomers/RemoveConnect";
        public const string VPBANK_LOYALTY_CONFIRM_CONNECT = "services/app/SecondaryCustomers/ConfirmConnect";
        public const string VPBANK_LOYALTY_UPDATE_PHONE_NUMBER = "services/app/SecondaryCustomers/UpdateConnectPhoneNumber";
        public const string VPBANK_LOYALTY_SEND_OTP_CONFIRM_CONNECT = "services/app/SecondaryCustomers/SendOtpConfirmConnect";
        public const string VPBANK_LOYALTY_VERIFY_OTP_CONFIRM_CONNECT = "services/app/SecondaryCustomers/VerifyOtpConfirmConnect";
        public const string VPBANK_LOYALTY_GET_CIF_CODE = "services/app/SecondaryCustomers/GetCifCodeByIdCard";

        // For insurance
        public const string INSURANCE_GETPROGRAM = "services/app/Insurance/FindProgram";
        public const string INSURANCE_GETPRODUCT = "services/app/Insurance/FindProduct";
        public const string INSURANCE_GETPACKAGE = "services/app/Insurance/FindPackage";
        public const string INSURANCE_GETCONTRACT = "services/app/Insurance/FindContract";
        public const string INSURANCE_PURCHASE_PACKAGE = "services/app/Insurance/PurchasePackage";
        public const string INSURANCE_DETAIL = "services/app/Insurance/FindProductByCode";

        // For loyalty brand
        public const string BRAND_GET_ALL = "services/app/Brand/GetAll";
        public const string BRAND_GET_ALL_BY_CATEGORY = "services/app/Brand/GetAllBrandByCategoryCode";
        public const string PROMINENT_BRAND_GET_LIST = "services/app/ProminentBrand/GetListProminentBrand";

        public const string VERIFY_OR_CREATE_REDEEM_ORDER = "services/app/GiftTransactions/VerifyAndCreateRedeemOrder";
        public const string UPDATE_ERROR_WHEN_CREATE_REDEEM = "services/app/GiftTransactions/HandleErrorWhenCreateRedeemPayment";
        public const string GET_GIFT_INFO_BY_ORDER_CODE = "services/app/GiftTransactions/GetGiftInfoFromOrderCode";

        // For loyalty InputReference
        public const string CREATE_INPUT_REFERENCE = "services/app/InputReference/CreateOrEdit";

        // For loyalty thirdPartyBrandMapping
        public const string GET_ALL_THIRD_PARTY_BRAND_BY_VENDOR_NAME = "services/app/ThirdPartyBrandMapping/GetAllThirdPartyBrandByVendorName";
        public const string GET_ALL_THIRD_PARTY_BRAND_BY_VENDOR_TYPE = "services/app/ThirdPartyBrandMapping/GetAllThirdPartyBrandByVendorType";

        //For loyalty FlashSale program
        public const string GETALL_FLASHSALE_PROGRAM = "services/app/FlashSaleProgram/GetAllFlashSaleProgramForAllMerchant";
        public const string GETALL_GIFT_CATEGORY_FLASHSALE_PROGRAM = "services/app/FlashSaleProgram/GetAllGiftCategoryFlashSaleProgramForAllMerchant";

        // For gift flash sale
        public const string GET_GIFT_DETAIL_FLASH_SALE_PROGRAM = "services/app/GiftFlashSaleProgram/GetGiftDetailFlashSaleProgramForMB";
        public const string GET_LIST_GIFT_BY_FLASH_SALE_PROGRAM = "services/app/GiftFlashSaleProgram/ListGiftByFlashSaleProgramForMB";
        public const string GET_LIST_FLASH_SALE_PROGRAM = "services/app/GiftFlashSaleProgram/GetListFlashSaleProgramForMB";
        public const string DELETE_LOY_ACCOUNTS = "services/app/SecondaryCustomers/DeleteAccount";

        // Get List Categories
        public const string GET_LIST_CATEGORIES = "services/app/GiftInfors/GetGiftCategoriesForMB";
        public const string GET_LIST_CATEGORIES_V1 = "services/app/GiftInfors/GetGiftCategoriesForMB_v1";
        public const string GET_GIFT_ALL_INFOR = "services/app/GiftInfors/GetAllInforsForMB";
        public const string GetGiftGroupForHomepageV1dot1 = "services/app/GiftInfors/GetGiftGroupForHomepageV1dot1";
        public const string GET_GIFTS_BY_BRAND = "services/app/GiftInfors/ListGiftInforByBrand";

        public const string CAMPAIGN_GIVE_COIN_GET_ACTIVE_CAMPAIGN = "services/app/CampaignGiveCoinAppSerivce/GetActiveCampaign";
        public const string CAMPAIGN_GIVE_COIN_GET_MEMBER_BY_CIF = "services/app/CampaignGiveCoinAppSerivce/GetMemberByCif";
        public const string CAMPAIGN_GIVE_COIN_CREATE_MEMBER = "services/app/CampaignGiveCoinAppSerivce/Create";
        public const string CAMPAIGN_GIVE_COIN_GET_TRANSACTION = "services/app/Transactions/GetTransactionForCampaign";
        public const string GetListRegistrationByMemberCode = "services/app/CampaignGiveCoinAppSerivce/GetListRegistrationByMemberCode";
        //For gift category type
        public const string GET_GIFT_CATEGORY_TYPE_CODE = "services/app/GiftCategoryType/GetCategoryTypeByGiftCode";

        // For Quest
        public const string GET_QUEST_BY_ID = "services/app/GmQuestV2/GetByMemberCode";
        public const string GET_LIST_QUEST_ACTIVE = "services/app/GmQuestV2/GetListQuestActive";
        public const string VIEW_MEMBER_PROGRESS_ON_QUEST = "services/app/GmQuestV2/MemberProgessOnQuestInfor";
        public const string PERFORM_ACTION_QUEST = "services/app/GmPerformActionV2/PerformAction";
        public const string GET_QUEST_ACTIVE_BY_CODE = "services/app/GmQuestV2/GetQuestActiveByCode";
        public const string MEMBER_ENROLMENT_QUEST = "services/app/GmQuestEnrolmentV2/Create";
        public const string MEMBER_CLAIM_MISSION = "services/app/GmPerformActionV2/MemberClaimMission";

        public const string EARNING_RULE_GETLIST = "services/app/EarningRule/GetAllEarningRuleActive";
        public const string GETCURRENTTHEME = "services/app/ThemeSetting/GetCurrentActiveTheme";
        public const string LIKE_ITEM_GIFTORBRAND = "services/app/MemberLikes/Like";
        public const string UNLIKE_ITEM_GIFTORBRAND = "services/app/MemberLikes/Unlike";
        public const string GETCURRETN_TANDC = "services/app/Article/GetCurrentTermAndCondition";
        public const string GETCURRENT_SECPOL = "services/app/Article/GetCurrentSecPolicy";

        //For Search Bar
        public const string GETALL_SEARCH_BAR = "services/app/SearchBar/GetSearchBarActive";

        public const string GET_GIFT_USAGE_ADDRESS = "services/app/GiftUsageAddress/GetListGiftUsageAddressByGiftCode";
        public const string GET_ALL_FEEDBACK_SUGGESSTION = "services/app/FeedbackSuggesstion/GetSuggesstionForFeedback";
        public const string MERCHANT_GIFT_CREATE_REDEEM = "services/app/GiftTransactions/CreateRedeemTransactionForVpo";
        public const string GetGiftGroupbyGroupType = "services/app/GiftInfors/GetGiftGroupbyGroupType";
        public const string GIFT_INFORS_GET_ALL_EFFECTIVE_CATEGORY_TopUpPhone = "services/app/GiftInfors/GetAllEffectiveCategory_TopUpPhone";
        public const string GET_GITF_PHOTO_CARD = "services/app/GiftPhotoCard/GetAll";

        public const string CREATE_MemberFirstUseToken = "services/app/MemberFirstUseToken/Create";

        public const string VPBANK_LOYALTY_UPDATE_PHONE_NUMBER_CONNECT = "services/app/SecondaryCustomers/UpdateConnectPhoneNumberForMbAndOperator";
        public const string GetGiftByCateType_V2 = "services/app/GiftInfors/GetGiftByCateType_V2";
        public const string GetAllArticleAndRelatedNews_Optimize_NEW = "services/app/Article/GetAllArticleAndRelatedNews_Optimize_NEW";

        public const string SearchGiftAndGiftGroupAndBrandForMB = "services/app/GiftInfors/SearchGiftAndGiftGroupAndBrandForMB";
        public const string GetListSuggest = "services/app/GiftInfors/GetListSuggest";
        public const string TICKET_MNGMT_CREATE = "services/app/CSTicket/Create"; //""services/app/Image/Upload";
        public const string TICKET_MNGMT_SEARCH = "services/app/CSTicket/GetAll";
        public const string TICKET_MNGMT_React = "services/app/CSTicket/ReactCSTicket";
        public const string TICKET_MNGMT_VIEWDETAIL = "services/app/CSTicket/FindOne";
        public const string TICKET_MNGMT_SYNC = "services/app/CSTicket/Sync";
        public const string TICKET_MNGMT_ADDCOMMENT_TOTICKET = "services/app/CSTicket/AddCommentToTicket";
        public const string MiniApp_GetAll = "services/app/MiniApp/GetAll";

        public const string CheckRefCode = "services/app/SecondaryCustomers/CheckRefCode";
        public const string GetListHistoryReferral = "services/app/FriendInvitation/GetListHistoryReferral";
        public const string CheckMemberHasInvited = "services/app/SecondaryCustomers/CheckMemberHasInvited";

        public const string Verify_Refferral_Code_v2 = "services/app/ReferralReward/VerifyRefferralCode_v2";

        public const string GetInforCampaignRef = "services/app/ReferralReward/GetInforCampaignRef";
        public const string CheckMemberRegisterCampaign = "services/app/FriendReferralMembers/CheckMemberRegisterCampaign";

        public const string RegisterCampaignFriendReferral = "services/app/FriendReferralMembers/Create";
        public const string GetTransactionInforReferral = "services/app/FriendInvitation/GetTransactionInforReferral";

        public const string Wheels_GETALL = "services/app/Wheels/GetAll";
        public const string Wheels_GETALL_SYSTEMWHEEL = "services/app/Wheels/GetSystemWheel";
        public const string Wheels_GetOneWheelByMemberCode = "services/app/Wheels/GetOneWheelByMemberCode";
        public const string Wheels_CreateWheelByMemberCode = "services/app/Wheels/CreateWheelByMemberCode";
        public const string Wheels_UpdateWheelByMemberCode = "services/app/Wheels/UpdateWheelByMemberCode";
        public const string Wheels_DoSpin = "services/app/Wheels/Spin";
        public const string Wheels_DeleteMemberWheel = "services/app/Wheels/DeleteMemberWheel";
        public const string Wheels_GetSpinHistory = "services/app/WheelSpinHistory/GetByMemberAndWheel";
        public const string GET_THIRDPARTY_GIFT_VENDORS = "services/app/ThirdPartyGiftVendor/GetAll";
        public const string GET_RECOMMENED_CATEGORIES = "services/app/GiftCategory/GetRecommendedCategories";
        public const string GET_RECOMMENDED_GIFTS = "services/app/GiftInfors/GetRecommendedGifts";

        public const string CHECK_CAMPAIGN_GIFT_SCAN_QRCODE = "services/app/GiftCampaignTransactions/CheckCampaignGiftScanQrCode";
        public const string SCAN_QRCODE_GIFT_CAMPAIGN = "services/app/GiftCampaignTransactions/ScanQRCodeGiftCampaign";
        public const string GetAllWithoutMemberCode = "services/app/GiftCategory/GetAllWithoutMemberCode";
        public const string GetAllWithoutMemberCodeForHomePage = "services/app/GiftCategory/GetAllWithoutMemberCodeForHomePage";
        public const string GetAllGiftWithoutMemberCode = "services/app/GiftInfors/GetQuaWithoutMemberCode";

        public const string GetByIdAndRelatedGiftWithoutMemberCode =
            "services/app/GiftInfors/GetByIdAndRelatedGiftWithoutMemberCode";

        public const string LIST_GIFT_TO_PAY = "services/app/GiftInfors/ListGiftToPayment";
        public const string VERIFY_OR_CREATE_REDEEM_CASH_VOUCHER = "services/app/GiftTransactions/VerifyAndCreateRedeemCashVoucher";
        public const string GIFTTRANSACTION_CREATEREDEEMTRANSACTION_CASH_VOUCHER = "services/app/GiftTransactions/CreateRedeemTransactionCashVoucher";
        public const string GET_ADDRESS_BY_3IDS = "services/app/LocationMapping/GetAddressBy3Ids";


        public const string CREATE_MEMBER_ACTION_FIRST = "services/app/SecondaryCustomers/CreateMemberActionFirst";
    }
}
