FROM mcr.microsoft.com/dotnet/core/aspnet:3.1-buster-slim AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/core/sdk:3.1-buster AS build
WORKDIR /src
COPY ["./", "./"]
RUN dotnet restore "./AKC.MobileAPI/AKC.MobileAPI.csproj"
COPY . .
WORKDIR /src/AKC.MobileAPI
RUN dotnet build "AKC.MobileAPI.csproj" --no-restore -c Release -o /app/build

FROM build AS publish
RUN dotnet publish --no-restore "AKC.MobileAPI.csproj" -c Release -o /app/publish
WORKDIR /app/publish

FROM base AS final
#ssh
ENV SSH_PASSWD "root:Docker!"
RUN sed -i 's|http://deb.debian.org/debian|http://archive.debian.org/debian|g' /etc/apt/sources.list \
    && sed -i 's|http://security.debian.org/debian-security|http://archive.debian.org/debian-security|g' /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y --no-install-recommends dialog \
    && apt-get update \
    && apt-get install -y --no-install-recommends openssh-server \
    && echo "$SSH_PASSWD" | chpasswd 

COPY sshd_config /etc/ssh/
COPY init.sh /usr/local/bin/
RUN chmod 777 /usr/local/bin/init.sh \
    && ln -s /usr/local/bin/init.sh /
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["init.sh"]