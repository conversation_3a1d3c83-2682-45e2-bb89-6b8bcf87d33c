using System;

namespace AKC.MobileAPI.DTO.Webstore
{
    public class WebstoreCallRewardLoginRequest
    {
        public string SmeLicenseCode { get; set; }
        public string RepPhone { get; set; }
        public string Password { get; set; }
    }

    public class WebstoreCallRewardLoginResponse
    {
        public int Result { get; set; }
        public WebstoreCallRewardLoginResponseItem Item { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }
    public class WebstoreCallRewardLoginResponseItem
    {
        public string AccessToken { get; set; }
        public string RefreshToken { get; set; }
        public string SmeCode { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } // Nếu login thất bại
        public DateTime ExpiresAt { get; set; }
    }

    public class WebstoreGetFullSmeInfoRequest
    {
        public string SmeCode { get; set; }
        public int? SmeId { get; set; }
        public string LicenseCode { get; set; }
    }

    public class WebstoreGetFullSmeInfoResponse
    {
        public int Result { get; set; }
        public WebstoreGetFullSmeInfoItem Item { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }

    public class WebstoreGetFullSmeInfoItem
    {
        public string Status { get; set; }

        public string ShortName { get; set; }
        public string LongName { get; set; }
        public string EnglishName { get; set; }

        public string Code { get; set; }

        public string Address { get; set; }
        public string ContactEmailAddress { get; set; }
        public string ContactPhoneNumber { get; set; }

        public DateTime? StartBusinessDate { get; set; }

        public string Logo { get; set; }

        public DateTime? JoinDate { get; set; }

        public string WalletAddress { get; set; }
        public string LicenseNumber { get; set; }
        public string TaxNumber { get; set; }
        public string RepPhone { get; set; }
        public bool IsDeleted { get; set; }
        public int SmeId { get; set; }
    }
    public class WebstoreGetSmeInfoRequest
    {
        public string SmeCode { get; set; }
        public string SmeLicenseCode { get; set; }
        public string RepPhone { get; set; }
    }
    
    public class WebstoreGetSmeInfoResponse
    {
        public int Result { get; set; }
        public WebstoreGetSmeInfoResponseItem Item { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }

    public class WebstoreGetSmeBalanceRequest
    {
        public string SmeCode { get; set; }
    }
    public class WebstoreSetSmePasswordRequest
    {
        public string SmeCode { get; set; }
        public string Password { get; set; }
    }

    public class WebstoreGetSmeBalanceResponse
    {
        public int Result { get; set; }
        public WebstoreGetSmeBalanceItem Item { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }

    public class WebstoreGetSmeBalanceItem
    {
        public string SmeCode { get; set; }
        public int SmeId { get; set; }
        public string SmeWalletAddress { get; set; }
        public string ExpiringDate { get; set; }
        public decimal Balance { get; set; }
        public decimal? ExpiringTokenAmount { get; set; }
    }
    public class WebstoreSetSmePasswordResponse
    {
        public int Result { get; set; }
        public WebstoreSetSmePasswordItem Item { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }
    public class WebstoreValidateAccessTokenResponse
    {
        public int Result { get; set; }
        public WebstoreValidateAccessTokenItem Item { get; set; } // YES  NO
        public string MessageDetail { get; set; }
        public string Message { get; set; }
    }

    public class WebstoreValidateAccessTokenItem
    {
        public bool IsValid { get; set; }
        public string MemberType { get; set; }
        public string MemberCode { get; set; }
    }

    public class WebstoreSetSmePasswordItem
    {
        public bool Success { get; set; }
        public string Message { get; set; }
    }

    public class WebstoreGetSmeInfoResponseItem
    {
        public bool IsExisting { get; set; }
        public string SmeCode { get; set; }
        public string SmeName { get; set; }
        public string RepPhone { get; set; }
        public int SmeId { get; set; }
        public string SmeWalletAddress { get; set; }
        public bool HasPassword { get; set; }
    }

    public class ChangePasswordInput
    {
        public string MemberCode { get; set; }
        public string OldPassword { get; set; }
        public string NewPassword { get; set; }
    }
    public class ChangePasswordOutput
    {
        
    }

    public class WebstoreCallRedwardRedeemInput
    {
        public int MemberId { get; set; }
        public int MerchantId { get; set; }
        public string OrderCode { get; set; }
        public decimal TotalRequestedAmount { get; set; }
    }
}