﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.DTO.GiftStoreExtends;
using AKC.MobileAPI.DTO.Loyalty.GiftTransactions;
using AKC.MobileAPI.Service.Abstract.GiftStoreExtends;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Constants;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/MerchantGiftStore")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyMerchantGiftStoreController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILinkIdLoyaltyGiftStoreExtendsService _giftTransactionsService;
        private readonly IExceptionReponseService _exceptionReponseService;
        private readonly ILoyaltyLanguageService _loyaltyLanguageService;
        private readonly ICommonHelperService _commonHelperService;
        public LoyaltyMerchantGiftStoreController(
            ILogger<LoyaltyGiftTransactionsController> logger,
            ILinkIdLoyaltyGiftStoreExtendsService giftTransactionsService,
            IExceptionReponseService exceptionReponseService,
            ILoyaltyLanguageService loyaltyLanguageService,
            ICommonHelperService commonHelperService)
        {
            _logger = logger;
            _giftTransactionsService = giftTransactionsService;
            _exceptionReponseService = exceptionReponseService;
            _loyaltyLanguageService = loyaltyLanguageService;
            _commonHelperService = commonHelperService;
        }

        [HttpGet]
        [Route("GetMerchantList")]
        public async Task<ActionResult<GiftStoreVendorGetMerchantListOutput>> GetMerchantList([FromQuery] GiftStoreVendorGetMerchantListInput input)
        {
            try
            {
                var result = await _giftTransactionsService.GetMerchantList(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant redeemtion get merchant list error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var resReward = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, resReward);
                }
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetBrands")]
        public async Task<ActionResult<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetBrandOutput>>> GetBrand([FromQuery] GiftStoreVendorGetBrandInput input)
        {
            try
            {
                var result = await _giftTransactionsService.GetBrands(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant redeemtion get brand error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var resReward = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, resReward);
                }
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetCategories")]
        public async Task<ActionResult<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetCategoryOutput>>> GetCategories([FromQuery] GiftStoreVendorGetCategoryInput input)
        {
            try
            {
                var result = await _giftTransactionsService.GetCategories(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant redeemtion get categories error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var resReward = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, resReward);
                }
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetGiftList")]
        public async Task<ActionResult<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetGiftListOutput>>> GetGiftList([FromQuery] GiftStoreVendorGetGiftListInput input)
        {
            try
            {
                var result = await _giftTransactionsService.GetGiftList(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant redeemtion get gift list error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var resReward = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, resReward);
                }
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetGiftDetail")]
        public async Task<ActionResult<GiftStoreExtendsLoyaltyResponse<GiftStoreVendorGetGiftDetailOutput>>> GetGiftDetail([FromQuery] GiftStoreVendorGetGiftDetailInput input)
        {
            try
            {
                var result = await _giftTransactionsService.GetGiftDetail(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant redeemtion get gift detail error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var resReward = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, resReward);
                }
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetListOrder")]
        public async Task<ActionResult<GiftStoreExtendsLoyaltyPageResultResponse<GiftStoreVendorGetDetailOrderOutput>>> GetListOrder([FromQuery] GiftStoreVendorGetListOrderInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _giftTransactionsService.GetListOrder(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant redeemtion get list order error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var resReward = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, resReward);
                }
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);

                return StatusCode(400, res);
            }
        }

        [HttpGet]
        [Route("GetDetailOrder")]
        public async Task<ActionResult<GiftStoreExtendsLoyaltyResponse<GiftStoreVendorGetDetailOrderOutput>>> GetDetailOrder([FromQuery] GiftStoreVendorGetDetailOrderInput input)
        {
            try
            {
                var result = await _giftTransactionsService.GetDetailOrder(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Merchant redeemtion get detail order error - " + JsonConvert.SerializeObject(ex));
                if (ex.GetType() == typeof(RewardException))
                {
                    var resReward = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    return StatusCode(400, resReward);
                }
                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                return StatusCode(400, res);
            }
        }

        [HttpPost]
        [Route("CreateRedeemTransaction")]
        public async Task<ActionResult<GiftStoreExtendsCreateRedeemTransactionOutput>> CreateRedeemTransaction(GiftStoreExtendsCreateRedeemTransactionInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _giftTransactionsService.CreateRedeemTransaction(input);
                _logger.LogInformation("loyalty gift store redeem " + input.MemberCode + "_Response" + JsonConvert.SerializeObject(result));
                if (result.SuccessedOrder)
                {
                    return StatusCode(200, result);
                } else if (result.ErrorCode == "BalanceNotEnough")
                {
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_BALANCE_NOT_ENOUGH);
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = "BalanceNotEnough",
                        Message = result.Messages,
                        MessageDetail = null,
                        ListMessages = listMessages,
                    });
                }
                else
                {
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(result.ErrorCode ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    return StatusCode(400, new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = result.ErrorCode ?? "SystemError",
                        Message = result.Messages,
                        MessageDetail = null,
                        ListMessages = listMessages,
                    });
                }
            }
            catch (Exception ex)
            {

                if (ex.GetType() == typeof(RewardException))
                {
                    var res = await _exceptionReponseService.GetExceptionRewardReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogInformation("loyalty gift store redeem " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
                else
                {
                    var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                    var listMessages = await _loyaltyLanguageService.GetListMessagesByKey(res.Code ?? CommonConstants.LANGUAGE_SOURCE_NAME_REDEEM_SYSTEM_ERROR);
                    res.ListMessages = listMessages;
                    _logger.LogInformation("loyalty gift store redeem " + input.MemberCode + "_Error" + JsonConvert.SerializeObject(ex));

                    return StatusCode(400, res);
                }
            }
        }
    }
}

