﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.Member
{
    public class RewardMemberTokenTransGetByIdInput
    {
        public string Sorting { get; set; }
        public int? MaxResultCount { get; set; }
        public int? SkipCount { get; set; }
        public int? MemberId { get; set; }
        public string NationalId { get; set; }
        public int? MerchantIdFilter { get; set; }
        public string FromDateFilter { get; set; }
        public string ToDateFilter { get; set; }
        public string OrderCodeFilter { get; set; }
        public string ActionCodeFilter { get; set; }
        public string ActionTypeFilter { get; set; }
    }

    public class RewardMemberTokenTransGetByIdDto
    {
        public string sorting { get; set; }
        public int? maxResultCount { get; set; }
        public int? skipCount { get; set; }
        public int? MemberId { get; set; }
        public string NationalId { get; set; }
        public int? MerchantIdFilter { get; set; }
        public string FromDateFilter { get; set; }
        public string ToDateFilter { get; set; }
        public string OrderCodeFilter { get; set; }
        public string ActionCodeFilter { get; set; }
        public string ActionTypeFilter { get; set; }
    }
}
