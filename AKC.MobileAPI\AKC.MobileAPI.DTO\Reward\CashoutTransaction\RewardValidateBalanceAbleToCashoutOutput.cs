﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AKC.MobileAPI.DTO.Reward.CashoutTransaction
{
    public class RewardValidateBalanceAbleToCashoutOutput
    {
        public int Result { get; set; }
        public string MessageDetail { get; set; }
        public string Message { get; set; }
        public RewardBalanceAbleToCashout items { get; set; }
    }

    public class RewardBalanceAbleToCashout
    {
        public decimal AbleToCashoutBalance { get; set; }
    }
}
