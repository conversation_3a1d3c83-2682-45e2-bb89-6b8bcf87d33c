﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AKC.MobileAPI.AuditLog
{
    public class RequestLogData
    {
        public string ControllerName { get; set; }
        public string ActionName { get; set; }
        public string Browser { get; set; }
        public string Ip { get; set; }
        public string Scheme { get; set; }
        public string Method { get; set; }
        public string ServerHost { get; set; }
        public string Path { get; set; }
        public string QueryString { get; set; }
        public string Body { get; set; }
        public string TrueClientIp { get; set; }
        public string XForwardedFor { get; set; }
    }
}
