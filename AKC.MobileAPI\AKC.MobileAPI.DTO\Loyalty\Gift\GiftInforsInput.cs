﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class GiftInforsInput
    {
        [Required]
        public string MemberCode { get; set; }
        public string Filter { get; set; }
        public int? GroupTypeFilter { get; set; }
        //public string StatusFilter { get; set; }
        public string Sorting { get; set; }

        [Range(0, int.MaxValue)]
        public int MaxResultCount { get; set; }

        [Range(0, int.MaxValue)]
        public int SkipCount { get; set; }

        [Range(0, 100)]
        public int MaxItem { get; set; }
        
        public bool SimplifiedResponse { get; set; } = true;
    }

    public class GetForHomePageNoMemberCodeInput
    {
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; } // Soos luong GiftGroup
        public int MaxItem { get; set; } // So luong gift per group
        public string Channel { get; set; } = "LinkID";
        [Range(0, int.MaxValue)]
        public int SkipGiftCount { get; set; }
        public int? GroupTypeFilter { get; set; }
        public int? GiftGroupId { get; set; }
    }

    public class GetGiftGroupForHomePageV1Dot1Input
    {
        [Required]
        public string MemberCode { get; set; }
    }

    public class GetGiftGroupForHomePageV1Dot1Output
    {
        public GiftInforsOutPutForMB Result { get; set; }
        public string TargetUrl { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
        public bool UnAuthorizedRequest { get; set; }
        public bool __abp { get; set; }
    }
}
