﻿using AKC.MobileAPI.DTO.Loyalty.Wheel;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Abstract.Loyalty
{
    public interface ILoyaltyWheelService
    {
        Task<LoyaltyResponeOutput<GetListWheelOutput>> GetListWheel(GetListWheelInput input);
        Task<LoyaltyResponeOutput<GetListWheelOutput>> GetSystemWheels(GetListWheelInput input);

        Task<LoyaltyResponeOutput<GetOneWheelOutput>> GetOneWheelByMemberCode(GetOneWheelInput input);

        Task<LoyaltyResponeOutput<CreateOrUpdateWheelByMemberCodeOutput>> CreateWheel(CreateWheelInput input);

        Task<LoyaltyResponeOutput<CreateOrUpdateWheelByMemberCodeOutput>> UpdateWheel(UpdateWheelInput input);
        Task<LoyaltyResponeOutput<SpinWheelOutput>> Spin(SpinWheelInput input);
        Task<LoyaltyResponeOutput<GetSpinHistoryOutput>> GetSpinHistory(GetSpinHistoryInput input);
        Task<LoyaltyResponeOutput<DeleteWheelOutput>> DeleteWheel(DeleteWheelInput input);
    }
}
