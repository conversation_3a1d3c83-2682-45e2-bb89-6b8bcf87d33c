﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.SecondaryCustomer
{
    public class LoyaltyVerifyReferralCodeInput
    {
        public string NationalId { get; set; }
        public string ReferralCode { get; set; }
        public string[] DistributionChannelList { get; set; }
        public decimal ReferenceAmount { get; set; }
    }

    public class LoyaltyVerifyReferralCodeInputDto
    {
        public long TenantId { get; set; }
        public long MerchantId { get; set; }
        public string MemberCode { get; set; }
        public string ReferralCode { get; set; }
        public string[] DistributionChannelList { get; set; }
        public decimal ReferenceAmount { get; set; }
    }

    public class CheckRefCodeInput
    {
        public string RefCode { get; set; }
    }

    public class CheckRefCodeOutput
    {
        public bool isExit { get; set; } = false;

    }

    public class CheckMemberHasInvitedOutput
    {
        public bool isInvited { get; set; } = false;
    }

    public class CheckMemberHasInvitedInput
    {
        [Required]
        public string MemberCode { get; set; }
    }
}
