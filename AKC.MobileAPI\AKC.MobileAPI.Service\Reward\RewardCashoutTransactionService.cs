﻿using AKC.MobileAPI.DTO.Reward.CashoutTransaction;
using AKC.MobileAPI.Service.Abstract.Reward;
using AKC.MobileAPI.Service.Constants;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Service.Reward
{
    public class RewardCashoutTransactionService : RewardBaseService, IRewardCashoutTransactionService
    {
        public RewardCashoutTransactionService(IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<RewardCreateCashoutTransactionOutput> CreateCashoutTransaction(RewardCreateCashoutTransactionInput input)
        {
            var request = new RewardCreateCashoutTransactionDto()
            {
                MemberId = input.MemberId,
                NationalId = input.MemberCode,
                MerchantId = input.MerchantId,
                TokenAmount = input.TokenAmount,
                OrderCode = input.OrderCode
            };
            var result = await PostRewardAsync<RewardCreateCashoutTransactionOutputDto>(RewardApiUrl.TOPUP_CASHOUT_CREATE, request);
            return new RewardCreateCashoutTransactionOutput()
            {
                Items = new RewardCreateCashoutTransactionResult()
                {
                    MemberCode = result.Items.NationalId,
                    MemberId = result.Items.MemberId,
                    TokenAmount = result.Items.TokenAmount,
                    MoneyAmount = result.Items.MoneyAmount,
                    FeeAmount = result.Items.FeeAmount,
                    TokenTransactionId = result.Items.TokenTransactionId,
                },
                Message = result.Message,
                MessageDetail = result.MessageDetail,
                Result = result.Result,
            };
        }

        public async Task<RewardRevertCashoutTransactionOutput> RevertCashoutTransaction(RewardRevertCashoutTransactionInput input)
        {
            return await PostRewardAsync<RewardRevertCashoutTransactionOutput>(RewardApiUrl.TOPUP_CASHOUT_REVERT, input);
        }

        public async Task<RewardValidateBalanceAbleToCashoutOutput> ValidateBalanceAbleToCashout(RewardValidateBalanceAbleToCashoutInput input)
        {
            var request = new RewardValidateBalanceAbleToCashoutDto()
            {
                MemberId = input.MemberId,
                NationalId = input.MemberCode,
                WalletAddress = input.UserAddress
            };
            return await PostRewardAsync<RewardValidateBalanceAbleToCashoutOutput>(RewardApiUrl.ValidateBalanceAbleToCashout, request);
        }

        public async Task<RewardGetGlobalSettingOutput> GetGlobalSetting()
        {
            return await GetRewardAsync<RewardGetGlobalSettingOutput>(RewardApiUrl.GetGlobalSettingInfo);
        }
    }
}
