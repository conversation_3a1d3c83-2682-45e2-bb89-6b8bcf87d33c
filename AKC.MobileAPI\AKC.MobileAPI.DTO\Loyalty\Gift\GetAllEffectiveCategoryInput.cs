﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace AKC.MobileAPI.DTO.Loyalty.Gift
{
    public class GetAllEffectiveCategoryInput
    {
        [Required]
        public string MemberCode { get; set; }
        public string Filter { get; set; }
        public string FullGiftCategoryCodeFilter { get; set; }
        public bool? IsEGiftFilter { get; set; }
        public int MaxItem { get; set; }
        public int? GiftGroupTypeFilter { get; set; }
        public decimal? MaxRequiredCoinFilter { get; set; }
        public decimal? FromCointFilter { get; set; }
        public decimal? ToCoinFilter { get; set; }
        public string RegionCodeFilter { get; set; }
        public string Sorting { get; set; }
        public string GiftGroupNameFilter { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
        public string BrandIdFilter { get; set; }
        public string ThirdPartyCategoryIdFilter { get; set; }
        public int? ThirdPartyBrandMappingIdFilter { get; set; }
        public string GiftTypeFilter { get; set; }
        public string CategoryTypeCode { get; set; }
    }

    public class GetGiftGroupByGroupTypeInput
    {
        [Required]
        public string MemberCode { get; set; }
        [Required]
        public int GroupTypeFilter { get; set; }
       // [Required]
        public string BrandIdFilter { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
        public string Sorting { get; set; }

    }
}
