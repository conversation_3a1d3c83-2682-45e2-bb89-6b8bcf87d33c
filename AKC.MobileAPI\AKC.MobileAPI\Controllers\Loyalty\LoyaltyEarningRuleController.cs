﻿using AKC.MobileAPI.DTO.Loyalty.EarningRule;
using AKC.MobileAPI.Service.Abstract.Loyalty;
using AKC.MobileAPI.Service.Common;
using AKC.MobileAPI.Service.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace AKC.MobileAPI.Controllers.Loyalty
{
    [Route("api/earning-rule")]
    [ApiController]
    [ApiConventionType(typeof(DefaultApiConventions))]
    [Authorize]
    public class LoyaltyEarningRuleController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly ILoyaltyEarningRuleService _loyaltyEarningRuleService;
        private readonly ICommonHelperService _commonHelperService;
        private readonly IExceptionReponseService _exceptionReponseService;

        public LoyaltyEarningRuleController(
            ILoyaltyEarningRuleService loyaltyEarningRuleService,
            ILogger<LoyaltyEarningRuleController> logger,
            ICommonHelperService commonHelperService,
            IExceptionReponseService exceptionReponseService
            )
        {
            _logger = logger;
            _loyaltyEarningRuleService = loyaltyEarningRuleService;
            _commonHelperService = commonHelperService;
            _exceptionReponseService = exceptionReponseService;
        }

        [HttpPost("get-list")]
        public async Task<ActionResult<EarningRuleOutput>> GetList([FromBody] EarningRuleInput input)
        {
            try
            {
                var authorization = Request.Headers["Authorization"].ToString();
                var checkAuthen = _commonHelperService.GetResponseInvalidToken(authorization, input.MemberCode);
                if (checkAuthen != null)
                {
                    return StatusCode(401, checkAuthen);
                }
                var result = await _loyaltyEarningRuleService.GetEarningRules(input);
                return StatusCode(200, result);
            }
            catch (Exception ex)
            {

                var res = await _exceptionReponseService.GetExceptionLoyaltyReponse(ex);
                _logger.LogError(ex, "GetListEarningRule Error - " + JsonConvert.SerializeObject(ex));
                return StatusCode(400, res);
            }
        }
    }
}
