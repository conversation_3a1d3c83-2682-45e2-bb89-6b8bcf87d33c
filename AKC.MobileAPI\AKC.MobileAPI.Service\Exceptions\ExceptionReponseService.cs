﻿using AKC.MobileAPI.DTO.Base;
using AKC.MobileAPI.Service.Exceptions.ThirdParty;
using AKC.MobileAPI.Service.Helpers.ThirdParty.VPBank;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using AKC.MobileAPI.Service.Helpers.ThirdParty.Appota;

namespace AKC.MobileAPI.Service.Exceptions
{
    public class ExceptionReponseService : IExceptionReponseService
    {
        public Task<RewardErrorResponse> GetExceptionRewardReponse(Exception ex)
        {
            try
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var errorData = ex.Data["ErrorData"].ToString();
                    dynamic errorBody = JsonConvert.DeserializeObject(errorData);
                    if (errorBody.status == 500 && errorBody.result != null && errorBody.result.message != null)
                    {
                        var resTxt = new RewardErrorResponse()
                        {
                            Result = 400,
                            Code = errorBody.result.code.ToString(),
                            MessageDetail = errorBody.result.message.ToString(),
                            Message = HttpStatusCode.BadRequest.ToString(),
                        };
                        return Task.FromResult(resTxt);
                    }
                    else
                    {
                        var json = JsonConvert.DeserializeObject<RewardErrorValidationResponse>(errorData);
                        if (json != null)
                        {
                            if (json.Message.GetType() == typeof(string))
                            {
                                var resTxt = new RewardErrorResponse()
                                {
                                    Code = "SystemError",
                                    Result = 400,
                                    MessageDetail = json.Message,
                                    Message = HttpStatusCode.BadRequest.ToString(),
                                };
                                return Task.FromResult(resTxt);
                            }
                            else if (json.Code == 400 && json.Message != null)
                            {
                                var txt = new List<string>();
                                json.Message.ForEach(x =>
                                {
                                    foreach (KeyValuePair<string, string> err in x.Constraints)
                                    {
                                        txt.Add(err.Value.ToString());
                                    }
                                });
                                var resArr = new RewardErrorResponse()
                                {
                                    Code = "SystemError",
                                    Result = 400,
                                    MessageDetail = txt,
                                    Message = HttpStatusCode.BadRequest.ToString(),
                                };
                                return Task.FromResult(resArr);
                            }
                        }
                    }
                }
                else if (ex.GetType() == typeof(ArgumentException))
                {
                    var resArg = new RewardErrorResponse()
                    {
                        Code = "SystemError",
                        Result = 400,
                        MessageDetail = null,
                        Message = ex.Message.ToString(),
                    };
                    return Task.FromResult(resArg);
                }
                var res = new RewardErrorResponse()
                {
                    Code = "SystemError",
                    Result = 400,
                    MessageDetail = null,
                    Message = "System error",
                };
                return Task.FromResult(res);
            }
            catch
            {
                var res = new RewardErrorResponse()
                {
                    Code = "SystemError",
                    Result = 400,
                    MessageDetail = null,
                    Message = "System error",
                };
                return Task.FromResult(res);
            }

        }

        public Task<RewardInvalidResponse> GetExceptionWithDataRewardReponse(Exception ex)
        {
            try
            {
                if (ex.GetType() == typeof(RewardException))
                {
                    var errorData = ex.Data["ErrorData"].ToString();
                    dynamic errorBody = JsonConvert.DeserializeObject(errorData);
                    if (errorBody.status == 500 && errorBody.result != null && errorBody.result.message != null)
                    {
                        var resTxt = new RewardInvalidResponse()
                        {
                            Result = 400,
                            Code = errorBody.result.code.ToString(),
                            numberOfRetries = errorBody.result.data != null && errorBody.result.data.numberOfRetries != null ? Int32.Parse(errorBody.result.data.numberOfRetries.ToString()) : null,
                            MessageDetail = errorBody.result.message.ToString(),
                            remainingTime = errorBody.result.data != null && errorBody.result.data.remainingTime != null ? int.Parse(errorBody.result.data.remainingTime.ToString()) : null,
                            Message = HttpStatusCode.BadRequest.ToString(),
                        };
                        return Task.FromResult(resTxt);
                    }
                    else
                    {
                        var json = JsonConvert.DeserializeObject<RewardErrorValidationResponse>(errorData);
                        if (json != null)
                        {
                            if (json.Message.GetType() == typeof(string))
                            {
                                var resTxt = new RewardInvalidResponse()
                                {
                                    Code = "SystemError",
                                    Result = 400,
                                    MessageDetail = json.Message,
                                    Message = HttpStatusCode.BadRequest.ToString(),
                                };
                                return Task.FromResult(resTxt);
                            }
                            else if (json.Code == 400 && json.Message != null)
                            {
                                var txt = new List<string>();
                                json.Message.ForEach(x =>
                                {
                                    foreach (KeyValuePair<string, string> err in x.Constraints)
                                    {
                                        txt.Add(err.Value.ToString());
                                    }
                                });
                                var resArr = new RewardInvalidResponse()
                                {
                                    Code = "SystemError",
                                    Result = 400,
                                    MessageDetail = txt,
                                    Message = HttpStatusCode.BadRequest.ToString(),
                                };
                                return Task.FromResult(resArr);
                            }
                        }
                    }
                }
                else if (ex.GetType() == typeof(ArgumentException))
                {
                    var resArg = new RewardInvalidResponse()
                    {
                        Code = "SystemError",
                        Result = 400,
                        MessageDetail = null,
                        Message = ex.Message.ToString(),
                    };
                    return Task.FromResult(resArg);
                }
                var res = new RewardInvalidResponse()
                {
                    Code = "SystemError",
                    Result = 400,
                    MessageDetail = null,
                    Message = "System error",
                };
                return Task.FromResult(res);
            }
            catch
            {
                var res = new RewardInvalidResponse()
                {
                    Code = "SystemError",
                    Result = 400,
                    MessageDetail = null,
                    Message = "System error",
                };
                return Task.FromResult(res);
            }
        }

        public Task<LoyaltyErrorResponse> GetExceptionLoyaltyReponse(Exception ex)
        {
            try
            {
                if (ex.GetType() == typeof(LoyaltyException))
                {
                    var errorData = ex.Data["ErrorData"].ToString();
                    dynamic errorBody = JsonConvert.DeserializeObject(errorData);
                    if (errorBody != null && errorBody.error != null && errorBody.error.message != null)
                    {
                        if (errorBody.error.validationErrors != null)
                        {
                            var txt = new List<string>();
                            foreach (var item in errorBody.error.validationErrors)
                            {
                                txt.Add(item.message.ToString());
                            }
                            var resValid = new LoyaltyErrorResponse()
                            {
                                Result = 400,
                                Code = errorBody.error.code.ToString(),
                                MessageDetail = txt,
                                Message = errorBody.error.message.ToString(),
                            };
                            return Task.FromResult(resValid);
                        }
                        var resErr = new LoyaltyErrorResponse()
                        {
                            Result = 400,
                            Code = errorBody.error.code.ToString(),
                            MessageDetail = errorBody?.error?.details?.ToString(),
                            Message = errorBody.error.message.ToString(),
                        };
                        return Task.FromResult(resErr);
                    }
                    var res = new LoyaltyErrorResponse()
                    {
                        Code = "SystemError",
                        Result = 400,
                        MessageDetail = null,
                        Message = "System error",
                    };
                    return Task.FromResult(res);
                }
                else if (ex.GetType() == typeof(LoyaltyTimeoutException))
                {
                    var timeoutEx = ex as LoyaltyTimeoutException;
                    var resTimeout = new LoyaltyErrorResponse()
                    {
                        Code = "408",
                        Result = 408,
                        MessageDetail = $"Request timeout when calling API: {timeoutEx?.ApiUrl}",
                        Message = "Request timeout. Please try again later.",
                    };
                    return Task.FromResult(resTimeout);
                }
                else if (ex.GetType() == typeof(ArgumentException))
                {
                    var resArg = new LoyaltyErrorResponse()
                    {
                        Code = "SystemError",
                        Result = 400,
                        MessageDetail = null,
                        Message = ex.Message.ToString(),
                    };
                    return Task.FromResult(resArg);
                } else if (ex.GetType() == typeof(ThirdPartyVPBankException))
                {
                    var errorData = ex.Data["ErrorData"].ToString();
                    dynamic errorBody = JsonConvert.DeserializeObject(errorData);
                    var error = ThirdPartyVPBankErrorMessageHelper.GetError(errorBody.error.ToString());
                    var resArg = new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = error.Code,
                        MessageDetail = null,
                        Message = error.Message,
                    };
                    return Task.FromResult(resArg);
                }
                else if (ex.GetType() == typeof(ThirdPartyAppotaException))
                {
                    var errorData = ex.Data["ErrorData"].ToString();
                    var apiGroup = ex.Data["ApiGroup"]?.ToString() ?? "1";
                    dynamic errorBody = JsonConvert.DeserializeObject(errorData);
                    // Choox nafy APPOTA van xai dc ThirdPartyVPBankErrorMessageHelper nhe'
                    var _____errorCode = "";
                    if (apiGroup == "1")
                    {
                        _____errorCode = errorBody.code?.ToString();
                    }
                    else if (apiGroup == "2")
                    {
                        _____errorCode = errorBody.errorCode?.ToString();
                    }
                    var error = ThirdPartyAppotaErrorMessageHelper.GetError(_____errorCode, apiGroup);
                    var resArg = new LoyaltyErrorResponse()
                    {
                        Result = 400,
                        Code = error.ErrorCode,
                        MessageDetail = null,
                        Message = error.ErrorMessage,
                    };
                    return Task.FromResult(resArg);
                }
                var resSys = new LoyaltyErrorResponse()
                {
                    Code = "SystemError",
                    Result = 400,
                    MessageDetail = null,
                    Message = "System error",
                };
                return Task.FromResult(resSys);
            }
            catch
            {
                var res = new LoyaltyErrorResponse()
                {
                    Code = "SystemError",
                    Result = 400,
                    MessageDetail = ex.Data["ErrorData"]?.ToString(),
                    Message = "System error",
                };
                return Task.FromResult(res);
            }
        }

        public Task<GamificationErrorResponse> GetExceptionGamificationReponse(Exception ex)
        {
            try
            {
                if (ex.GetType() == typeof(GamificationException))
                {
                    var errorData = ex.Data["ErrorData"].ToString();
                    dynamic errorBody = JsonConvert.DeserializeObject(errorData);
                    if (errorBody.statusCode == 500 && errorBody != null && errorBody.message != null)
                    {
                        var resTxt = new GamificationErrorResponse()
                        {
                            Result = 400,
                            Code = errorBody.statusCode.ToString(),
                            MessageDetail = errorBody.message.ToString(),
                            Message = HttpStatusCode.BadRequest.ToString(),
                        };
                        return Task.FromResult(resTxt);
                    }
                    else if (errorBody.statusCode == 404)
                    {
                        var resTxt = new GamificationErrorResponse()
                        {
                            Result = 404,
                            Code = errorBody.statusCode.ToString(),
                            MessageDetail = errorBody.message.ToString(),
                            Message = HttpStatusCode.BadRequest.ToString(),
                        };
                        return Task.FromResult(resTxt);
                    }
                    else
                    {
                        var json = JsonConvert.DeserializeObject<RewardErrorValidationResponse>(errorData);
                        if (json != null)
                        {
                            if (json.Message.GetType() == typeof(string))
                            {
                                var resTxt = new GamificationErrorResponse()
                                {
                                    Code = "SystemError",
                                    Result = 400,
                                    MessageDetail = json.Message,
                                    Message = HttpStatusCode.BadRequest.ToString(),
                                };
                                return Task.FromResult(resTxt);
                            }
                            else if (json.Code == 400 && json.Message != null)
                            {
                                var txt = new List<string>();
                                json.Message.ForEach(x =>
                                {
                                    foreach (KeyValuePair<string, string> err in x.Constraints)
                                    {
                                        txt.Add(err.Value.ToString());
                                    }
                                });
                                var resArr = new GamificationErrorResponse()
                                {
                                    Code = "SystemError",
                                    Result = 400,
                                    MessageDetail = txt,
                                    Message = HttpStatusCode.BadRequest.ToString(),
                                };
                                return Task.FromResult(resArr);
                            }
                        }
                    }
                }
                else if (ex.GetType() == typeof(ArgumentException))
                {
                    var resArg = new GamificationErrorResponse()
                    {
                        Code = "SystemError",
                        Result = 400,
                        MessageDetail = null,
                        Message = ex.Message.ToString(),
                    };
                    return Task.FromResult(resArg);
                }
                var res = new GamificationErrorResponse()
                {
                    Code = "SystemError",
                    Result = 400,
                    MessageDetail = null,
                    Message = HttpStatusCode.InternalServerError.ToString(),
                };
                return Task.FromResult(res);
            }
            catch
            {
                var res = new GamificationErrorResponse()
                {
                    Code = "SystemError",
                    Result = 400,
                    MessageDetail = null,
                    Message = HttpStatusCode.InternalServerError.ToString(),
                };
                return Task.FromResult(res);
            }
        }
    }
}
